<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">


    <style name="appTheme" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">#000000</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <style name="Chuck.Theme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">#00BCD4</item>
        <item name="colorPrimaryDark">#0097A7</item>
        <item name="colorAccent">#FF9800</item>
    </style>

    <style name="Chuck.TextAppearance.ListItem" parent="android:TextAppearance">
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Chuck.TextAppearance.Label" parent="android:TextAppearance.Small">
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Chuck.TextAppearance.Value" parent="android:TextAppearance.Small">
        <item name="android:paddingLeft">16dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:textIsSelectable">true</item>
    </style>

    <style name="Chuck.TextAppearance.TransactionTitle" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:gravity">top</item>
        <item name="android:textSize" tools:ignore="SpUsage">16dp</item>
        <item name="android:maxLines">2</item>
    </style>

    <style name="kit_check_box_left">
        <item name="android:gravity">center</item>
        <item name="android:textSize">14dp</item>
        <item name="android:textColor">@color/kit_radio_button_text_color</item>
        <item name="android:layout_height">34dp</item>
        <item name="android:layout_width">70dp</item>
        <item name="android:button">@null</item>
        <item name="android:background">@drawable/kit_radio_button_background_left</item>
    </style>

    <style name="kit_check_box_center">
        <item name="android:gravity">center</item>
        <item name="android:textSize">14dp</item>
        <item name="android:textColor">@color/kit_radio_button_text_color</item>
        <item name="android:layout_height">34dp</item>
        <item name="android:layout_width">70dp</item>
        <item name="android:button">@null</item>
        <item name="android:background">@drawable/kit_radio_button_background</item>
    </style>

    <style name="kit_check_box_right">
        <item name="android:gravity">center</item>
        <item name="android:textSize">14dp</item>
        <item name="android:textColor">@color/kit_radio_button_text_color</item>
        <item name="android:layout_height">34dp</item>
        <item name="android:layout_width">70dp</item>
        <item name="android:button">@null</item>
        <item name="android:background">@drawable/kit_radio_button_background_right</item>
    </style>

    <style name="text_18_ffffff">
        <item name="android:textSize">18.0dip</item>
        <item name="android:textColor">#777733</item>
    </style>

    <style name="text_16_666666">
        <item name="android:textSize">16.0dip</item>
        <item name="android:textColor">#ff666666</item>
    </style>

    <style name="sdw_white">
        <item name="android:shadowColor">#7fffffff</item>
        <item name="android:shadowDx">0.0</item>
        <item name="android:shadowDy">0.65</item>
        <item name="android:shadowRadius">1.0</item>
    </style>

    <style name="sdw_79351b">
        <item name="android:shadowColor">#ff79351b</item>
        <item name="android:shadowDx">0.0</item>
        <item name="android:shadowDy">1.0</item>
        <item name="android:shadowRadius">1.0</item>
    </style>

    <style name="text_15_black_sdw" parent="@style/sdw_79351b">
        <item name="android:textSize">15.0dip</item>
        <item name="android:textColor">#000000</item>
    </style>

    <style name="text_15_666666_sdw" parent="@style/sdw_white">
        <item name="android:textSize">15.0dip</item>
        <item name="android:textColor">#ff666666</item>
    </style>

    <style name="Dialog" parent="android:style/Theme.Dialog">
        <item name="android:background">#00000000</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
    </style>
</resources>