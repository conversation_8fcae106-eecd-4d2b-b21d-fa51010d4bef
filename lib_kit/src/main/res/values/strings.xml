<resources xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingTranslation">
    <string name="kit_app_name">devHelperTools</string>
    <string name="kit_chuck_name">网络请求</string>
    <string name="kit_chuck_notification_title">网络数据监控平台</string>
    <string name="kit_chuck_clear">Clear</string>
    <string name="kit_chuck_browse_sql_database">DB</string>
    <string name="kit_chuck_overview">Overview</string>
    <string name="kit_chuck_request">Request</string>
    <string name="kit_chuck_response">Response</string>
    <string name="kit_chuck_url">URL</string>
    <string name="kit_chuck_method">Method</string>
    <string name="kit_chuck_params">params</string>
    <string name="kit_chuck_protocol">Protocol</string>
    <string name="kit_chuck_status">Status</string>
    <string name="kit_chuck_ssl">SSL</string>
    <string name="kit_chuck_request_time">Request time</string>
    <string name="kit_chuck_response_time">Response time</string>
    <string name="kit_chuck_duration">Duration</string>
    <string name="kit_chuck_request_size">Request size</string>
    <string name="kit_chuck_response_size">Response size</string>
    <string name="kit_chuck_total_size">Total size</string>
    <string name="kit_chuck_yes">Yes</string>
    <string name="kit_chuck_no">No</string>
    <string name="kit_chuck_share">Share</string>
    <string name="kit_chuck_share_as_text">Share as text</string>
    <string name="kit_chuck_share_as_curl">Share as curl command</string>
    <string name="kit_chuck_body_omitted">(encoded or binary body omitted)</string>
    <string name="kit_chuck_search">Search</string>
    <string name="kit_chuck_body_unexpected_eof">\n\n--- Unexpected end of content ---</string>
    <string name="kit_chuck_body_content_truncated">\n\n--- Content truncated ---</string>
    <string name="kit_notification_category">Chuck HTTP notifications</string>
    <string name="string_fileprovider_authority">cn.com.vau.fileprovider</string>
    <string name="kit_target_element_not_found">could not found view in (%1$.0f , %2$.0f), please select view again</string>
</resources>
