package cn.com.vau.kit.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Environment;

import com.facebook.cache.disk.DiskCacheConfig;
import com.facebook.common.memory.NoOpMemoryTrimmableRegistry;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.imagepipeline.core.ImagePipelineConfig;
import cn.com.vau.kit.Kit;



public class KitFrescoUtils {

    private KitFrescoUtils() {
    }

    private static KitFrescoUtils instance = new KitFrescoUtils();

    public static KitFrescoUtils getInstance() {
        return instance;
    }

    public void init(Context context) {
        DiskCacheConfig build = DiskCacheConfig.newBuilder(context)
                .setBaseDirectoryName("image")
                .setMaxCacheSize(1024 * 1024 * 10)
                .setBaseDirectoryPath(Environment.getExternalStorageDirectory())
                .build();

        //生成配置文件
        ImagePipelineConfig config = ImagePipelineConfig.newBuilder(context)
                .setMainDiskCacheConfig(build)
                .setBitmapsConfig(Bitmap.Config.RGB_565)
                .setMemoryTrimmableRegistry(NoOpMemoryTrimmableRegistry.getInstance())
                .build();
        Fresco.initialize(context, config);
    }


    /**
     * 设置加载网络地址
     *
     * @param simpleDraweeView
     * @param url
     */
    public void loadUri(SimpleDraweeView simpleDraweeView, String url) {
        final Uri uri = Uri.parse(url);
        simpleDraweeView.setImageURI(uri);
    }

    /**
     * 加载本地文件
     *
     * @param simpleDraweeView
     * @param path
     */
    public void loadLocalFile(SimpleDraweeView simpleDraweeView, String path) {
        final Uri uri = Uri.parse("file://" + path);
        simpleDraweeView.setImageURI(uri);
    }

    /**
     * 加载res资源
     *
     * @param simpleDraweeView
     * @param res
     */
    public void loadRes(SimpleDraweeView simpleDraweeView, int res) {
        Uri uri = Uri.parse("res://" +
                Kit.getInstance().getApplication().getPackageName() +
                "/" + res);
        simpleDraweeView.setImageURI(uri);
    }

} 