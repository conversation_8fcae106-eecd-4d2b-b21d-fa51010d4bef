package cn.com.vau.kit.util;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;

import cn.com.vau.kit.Kit;
import cn.com.vau.kit.activity.MainPanelActivity;
import cn.com.vau.kit.activity.TransferCenterKitActivity;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;



public class KitForegroundUtil {


    private static final KitForegroundUtil instance = new KitForegroundUtil();

    private KitForegroundUtil() {
    }

    public static KitForegroundUtil getInstance() {
        return instance;
    }

    public boolean isAppForeground() {
        Context applicationContext = Kit.getInstance().getApplication().getApplicationContext();
        ActivityManager activityManager = (ActivityManager) applicationContext.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> processes;
        if (activityManager != null) {
            processes = activityManager.getRunningAppProcesses();
        } else {
            return false;
        }
        for (ActivityManager.RunningAppProcessInfo processInfo : processes) {
            if (processInfo.processName.equals(applicationContext.getPackageName())) {
                if (processInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
                    return true;
                }
            }
        }
        return false;
    }

    private final List<WeakReference<Activity>> taskActivity = new ArrayList<>();

    private int count;

    public int getCount() {
        return count;
    }

    public int getTaskActivity() {
        return taskActivity.size();
    }


    public boolean isRootActivity(Activity activity) {
        if (taskActivity != null) {
            WeakReference<Activity> activityWeakReference = taskActivity.get(0);
            if (activityWeakReference != null) {
                return activityWeakReference.get() == activity;
            }
        }
        return false;
    }


    public Activity getRootActivity() {
        if (taskActivity.size() > 0) {
            WeakReference<Activity> activityWeakReference = taskActivity.get(0);
            if (activityWeakReference != null) {
                return activityWeakReference.get();
            }
        }
        return null;
    }

    public Activity getTopActivity() {
        int size = taskActivity.size();
        if (size == 0) {
            return null;
        }
        WeakReference<Activity> weakReference = taskActivity.get(size - 1);
        return weakReference.get();
    }


    public void finishStackPanelAct() {
        for (WeakReference<Activity> weakReference : taskActivity) {
            if (weakReference == null) continue;
            Activity activity = weakReference.get();
            if (activity instanceof MainPanelActivity
                    || activity instanceof TransferCenterKitActivity) {
                activity.finish();
            }

        }
    }

    public void register(Application application) {
        application.registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
                taskActivity.add(new WeakReference<>(activity));
                count++;
            }

            @Override
            public void onActivityStarted(Activity activity) {

            }

            @Override
            public void onActivityResumed(Activity activity) {

            }

            @Override
            public void onActivityPaused(Activity activity) {

            }

            @Override
            public void onActivityStopped(Activity activity) {

            }

            @Override
            public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

            }

            @Override
            public void onActivityDestroyed(Activity activity) {
                Iterator<WeakReference<Activity>> iterator = taskActivity.iterator();
                while (iterator.hasNext()) {
                    WeakReference<Activity> next = iterator.next();
                    Activity activity1 = next.get();
                    if (activity1 == null
                            || activity1 == activity) {
                        iterator.remove();
                        break;
                    }
                }
                count--;
            }
        });
    }

}