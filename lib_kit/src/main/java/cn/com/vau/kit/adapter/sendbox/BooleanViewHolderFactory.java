package cn.com.vau.kit.adapter.sendbox;

import android.content.Context;
import android.content.SharedPreferences;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Spinner;
import android.widget.TextView;

import cn.com.vau.kit.Kit;
import cn.com.vau.kit.R;
import cn.com.vau.kit.adapter.ViewAndHolderItem;
import cn.com.vau.kit.adapter.base.BaseKitHolder;
import cn.com.vau.kit.bean.BaseSpInfo;
import cn.com.vau.kit.bean.BooleanSpInfo;

import java.util.ArrayList;
import java.util.List;


public class BooleanViewHolderFactory implements ViewAndHolderItem<BaseSpInfo, BooleanViewHolderFactory.BooleanViewHolder> {

    private Context context;

    private SharedPreferences.Editor editor;

    public BooleanViewHolderFactory(Context context, SharedPreferences.Editor editor) {
        this.context = context;
        this.editor = editor;
    }

    @Override
    public boolean isSupport(BaseSpInfo baseSpInfo) {
        return baseSpInfo instanceof BooleanSpInfo;
    }

    @Override
    public BooleanViewHolder createHolder(View convertView) {
        return new BooleanViewHolder(convertView, editor);
    }

    @Override
    public View createConvertView() {
        return LayoutInflater.from(context).inflate(R.layout.kit_send_box_sp_boolean_item, null);
    }

    public static class BooleanViewHolder extends BaseKitHolder<BooleanSpInfo> {

        private final List<Boolean> selected = new ArrayList<Boolean>() {{
            add(true);
            add(false);
        }};

        private SharedPreferences.Editor editor;
        private final TextView mKeyName;
        private final TextView mValueClass;
        private final Spinner mValue;

        public BooleanViewHolder(View convertView, SharedPreferences.Editor editor) {
            this.editor = editor;
            mKeyName = convertView.findViewById(R.id.mKeyName);
            mValueClass = convertView.findViewById(R.id.mValueClass);
            mValue = convertView.findViewById(R.id.mValue);
        }

        @Override
        protected void initValue(final BooleanSpInfo booleanSpInfo) {
            mKeyName.setText(booleanSpInfo.getKey());
            mValueClass.setText("Boolean");
            mValue.setAdapter(new ArrayAdapter<>(Kit.getInstance().getApplication(), android.R.layout.simple_list_item_1, selected));
            mValue.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> adapterView, View view, int position, long l) {
                    Boolean value = selected.get(position);
                    if (editor != null) {
                        editor.putBoolean(booleanSpInfo.getKey(), value);
                        booleanSpInfo.setValue(value);
                    }
                }

                @Override
                public void onNothingSelected(AdapterView<?> adapterView) {

                }
            });
            mValue.setSelection(selected.indexOf(booleanSpInfo.isValue()));
        }
    }

}
