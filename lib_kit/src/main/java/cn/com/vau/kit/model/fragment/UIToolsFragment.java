package cn.com.vau.kit.model.fragment;

import android.app.Activity;
import android.graphics.Rect;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import cn.com.vau.kit.R;
import cn.com.vau.kit.activity.KITUITransparentActivity;
import cn.com.vau.kit.activity.UserInfoActivity;
import cn.com.vau.kit.bean.CommonToolsBean;
import cn.com.vau.kit.chunk.internal.view.ExpandCombineLayout;
import cn.com.vau.kit.util.KitAppUtil;
import cn.com.vau.kit.util.KitMainHandler;
import cn.com.vau.kit.util.KitScaleUtil;

import java.util.ArrayList;
import java.util.List;


public class UIToolsFragment extends BaseKitFragment {


    private RecyclerView mRecycleView;

    public static UIToolsFragment newInstance() {
        return new UIToolsFragment();
    }


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_common_tools_fragment, container, false);
    }


    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mRecycleView = view.findViewById(R.id.mRecycleView);
        mRecycleView.setLayoutManager(new GridLayoutManager(getBaseActivity(), 3));
        mRecycleView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, int itemPosition, @NonNull RecyclerView parent) {
                super.getItemOffsets(outRect, itemPosition, parent);
                outRect.set(0, KitScaleUtil.dip2px(30), KitScaleUtil.dip2px(10), 0);
            }
        });
        CommonToolAdapter adapter = new CommonToolAdapter();
        adapter.setData(result);
        mRecycleView.setAdapter(adapter);
    }


    private final List<CommonToolsBean> result = new ArrayList<CommonToolsBean>() {
        {

            add(getCommonItem("取色器", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //初始化颜色选择器
                    initColorPicker();
                }
            }));

            add(getCommonItem("对齐标尺", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    RulerFragment.initRuler();
                }
            }));

            add(getCommonItem("布局边框", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    LayoutBorderFragment.initLayoutBorder();
                }
            }));

            add(getCommonItem("捕捉控件", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    getActivity().finish();
                    KitMainHandler.getMainHandler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            KitAppUtil.open(KITUITransparentActivity.Type.TYPE_EDIT_ATTR);
                        }
                    }, 500);

                }
            }));
            add(getCommonItem("相对位置", R.drawable.kit_dk_sys_info, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    getActivity().finish();
                    KitMainHandler.getMainHandler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            KitAppUtil.open(KITUITransparentActivity.Type.TYPE_RELATIVE_POSITION);
                        }
                    }, 500);

                }
            }));
        }
    };


    private static final String COLOR_TAG = "COLOR_TAG";

    // 初始化颜色选择器
    private void initColorPicker() {
        Activity activity = getBaseActivity();
        if (activity instanceof FragmentActivity) {
            FragmentActivity fragmentActivity = (FragmentActivity) activity;
            FragmentManager supportFragmentManager = fragmentActivity.getSupportFragmentManager();
            Fragment fragment = supportFragmentManager.findFragmentByTag(COLOR_TAG);
            if (fragment == null) {
                fragment = new ColorPickerFragment();
                FragmentTransaction fragmentTransaction = supportFragmentManager.beginTransaction();
                fragmentTransaction.add(fragment, COLOR_TAG);
                fragmentTransaction.commitAllowingStateLoss();
            } else {
                if (fragment instanceof ColorPickerFragment) {
                    ColorPickerFragment colorPickerFragment = (ColorPickerFragment) fragment;
                    colorPickerFragment.requestCaptureScreen();
                }
            }
        }

    }



    private CommonToolsBean getCommonItem(String name, @DrawableRes int resourceBg, View.OnClickListener mListener) {
        CommonToolsBean item = new CommonToolsBean();
        item.setName(name);
        item.setResourceBg(resourceBg);
        item.setmListener(mListener);
        return item;
    }



    private class CommonToolAdapter extends RecyclerView.Adapter<CommonToolAdapter.CommonToolViewHolder> {

        private final List<CommonToolsBean> data = new ArrayList<>();


        private void setData(List<CommonToolsBean> source) {
            data.clear();
            if (source != null) {
                data.addAll(source);
            }
            notifyDataSetChanged();
        }


        @Override
        public CommonToolViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            return new CommonToolViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.kit_common_tool_item, null));
        }

        @Override
        public void onBindViewHolder(CommonToolViewHolder holder, int position) {
            CommonToolsBean commonToolsBean = data.get(position);
            if (commonToolsBean != null) {
                holder.mName.setText(commonToolsBean.getName());
                holder.mImageView.setImageResource(commonToolsBean.getResourceBg());
                holder.itemView.setOnClickListener(commonToolsBean.getmListener());
            }
        }

        @Override
        public int getItemCount() {
            return data.size();
        }

        public class CommonToolViewHolder extends RecyclerView.ViewHolder {

            private ImageView mImageView;

            private TextView mName;

            public CommonToolViewHolder(View itemView) {
                super(itemView);

                mName = itemView.findViewById(R.id.mName);

                mImageView = itemView.findViewById(R.id.mImageView);
            }
        }

    }

}