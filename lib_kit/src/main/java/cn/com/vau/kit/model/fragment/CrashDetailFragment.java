package cn.com.vau.kit.model.fragment;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.method.ScrollingMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import cn.com.vau.kit.R;
import cn.com.vau.kit.adapter.crash.CrashAdapter;
import cn.com.vau.kit.util.KitClipBoardUtil;
import cn.com.vau.kit.util.KitFileUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


public class CrashDetailFragment extends BaseKitFragment {


    private ListView mListView;

    private TextView mTextView;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_crash_detail_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView(view);
        showCrashMenu();
        refreshAdapter();
    }


    //初始化View
    private void initView(@NonNull View view) {
        mListView = view.findViewById(R.id.mListView);
        mTextView = view.findViewById(R.id.mTextView);
        mTextView.setMovementMethod(ScrollingMovementMethod.getInstance());
        mTextView.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                String content = mTextView.getText().toString();
                KitClipBoardUtil.copyToClipBoard(content);
                Toast.makeText(getBaseActivity(), "复制成功", Toast.LENGTH_SHORT).show();
                return false;
            }
        });
    }

    //刷新适配器
    private void refreshAdapter() {
        CrashAdapter adapter = new CrashAdapter();
        mListView.setAdapter(adapter);
        mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                onItemClickListener(parent, position);
            }
        });
        adapter.setData(getFiles());
    }

    //点击奔溃ITEM
    private void onItemClickListener(AdapterView<?> parent, int position) {
        Toast.makeText(getBaseActivity(), "长按复制", Toast.LENGTH_SHORT).show();
        showContentView();
        File itemFile = (File) parent.getItemAtPosition(position);
        mTextView.setText(KitFileUtil.readFile(itemFile));
    }

    //获得奔溃文件集合
    private List<File> getFiles() {
        File crashRootFile = KitFileUtil.getCrashRootFile();
        File[] files = crashRootFile.listFiles();
        if (files == null) return null;
        return new ArrayList<>(Arrays.asList(files));
    }

    // 显示奔溃内容
    private void showContentView() {
        mTextView.setVisibility(View.VISIBLE);
        mListView.setVisibility(View.GONE);
    }

    // 显示奔溃目录
    private void showCrashMenu() {
        mTextView.setVisibility(View.GONE);
        mListView.setVisibility(View.VISIBLE);
    }

    @Override
    public boolean onBackPressListener() {
        boolean canFinish = mListView.getVisibility() == View.VISIBLE;
        if (!canFinish) {
            showCrashMenu();
        }
        return canFinish;
    }
}
