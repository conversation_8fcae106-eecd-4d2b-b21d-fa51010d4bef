package cn.com.vau.kit.shake.info;

import android.app.Activity;
import android.content.res.Resources;
import android.util.DisplayMetrics;

import java.lang.ref.WeakReference;


public class DisplayInfo {
    private WeakReference<Activity> activityWeakReference;

    private static int screenWidth;
    private static int screenHeight;

    private static float density;// 屏幕密度（像素比例：0.75/1.0/1.5/2.0）
    private static int densityDPI;// 屏幕密度（每寸像素：120/160/240/320）

    public DisplayInfo(Activity activity) {
        activityWeakReference = new WeakReference<>(activity);

        initScreenDimension();
    }

    public int getScreenWidth() {
        if (screenWidth == 0) {
            initScreenDimension();
        }

        return screenWidth;
    }

    public int getScreenHeight() {
        if (screenHeight == 0) {
            initScreenDimension();
        }

        return screenHeight;
    }

    public float getDensity() {
        if (density < 0.1) {
            initScreenDimension();
        }

        return density;
    }

    public int getDensityDPI() {
        if (densityDPI == 0) {
            initScreenDimension();
        }

        return densityDPI;
    }

    private void initScreenDimension() {
        Activity activity = activityWeakReference.get();
        if (activity == null) return;

        Resources res = activity.getResources();
        DisplayMetrics metrics = res.getDisplayMetrics();

        screenWidth = metrics.widthPixels;
        screenHeight = metrics.heightPixels;

        density  = metrics.density;
        densityDPI = metrics.densityDpi;
    }
}
