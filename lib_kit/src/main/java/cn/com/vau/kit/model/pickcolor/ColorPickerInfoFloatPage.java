package cn.com.vau.kit.model.pickcolor;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import androidx.annotation.ColorInt;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import cn.com.vau.kit.R;
import cn.com.vau.kit.util.KitColorUtil;
import cn.com.vau.kit.util.KitScaleUtil;



public class ColorPickerInfoFloatPage extends BaseFloatPage implements TouchProxy.OnTouchEventListener {
    private WindowManager mWindowManager;
    private ImageView mColor;
    private TextView mColorHex;
    private ImageView mClose;
    private TouchProxy mTouchProxy = new TouchProxy(this);

    @Override
    protected void onCreate(Context context) {
        super.onCreate(context);
        mWindowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
    }

    @Override
    protected View onCreateView(Context context, ViewGroup view) {
        return LayoutInflater.from(context).inflate(R.layout.kit_float_color_picker_info, null);
    }

    @Override
    protected void onLayoutParamsCreated(WindowManager.LayoutParams params) {
        params.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        params.width = WindowManager.LayoutParams.MATCH_PARENT;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
        params.x = 0;
        params.y = KitScaleUtil.getHeightPixels() - KitScaleUtil.dip2px(85);
    }

    @Override
    protected void onViewCreated(View view) {
        super.onViewCreated(view);
        initView();
    }

    private void initView() {
        getRootView().setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                return mTouchProxy.onTouchEvent(v, event);
            }
        });
        mColor = findViewById(R.id.color);
        mColorHex = findViewById(R.id.color_hex);
        mClose = findViewById(R.id.close);
        mClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                ColorPickConfig.setColorPickOpen(getContext(), false);
                FloatPageManager.getInstance().removeAll(ColorPickerFloatPage.class);
                FloatPageManager.getInstance().removeAll(ColorPickerInfoFloatPage.class);
            }
        });
    }

    public void showInfo(@ColorInt int colorInt, int x, int y) {
        mColor.setImageDrawable(new ColorDrawable(colorInt));
        mColorHex.setText(String.format(ColorPickConstants.TEXT_FOCUS_INFO, KitColorUtil.parseColorInt(colorInt), x + ColorPickConstants.PIX_INTERVAL, y + ColorPickConstants.PIX_INTERVAL));
    }

    @Override
    public void onMove(int x, int y, int dx, int dy) {
        getLayoutParams().x += dx;
        getLayoutParams().y += dy;
        mWindowManager.updateViewLayout(getRootView(), getLayoutParams());
    }

    @Override
    public void onUp(int x, int y) {

    }

    @Override
    public void onDown(int x, int y) {

    }
}