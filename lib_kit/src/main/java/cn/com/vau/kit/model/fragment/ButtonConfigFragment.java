package cn.com.vau.kit.model.fragment;

import android.graphics.Rect;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import cn.com.vau.kit.biz.AppCallBackItf;
import cn.com.vau.kit.biz.ButtonConfigBean;
import cn.com.vau.kit.Kit;
import cn.com.vau.kit.R;
import cn.com.vau.kit.util.KitScaleUtil;

import java.util.ArrayList;
import java.util.List;

public class ButtonConfigFragment extends BaseKitFragment {

    private RecyclerView mRecycleView;

    public static ButtonConfigFragment newInstance() {
        return new ButtonConfigFragment();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_common_tools_fragment, container, false);
    }


    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mRecycleView = view.findViewById(R.id.mRecycleView);
        mRecycleView.setLayoutManager(new GridLayoutManager(getBaseActivity(), 3));
        mRecycleView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, int itemPosition, @NonNull RecyclerView parent) {
                super.getItemOffsets(outRect, itemPosition, parent);
                outRect.set(0, KitScaleUtil.dip2px(30), KitScaleUtil.dip2px(10), 0);
            }
        });

        AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
        if (appCallBackItf != null) {
            List<ButtonConfigBean> buttonConfigList = appCallBackItf.getButtonConfigList();
            ButtonConfigAdapter adapter = new ButtonConfigAdapter();
            adapter.setData(buttonConfigList);
            mRecycleView.setAdapter(adapter);
        }
    }



    private class ButtonConfigAdapter extends RecyclerView.Adapter<ButtonConfigAdapter.CommonToolViewHolder> {

        private final List<ButtonConfigBean> data = new ArrayList<>();


        private void setData(List<ButtonConfigBean> source) {
            data.clear();
            if (source != null) {
                data.addAll(source);
            }
            notifyDataSetChanged();
        }


        @Override
        public CommonToolViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            return new CommonToolViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.kit_common_tool_item, null));
        }

        @Override
        public void onBindViewHolder(CommonToolViewHolder holder, int position) {
            ButtonConfigBean commonToolsBean = data.get(position);
            if (commonToolsBean != null) {
                holder.mName.setText(commonToolsBean.getName());
                holder.itemView.setOnClickListener(commonToolsBean.getmListener());
            }
        }

        @Override
        public int getItemCount() {
            return data.size();
        }

        public class CommonToolViewHolder extends RecyclerView.ViewHolder {


            private TextView mName;

            public CommonToolViewHolder(View itemView) {
                super(itemView);

                mName = itemView.findViewById(R.id.mName);

            }
        }

    }

}