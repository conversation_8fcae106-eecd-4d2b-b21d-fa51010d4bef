<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_MOCK_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.permission.READ_LOGS" />
    <uses-permission android:name="android.permission.INJECT_EVENTS" />
    <uses-permission android:name="android.permission.INTERNET" />

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <uses-feature
        android:glEsVersion="0x00030000"
        android:required="true" />

    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <!-- Window -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />


    <application>
        <activity
            android:name=".activity.TransferCenterKitActivity"
            android:configChanges="orientation|keyboardHidden"
            android:forceDarkAllowed="false"
            android:screenOrientation="portrait"
            tools:targetApi="q" />

        <provider
            android:name=".chunk.internal.data.ChuckContentProvider"
            android:authorities="${applicationId}.chuck.provider"
            android:exported="false" />


        <activity
            android:name=".chunk.internal.ui.ChunkMainActivity"
            android:configChanges="orientation|keyboardHidden"
            android:forceDarkAllowed="false"
            android:label="@string/kit_chuck_name"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:taskAffinity="com.readystatesoftware.chuck.task"
            tools:targetApi="q" />

        <activity
            android:name=".chunk.internal.ui.TransactionActivity"
            android:configChanges="orientation|keyboardHidden"
            android:forceDarkAllowed="false"
            android:parentActivityName=".chunk.internal.ui.ChunkMainActivity"
            android:screenOrientation="portrait"
            tools:targetApi="q" />

        <activity
            android:name=".activity.UserInfoActivity"
            android:configChanges="orientation|keyboardHidden"
            android:forceDarkAllowed="false"
            android:screenOrientation="portrait"
            tools:targetApi="q" />

        <service
            android:name=".chunk.internal.support.ClearTransactionsService"
            android:exported="false"
            android:forceDarkAllowed="false"
            tools:targetApi="q" />

        <service
            android:name=".service.MockClickService"
            android:exported="false"
            android:forceDarkAllowed="false"
            tools:targetApi="q" />

        <activity
            android:name=".activity.MainPanelActivity"
            android:configChanges="orientation|keyboardHidden"
            android:forceDarkAllowed="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            tools:targetApi="q" />

        <activity
            android:name=".activity.KITUITransparentActivity"
            android:launchMode="singleTask"
            android:theme="@style/Kit_UI_Theme.AppCompat.Translucent" />

    </application>

</manifest>