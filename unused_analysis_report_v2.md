# Android 未使用类和资源分析报告

## 📊 项目信息

**分析时间**: 2025-06-20 15:51:32
**Git分支**: feature_base_372_resource_scan
**最新提交**: f96c3835
**提交者**: <PERSON> Liang
**提交时间**: 2025-06-19 14:21:48 +0800

## 🎯 分析结果

**扫描模块**: app
**扫描类**: ✅
**扫描资源**: ✅

### 📈 统计概览

**发现的未使用类总数**: 25 个

#### 类分布

- **app模块**: 25 个类

**发现的未使用资源总数**: 218 个

#### 资源分布

- **app模块**: 218 个资源

## 📋 未使用类详细列表

### app 模块 - 类

| 序号 | 类名                             | 类型         | 文件路径                                                                                    | 提交哈希       | 提交者        | 最后提交时间     | 提交信息                                                 |
|----|--------------------------------|------------|-----------------------------------------------------------------------------------------|------------|------------|------------|------------------------------------------------------|
| 1  | `ErrorLogReportBean`           | class      | `app/src/main/java/cn/com/vau/common/greendao/dbUtils/ErrorLogReportBean.java`          | `8f27a463` | Haipeng    | 2019-12-11 | 错误日志上传工具类                                            |
| 2  | `FpsCounterUtil`               | class      | `app/src/main/java/cn/com/vau/util/opt/FpsCounterUtil.java`                             | `c6c5c7b7` | eyal       | 2024-11-25 | 注掉了生产消费Log日志                                         |
| 3  | `CalendarFiltersEvent`         | class      | `app/src/main/java/cn/com/vau/page/CalendarFiltersEvent.kt`                             | `6f66bea6` | DTDT       | 2024-08-12 | 349 string & param_bundle                            |
| 4  | `TmpRecordUtil`                | class      | `app/src/main/java/cn/com/vau/util/TmpRecordUtil.kt`                                    | `9c03f989` | eyal       | 2025-05-28 | 为了查找NaN问题，上报为不严重类型                                   |
| 5  | `StShareOrderData`             | class      | `app/src/main/java/cn/com/vau/data/init/StShareOrderData.kt`                            | `6c00388f` | DTDT       | 2024-09-26 | 351 合并适配器：多品牌挂单 - 跟单自主挂单                             |
| 6  | `CSConsultBean`                | class      | `app/src/main/java/cn/com/vau/data/msg/MsgDataFile.kt`                                  | `bdff28ab` | GGjin      | 2025-03-13 | [fix] 修改faq页面                                        |
| 7  | `ChannelKeyBean`               | class      | `app/src/main/java/cn/com/vau/data/msg/MsgDataFile.kt`                                  | `bdff28ab` | GGjin      | 2025-03-13 | [fix] 修改faq页面                                        |
| 8  | `EnumAdapterFollowStrategy`    | enum class | `app/src/main/java/cn/com/vau/data/enums/EnumAdapterFollowStrategy.kt`                  | `84c63286` | DTDT       | 2024-10-08 | 351 ui 改版 v5 [策略订单详情页]                               |
| 9  | `HytechClassicFooter`          | class      | `app/src/main/java/cn/com/vau/common/application/HytechClassicFooter.kt`                | `dc3cc3da` | Brin       | 2025-06-16 | [UI] 修改 lottie 动画文件                                  |
| 10 | `BaseBindingPerformance`       | class      | `app/src/main/java/cn/com/vau/common/performance/BaseBindingPerformance.kt`             | `30191e3a` | array.zhou | 2025-01-13 | build(performance):功能拆分                              |
| 11 | `ArrowBottomAdapter`           | class      | `app/src/main/java/cn/com/vau/common/view/popup/adapter/ArrowBottomAdapter.kt`          | `82151549` | GGjin      | 2024-04-02 | 创建策略添加弹窗和逻辑                                          |
| 12 | `AppEventsLoggerInitializer`   | class      | `app/src/main/java/cn/com/vau/common/utils/initializer/AppEventsLoggerInitializer.kt`   | `0a40b628` | Zheng      | 2021-12-09 | bug                                                  |
| 13 | `StSignalInvestedAdapter`      | class      | `app/src/main/java/cn/com/vau/signals/stSignal/adapter/StSignalInvestedAdapter.kt`      | `7377b037` | GGjin      | 2025-03-24 | [fix] 修改部分文件夹名，删除入金相关页面                              |
| 14 | `HistoryRequestBean`           | class      | `app/src/main/java/cn/com/vau/trade/bean/HistoryRequestBean.kt`                         | `7e86e413` | DTDT       | 2023-07-04 | 331                                                  |
| 15 | `OrderPendingDiffItemCallback` | class      | `app/src/main/java/cn/com/vau/trade/adapter/OrderPendingDiffItemCallback.kt`            | `593e5475` | DTDT       | 2024-07-24 | 349 init v4                                          |
| 16 | `MarketDiffItemCallback`       | class      | `app/src/main/java/cn/com/vau/trade/adapter/MarketDiffItemCallback.kt`                  | `ba5f27e3` | DTDT       | 2024-07-23 | 349 init v2                                          |
| 17 | `U`                            | class      | `app/src/main/java/cn/com/vau/trade/fragment/deal/U.kt`                                 | `c6c5c7b7` | eyal       | 2024-11-25 | 注掉了生产消费Log日志                                         |
| 18 | `ProductItemNewsFragment`      | class      | `app/src/main/java/cn/com/vau/trade/fragment/kchart/ProductItemNewsFragment.kt`         | `92e6e62f` | WangJian   | 2025-04-30 | 替换LinearLayoutManager为WrapContentLinearLayoutManager |
| 19 | `TestElapsedTimeUtil`          | class      | `app/src/main/java/cn/com/vau/util/opt/TestElapsedTimeUtil.kt`                          | `3d6e8eb0` | eyal       | 2024-09-21 | 提交工具类。目前master_st分支不应该使用。只是方便我拉分支做性能优化使用             |
| 20 | `LooperDetectorUtil`           | class      | `app/src/main/java/cn/com/vau/util/opt/LooperDetectorUtil.kt`                           | `3d6e8eb0` | eyal       | 2024-09-21 | 提交工具类。目前master_st分支不应该使用。只是方便我拉分支做性能优化使用             |
| 21 | `StrictModeUtil`               | class      | `app/src/main/java/cn/com/vau/util/opt/StrictModeUtil.kt`                               | `3d6e8eb0` | eyal       | 2024-09-21 | 提交工具类。目前master_st分支不应该使用。只是方便我拉分支做性能优化使用             |
| 22 | `NetworkUtils`                 | object     | `app/src/main/java/cn/com/vau/util/widget/webview/utils/NetworkUtils.kt`                | `3d3195f6` | Liam Liang | 2025-01-13 | [WebView]预加载、WebViewPool提供WebView加载速度                |
| 23 | `MineType`                     | object     | `app/src/main/java/cn/com/vau/util/widget/webview/offline/type/MineType.kt`             | `d0bcd898` | Liam Liang | 2025-02-26 | [WebView优化]URL预加载,js、css、字体等资源内置                     |
| 24 | `OpenAccountSecondContract`    | interface  | `app/src/main/java/cn/com/vau/page/user/openAccountSecond/OpenAccountSecondContract.kt` | `b95c2fb3` | DTDT       | 2024-08-24 | 349 data                                             |
| 25 | `OpenAcountForthModel`         | class      | `app/src/main/java/cn/com/vau/page/user/openAccountForth/OpenAcountForthModel.kt`       | `22b52159` | Zheng      | 2021-04-12 | bug                                                  |

## 📋 未使用资源详细列表

### app 模块 - 资源

| 序号  | 资源名称                                                                | 资源类型     | 文件路径                                                                                              | 提交哈希       | 提交者        | 最后提交时间     | 提交信息                                         |
|-----|---------------------------------------------------------------------|----------|---------------------------------------------------------------------------------------------------|------------|------------|------------|----------------------------------------------|
| 1   | `in`                                                                | anim     | `app/src/main/res/anim/in.xml`                                                                    | `aa925f47` | guangyang  | 2023-05-11 | 提交资源                                         |
| 2   | `out`                                                               | anim     | `app/src/main/res/anim/out.xml`                                                                   | `aa925f47` | guangyang  | 2023-05-11 | 提交资源                                         |
| 3   | `dialog_exit_anim`                                                  | anim     | `app/src/main/res/anim/dialog_exit_anim.xml`                                                      | `0e664247` | Zheng      | 2019-10-14 | test                                         |
| 4   | `dialog_enter_anim`                                                 | anim     | `app/src/main/res/anim/dialog_enter_anim.xml`                                                     | `0e664247` | Zheng      | 2019-10-14 | test                                         |
| 5   | `shape_stroke_ce35728_solid_c1fe35728_r16`                          | drawable | `app/src/main/res/drawable/shape_stroke_ce35728_solid_c1fe35728_r16.xml`                          | `97c92c06` | GGjin      | 2024-11-08 | [3.51.0] 删除无用颜色                              |
| 6   | `draw_shape_stroke_c1e1e1e_cdeffffff_r4`                            | drawable | `app/src/main/res/drawable/draw_shape_stroke_c1e1e1e_cdeffffff_r4.xml`                            | `bf702e12` | GGjin      | 2024-11-06 | [3.51.0] 修改bug，修改asic的图标                     |
| 7   | `draw_shape_stroke_c731e1e1e_c61ffffff_r4`                          | drawable | `app/src/main/res/drawable/draw_shape_stroke_c731e1e1e_c61ffffff_r4.xml`                          | `3d240592` | GGjin      | 2024-11-07 | [3.51.0] 修改3d3d3d的文件                         |
| 8   | `draw_shape_stroke_c731e1e1e_c61ffffff_r10`                         | drawable | `app/src/main/res/drawable/draw_shape_stroke_c731e1e1e_c61ffffff_r10.xml`                         | `3778b6b1` | GGjin      | 2024-11-06 | [3.51.0] 删除无用颜色                              |
| 9   | `shape_stroke_c00c79c_solid_c1f00c79c_r16`                          | drawable | `app/src/main/res/drawable/shape_stroke_c00c79c_solid_c1f00c79c_r16.xml`                          | `e8f03721` | WangJian   | 2024-11-08 | [3.51.0] UI视觉改版-清理颜色色值                       |
| 10  | `shape_stroke_c00c79c_solid_c1f00c79c_r2`                           | drawable | `app/src/main/res/drawable/shape_stroke_c00c79c_solid_c1f00c79c_r2.xml`                           | `e8f03721` | WangJian   | 2024-11-08 | [3.51.0] UI视觉改版-清理颜色色值                       |
| 11  | `draw_bitmap2_confirm10x10_c731e1e1e_c61ffffff`                     | drawable | `app/src/main/res/drawable/draw_bitmap2_confirm10x10_c731e1e1e_c61ffffff.xml`                     | `7df2704f` | DTDT       | 2024-11-05 | 351 icon2                                    |
| 12  | `draw_bitmap2_expand_down_c731e1e1e_c61ffffff`                      | drawable | `app/src/main/res/drawable/draw_bitmap2_expand_down_c731e1e1e_c61ffffff.xml`                      | `745b7980` | array.zhou | 2025-05-21 | feat(订单合并):产品对+资产                            |
| 13  | `draw_bitmap2_close12x12_c731e1e1e_c61ffffff`                       | drawable | `app/src/main/res/drawable/draw_bitmap2_close12x12_c731e1e1e_c61ffffff.xml`                       | `7df2704f` | DTDT       | 2024-11-05 | 351 icon2                                    |
| 14  | `shape_c99ffffff_r100`                                              | drawable | `app/src/main/res/drawable/shape_c99ffffff_r100.xml`                                              | `3c2a0525` | GGjin      | 2024-11-02 | [3.51.0] 修改分享弹窗样式以及弹窗底部导航条的颜色                |
| 15  | `shape_stroke_cebffffff_r8`                                         | drawable | `app/src/main/res/drawable/shape_stroke_cebffffff_r8.xml`                                         | `e17366d3` | lvyang     | 2024-10-30 | [3.51.0] UI改版                                |
| 16  | `draw_shape_cffffff_c1a1d20_r10`                                    | drawable | `app/src/main/res/drawable/draw_shape_cffffff_c1a1d20_r10.xml`                                    | `a80dc4b2` | GGjin      | 2024-11-07 | [3.51.0] 删除无用颜色                              |
| 17  | `draw_shape_stroke_c1e1e1e_cdeffffff_solid_c0a1e1e1e_c0affffff_r10` | drawable | `app/src/main/res/drawable/draw_shape_stroke_c1e1e1e_cdeffffff_solid_c0a1e1e1e_c0affffff_r10.xml` | `3d240592` | GGjin      | 2024-11-07 | [3.51.0] 修改3d3d3d的文件                         |
| 18  | `draw_bitmap_img_source_permission_bank_c1e1e1e_cffffff`            | drawable | `app/src/main/res/drawable/draw_bitmap_img_source_permission_bank_c1e1e1e_cffffff.xml`            | `a80dc4b2` | GGjin      | 2024-11-07 | [3.51.0] 删除无用颜色                              |
| 19  | `select_rename_passkey_et_bg`                                       | drawable | `app/src/main/res/drawable/select_rename_passkey_et_bg.xml`                                       | `45f0c8a9` | felix      | 2024-12-27 | [354] UI 修改                                  |
| 20  | `draw_bitmap2_triangle_down_tab_c731e1e1e_c61ffffff`                | drawable | `app/src/main/res/drawable/draw_bitmap2_triangle_down_tab_c731e1e1e_c61ffffff.xml`                | `face318b` | DTDT       | 2024-11-04 | 351 UI 改版 clear icon source                  |
| 21  | `draw_indicator_color_c731e1e1e_c61ffffff`                          | drawable | `app/src/main/res/drawable/draw_indicator_color_c731e1e1e_c61ffffff.xml`                          | `3c2a0525` | GGjin      | 2024-11-02 | [3.51.0] 修改分享弹窗样式以及弹窗底部导航条的颜色                |
| 22  | `draw_indicator_color_c1e1e1e_cebffffff`                            | drawable | `app/src/main/res/drawable/draw_indicator_color_c1e1e1e_cebffffff.xml`                            | `3c2a0525` | GGjin      | 2024-11-02 | [3.51.0] 修改分享弹窗样式以及弹窗底部导航条的颜色                |
| 23  | `draw_shape_line_c1f1e1e1e_c1fffffff`                               | drawable | `app/src/main/res/drawable/draw_shape_line_c1f1e1e1e_c1fffffff.xml`                               | `b2327dcd` | GGjin      | 2024-11-01 | [3.51.0] 修改设置页面以及语言、账户活动页面                   |
| 24  | `shape_stroke_c1e1e1e_r8`                                           | drawable | `app/src/main/res/drawable/shape_stroke_c1e1e1e_r8.xml`                                           | `e17366d3` | lvyang     | 2024-10-30 | [3.51.0] UI改版                                |
| 25  | `draw_shape_c1e1e1e_cebffffff_r10`                                  | drawable | `app/src/main/res/drawable/draw_shape_c1e1e1e_cebffffff_r10.xml`                                  | `58f3d491` | DTDT       | 2024-11-07 | 351 UI 改版 透明度:92%  ->  DB 错误修改               |
| 26  | `draw_shape_stroke_c731e1e1e_c61ffffff_r100`                        | drawable | `app/src/main/res/drawable/draw_shape_stroke_c731e1e1e_c61ffffff_r100.xml`                        | `1e8f068d` | DTDT       | 2024-11-04 | 351 UI改版 icon2                               |
| 27  | `draw_bitmap2_triangle_down_tab_cffffff_c1a1d20`                    | drawable | `app/src/main/res/drawable/draw_bitmap2_triangle_down_tab_cffffff_c1a1d20.xml`                    | `a80dc4b2` | GGjin      | 2024-11-07 | [3.51.0] 删除无用颜色                              |
| 28  | `shape_stroke_ce35728_solid_c1fe35728_r2`                           | drawable | `app/src/main/res/drawable/shape_stroke_ce35728_solid_c1fe35728_r2.xml`                           | `97c92c06` | GGjin      | 2024-11-08 | [3.51.0] 删除无用颜色                              |
| 29  | `draw_bitmap2_expand_up_c731e1e1e_c61ffffff`                        | drawable | `app/src/main/res/drawable/draw_bitmap2_expand_up_c731e1e1e_c61ffffff.xml`                        | `745b7980` | array.zhou | 2025-05-21 | feat(订单合并):产品对+资产                            |
| 30  | `draw_bitmap_img_source_permission_bink_c731e1e1e_c61ffffff`        | drawable | `app/src/main/res/drawable/draw_bitmap_img_source_permission_bink_c731e1e1e_c61ffffff.xml`        | `60b065de` | GGjin      | 2024-11-02 | [3.51.0] 修改认证中心以及相关图标                        |
| 31  | `select_price_alter_et_bg`                                          | drawable | `app/src/main/res/drawable/select_price_alter_et_bg.xml`                                          | `45f0c8a9` | felix      | 2024-12-27 | [354] UI 修改                                  |
| 32  | `include_order_volume`                                              | layout   | `app/src/main/res/layout/include_order_volume.xml`                                                | `3f1423df` | GGjin      | 2024-11-15 | [3.51.0] 修改bug                               |
| 33  | `layout_history_empty`                                              | layout   | `app/src/main/res/layout/layout_history_empty.xml`                                                | `ebe3dbec` | brin       | 2025-02-05 | [UI] 添加历史记录空视图                               |
| 34  | `item_price_alert`                                                  | layout   | `app/src/main/res/layout/item_price_alert.xml`                                                    | `d7981e24` | GGjin      | 2024-11-08 | [3.51.0] 修改分享的颜色                             |
| 35  | `dialog_auto_reject`                                                | layout   | `app/src/main/res/layout/dialog_auto_reject.xml`                                                  | `ae2f933d` | GGjin      | 2025-01-08 | [354] 修改部分使用weight的地方                        |
| 36  | `item_notices`                                                      | layout   | `app/src/main/res/layout/item_notices.xml`                                                        | `86e08607` | GGjin      | 2024-11-05 | [3.51.0] 修改消息页面                              |
| 37  | `footer_add_credit`                                                 | layout   | `app/src/main/res/layout/footer_add_credit.xml`                                                   | `2dcb5207` | GGjin      | 2024-11-05 | [3.51.0] 修改选择信用卡相关页面，删除图标                    |
| 38  | `item_system_message`                                               | layout   | `app/src/main/res/layout/item_system_message.xml`                                                 | `86e08607` | GGjin      | 2024-11-05 | [3.51.0] 修改消息页面                              |
| 39  | `activity_select_coupon`                                            | layout   | `app/src/main/res/layout/activity_select_coupon.xml`                                              | `59b0c322` | GGjin      | 2025-04-14 | [coupon] 优惠券详情页面重构，删除无用优惠券页面                 |
| 40  | `item_other_msg`                                                    | layout   | `app/src/main/res/layout/item_other_msg.xml`                                                      | `9dbab594` | GGjin      | 2024-11-07 | [3.51.0] 删除无用颜色                              |
| 41  | `srl_classics_footer_hytech`                                        | layout   | `app/src/main/res/layout/srl_classics_footer_hytech.xml`                                          | `9923ede9` | Brin       | 2025-06-12 | [UI] 下拉刷新，上拉加载                               |
| 42  | `dialog_sumsub_agree`                                               | layout   | `app/src/main/res/layout/dialog_sumsub_agree.xml`                                                 | `28bab439` | GGjin      | 2025-05-21 | 【kyc】ui走查修改                                  |
| 43  | `header_recycler_order_history`                                     | layout   | `app/src/main/res/layout/header_recycler_order_history.xml`                                       | `e4b0c41c` | GGjin      | 2024-11-19 | [3.51.0] 适配阿拉伯语样式                            |
| 44  | `layout_bottom_tip_copy_trading_glossary`                           | layout   | `app/src/main/res/layout/layout_bottom_tip_copy_trading_glossary.xml`                             | `151ab3eb` | array.zhou | 2025-03-04 | feat(kit):merge master_st                    |
| 45  | `dialog_common_list_menu`                                           | layout   | `app/src/main/res/layout/dialog_common_list_menu.xml`                                             | `22b52159` | Zheng      | 2021-04-12 | bug                                          |
| 46  | `item_popup_bottom_list_string`                                     | layout   | `app/src/main/res/layout/item_popup_bottom_list_string.xml`                                       | `61b1bafc` | DTDT       | 2024-10-23 | 351 UI全局改版 v1                                |
| 47  | `item_coupon_manager`                                               | layout   | `app/src/main/res/layout/item_coupon_manager.xml`                                                 | `bd1eed1a` | GGjin      | 2024-11-18 | [3.51.0] 修改开户相关页面的显示                         |
| 48  | `dialog_bottom_list`                                                | layout   | `app/src/main/res/layout/dialog_bottom_list.xml`                                                  | `23c9a377` | GGjin      | 2024-11-11 | [3.51.0] 修改bug                               |
| 49  | `dialog_close`                                                      | layout   | `app/src/main/res/layout/dialog_close.xml`                                                        | `8c0b3ce8` | GGjin      | 2024-11-04 | [3.51.0] 修改导航弹窗关闭图标                          |
| 50  | `include_order_trade_type`                                          | layout   | `app/src/main/res/layout/include_order_trade_type.xml`                                            | `cd3e45cc` | DTDT       | 2024-11-22 | 352 NewOrder 顶部 sell/buy 背景图修改               |
| 51  | `fragment_tfa_unbind_prompt`                                        | layout   | `app/src/main/res/layout/fragment_tfa_unbind_prompt.xml`                                          | `c69f9bf7` | lvyang     | 2025-03-14 | [bugfix] 去掉2fa相关页面顶部提示文案背景圆角                 |
| 52  | `dialog_credit_transfer`                                            | layout   | `app/src/main/res/layout/dialog_credit_transfer.xml`                                              | `ae2f933d` | GGjin      | 2025-01-08 | [354] 修改部分使用weight的地方                        |
| 53  | `foot_land_kline_tab_interval`                                      | layout   | `app/src/main/res/layout/foot_land_kline_tab_interval.xml`                                        | `98af6407` | tianyuhe   | 2025-04-01 | 【kline】切换时间栏逻辑处理                             |
| 54  | `fragment_refresh_slide`                                            | layout   | `app/src/main/res/layout/fragment_refresh_slide.xml`                                              | `c31e9b6b` | GGjin      | 2025-04-28 | [discover] 优化发现页面逻辑，修改LinearLayoutManager的使用 |
| 55  | `item_recycler_trading_view_parameters_count`                       | layout   | `app/src/main/res/layout/item_recycler_trading_view_parameters_count.xml`                         | `ef39e369` | GGjin      | 2025-04-03 | [kline] 初步提交 弹窗修改                            |
| 56  | `item_credit`                                                       | layout   | `app/src/main/res/layout/item_credit.xml`                                                         | `454a15f7` | GGjin      | 2024-11-27 | [3.52.0] 去除ImageFilterView的使用                |
| 57  | `layout_coupon_rule`                                                | layout   | `app/src/main/res/layout/layout_coupon_rule.xml`                                                  | `bdc49f02` | DTDT       | 2024-11-09 | 351 UI 走查 bug                                |
| 58  | `facebook_app_id`                                                   | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 59  | `facebook_client_token`                                             | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 60  | `fb_login_protocol_scheme`                                          | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 61  | `majuscule_buy`                                                     | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 62  | `majuscule_sell`                                                    | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 63  | `transfer_funds`                                                    | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 64  | `unfortunately_we_postal_gift_virtual_card`                         | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 65  | `from`                                                              | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 66  | `to`                                                                | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 67  | `select_symbol`                                                     | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 68  | `stop_loss_take_profit_setting`                                     | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 69  | `please_select_account`                                             | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 70  | `your_transfer_request_shortly`                                     | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 71  | `choose_from_album`                                                 | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 72  | `please_select_the_account`                                         | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 73  | `please_enter_the_correct_transfer_amount`                          | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 74  | `please_enter_amount`                                               | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 75  | `please_enter_your_first_name`                                      | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 76  | `please_enter_your_last_name`                                       | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 77  | `The_transfer_amount_than`                                          | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 78  | `customize`                                                         | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 79  | `order_placed`                                                      | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 80  | `order`                                                             | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 81  | `change`                                                            | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 82  | `open_trades`                                                       | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 83  | `symbol_information`                                                | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 84  | `reminder`                                                          | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 85  | `album`                                                             | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 86  | `photo`                                                             | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 87  | `required_margin`                                                   | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 88  | `sl`                                                                | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 89  | `tp`                                                                | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 90  | `your_funds_will_hours`                                             | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 91  | `please_enter_the_correctly`                                        | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 92  | `please_upload_a_card`                                              | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 93  | `unlink_facebook_account`                                           | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 94  | `existing_clients_please_here`                                      | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 95  | `estimation`                                                        | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 96  | `promotional_bonuses_are_accounts`                                  | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 97  | `please_carefully_read_promotion`                                   | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 98  | `click_to_view_cs`                                                  | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 99  | `are_you_a_purposes`                                                | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 100 | `type_of_identification`                                            | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 101 | `identification_number`                                             | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 102 | `scan_failed_number_manually`                                       | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 103 | `closed_profit_and_loss`                                            | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 104 | `total_profit_loss`                                                 | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 105 | `employment`                                                        | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 106 | `vantage_fx_strictly_trading_account`                               | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 107 | `clients_acknowledge_that_that_delay`                               | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 108 | `client_understands_that_in_place`                                  | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 109 | `vantage_fx_will_ments_systems`                                     | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 110 | `vantage_fx_will_sending_payments`                                  | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 111 | `the_amount_of_less_than_x`                                         | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 112 | `the_amount_of_higher_than_x`                                       | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 113 | `e_g`                                                               | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 114 | `please_note_that_please_vantage_fx_com`                            | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 115 | `please_note_if_your_trading_account`                               | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 116 | `quick_close`                                                       | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 117 | `add_card`                                                          | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 118 | `position_your_debit_of_it`                                         | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 119 | `card_details`                                                      | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 120 | `the_theme_can_profile_page`                                        | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 121 | `performance_of_x_chart_simulation`                                 | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 122 | `leverage_explain_item_1`                                           | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 123 | `open__position`                                                    | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 124 | `available_after_verification`                                      | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 125 | `you_need_completed_bank_channel_authentication`                    | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 126 | `error_at_price_range`                                              | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 127 | `_return`                                                           | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 128 | `unrealised_return`                                                 | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 129 | `updated_on_x`                                                      | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 130 | `unrealised_profit_loss_x`                                          | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 131 | `share_account_no`                                                  | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 132 | `account_no_copied`                                                 | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 133 | `profit_and_loss`                                                   | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 134 | `submitted_successfully`                                            | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 135 | `payment_failed`                                                    | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 136 | `disable_authenticator`                                             | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 137 | `two_factor_authentication_disabled_successfully`                   | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 138 | `_2fa_disabled`                                                     | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 139 | `security_level_low_security`                                       | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 140 | `security_level_fair_security`                                      | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 141 | `more_options`                                                      | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 142 | `processing_time`                                                   | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 143 | `fee`                                                               | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 144 | `min_deposit_amount`                                                | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 145 | `max_deposit_amount`                                                | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 146 | `last_updated_at_x`                                                 | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 147 | `x_coupons_available`                                               | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 148 | `investment_and_return`                                             | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 149 | `x_strategies_in_favourite`                                         | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 150 | `recommended_strategies`                                            | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 151 | `transferable_balance`                                              | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 152 | `no_available_accounts_your_please_account_management`              | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 153 | `last_1_week`                                                       | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 154 | `last_1_month`                                                      | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 155 | `closed_by_trader`                                                  | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 156 | `stop_loss_take_profit_slash`                                       | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 157 | `create_time`                                                       | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 158 | `show_account_number_on_image`                                      | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 159 | `repeat_order`                                                      | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 160 | `step_1_select_an_account`                                          | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 161 | `step_2_share`                                                      | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 162 | `promotions`                                                        | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 163 | `save_x_min_x`                                                      | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 164 | `enjoy_x_min_x`                                                     | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 165 | `cashback`                                                          | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 166 | `rebate`                                                            | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 167 | `apply_coupon`                                                      | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 168 | `top_up_to_get_x_cashback_x_view_all_coupons_here`                  | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 169 | `top_up_to_get_x_rebate_x_view_all_coupons_here`                    | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 170 | `_1_coupon_is_visit_deposit_order`                                  | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 171 | `x_coupon_are_visit_deposit_order`                                  | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 172 | `youve_selected_a_upgrade_view_all_coupons_here`                    | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 173 | `some_coupons_may_check_before_applying`                            | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 174 | `no_coupons_available_for_this_payment_method`                      | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 175 | `_1_coupon_available`                                               | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 176 | `copying`                                                           | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 177 | `upgrade`                                                           | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 178 | `unable_to_verify_please_more_information`                          | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 179 | `this_deposit_method_tap_to_proceed`                                | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 180 | `verification_equireds`                                             | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 181 | `edit_position`                                                     | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 182 | `close_and_reverse`                                                 | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 183 | `glossary_reverse_1`                                                | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 184 | `tips_reverse_1`                                                    | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 185 | `price_reaches`                                                     | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 186 | `triggered_when_the_set_price`                                      | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 187 | `modified_closed`                                                   | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 188 | `_2fa_unbind_error_msg`                                             | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 189 | `unable_to_add_passkey`                                             | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 190 | `cannot_add_the_to_proceed`                                         | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 191 | `go_to_settings`                                                    | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 192 | `close_all_x`                                                       | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 193 | `no_message`                                                        | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 194 | `coupon_redemption_successful`                                      | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 195 | `the_coupon_has_please_use_it`                                      | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 196 | `send_otp_via_zalo`                                                 | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 197 | `send_otp_via_voice_call`                                           | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 198 | `voice_call`                                                        | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 199 | `the_verification_code_x_to`                                        | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 200 | `financial_and_trading_details`                                     | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 201 | `we_could_not_please_customer_support`                              | string   | `app/src/main/res/values/strings.xml`                                                             | `1cae10e6` | lvyang     | 2025-06-18 | [strings] 多语言国际化                             |
| 202 | `img_splash_mclaren`                                                | drawable | `app/src/main/res/drawable/img_splash_mclaren.webp`                                               | `f412eb2e` | felix      | 2024-11-05 | [351] UI改版，图片资源清理                            |
| 203 | `icon_source2_info_2_14x14`                                         | drawable | `app/src/main/res/drawable-xxhdpi/icon_source2_info_2_14x14.webp`                                 | `151ab3eb` | array.zhou | 2025-03-04 | feat(kit):merge master_st                    |
| 204 | `img_welcome_03`                                                    | drawable | `app/src/main/res/drawable-xxhdpi/img_welcome_03.webp`                                            | `f584874b` | GGjin      | 2024-11-19 | [3.51.0] 添加埋点，适配阿拉伯语，欢迎页面替换为新的三张图片           |
| 205 | `img_share_play`                                                    | drawable | `app/src/main/res/drawable-xxhdpi/img_share_play.webp`                                            | `37d8498e` | GGjin      | 2024-11-08 | [3.51.0] 删除分享使用的特殊颜色，修改图片格式                  |
| 206 | `img_welcome_02`                                                    | drawable | `app/src/main/res/drawable-xxhdpi/img_welcome_02.webp`                                            | `f584874b` | GGjin      | 2024-11-19 | [3.51.0] 添加埋点，适配阿拉伯语，欢迎页面替换为新的三张图片           |
| 207 | `img_profile_wallet_new`                                            | drawable | `app/src/main/res/drawable-xxhdpi/img_profile_wallet_new.webp`                                    | `67caf8f3` | GGjin      | 2025-01-15 | [355] 修改钱包icon的new图标                         |
| 208 | `img_st_follow_empty`                                               | drawable | `app/src/main/res/drawable-xxhdpi/img_st_follow_empty.webp`                                       | `37d8498e` | GGjin      | 2024-11-08 | [3.51.0] 删除分享使用的特殊颜色，修改图片格式                  |
| 209 | `icon2_cb_x_circle_cf44040`                                         | drawable | `app/src/main/res/drawable-xxhdpi/icon2_cb_x_circle_cf44040.webp`                                 | `37d8498e` | GGjin      | 2024-11-08 | [3.51.0] 删除分享使用的特殊颜色，修改图片格式                  |
| 210 | `img_refresh`                                                       | drawable | `app/src/main/res/drawable-xxhdpi/img_refresh.webp`                                               | `37d8498e` | GGjin      | 2024-11-08 | [3.51.0] 删除分享使用的特殊颜色，修改图片格式                  |
| 211 | `img_source_coupons`                                                | drawable | `app/src/main/res/drawable-xxhdpi/img_source_coupons.webp`                                        | `37d8498e` | GGjin      | 2024-11-08 | [3.51.0] 删除分享使用的特殊颜色，修改图片格式                  |
| 212 | `icon2_profile_transfer_d`                                          | drawable | `app/src/main/res/drawable-xxhdpi/icon2_profile_transfer_d.webp`                                  | `37d8498e` | GGjin      | 2024-11-08 | [3.51.0] 删除分享使用的特殊颜色，修改图片格式                  |
| 213 | `img_share_background`                                              | drawable | `app/src/main/res/drawable-xxhdpi/img_share_background.webp`                                      | `37d8498e` | GGjin      | 2024-11-08 | [3.51.0] 删除分享使用的特殊颜色，修改图片格式                  |
| 214 | `icon_history`                                                      | drawable | `app/src/main/res/drawable-xxhdpi/icon_history.webp`                                              | `9b1f0098` | brin       | 2025-02-26 | [feature] 历史订单详情联调                           |
| 215 | `img_source_right_8x6`                                              | drawable | `app/src/main/res/drawable-xxhdpi/img_source_right_8x6.webp`                                      | `37d8498e` | GGjin      | 2024-11-08 | [3.51.0] 删除分享使用的特殊颜色，修改图片格式                  |
| 216 | `img_refresh_d`                                                     | drawable | `app/src/main/res/drawable-xxhdpi/img_refresh_d.webp`                                             | `37d8498e` | GGjin      | 2024-11-08 | [3.51.0] 删除分享使用的特殊颜色，修改图片格式                  |
| 217 | `img_welcome_01`                                                    | drawable | `app/src/main/res/drawable-xxhdpi/img_welcome_01.webp`                                            | `f584874b` | GGjin      | 2024-11-19 | [3.51.0] 添加埋点，适配阿拉伯语，欢迎页面替换为新的三张图片           |
| 218 | `ic_launcher1`                                                      | mipmap   | `app/src/main/res/mipmap-mdpi/ic_launcher1.webp`                                                  | `739e579d` | GGjin      | 2024-09-13 | [3.50.0] 添加15周年图标，部分弹窗优化底部navigation bar显示   |


## 🔍 分析说明

### 检测范围
- ✅ **Java类**: 扫描所有.java文件中的类定义
- ✅ **Kotlin类**: 扫描所有.kt文件中的类定义
- ✅ **多类文件**: 支持单文件多类的情况
- ✅ **AndroidManifest**: 检查Manifest中声明的组件
- ✅ **资源文件**: 扫描layout、drawable、values等资源

### 使用情况分析 (v2.0 增强)
1. **Import语句**: 检查类是否被导入
2. **实例化**: 检查new关键字和构造函数调用
3. **静态调用**: 检查类的静态方法和字段访问 (增强)
4. **单例模式**: 检查getInstance()等单例调用 (新增)
5. **继承关系**: 检查类的继承和接口实现
6. **泛型使用**: 检查泛型参数中的类引用
7. **Kotlin特殊语法**: object、::class等 (新增)
8. **注解使用**: @AnnotationName模式 (新增)
9. **Manifest声明**: 检查AndroidManifest.xml中的组件声明
10. **资源引用**: R.type.name和@type/name模式

### 过滤规则
- 排除自动生成的类（ViewBinding、DataBinding等）
- 排除测试类
- 排除BuildConfig和R类
- 保留Android组件类（Activity、Fragment、Service等）
- 保留应用图标等特殊资源

## ⚠️ 清理注意事项

### 1. 删除前的验证步骤
1. **备份代码**: 创建新分支进行清理
2. **运行测试**: 确保所有单元测试通过
3. **构建验证**: 执行完整的构建流程
4. **功能测试**: 手动测试相关功能

### 2. 特别注意
- **反射调用**: 某些类可能通过反射使用，工具无法检测
- **注解处理**: 某些类可能被注解处理器使用
- **配置文件**: 某些类可能在XML或其他配置文件中引用
- **动态加载**: 某些类可能被动态加载
- **资源引用**: 某些资源可能在代码生成或动态加载中使用

### 3. 建议的清理顺序
1. **第一批**: 明显的工具类和帮助类
2. **第二批**: 旧的Activity和Fragment
3. **第三批**: 数据模型类和未使用资源
4. **第四批**: 其他类型

### 4. 版本更新日志
- **v2.0**: 修复静态方法调用检测、增强单例模式检测、改进Kotlin支持、合并类和资源报告

---
*此报告由未使用类和资源分析器 v2.0 生成。建议在删除前进行充分验证，避免误删重要文件。*
