# Android 未使用类和资源扫描器 - 更新日志

## v2.8 (2025-06-20)

### 🎯 核心优化
- **增强Android组件检测**: 对Activity、Fragment、Service等组件采用更严格的使用检查
- **修复"僵尸Activity"问题**: 只在AndroidManifest.xml中声明但无实际跳转使用的Activity将被标记为未使用
- **双重验证机制**: Android组件必须既在Manifest中声明，又有实际代码使用才算已使用
- **支持组件使用模式检测**: Activity跳转、Fragment替换、Service启动等使用模式
- **保护主要组件**: MainActivity和Application类即使没有代码跳转也会被保留

### 🔧 技术改进
- **Android组件识别**: 通过继承关系和类名后缀识别Android组件
  ```regex
  r'class\s+' + class_name + r'\s*:\s*\w*Activity'  # Kotlin Activity
  r'class\s+' + class_name + r'\s+extends\s+\w*Activity'  # Java Activity
  ```
- **Activity跳转检测**: 识别各种Activity启动模式
  ```regex
  r'Intent\s*\(\s*\w+\s*,\s*(\w+)::class\.java\s*\)'  # Intent(context, Activity::class.java)
  r'Intent\s*\(\s*\w+\s*,\s*(\w+)\.class\s*\)'       # Intent(context, Activity.class)
  r'(\w+Activity)\.open\s*\('                        # ActivityName.open(
  ```
- **Fragment使用检测**: 识别Fragment的各种使用方式
  ```regex
  r'(\w+Fragment)\s*\('                              # FragmentName()
  r'supportFragmentManager\.replace\s*\([^,]+,\s*(\w+Fragment)'  # replace(id, Fragment)
  r'(\w+Fragment)\.newInstance\s*\('                 # Fragment.newInstance()
  ```
- **Service使用检测**: 识别Service的启动和绑定
  ```regex
  r'startService\s*\([^)]*(\w+Service)'               # startService(ServiceIntent)
  r'bindService\s*\([^)]*(\w+Service)'                # bindService(ServiceIntent)
  ```

### 📊 扫描结果改进
- **更精准的组件检测**: 能够识别真正未使用的Android组件
- **减少过度保护**: 不再仅因为在Manifest中声明就保护所有组件
- **更实用的清理建议**: 帮助识别可以安全删除的"僵尸Activity"

### 🛡️ 安全性提升
- **保护主要组件**: MainActivity、Application等关键组件会被特殊保护
- **双重验证**: 确保组件既有声明又有使用才被保留
- **更智能的判断**: 区分Android组件和普通类的不同处理策略

---

## v2.7 (2025-06-20)

### 🎯 核心优化
- **增强ViewBinding/DataBinding检测**: 正确识别layout资源通过ViewBinding方式的使用
- **修复layout资源误报问题**: 解决了 `include_fragment_trades_login_live_account.xml`、`activity_open_acount_fifth_white.xml`、`activity_bind_email.xml`、`fragment_kline_info.xml` 等被误报的问题
- **支持Binding类名转换**: 自动将Binding类名转换为对应的layout文件名
- **重点关注layout类型**: 专门针对layout资源的ViewBinding/DataBinding使用情况进行检测

### 🔧 技术改进
- **ViewBinding使用模式检测**: 新增多种ViewBinding使用模式的检测
  ```regex
  r'(\w+)Binding\.inflate'  # XxxBinding.inflate
  r'(\w+)Binding\('         # XxxBinding(
  r':\s*(\w+)Binding'       # : XxxBinding
  r'(\w+)Binding\?'         # XxxBinding?
  r'(\w+)Binding\s*='       # XxxBinding =
  ```
- **Binding类名转换算法**: 将驼峰命名的Binding类转换为下划线命名的layout文件
  ```python
  # ActivityMainBinding -> activity_main
  # IncludeFragmentTradesLoginLiveAccount -> include_fragment_trades_login_live_account
  layout_name = re.sub(r'(?<!^)(?=[A-Z])', '_', binding_class_prefix).lower()
  ```
- **资源使用分析增强**: 在资源扫描中集成ViewBinding检测逻辑

### 📊 扫描结果改进
- **减少layout资源误报**: 正确识别通过ViewBinding使用的layout文件
- **更准确的资源使用检测**: 支持现代Android开发中常用的ViewBinding模式
- **更安全的清理建议**: 避免删除通过ViewBinding使用的layout资源

### 🛡️ 安全性提升
- **保护ViewBinding资源**: 通过ViewBinding使用的layout文件不会被误删
- **保护DataBinding资源**: 通过DataBinding使用的layout文件不会被误删
- **更全面的使用检测**: 覆盖传统R.layout.xxx和现代ViewBinding两种使用方式

---

## v2.6 (2025-06-20)

### 🎯 重大准确性修复
- **修复12个重要类的误报问题**: 经过用户反馈验证，修复了 `GlobelData`、`OrderReplyData`、`WebTVData`、`CurrencyTransformData`、`FixedFlingAndDragAppBarLayout`、`WebDownLoadListener`、`SecurityStatusDialog`、`PreloadWebChromeClient`、`PreloadWebViewClient`、`SquareFrameLayout`、`LoadingPopup`、`StStrategyCopyRepository`、`DragLayout`、`KLineDrawingToolbar` 等类的误报
- **增强API返回类型检测**: 正确识别 `ApiResponse<WebTVData?>`、`ApiResponse<CurrencyTransformData.Data>` 等API返回类型
- **增强构造函数调用检测**: 正确识别 `XPopup.asCustom(LoadingPopup())`、`setDownloadListener(WebDownLoadListener())` 等构造函数调用
- **增强XML完整包名检测**: 支持 `com.google.android.material.appbar.FixedFlingAndDragAppBarLayout` 等完整包名的自定义View

### 🔧 技术改进
- **API返回类型模式**: 新增多种API返回类型的检测模式
  ```regex
  r':\s*ApiResponse<(\w+)'       # : ApiResponse<ClassName
  r'ApiResponse<(\w+)\?>'        # ApiResponse<ClassName?>
  r'ApiResponse<(\w+)\.(\w+)>'   # ApiResponse<ClassName.InnerClass>
  ```
- **构造函数调用模式**: 新增多种构造函数调用的检测模式
  ```regex
  r'asCustom\s*\(\s*(\w+)\s*\('  # asCustom(ClassName(
  r'setDownloadListener\s*\(\s*(\w+)\s*\('  # setDownloadListener(ClassName(
  r'setWebChromeClient\s*\(\s*(\w+)\s*\('   # setWebChromeClient(ClassName(
  ```
- **XML完整包名检测**: 支持更多完整包名的自定义View检测
  ```regex
  r'<(cn\.com\.vau\.\w+(?:\.\w+)*)'  # <cn.com.vau.package.ClassName
  r'<(com\.google\.android\.material\.\w+(?:\.\w+)*)'  # <com.google.android.material.package.ClassName
  ```

### 📊 扫描结果改进
- **大幅减少误报**: 修复了用户反馈的所有12个误报类
- **更准确的使用检测**: 正确识别各种复杂的使用模式
- **更安全的清理建议**: 进一步降低误删重要文件的风险

### 🛡️ 安全性提升
- **保护API相关类**: 作为API返回类型的类不会被误删
- **保护构造函数参数**: 作为构造函数参数的类不会被误删
- **保护XML中的完整包名类**: 在XML中使用完整包名的自定义View不会被误删

---

## v2.5 (2025-06-20)

### 🎯 核心优化
- **增强XML中类使用检测**: 正确识别XML布局中的自定义View，如 `<PickerViewNew>`、`<HeartLayout>` 等
- **增强同文件内类使用检测**: 正确识别同一Kotlin文件中的类引用，如 `LineHeightSpanDefault` 等
- **增强复杂泛型检测**: 支持可空泛型、内部类泛型等复杂模式
- **修复多个误报问题**: 解决了 `OnCaptureCallback`、`GlobelData`、`HistoryUtil`、`OrderReplyData` 等被误报的问题

### 🔧 技术改进
- **XML类使用检测**: 新增对XML文件中自定义View的检测
  ```regex
  r'<([A-Z]\w*(?:\.[A-Z]\w*)*)'     # <ClassName 或 <package.ClassName
  r'class="([^"]+)"'                # class="ClassName"
  r'android:name="([^"]+)"'         # android:name="ClassName"
  ```
- **复杂泛型模式**: 支持更多泛型使用场景
  ```regex
  r'(\w+)<(\w+)\?>'                 # 可空泛型: ApiResponse<WebTVData?>
  r'(\w+)<(\w+)\.(\w+)>'           # 内部类泛型: ApiResponse<CurrencyTransformData.Data>
  r'List<(\w+)\?>'                 # 可空列表: List<GuidanceLevel?>
  r'(\w+)\?\s*=\s*null'           # 可空声明: GuidanceLevel? = null
  ```
- **同文件内类检测**: 检测同一文件中多个类之间的引用关系

### 📊 扫描结果改进
- **减少XML相关误报**: 正确识别在XML中使用的自定义View
- **减少泛型相关误报**: 支持更复杂的泛型使用模式
- **减少同文件误报**: 正确识别文件内部的类引用关系
- **更精准的清理建议**: 进一步提高扫描准确性

### 🛡️ 安全性提升
- **保护XML中使用的类**: 在布局文件中使用的自定义View不会被误删
- **保护复杂泛型类**: 作为泛型参数使用的类不会被误删
- **保护文件内引用**: 同文件内被引用的类不会被误删

---

## v2.4 (2025-06-20)

### 🎯 核心优化
- **移除内部类扫描**: 只扫描外部类，避免内部类误报问题（如 `HasPermission`、`NotLogin` 等object类）
- **增强默认参数检测**: 正确识别默认参数中的类引用，如 `tree: Timber.Tree = CatchTree()`
- **修复工具类误报**: 解决了 `CatchTree`、`StorageConstants` 等被误报的问题

### 🔧 技术改进
- **改进内部类检测逻辑**: 通过缩进和上下文分析准确识别内部类
- **新增默认参数模式**: 支持 `= ClassName()` 和 `= ClassName(` 等默认参数模式
- **优化类定义提取**: 只提取外部类，忽略所有内部类、嵌套类和object

### 📊 扫描结果改进
- **减少内部类误报**: 不再将内部类标记为未使用
- **更准确的外部类检测**: 专注于真正可以删除的外部类文件
- **更安全的清理建议**: 避免删除可能被外部类使用的内部类

### 🛡️ 安全性提升
- **保护内部类**: 内部类通常被外部类使用，不应被删除
- **保护默认参数**: 作为默认参数的类不会被误删
- **更保守的策略**: 只标记真正独立且未使用的外部类

---

## v2.3 (2025-06-20)

### 🎯 核心优化
- **增强泛型使用检测**: 正确识别泛型参数中的类引用，如 `BaseActivity<Presenter, Model>` 模式
- **优化资源扫描范围**: 移除 `attr`、`dimen`、`color`、`style` 类型的资源扫描，这些样式资源通常不应删除
- **修复泛型类误报问题**: 解决了 `AddOrForgotSecurityPWDPresenter` 等通过泛型使用的类被误报的问题

### 🔧 技术改进
- **扩展泛型检测模式**: 新增多种泛型使用模式的正则表达式
  ```regex
  r'class\s+\w+\s*:\s*\w+<(\w+)'     # Kotlin: class Activity : BaseActivity<Presenter>
  r'class\s+\w+\s+extends\s+\w+<(\w+)' # Java: class Activity extends BaseActivity<Presenter>
  r'(\w+)<\w+,\s*\w+>'               # 泛型实例化: BaseActivity<Presenter, Model>
  ```
- **精简资源扫描**: 只扫描 `string` 和 `array` 类型的资源，避免误删样式相关资源

### 📊 扫描结果改进
- **减少类的误报**: 正确识别泛型参数中使用的类
- **减少资源误报**: 不再扫描样式相关的资源类型
- **更精准的清理建议**: 提供更安全的删除建议

### 🛡️ 安全性提升
- **保护泛型使用的类**: 即使类没有直接调用，只要作为泛型参数使用就保留
- **保护样式资源**: 不扫描可能影响UI的样式相关资源
- **更保守的策略**: 进一步降低误删重要文件的风险

---

## v2.2 (2025-06-20)

### 🎯 用户体验优化
- **优化表格标题**: 将"提交时间"改为"最后提交时间"，更加明确表示这是文件的最后一次修改时间
- **提高可读性**: 让用户更清楚地理解Git信息的含义

### 📊 报告格式改进
- **类列表表格**: 标题从"提交时间"改为"最后提交时间"
- **资源列表表格**: 标题从"提交时间"改为"最后提交时间"
- **保持功能不变**: Git信息获取逻辑保持不变，仍然是获取文件的最后提交时间

---

## v2.1 (2025-06-20)

### 🎯 核心优化
- **采用更宽松但更安全的检测策略**: 只要在其他类中有任何形式的引用，就不归类为未使用的类
- **增强类型引用检测**: 新增对字段类型、方法参数类型、返回值类型等的检测
- **修复接口类误报问题**: 解决了 `ScrollHandle`、`OnLinkClickListener`、`OnGetLineCountListener` 等接口被误报的问题

### 🔧 技术改进
- **扩展类型声明模式**: 支持 `private Type fieldName`、`(Type paramName)` 等模式
- **改进Java和Kotlin类型引用识别**: 更全面地识别各种类型引用场景
- **优化正则表达式**: 新增多种类型引用的匹配模式

### 📊 扫描结果改进
- **进一步减少误报**: 未使用类从284个减少到256个（约10%的进一步提升）
- **更安全的清理建议**: 降低误删重要接口和类型的风险
- **更准确的识别**: 正确识别了所有在其他类中被引用的接口和类

### 🛡️ 安全性提升
- **防止误删接口**: 即使接口没有被直接调用，只要被引用就保留
- **保护类型声明**: 作为字段类型、参数类型的类不会被误删
- **更保守的策略**: 宁可保留可能有用的类，也不误删重要的类

---

## v2.0 (2025-06-20)

### 🎯 主要改进
- **修复静态方法调用检测不准确问题**: 解决了 `MD5Util.parseStrToMd5U32()` 等静态方法调用未被正确识别的问题
- **增强单例模式检测**: 新增对 `CalendarUtil.getInstance()` 等单例调用模式的检测
- **改进Kotlin object调用检测**: 优化对Kotlin object类型的静态调用识别
- **优化正则表达式**: 提高匹配准确性，减少误报
- **支持合并类和资源扫描结果**: 在同一个Markdown文件中显示类和资源的扫描结果
- **添加模块配置选项**: 支持通过配置文件灵活配置扫描范围
- **移除行号显示**: 优化报告格式，去掉行号列
- **增强AndroidManifest解析**: 改进对Manifest中组件声明的识别

### 🔧 技术优化
- **改进import语句分析**: 更准确地识别类的导入和使用
- **增强内部类检测**: 支持 `OuterClass.InnerClass.CONSTANT` 等多级访问模式
- **优化类使用情况分析**: 新增多种使用模式的检测
- **支持配置文件**: 通过 `scan_config.json` 灵活配置扫描选项

### 📊 扫描结果改进
- **显著减少误报**: 未使用类从992个减少到388个（约60%的准确性提升）
- **更准确的识别**: 正确识别了之前误报的工具类如 MD5Util、CalendarUtil 等
- **更全面的分析**: 支持同时扫描类和资源文件

### 🛠️ 新增功能
- **配置文件支持**: 可通过JSON配置文件自定义扫描参数
- **模块选择**: 支持选择扫描特定模块（app、lib_kit、kline）
- **排除规则**: 可配置需要排除的类和资源模式
- **合并报告**: 类和资源扫描结果合并在同一个报告中

### 📝 报告格式优化
- **去除行号**: 简化表格显示，提高可读性
- **增强Git信息**: 包含详细的提交历史信息
- **改进说明文档**: 更详细的分析说明和清理建议

### ⚠️ 已知问题
- 某些通过反射调用的类可能仍会被误报为未使用
- 动态加载的类和资源可能无法被正确识别
- 注解处理器生成的类使用情况可能检测不完整

### 🔄 向后兼容性
- 保持与v1.0版本的基本功能兼容
- 配置文件为可选，不影响现有使用方式

---

## v1.0 (2025-06-20)

### 🎯 初始版本功能
- 基础的Java和Kotlin类扫描
- 简单的资源文件扫描
- 基本的使用情况分析
- Markdown格式报告生成
- Git提交信息集成

### 📊 扫描范围
- 支持扫描app、lib_kit、kline模块
- 检测类定义和基本使用情况
- 生成详细的分析报告

### 🔍 检测能力
- 基本的import语句分析
- 简单的静态调用检测
- AndroidManifest组件声明检查
- 基础的资源引用分析

---

## 使用说明

### 快速开始
```bash
# 使用默认配置
python3 scan_unused_classes_v2.py

# 使用自定义配置
# 1. 修改 scan_config.json 文件
# 2. 运行扫描脚本
python3 scan_unused_classes_v2.py
```

### 配置文件示例
```json
{
  "scan_modules": ["app"],
  "scan_classes": true,
  "scan_resources": true,
  "include_lib_modules": false,
  "output_file": "unused_analysis_report_v2.md"
}
```

### 注意事项
1. 建议在删除前创建新分支进行验证
2. 运行完整的测试套件确保功能正常
3. 手动验证重要的类和资源
4. 注意反射调用和动态加载的情况

---

*此更新日志记录了扫描器的完善过程和主要改进点，便于追溯脚本的发展历程。*
