package com.upex.common.utils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

public class DataUtils {
    public static long dateStringToTimestamp(String dateString, String dateFormat) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
            sdf.setTimeZone(TimeZone.getTimeZone("GTM+2"));
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(sdf.parse(dateString));

            long timestamp = calendar.getTimeInMillis();
            return timestamp ;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    public static String timestampToDate(long timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd hh:mm");
        sdf.setTimeZone(TimeZone.getTimeZone("GTM+2"));
        Date date = new Date(timestamp*1000);
        return sdf.format(date);
    }
}
