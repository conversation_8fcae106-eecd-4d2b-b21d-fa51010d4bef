package com.upex.common.utils.display

import android.app.Activity
import android.content.Context
import android.graphics.Point
import android.graphics.Rect
import android.os.Build
import android.provider.Settings
import android.util.DisplayMetrics
import android.view.Display
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.ImageView
import com.upex.common.utils.app.ApplicationUtil

/**
 * Created by lion on 2017/4/11.
 */
object ToolDisplayUtils {
    /**
     * dp2px
     */
    fun dp2px(context: Context, dpValue: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (dpValue * scale + 0.5f).toInt()
    }

    fun dp2px(dpValue: Float): Int {
        val scale = ApplicationUtil.app.resources.displayMetrics.density
        return (dpValue * scale + 0.5f).toInt()
    }

    /**
     * px2dp
     */
    fun px2dp(context: Context, pxValue: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (pxValue / scale + 0.5f).toInt()
    }

    /**
     * sp to px
     *
     * @param context activity context
     * @param sp      the font size sp
     * @return pixel
     */
    fun sp2px(context: Context, sp: Float): Int {
        val fontScale = context.resources.displayMetrics.scaledDensity
        return (sp * fontScale + 0.5f).toInt()
    }

    fun sp2px(sp: Float): Int {
        val fontScale = ApplicationUtil.app.resources.displayMetrics.density
        return (sp * fontScale + 0.5f).toInt()
    }

    // 将px值转换为sp值，保证文字大小不变
    fun px2sp(context: Context, pxValue: Float): Int {
        val fontScale = context.resources.displayMetrics.scaledDensity
        return (pxValue / fontScale + 0.5f).toInt()
    }

    fun getStatusBarHeight(context: Context): Int {
        var result = 0
        val resourceId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = context.resources.getDimensionPixelSize(resourceId)
        }
        return result
    }

    fun getScreenSize(context: Context): Rect {
        val wm = context
                .getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val width = wm.defaultDisplay.width
        val height = wm.defaultDisplay.height
        val size = Rect()
        size[0, 0, width] = height
        return size
    }

    private var wm: WindowManager? = null

    /**
     * 获取真实屏幕高度
     *
     * @return
     */
    val realHeight: Int
        get() {
            if (null == wm) {
                wm =  ApplicationUtil.app.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            }
            val point = Point()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                wm!!.defaultDisplay.getRealSize(point)
            } else {
                wm!!.defaultDisplay.getSize(point)
            }
            return point.y
        }

    /**
     * 判断是否显示了导航栏
     * (说明这里的context 一定要是activity的context 否则类型转换失败)
     *
     * @param context
     * @return
     */
    fun isShowNavBar(context: Activity?): Boolean {
        if (null == context) {
            return false
        }
        /**
         * 获取应用区域高度
         */
        val outRect1 = Rect()
        try {
            context.window.decorView.getWindowVisibleDisplayFrame(outRect1)
        } catch (e: ClassCastException) {
            e.printStackTrace()
            return false
        }
        val activityHeight = outRect1.height()

        /**
         * 获取状态栏高度
         */
        val statuBarHeight = getStatusBarHeight(context)

        /**
         * 屏幕物理高度 减去 状态栏高度
         */
        val remainHeight = realHeight - statuBarHeight
        /**
         * 剩余高度跟应用区域高度相等 说明导航栏没有显示 否则相反
         */
        return activityHeight != remainHeight
    }

    /**
     * 去除状态栏 和 导航条的高度
     */
    fun getHeight(context: Context): Int {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = windowManager.defaultDisplay
        val dm = DisplayMetrics()
        display.getMetrics(dm)
        return dm.heightPixels
    }

    /**
     * 包含状态栏，不包含导航条 的高度
     */
    fun getHeightWithStatusBar(context: Context):Int{
        return getHeight(context) + getStatusBarHeight(context)
    }

    fun getRealHeight(context: Context): Int {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = windowManager.defaultDisplay
        val dm = DisplayMetrics()
        display.getRealMetrics(dm)
        return dm.heightPixels
    }

    fun getVirtualBarHeight(context: Context): Int {
        var vh = 0
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = windowManager.defaultDisplay
        val dm = DisplayMetrics()
        try {
            val c = Class.forName("android.view.Display")
            val method = c.getMethod("getRealMetrics", DisplayMetrics::class.java)
            method.invoke(display, dm)
            vh = dm.heightPixels - display.height
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return vh
    }

    fun checkDeviceHasNavigationBar(context: Context): Boolean {
        var hasNavigationBar = false;
        var rs = context.getResources();
        var id = rs.getIdentifier("config_showNavigationBar", "bool", "android");
        if (id > 0) {
            hasNavigationBar = rs.getBoolean(id)
        }
        try {
            var systemPropertiesClass = Class.forName("android.os.SystemProperties");
            var m = systemPropertiesClass.getMethod("get", String.javaClass)
            var navBarOverride = m.invoke(systemPropertiesClass, "qemu.hw.mainkeys");
            if ("1" == navBarOverride) {
                hasNavigationBar = false;
            } else if ("0" == navBarOverride) {
                hasNavigationBar = true;
            }
        } catch (e: Exception) {
            e.printStackTrace()

        }
        return hasNavigationBar;
    }

    fun isNavigationBarShowing(context: Context): Boolean {
        //判断手机底部是否支持导航栏显示
        var haveNavigationBar = checkDeviceHasNavigationBar(context);
        if (haveNavigationBar) {
            if (Build.VERSION.SDK_INT >= 17) {
                var brand = Build.BRAND
                var mDeviceInfo = ""
                mDeviceInfo = if (brand.equals("HUAWEI", ignoreCase = true)) {
                    "navigationbar_is_min";
                } else if (brand.equals("XIAOMI", ignoreCase = true)) {
                    "force_fsg_nav_bar";
                } else if (brand.equals("VIVO", ignoreCase = true)) {
                    "navigation_gesture_on";
                } else if (brand.equals("OPPO", ignoreCase = true)) {
                    "navigation_gesture_on";
                } else {
                    "navigationbar_is_min";
                }
                if (Settings.Global.getInt(context.contentResolver, mDeviceInfo, 0) == 0) {
                    return true
                }
            }
        }
        return false
    }

    fun getVirtualBarHeightToDp(context: Context): Int {
        return px2dp(context, DisplayUtils.getVirtualBarHeight(context).toFloat())
    }

    /**
     *
     * @param iv
     * @param rate
     * @param baseX
     * @param lp
     */
    fun fitXYImageView(iv: ImageView, rate: Double, baseX: Boolean, lp: ViewGroup.LayoutParams) {
        try {
            if (baseX) {
                val height = (getScreenSize( ApplicationUtil.app).width() * rate).toInt()
                lp.height = height
            } else {
                val wid = (getScreenSize( ApplicationUtil.app).height() * rate).toInt()
                lp.width = wid
            }
            iv.layoutParams = lp
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    /**
     * Get Display
     *
     * @param context Context for get WindowManager
     * @return Display
     */
    fun getDisplay(context: Context): Display? {
        val wm: WindowManager?
        wm = if (context is Activity) {
            context.windowManager
        } else {
            context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        }
        return wm?.defaultDisplay
    }

    fun getScreenWidth(context: Context?): Int {
        var tempContext = getContext(context)
        val display = getDisplay(tempContext) ?: return 0
        val point = Point()
        display.getSize(point)
        return point.x
    }

    fun getScreenWidth_Sub(context: Context?, value_dp: Float): Float {
        return getScreenWidth(context) - dp2px(value_dp).toFloat()
    }


    fun getScreenHeight(context: Context): Int {
        val display = getDisplay(context) ?: return 0
        val point = Point()
        display.getSize(point)
        return point.y
    }

    fun getScreenRealWidth(context: Context): Int {
        val display = getDisplay(context) ?: return 0
        val outSize = Point()
        display.getRealSize(outSize)
        return outSize.x
    }

    fun getScreenRealHeight(context: Context): Int {
        val display = getDisplay(context) ?: return 0
        val outSize = Point()
        display.getRealSize(outSize)
        return outSize.y
    }


    fun getContext(context: Context?): Context {
        if (context != null) {
            return context
        }
        return ApplicationUtil.app
    }

}