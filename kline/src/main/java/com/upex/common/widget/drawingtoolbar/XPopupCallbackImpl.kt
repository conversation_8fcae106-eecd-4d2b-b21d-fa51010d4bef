package com.upex.common.widget.drawingtoolbar

import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.interfaces.XPopupCallback

open class XPopupCallbackImpl: XPopupCallback {
    override fun onCreated(popupView: BasePopupView?) {
        
        
    }

    override fun beforeShow(popupView: BasePopupView?) {

    }

    override fun onShow(popupView: BasePopupView?) {

    }

    override fun onDismiss(popupView: BasePopupView?) {

    }

    override fun beforeDismiss(popupView: BasePopupView?) {

    }

    override fun onBackPressed(popupView: BasePopupView?): <PERSON><PERSON><PERSON> {
        return false
    }

    override fun onKeyBoardStateChanged(popupView: BasePopupView?, height: Int) {

    }

    override fun onDrag(popupView: BasePopupView?, value: Int, percent: Float, upOrLeft: Bo<PERSON>an) {

    }

    override fun onClickOutside(popupView: BasePopupView?) {

    }
}