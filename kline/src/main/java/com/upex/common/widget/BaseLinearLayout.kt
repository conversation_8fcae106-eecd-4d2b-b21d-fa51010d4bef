package com.upex.common.widget

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.widget.LinearLayout
import com.upex.common.widget.view.BaseDrawable
//import com.upex.common.widget.view.StyleDrawableUtil
import com.upex.common.widget.view.baseview.BaseViewInter

/**
 * BaseLinearLayout
 * 实现了快捷背景
 */
open class BaseLinearLayout : LinearLayout, BaseViewInter {

    constructor(context: Context) : super(context)

    constructor(
            context: Context,
            attrs: AttributeSet?
    ) : super(context, attrs) {
        analizeStyle(context, attrs)
    }

    constructor(
            context: Context,
            attrs: AttributeSet?,
            defStyleAttr: Int
    ) : super(context, attrs, defStyleAttr) {
        analizeStyle(context, attrs)
    }

    //基础样式
    override var baseDrawable: BaseDrawable = BaseDrawable()

    /**
     * 解析样式
     */
    override fun analizeStyle(context: Context,
                     attrs: AttributeSet?) {
        if (attrs == null) return
//        StyleDrawableUtil.analyzeAttrs(context, attrs, this, baseDrawable)
        updateBackDrawable()
    }

    fun setOriginalBackground(backgroundDrawable: Drawable?) {
        baseDrawable.systemDrawable = backgroundDrawable
        background = baseDrawable.getDrawable()
    }

    fun updateBackGroundColor(color: Int) {
        baseDrawable.mNormalColor = color
        background = baseDrawable.getDrawable()
    }

    override fun updateBackDrawable() {
        if (baseDrawable.isCustomDrawable()) {
            background = baseDrawable.getDrawable()
        }
    }

}