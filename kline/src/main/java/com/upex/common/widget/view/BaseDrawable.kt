package com.upex.common.widget.view

import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.graphics.drawable.StateListDrawable
import androidx.annotation.ColorInt
//import com.google.android.material.shape.MaterialShapeDrawable

/**
 * 通用基础drawable类
 */
class BaseDrawable{
    //系统的drawable，如果它存在则使用baseDrawable内容，用来做兼容处理
    var systemDrawable: Drawable? = null

    /**
     * 边框与圆角
     */
    var mCorner:Float = 0f

    var mStrokeColor = 0

    var mStrokeWidth = 0.0f

    var mLeftTopCorner:Float = 0f

    var mRightTopCorner:Float = 0f

    var mLeftBottomCorner:Float = 0f

    var mRightBottomCorner:Float = 0f


    /**
     * 颜色
     */
    var mNormalColor = 0

//    var mPressedColor = 0

    var mDisableColor = 0

//    var mSelectColor = 0

    /**
     * 渐变相关
      */
    var mElevation = 0f
    var mShadowColor = Color.TRANSPARENT
    var mShadowRadius = 0

    /**
     * 创建drawable
     */
    private fun getGradientDrawable(@ColorInt color: Int): Drawable {
//        val msd = MyMaterialShapeDrawable()
//        msd.fillColor = ColorStateList.valueOf(color)
//        msd.setCornerSize(mCorner.toFloat())
//        msd.strokeColor = ColorStateList.valueOf(mStrokeColor)
//        msd.strokeWidth = mStrokeWidth
//        if (mShadowRadius > 0 || mElevation > 0f) {
//            msd.shadowCompatibilityMode = MaterialShapeDrawable.SHADOW_COMPAT_MODE_ALWAYS
//            msd.setShadowColor(mShadowColor)
//            if (mElevation > 0) {
//                msd.elevation = mElevation
//            }
//            if (mShadowRadius > 0) {
//                msd.shadowRadius = mShadowRadius * 4
//            }
//        }
//        if (mCorner <= 0.0f) {
//            msd.shapeAppearanceModel = msd.shapeAppearanceModel.toBuilder()
//                .setTopLeftCornerSize(mLeftTopCorner.toFloat())
//                .setTopRightCornerSize(mRightTopCorner.toFloat())
//                .setBottomRightCornerSize(mRightBottomCorner.toFloat())
//                .setBottomLeftCornerSize(mLeftBottomCorner.toFloat()).build()
//        }
        return getDrawable()
    }

    /**
     * 获取drawable
     */
    fun getDrawable(): Drawable {
        if (systemDrawable != null) {
            return systemDrawable!!
        }
        val stateListDrawable = StateListDrawable()
        stateListDrawable.addState(intArrayOf(-android.R.attr.state_enabled), getGradientDrawable(mDisableColor))
        stateListDrawable.addState(intArrayOf(android.R.attr.state_enabled), getGradientDrawable(mNormalColor))
        return stateListDrawable
//        stateListDrawable.addState(intArrayOf(R.attr.state_selected), getGradientDrawable(mSelectColor))
//        stateListDrawable.addState(intArrayOf(-R.attr.state_pressed), getGradientDrawable(mPressedColor))
//        stateListDrawable.addState(intArrayOf(R.attr.state_pressed), getGradientDrawable(mPressedColor))
    }

    fun isCustomDrawable():Boolean{
        return !(mCorner==0f &&
                mStrokeColor==0&&
                mStrokeWidth==0.0f&&
                mLeftTopCorner==0f&&
                mRightTopCorner==0f&&
                mLeftBottomCorner==0f&&
                mRightBottomCorner==0f&&
                mNormalColor==0&&
                mDisableColor==0&&
                mElevation==0f&&
                mShadowColor==Color.TRANSPARENT
                && mShadowRadius== 0) ||
                systemDrawable!=null

    }


}