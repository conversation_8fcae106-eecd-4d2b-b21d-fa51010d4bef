package com.upex.common.drawTools

import android.graphics.PointF
import android.graphics.RectF
import android.os.Parcel
import android.os.Parcelable
import com.example.myapplication.R
import com.upex.common.drawTools.db.Line
//import com.upex.common.constants.Constant
//import com.upex.common.utils.CommonSPUtil
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlin.math.abs
import kotlin.math.pow
import kotlin.math.sqrt


var drawShowAll = true

/**
 * 是不是在圆中
 */
fun isInCircle(x:Float?,y:Float?,x1:Float?,y1:Float?,r:Float?):<PERSON><PERSON>an{
    if (x==null||y==null||x1==null||y1==null||r==null){
        return false
    }
    return sqrt((x - x1) * (x - x1) + (y - y1) * (y - y1)) <= r
}

/**
 * 点到直线的距离是不是小于R
 */
fun isInLine(x: Double, y: Double, x1: Double, y1: Double, x2: Double, y2: Double,r:Double):Boolean{
    if (distancePointToPoint(x, y, x1, y1) <= r || distancePointToPoint(x, y, x2, y2) <= r){
        return true
    }else if (calculateDistance(x, y, x1, y1,x2, y2) <= r ){
        return true
    }
    return false
}


/**
 * 点到线段的距离是不是小于R
 */
fun isInSegment(x: Double, y: Double, x1: Double, y1: Double, x2: Double, y2: Double,r:Double):Boolean{
    val distance1 = distancePointToPoint(x, y, x1, y1)
    val distance2 = distancePointToPoint(x, y, x2, y2)
    val distancePointToLine = getLength( x1, y1, x2, y2,x, y)
    if (distancePointToLine <= distance1 && distancePointToLine <= distance2 && distancePointToLine <= r) {
        return true
    }
    return false
}

fun calculateDistance(x: Double, y: Double, x1: Double, y1: Double, x2: Double, y2: Double): Double {
    val numerator = abs((y2 - y1) * x - (x2 - x1) * y + x2 * y1 - y2 * x1)
    val denominator = sqrt((y2 - y1) * (y2 - y1) + (x2 - x1) * (x2 - x1))
    return numerator / denominator
}

fun getLength(lx1: Double, ly1: Double, lx2: Double, ly2: Double, px: Double, py: Double): Double {
    var length: Double? = null
    val b: Double = getLenWithPoints(lx1, ly1, px, py)
    val c: Double = getLenWithPoints(lx2, ly2, px, py)
    val a: Double = getLenWithPoints(lx1, ly1, lx2, ly2)
    length = if (c + b == a) { // 点在线段上
        0.0
    } else if (c * c >= a * a + b * b) { // 组成直角三角形或钝角三角形，投影在point1延长线上，
        b
    } else if (b * b >= a * a + c * c) { // 组成直角三角形或钝角三角形，投影在point2延长线上，
        c
    } else {
        // 组成锐角三角形，则求三角形的高
        val p = (a + b + c) / 2 // 半周长
        val s = Math.sqrt(p * (p - a) * (p - b) * (p - c)) // 海伦公式求面积
        2 * s / c // 返回点到线的距离（利用三角形面积公式求高）
    }
    return length
}

private fun getLenWithPoints(p1x: Double, p1y: Double, p2x: Double, p2y: Double): Double {
    return sqrt((p2x - p1x).pow(2.0) + (p2y - p1y).pow(2.0))
}

/**
 * 点到点到距离
 */
fun distancePointToPoint(x: Double, y: Double, x1: Double, y1: Double): Double{
    return  sqrt((x - x1) * (x - x1) + (y - y1) * (y - y1))
}

/**
 * 获取直线和矩形相交的点，有可能是2个，1个，0个
 */
fun getIntersectionPoints(rectF: RectF, a: PointF, b: PointF): List<PointF> {
    val points = mutableListOf<PointF>()

    // 计算直线的斜率和截距
    val k = (b.y - a.y) / (b.x - a.x)
    val b1 = a.y - k * a.x

    // 计算直线与左侧边界的交点
    var x = rectF.left
    var y = k * x + b1
    if (y >= rectF.top && y <= rectF.bottom) {
        points.add(PointF(x, y))
    }

    // 计算直线与右侧边界的交点
    x = rectF.right
    y = k * x + b1
    if (y >= rectF.top && y <= rectF.bottom) {
        points.add(PointF(x, y))
    }

    // 计算直线与上方边界的交点
    var y2 = rectF.top
    var x2 = (y2 - b1) / k
    if (x2 >= rectF.left && x2 <= rectF.right) {
        points.add(PointF(x2, y2))
    }

    // 计算直线与下方边界的交点
    y2 = rectF.bottom
    x2 = (y2 - b1) / k
    if (x2 >= rectF.left && x2 <= rectF.right) {
        points.add(PointF(x2, y2))
    }

    return points.distinct()
}

/**
 * 获取射线和矩形相交的点，有可能是2个，1个，0个
 */
fun getIntersectionPointsRay(rectF: RectF, a: PointF, b: PointF): List<PointF> {
    val points = mutableListOf<PointF>()

    // 计算直线的斜率和截距
    val k = (b.y - a.y) / (b.x - a.x)
    val b1 = a.y - k * a.x

    // 计算直线与左侧边界的交点
    var x = rectF.left
    var y = k * x + b1
    if (y >= rectF.top && y <= rectF.bottom) {
        points.add(PointF(x, y))
    }

    // 计算直线与右侧边界的交点
    x = rectF.right
    y = k * x + b1
    if (y >= rectF.top && y <= rectF.bottom) {
        points.add(PointF(x, y))
    }

    // 计算直线与上方边界的交点
    var y2 = rectF.top
    var x2 = (y2 - b1) / k
    if (x2 >= rectF.left && x2 <= rectF.right) {
        points.add(PointF(x2, y2))
    }

    // 计算直线与下方边界的交点
    y2 = rectF.bottom
    x2 = (y2 - b1) / k
    if (x2 >= rectF.left && x2 <= rectF.right) {
        points.add(PointF(x2, y2))
    }
    return points.filter { (it.x - b.x) * (b.x - a.x) >= 0 || (it.x - a.x) * (b.x - a.x) >= 0 }.distinct()
}

fun calculateParallelLine1(x1: Float, y1: Float, x2: Float, y2: Float, x3: Float, y3: Float): Pair<PointF, PointF> {
    val k = if (x2 - x1 == 0f) Float.POSITIVE_INFINITY else (y2 - y1) / (x2 - x1)
    val b = y3 - k * x3
    val x4 = x1
    val y4 = k * x4 + b
    val x5 = x2
    val y5 = k * x5 + b
    return Pair(PointF(x4, y4), PointF(x5, y5))
}

fun setTransparency(color: Int,alpha: Int) :Int {
    return (color and 0x00FFFFFF) or (alpha shl 24)
}
val LineList by lazy { listOf(
    LineTypePopupWindowBean(
        DrawToolsType.TrendLine
    ),
    LineTypePopupWindowBean(
        DrawToolsType.ExtendTrendLine
    ),
    LineTypePopupWindowBean(
        DrawToolsType.Ray
    ),
    LineTypePopupWindowBean(
        DrawToolsType.HorizontalStraightLine
    ),
    LineTypePopupWindowBean(

        DrawToolsType.VerticalSegment
    ),
    LineTypePopupWindowBean(

        DrawToolsType.ParallelLine
    ),
    LineTypePopupWindowBean(
        DrawToolsType.PriceLine
    )
) }

val WaveList by lazy {
    listOf(
        LineTypePopupWindowBean(
            DrawToolsType.ThreeWaves
        ),
        LineTypePopupWindowBean(
            DrawToolsType.FiveWaves
        ),
    )
}

val ColorList by lazy{
    listOf(
        ColorPopupWindowBean(DrawToolsColors.COLOR_0),
        ColorPopupWindowBean(DrawToolsColors.COLOR_1),
        ColorPopupWindowBean(DrawToolsColors.COLOR_2),
        ColorPopupWindowBean(DrawToolsColors.COLOR_3),
        ColorPopupWindowBean(DrawToolsColors.COLOR_4),
        ColorPopupWindowBean(DrawToolsColors.COLOR_5),
    )
}

val LineWidthList by lazy { listOf(
    LinePopupWindowBean(DrawToolsLineWidth.LineWidth0),
    LinePopupWindowBean(DrawToolsLineWidth.LineWidth1),
    LinePopupWindowBean(DrawToolsLineWidth.LineWidth2),
    LinePopupWindowBean(DrawToolsLineWidth.LineWidth3)
) }

val LineStyleList by lazy {
    listOf(
        LineStylePopupWindowBean(DrawToolsLineStyle.LineStyle0),
        LineStylePopupWindowBean(DrawToolsLineStyle.LineStyle1),
        LineStylePopupWindowBean(DrawToolsLineStyle.LineStyle2),
        LineStylePopupWindowBean(DrawToolsLineStyle.LineStyle3)
    )
}

val FillList by lazy {
    listOf(
        ColorPopupWindowBean(DrawToolsColors.FILL_COLOR_0),
        ColorPopupWindowBean(DrawToolsColors.FILL_COLOR_1),
        ColorPopupWindowBean(DrawToolsColors.FILL_COLOR_2),
        ColorPopupWindowBean(DrawToolsColors.FILL_COLOR_3,1, 0),
        ColorPopupWindowBean(DrawToolsColors.FILL_COLOR_4),
        ColorPopupWindowBean(DrawToolsColors.FILL_COLOR_5),
        ColorPopupWindowBean(DrawToolsColors.FILL_COLOR_6),
    )
}

data class ColorPopupWindowBean(var colorType: DrawToolsColors, val style:Int = 0, var iconResId:Int = 0)
data class LinePopupWindowBean(var lineWidth: DrawToolsLineWidth)
data class LineStylePopupWindowBean(var lineStyle: DrawToolsLineStyle)
data class LineTypePopupWindowBean(val trendLineType: DrawToolsType)

data class DrawToolShape(
    var shapeColor: Int,
    var shapeFillColor: Int,
    var shapeType: String?,
    var shapeLineWidth: Int,
    var shapeLineStyle: Int,
    var pairName: String?,
    var business: String?,
    var shapeIsLock: Boolean,
    var canDelete:Boolean = false,
    var isSelect:Boolean = false,
    var isHasFillColor:Boolean = false,
    var show:Boolean = false,
    var showBottom:Boolean = false,
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readInt(),
        parcel.readInt(),
        parcel.readString(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readString(),
        parcel.readString(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte()
    ) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(shapeColor)
        parcel.writeInt(shapeFillColor)
        parcel.writeString(shapeType)
        parcel.writeInt(shapeLineWidth)
        parcel.writeInt(shapeLineStyle)
        parcel.writeString(pairName)
        parcel.writeString(business)
        parcel.writeByte(if (shapeIsLock) 1 else 0)
        parcel.writeByte(if (canDelete) 1 else 0)
        parcel.writeByte(if (isSelect) 1 else 0)
        parcel.writeByte(if (isHasFillColor) 1 else 0)
        parcel.writeByte(if (show) 1 else 0)
        parcel.writeByte(if (showBottom) 1 else 0)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<DrawToolShape> {
        override fun createFromParcel(parcel: Parcel): DrawToolShape {
            return DrawToolShape(parcel)
        }

        override fun newArray(size: Int): Array<DrawToolShape?> {
            return arrayOfNulls(size)
        }
    }
}

object DrawShapeUtils{

    private var _drawShapeFlow = MutableStateFlow<DrawToolShape?>(null)

    val drawShapeFlow: StateFlow<DrawToolShape?>
        get() = _drawShapeFlow

    fun changeShape(drawShape: DrawToolShape) {
        _drawShapeFlow.value = drawShape
//        CommonSPUtil.putDrawToolShape(drawShape)
    }

    var drawShapeLock = MutableStateFlow<Boolean>(false)

    var drawShapeCanDelete = MutableStateFlow<Boolean>(false)

    var isHasFillColor = MutableStateFlow<Boolean>(false)

    fun init(drawShape: DrawToolShape?) {
        if (drawShape == null){
            _drawShapeFlow.value = DrawToolShape(
                DrawToolsColors.COLOR_4.color,
                DrawToolsColors.FILL_COLOR_5.color,
                "",
                DrawToolsLineWidth.LineWidth0.width,
                DrawToolsLineStyle.LineStyle3.number,
                "common",
                "common",
                false,
            )
        } else {
            _drawShapeFlow.value = drawShape//todo
        }
    }
}











