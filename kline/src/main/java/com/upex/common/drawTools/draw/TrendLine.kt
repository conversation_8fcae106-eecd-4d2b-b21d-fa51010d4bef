package com.upex.common.drawTools.draw

import android.graphics.RectF
import com.upex.common.drawTools.isInSegment


class TrendLine: BaseDraw() {
    override fun setTotalPointCount():Int {
        return 2
    }

    override fun isSamePre() = true

    override fun isInSelectArea(x:Float,y:Float,mRect: RectF): Boolean {
        val contains = isInSegment(
            x.toDouble(),
            y.toDouble(),
            mPointList.first().x.toDouble(),
            mPointList.first().y.toDouble(),
            mPointList.last().x.toDouble(),
            mPointList.last().y.toDouble(),
            SELECT_DISTANCE.toDouble()
        )
        if (contains){
            return true
        }
        return super.isInSelectArea(x,y,mRect)
    }
}