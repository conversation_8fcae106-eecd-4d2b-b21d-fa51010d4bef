package com.upex.common.drawTools.draw

import android.graphics.Canvas
import android.graphics.PointF
import android.graphics.RectF
import com.upex.common.drawTools.getIntersectionPoints
import com.upex.common.drawTools.isInLine


class ExtendTrendLine : BaseDraw() {
    override fun setTotalPointCount(): Int {
        return 2
    }

    override fun onFinalDraw(canvas: Canvas, mRect: RectF) {
        val lineRectIntersection = getIntersectionPoints(
            mRect,
            PointF(mPointList.first().x, mPointList.first().y),
            PointF(mPointList.last().x, mPointList.last().y)
        )
        if (lineRectIntersection.size > 1) {
            canvas.drawLine(
                lineRectIntersection.first().x,
                lineRectIntersection.first().y,
                lineRectIntersection.last().x,
                lineRectIntersection.last().y,
                getPaint()
            )
        }
    }

    override fun isSamePre() = false

    override fun isInSelectArea(x: Float, y: Float, mRect: RectF): Boolean {
        val contains = isInLine(
            x.toDouble(),
            y.toDouble(),
            mPointList.first().x.toDouble(),
            mPointList.first().y.toDouble(),
            mPointList.last().x.toDouble(),
            mPointList.last().y.toDouble(),
            SELECT_DISTANCE.toDouble()
        )
        if (contains){
            return true
        }
        return super.isInSelectArea(x, y, mRect)
    }
}