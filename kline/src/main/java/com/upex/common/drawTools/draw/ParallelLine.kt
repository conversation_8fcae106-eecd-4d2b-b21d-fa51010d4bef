package com.upex.common.drawTools.draw

import android.graphics.Canvas
import android.graphics.RectF
import android.graphics.Region
import com.upex.common.drawTools.calculateParallelLine1
import kotlin.math.max
import kotlin.math.min


// TODO: 算法再看
class ParallelLine: BaseDraw() {
    override fun setTotalPointCount():Int {
        return 3
    }

    override fun onFinalDraw(canvas: Canvas, mRect: RectF) {

        fillPath.reset()

        val calculateParallelLine = calculateParallelLine1(
            mPointList.first().x,
            mPointList.first().y,
            mPointList[1].x,
            mPointList[1].y,
            mPointList.last().x,
            mPointList.last().y
        )

        fillPath.moveTo(mPointList.first().x, mPointList.first().y)
        fillPath.lineTo(mPointList[1].x, mPointList[1].y,)
        fillPath.lineTo(calculateParallelLine.second.x, calculateParallelLine.second.y)
        fillPath.lineTo(calculateParallelLine.first.x, calculateParallelLine.first.y)

        fillPath.close()

        canvas.drawPath(fillPath,getFillPaint())
        canvas.drawLine(mPointList[1].x, mPointList[1].y, mPointList.first().x, mPointList.first().y,getPaint())
        canvas.drawLine(calculateParallelLine.first.x, calculateParallelLine.first.y, calculateParallelLine.second.x, calculateParallelLine.second.y,getPaint())
        canvas.drawLine(calculateParallelLine.first.x, (calculateParallelLine.first.y + mPointList.first().y) / 2, calculateParallelLine.second.x, (mPointList[1].y + calculateParallelLine.second.y) / 2,getDashPathEffectPaint())
    }

    override fun onEditPoint(editIndex: Int,x: Float,y: Float) {
        if (editIndex<2){
            if (mPointList.last().x < mPointList[0].x && mPointList.last().x < mPointList[1].x){
                mPointList.last().x = mPointList[editIndex].x
            }else if (mPointList.last().x > mPointList[0].x && mPointList.last().x > mPointList[1].x){
                mPointList.last().x = mPointList[editIndex].x
            }
        }else{
            if ( mPointList.last().x > mPointList[0].x && mPointList.last().x > mPointList[1].x){
                mPointList.last().x = max(mPointList[0].x,mPointList[1].x)
            }else if (mPointList.last().x < mPointList[0].x && mPointList.last().x < mPointList[1].x){
                mPointList.last().x = min(mPointList[0].x,mPointList[1].x)
            }
        }
    }

    override fun isSamePre() = false

    override fun isHasFill() = true

    override fun isInSelectArea(x:Float,y:Float, mRect: RectF): Boolean {

        val pathBounds = RectF()
        fillPath.computeBounds(pathBounds, true)
        val pathRegion = Region().apply {
            setPath(fillPath, Region(pathBounds.left.toInt(), pathBounds.top.toInt(), pathBounds.right.toInt(), pathBounds.bottom.toInt()))
        }
        if(pathRegion.contains(x.toInt(), y.toInt())){
            return true
        }
        return super.isInSelectArea(x,y, mRect)
    }
}