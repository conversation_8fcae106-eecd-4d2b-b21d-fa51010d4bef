package com.upex.common.drawTools.draw

import android.graphics.RectF
import com.upex.common.drawTools.isInSegment
import com.upex.common.utils.display.DisplayUtils

class ThreeWaves: BaseDraw() {

    override fun setTotalPointCount():Int {
        return 4
    }

    override fun isSamePre() = true

    override fun isInSelectArea(x:Float,y:Float, mRect: RectF): Boolean {
        mPointList.forEachIndexed { index, drawPoint ->
            if (index>0){
                val inSegment = isInSegment(
                    x.toDouble(),
                    y.toDouble(),
                    mPointList[index-1].x.toDouble(),
                    mPointList[index-1].y.toDouble(),
                    drawPoint.x.toDouble(),
                    drawPoint.y.toDouble(),
                    DisplayUtils.dp2px(6f).toDouble()
                )
                if (inSegment){
                    return true
                }
            }
        }
        return super.isInSelectArea(x,y, mRect)
    }
}