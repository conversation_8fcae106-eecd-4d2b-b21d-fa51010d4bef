package com.upex.common.drawTools

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.*
import android.graphics.drawable.BitmapDrawable
import android.util.AttributeSet
import android.util.Log
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.GestureDetectorCompat
import androidx.lifecycle.Observer
import com.example.myapplication.R
import com.github.tifezh.kchartlib.chart.BaseKChartView
import com.github.tifezh.kchartlib.chart.impl.IValueFormatter
import com.github.tifezh.kchartlib.helper.bean.KLineEntity
import com.github.tifezh.kchartlib.helper.chart.KChartView
import com.github.tifezh.kchartlib.utils.DpConstant
import com.github.tifezh.kchartlib.utils.DpConstant.dp1
import com.github.tifezh.kchartlib.utils.DpConstant.dp2
import com.github.tifezh.kchartlib.utils.DpConstant.dp90
//import com.upex.common.utils.ResUtil
import com.upex.common.drawTools.db.DrawPoint
import com.upex.common.drawTools.db.DrawToolsDatabase
import com.upex.common.drawTools.db.Line
import com.upex.common.drawTools.draw.*
import com.upex.common.interfaces.IDrawDataManager
import com.upex.common.utils.display.DensityUtil
import com.upex.common.utils.display.DisplayUtils
import com.upex.common.utils.toLongCatching
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlin.Exception


class DrawView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr), GestureDetector.OnGestureListener {

    private var onDrawToolsOpen = false
    private var mKChartView:BaseKChartView?= null
    var digits = 2
    private var drawDataManager: IDrawDataManager? = null
//    private var drawList = ArrayList<BaseDraw>()

    fun setDrawToolsOpen(boolean: Boolean){
        this.onDrawToolsOpen = boolean
        setAllUnSelected()
        mCurrentDraw = null
        invalidate()
    }

    var showAllDraws = true
        set(value) {
            field = value
            drawDataManager?.getDrawList()?.forEach {
                it.show = showAllDraws
            }
            invalidate()
            drawShowAll = showAllDraws
        }

    var pairName: String = ""
    private var businessType: String = ""

    private val mMagnifierPaint = Paint()

    //放大镜 bitmap
    private var mChartViewBackgroundBitmap: Bitmap? = null
    private var mDrawToolsViewBackgroundBitmap: Bitmap? = null
    private var mMagnifiedBitmap: Bitmap? = null

    private val mMagnifierMatrix = Matrix()
    private val mMagnifierSize = dp90()
    private val mMagnifierScale = 1.2f
    private val mMagnifierClipPath = Path()
    private val mMagnifierBorderPaint = Paint().apply {
        color = context.getColor(R.color.black)
        style = Paint.Style.STROKE
        isAntiAlias = true
        strokeWidth = dp1().toFloat()
    }
    private var magnifierBorderColor = Color.BLACK

    private var mDetector: GestureDetectorCompat = GestureDetectorCompat(context, this)

    /**
     * 是否连续划线
     */
    private var mContinuous: Boolean = false

    private var mCurrentType : DrawToolsType? = null

    private var mCurrentDraw: BaseDraw? = null

    var mDrawRect = RectF()
    var clipRect = RectF()

    private val mMagnifierRect =  RectF(0f, 0f, mMagnifierSize.toFloat(), mMagnifierSize.toFloat())
    private val mMagnifierDestRect =  RectF()

    /**
     * 1.设置类型
     * 2.生成对应的图形
     * 3.把之前的都设置为不选中
     */
    fun setCurrentType(drawType: DrawToolsType) {
        mCurrentType = drawType
        mCurrentDraw?.isSelect = false
        mCurrentDraw = drawDataManager?.createNewDraw(drawType)
        mCurrentDraw?.digits = digits
        line.lineType = drawType.saveName
        mCurrentDraw?.line = line.copy()
        if (drawType == DrawToolsType.Highlighter){
            mCurrentDraw?.line?.color = DrawToolsColors.FILL_COLOR_5.color
            mCurrentDraw?.line?.lineWidth = DensityUtil.dp2px(20f)
        }
        mCurrentDraw?.isSelect = true

        setStepCallBack()
        setAllUnSelected()
        invalidate()
    }

    private val dataObserver: Observer<DrawToolShape> = Observer {
        line.color = it.shapeColor
        line.fillColor = it.shapeFillColor
        line.lineWidth = it.shapeLineWidth
        line.lineStyle = it.shapeLineStyle

        mCurrentDraw?.let { draw ->
            draw.line?.apply {
                color = it.shapeColor
                fillColor = it.shapeFillColor
                lineWidth = it.shapeLineWidth
                lineStyle = it.shapeLineStyle
            }
        }
        drawDataManager?.getInitLiveData()?.removeObserver(dataObserver)
    }

    fun setDataManager(drawDataManager: IDrawDataManager) {
        this.drawDataManager = drawDataManager
        drawDataManager.getInitLiveData().observeForever(dataObserver)
    }

    fun setCurrentDrawLineColor(color:Int) {//暴漏外部设置画笔颜色
        mCurrentDraw?.line?.color = color
        line.color = color
        drawDataManager?.saveToDb(pairName, businessType, mCurrentDraw)
        setDrawShape(mCurrentDraw)
        invalidate()
    }

    fun getLineColor(): Int? {
        return line.color
    }

    fun getLineWidth(): Int? {
        return line.lineWidth
    }

    fun getCurrentLineDrawView(): BaseDraw? {//暴漏外部获取当前的图形，用于获取画笔粗细颜色
        return mCurrentDraw
    }

    fun setCurrentDrawLineWidth(lineWidth:Int){//暴漏外部设置画笔粗细
        mCurrentDraw?.line?.lineWidth = lineWidth
        line.lineWidth = lineWidth
        drawDataManager?.saveToDb(pairName, businessType, mCurrentDraw)
        setDrawShape(mCurrentDraw)
        invalidate()
    }

    fun setCurrentDrawLineType(lineStyle:DrawToolsLineStyle){
        mCurrentDraw?.line?.lineStyle = lineStyle.number
        line.lineStyle = lineStyle.number
        invalidate()
    }

    fun setLightTheme(lightTheme: Boolean) {
        magnifierBorderColor = ContextCompat.getColor(context, if (lightTheme) { R.color.cFF1E1E1E } else { R.color.cEBFFFFFF })
    }

    private fun setAllUnSelected(){
        drawDataManager?.getDrawList()?.forEach {
            if (it.isSelect) {
                it.isSelect = false
            }
        }
    }

    var lastX = 0.0f
    var lastY = 0.0f

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (!onDrawToolsOpen){
            return false
        }
        var onTouchEvent = true
        if (event != null) {
            when (event.action) {
                MotionEvent.ACTION_DOWN-> {
                    parent.requestDisallowInterceptTouchEvent(true)
                }
                MotionEvent.ACTION_MOVE->{
                    mCurrentDraw?.let {
                        onMove(event.x, event.y,lastX - event.x,lastY - event.y )
                    }
                }
                MotionEvent.ACTION_UP -> {
                    onActionUp(event)
                    mMagnifiedBitmap = null
                    parent.requestDisallowInterceptTouchEvent(true)
                }
                MotionEvent.ACTION_CANCEL -> {
                    mMagnifiedBitmap = null
                    parent.requestDisallowInterceptTouchEvent(true)
                }
            }
            onTouchEvent = mDetector.onTouchEvent(event)
            lastX = event.x
            lastY = event.y
        }
        if(mCurrentDraw?.isFinish == false){
            return true
        }
        return onTouchEvent
    }

    /**
     * 抬起来手指
     * 0.设置最后的点 x，y
     * 1.把当前的加入到list中
     * 2.如果是连续画线的话，就在创建一个
     * 3.抬起来手指 保存到数据库
     * 4.回调
     */
    private fun onActionUp(event: MotionEvent){
        mCurrentDraw?.apply {
            onUp(setPositionX(event.x), setPositionY(event.y))
            setStepCallBack()
            if (isFinish){
                showBottomLayout?.invoke(true)
                saveTimeAndPrice(this)
                if (drawDataManager?.getDrawList()?.contains(this) == false) {
                    drawDataManager?.getDrawList()?.add(this)
                    showAllDraws = true
                    eyesShow?.invoke(showAllDraws)
                    drawShowAll = showAllDraws
                }
                 drawDataManager?.saveToDb(pairName, businessType, this@apply)
                if (mContinuous){
                    mCurrentType?.let { setCurrentType(it) }
                }else{
                    mCurrentType = null
                }
            }
        }
        invalidate()
    }

    private fun isShowInScreen(baseDraw: BaseDraw):Boolean {
        for (drawPoint in baseDraw.mPointList) {
            if (mDrawRect.contains(drawPoint.x,drawPoint.y)){
                return true
            }
        }
        return false
    }

    private fun saveTimeAndPrice(baseDraw: BaseDraw) {
        if (isShowInScreen(baseDraw).not()){
            return
        }
        for (drawPoint in baseDraw.mPointList) {
//            drawPoint.time = xToTime(drawPoint.x)
            drawPoint.price = yToPrice(drawPoint.y)
            val pointWidth = ((mKChartView?.mPointWidth ?: DisplayUtils.dp2px(8f).toFloat())/2f) * (mKChartView?.mScaleX?:1f)
//            val pointX = mKChartView?.xToTranslateX(drawPoint.x) ?: 0f
//            var index = (pointX / (mKChartView?.mPointWidth ?: DisplayUtils.dp2px(8f).toFloat())).toInt()
            var index = mKChartView?.calculateSelectedX(drawPoint.x)?:0
            Log.d("saveTimeAndPrice","Felix=drawPoint.x == ${drawPoint.x}")
            Log.d("saveTimeAndPrice","Felix=1111=index == $index")
            if (index >= (mKChartView?.adapter?.count ?: 0)){
                index = (mKChartView?.adapter?.count ?: 1) - 1
            }

            if (index < 0) {
                index = 0
            }
            Log.d("saveTimeAndPrice","Felix==index == $index")
            val time = (mKChartView?.adapter?.getItem(index) as? KLineEntity)?.datetime
            val X = mKChartView?.getX(index)?:0f
            Log.d("saveTimeAndPrice","Felix==X == $X")
            val realPointX = mKChartView?.translateXtoX(X)?:0f

            val offset = (drawPoint.x - realPointX + pointWidth)
            Log.d("saveTimeAndPrice","Felix==mKChartView?.mScaleX == ${mKChartView?.mScaleX}")
            Log.d("saveTimeAndPrice","Felix==offset == $offset")
//            drawPoint.timeStr = time
            drawPoint.offset = offset
            val offsetTime = offsetToTime(offset)
            drawPoint.timeStr = (time.toLongCatching()+offsetTime).toString()

            Log.d("saveTimeAndPrice","Felix==time == $time")
            Log.d("saveTimeAndPrice","Felix==drawPoint.timeStr == ${drawPoint.timeStr}")
            Log.d("saveTimeAndPrice","Felix==offsetToTime == ${offsetTime}")
            Log.d("saveTimeAndPrice","Felix==step == ${mKChartView?.adapter?.step}")

        }
//        val point = baseDraw.mPointList[baseDraw.mPointList.size-1]
    }

    private fun offsetToTime(offset:Float):Long {
        val step = mKChartView?.adapter?.step?:3600L
        val pointWidth = (mKChartView?.mPointWidth ?: DisplayUtils.dp2px(8f).toFloat()) * (mKChartView?.mScaleX?:1f)
        val time = step * (offset/pointWidth)

        return time.toLong()
    }

    private fun xToTimeStr(x: Float):String {
        var time:String? = "0"
        val pointX = mKChartView?.xToTranslateX(x) ?: 0f
        var index = (pointX / (mKChartView?.mPointWidth ?: 8f)).toInt()
        if (index >= (mKChartView?.adapter?.count ?: 0)){
            index = (mKChartView?.adapter?.count ?: 1) - 1
        }

        if (index < 0) {
            index = 0
        }
        time = (mKChartView?.adapter?.getItem(index) as? KLineEntity)?.datetime
        val realPointX = mKChartView?.translateXtoX(mKChartView?.getX(index)?:0f)?:0f
//        val offset = (drawPoint.x - realPointX)/(mKChartView?.mScaleX?:1f)
//        Log.d("Felix","Felix==offset == $offset")
//        drawPoint.timeStr = time
//        drawPoint.offset = offset
        Log.d("Felix","Felix==time == $time")
        return time?:"0"
    }


    private fun xToTime(x: Float): Long {
        if (rightTime == leftTime) {
            return 0L
        }
        return (leftTime + (x * (rightTime - leftTime) / width)).toLong()
    }

    private fun yToPrice(y: Float): Double {
        return topPrice - ((y - mDrawRect.top) / (mDrawRect.height() / (topPrice - bottomPrice)))
    }

    private fun timeToX(time: Long): Float {
        if (rightTime == leftTime) {
            return 0F
        }
        return (width * (time - leftTime.toDouble()) / (rightTime - leftTime)).toFloat()
    }

    private fun timeStrToX(time:String?,offset:Float):Float {
        if (time == null) return 0f
        Log.d("timeStrToX","==time==$time")
        val count = mKChartView?.adapter?.count?:0
        val step = mKChartView?.adapter?.step ?: (24 * 60 * 60)
        val timeLong = time.toLongCatching()
        var index = -1
        for (i in 0 until count){
            val kLineEntity = mKChartView?.adapter?.getItem(i) as? KLineEntity ?: continue
            val timestamp = kLineEntity.timestampLong
            if (i ==0 && timeLong < timestamp){
                index = i
                break
            }
            if (i == count -1 && timeLong>= timestamp){
                index = i
                break
            }
            if (timeLong >= timestamp && timeLong <= timestamp + step ) {
                index = i
                break
            }
//            var kLineEntityNext:KLineEntity? = null
//            if (i + 1 < count) {
//                kLineEntityNext = mKChartView?.adapter?.getItem(i+1) as? KLineEntity
//            }
//
//            if (kLineEntityNext != null) {
//                if (time.compareTo( kLineEntity.timestamp) !=-1  && time.compareTo(kLineEntityNext.timestamp) == -1 ){
//                    index = i
//                    break
//                }
//            } else if (i == count -1 && time.compareTo(kLineEntity.timestamp) != -1){
//                index = i
//            }

//            if (kLineEntity.timestamp == time){
//                index = i
//                break
//            }
        }
        Log.d("timeStrToX","==index==$index")
        if (index != -1){
            val kLineEntity = mKChartView?.adapter?.getItem(index) as? KLineEntity?:return 0f
            val x = mKChartView?.translateXtoX(mKChartView?.getX(index)?:0f)?:return 0f
            Log.d("timeStrToX","==xt==$x")

            val timeOff = time.toLongCatching() - kLineEntity.timestampLong
            val offset1 = timeOffsetToX(timeOff)
            val pointWidth = ((mKChartView?.mPointWidth ?: DisplayUtils.dp2px(8f).toFloat())/2f) * (mKChartView?.mScaleX?:1f)
            val xt = x + (offset1 - pointWidth)
            Log.d("timeStrToX","==xt=1111=$xt")
            return xt
        }

        return 0f
    }

    private fun timeOffsetToX(timeOff:Long):Float {
        val step = mKChartView?.adapter?.step?:3600L
        val pointWidth =( mKChartView?.mPointWidth ?: DisplayUtils.dp2px(8f).toFloat()) * (mKChartView?.mScaleX?:1f)
        val offset = pointWidth * timeOff/step.toFloat()
        return offset

    }

    private fun priceToY(price: Double): Float {
        return ((topPrice - price) * (mDrawRect.height()/(topPrice - bottomPrice)) + mDrawRect.top).toFloat()
    }


    override fun onDraw(canvas: Canvas) {
        try {
            drawDataManager?.getDrawList()?.forEach {
                if (it.show) {
                    computerPoint(it)
                    it.onDraw(canvas, mDrawRect, clipRect)
                }
            }

            if (mCurrentDraw?.isFinish == false) {
                if (mCurrentDraw?.show == true) {
                    mCurrentDraw?.onDraw(canvas, mDrawRect, clipRect)
                }
            }

            canvas.let {
                mMagnifiedBitmap?.let {
                    mMagnifierClipPath.reset()
                    val srcRectF = mMagnifierRect
                    val destRectF = mMagnifierDestRect
                    mMagnifierMatrix.mapRect(destRectF, srcRectF)
                    mMagnifierClipPath.addOval(destRectF, Path.Direction.CW)
                    canvas.save()
                    canvas.clipPath(mMagnifierClipPath)
                    canvas.drawBitmap(it, mMagnifierMatrix, mMagnifierPaint)
                    mMagnifierBorderPaint.color = magnifierBorderColor
                    canvas.drawOval(destRectF, mMagnifierBorderPaint)
                    canvas.restore()
                }
            }
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    private fun computerPoint(baseDraw: BaseDraw) {
        for (drawPoint in baseDraw.mPointList) {
//            drawPoint.x = timeToX(drawPoint.time)
            drawPoint.x = timeStrToX(drawPoint.timeStr,drawPoint.offset)
            drawPoint.y = priceToY(drawPoint.price)
        }
        val point = baseDraw.mPointList[baseDraw.mPointList.size-1]
    }

    private fun setDrawShape(baseDraw:BaseDraw?){//设置绘图的颜色和画笔粗细
        baseDraw?.let {
            DrawShapeUtils.isHasFillColor.value = baseDraw.isHasFill() == true
            it.line?.let { line->
                DrawShapeUtils.drawShapeFlow.value?.let { drawShape->
                    val copy = drawShape.copy(
                        shapeColor = line.color,
                        shapeFillColor = line.fillColor,
                        shapeLineWidth = line.lineWidth,
                        shapeLineStyle = line.lineStyle
                    )
                    drawConfigSave?.invoke(copy)//回调外部保存sp
                    DrawShapeUtils.changeShape(copy)
                    Log.d("BaseDraw","==11111")
                }
                DrawShapeUtils.drawShapeLock.value = line.isLock
            }
            DrawShapeUtils.drawShapeCanDelete.value = false
        }
    }

    private fun hitTarget(event: MotionEvent?): BaseDraw? {
        event?.let {
            drawDataManager?.getDrawList()?.reversed()?.forEach { draw ->
                if (draw.onSelect(it.x, it.y, mDrawRect)) {
                    return draw
                }
            }
        }
        return null
    }

    var onStep: ((String?, Int?, Int?, Boolean?) -> Unit)? = null

    @Deprecated("无用")
    var leftTime = 0L
    @Deprecated("无用")
    var rightTime = 0L
    var topPrice = 0.0
    var bottomPrice = 0.0

    fun setDrawRect(left:Float,top:Float,right: Float,bottom: Float){
        mDrawRect.set(left, top, right, bottom)
        clipRect.set(left, top, right, bottom + DpConstant.dp18())
    }

    fun setDrawLeft(left: Long) {
        this.leftTime = left
        invalidate()
    }

    fun setDrawRight(right: Long) {
        this.rightTime = right
    }

    fun setDrawTop(top: Double) {
        this.topPrice = top
    }

    fun setDrawBottom(bottom: Double) {
        this.bottomPrice = bottom
    }

    private var mValueFormatter: IValueFormatter? = null

    fun setValueFormatter(valueFormatter: IValueFormatter?) {
        this.mValueFormatter = valueFormatter
        drawDataManager?.getDrawList()?.forEach {
            it.setValueFormatter(mValueFormatter)
        }
    }

//    fun setDigits(digits:Int){
//        this.digits = digits
//        mDrawList.forEach {
//            it.digits = digits
//        }
//    }

    /**
     * 是否连续绘制
     */
    fun setContinuation(boolean: Boolean){
        this.mContinuous = boolean
    }

    fun getContinuation():Boolean{
        return mContinuous
    }

    fun setLock(lock: Boolean){
        mCurrentDraw?.line?.isLock = lock
        invalidate()
        drawDataManager?.saveToDb(pairName, businessType, mCurrentDraw)
    }


    override fun onDown(event: MotionEvent): Boolean {
        val preCheck = preCheck(event)
        event?.let {
            mCurrentDraw?.onDown(setPositionX(it.x), setPositionY(it.y), mDrawRect)
        }
        mCurrentDraw?.let {
            saveTimeAndPrice(it)
        }
        invalidate()
        return preCheck
    }

    private fun setPositionX(x: Float) :Float{
        var x1 = x
        if (x1 < mDrawRect.left) {
            x1 = mDrawRect.left
        }
        if (x1 > mDrawRect.right) {
            x1 = mDrawRect.right
        }
        return x1
    }

    private fun setPositionY(y: Float):Float{
        var y1 = y
        if (y1 < mDrawRect.top) {
            y1 = mDrawRect.top

        }
        if (y1 > mDrawRect.bottom) {
            y1 = mDrawRect.bottom
        }
        return y1
    }

    private fun onClick(event: MotionEvent?): Boolean {
        if (mContinuous){
            return false
        }
        if (drawDataManager?.getDrawList()?.isNotEmpty() == true && drawDataManager?.getDrawList()?.last()?.isFinish == true) {
            val hitTarget = hitTarget(event)
            mCurrentDraw?.isSelect = false
            if (hitTarget != null) {
                mCurrentDraw = hitTarget
                mCurrentDraw?.isSelect = true
            }else{
                mCurrentDraw = null
            }
            Log.d("BaseDraw","==hitTarget != null===${hitTarget != null}")
            showBottomLayout?.invoke(hitTarget != null)
            return hitTarget != null
        }
        return false
    }

    private fun preCheck(event: MotionEvent?): Boolean {
        if (mCurrentDraw?.isSelect == true || mCurrentDraw?.isFinish == false) {
            return true
        } else if (drawDataManager?.getDrawList()?.isNotEmpty() == true) {
            val hitTarget = hitTarget(event)
            return hitTarget != null
        }
        return false
    }

    override fun onShowPress(e: MotionEvent) {}

    override fun onSingleTapUp(event: MotionEvent): Boolean {
        if (mCurrentDraw?.isFinish == true || (mCurrentDraw == null && drawDataManager?.getDrawList()?.isNotEmpty() == true)) {
            val onClick = onClick(event)
            DrawShapeUtils.drawShapeCanDelete.value = onClick
        }
        invalidate()
        return true
    }

    override fun onScroll(
        e1: MotionEvent?,
        e2: MotionEvent,
        distanceX: Float,
        distanceY: Float
    ): Boolean {
        invalidate()
        return true
    }

    fun release() {
        BitmapHelper.recycler(mChartViewBackgroundBitmap, mMagnifiedBitmap,mDrawToolsViewBackgroundBitmap)
    }


    private fun onMove(x: Float,y: Float,distanceX: Float = 0f, distanceY: Float = 0f){
        mCurrentDraw?.let { draw ->
            val show = draw.onMove(setPositionX(x), setPositionY(y), distanceX, distanceY)
            saveTimeAndPrice(draw)
            if (show) {
                updateMagnifier(x, y)
            } else {
                mMagnifiedBitmap = null
            }
        }
    }

    var viewGroup : ViewGroup? = null
    var view : View? = null

    private fun updateMagnifier(x: Float, y: Float) {
        view?.let { view->
            mChartViewBackgroundBitmap = Bitmap.createBitmap(view.width, mDrawRect.height().toInt(), Bitmap.Config.ARGB_8888)
            mChartViewBackgroundBitmap?.let {
                val canvas = Canvas(it)
                canvas.clipRect(
                    (x - mMagnifierSize / 2).toInt(),
                    (y - mMagnifierSize / 2).toInt(),
                    (x + mMagnifierSize / 2).toInt(),
                    (y + mMagnifierSize / 2).toInt()
                )
                view.draw(canvas)
            }
            mDrawToolsViewBackgroundBitmap = Bitmap.createBitmap(width, mDrawRect.height().toInt(), Bitmap.Config.ARGB_8888)
            mDrawToolsViewBackgroundBitmap?.let {
                val canvas = Canvas(it)
                canvas.clipRect(
                    (x - mMagnifierSize / 2).toInt(),
                    (y - mMagnifierSize / 2).toInt(),
                    (x + mMagnifierSize / 2).toInt(),
                    (y + mMagnifierSize / 2).toInt()
                )
                draw(canvas)
            }
        }

        // 获取触摸点附近的图像区域
        val srcRect = Rect(
            (x - mMagnifierSize / 2).toInt(),
            (y - mMagnifierSize / 2).toInt(),
            (x + mMagnifierSize / 2).toInt(),
            (y + mMagnifierSize / 2).toInt()
        )
        // 根据触摸点位置决定放大镜位置
        val destRect = if (x < width / 2) {
            Rect(width - mMagnifierSize - 70 , 0, width - 70, mMagnifierSize)
        } else {
            Rect(0, 0, mMagnifierSize, mMagnifierSize)
        }
        // 创建放大后的图像
        mMagnifiedBitmap = Bitmap.createBitmap(
            mMagnifierSize,
            mMagnifierSize,
            mChartViewBackgroundBitmap?.config ?: Bitmap.Config.ARGB_8888
        )

        // 将触摸点附近的图像区域绘制到放大后的图像上
        if (mMagnifiedBitmap!=null){
            val magnifiedCanvas = Canvas(mMagnifiedBitmap!!)
            if (mChartViewBackgroundBitmap!=null){
                magnifiedCanvas.drawBitmap(mChartViewBackgroundBitmap!!, srcRect, Rect(0, 0, mMagnifierSize, mMagnifierSize), mMagnifierPaint)
            }
            if (mDrawToolsViewBackgroundBitmap!=null){
                magnifiedCanvas.drawBitmap(mDrawToolsViewBackgroundBitmap!!, srcRect, Rect(0, 0, mMagnifierSize, mMagnifierSize), mMagnifierPaint)
            }
        }
        // 更新放大镜矩阵
        mMagnifierMatrix.reset()
        mMagnifierMatrix.postScale(mMagnifierScale, mMagnifierScale)
        mMagnifierMatrix.postTranslate(destRect.left.toFloat(), destRect.top.toFloat())
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        val drawable = background as? BitmapDrawable
        if (drawable != null) {
            mChartViewBackgroundBitmap = drawable.bitmap
        } else {
            // 如果背景不是BitmapDrawable，请创建一个空白的Bitmap
            try {
                mChartViewBackgroundBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)
            }catch (e:Exception){
                e.printStackTrace()
            }
        }
    }

    override fun onLongPress(e: MotionEvent) {}

    override fun onFling(
        e1: MotionEvent?,
        e2: MotionEvent,
        velocityX: Float,
        velocityY: Float
    ): Boolean {
        return false
    }

    private fun setStepCallBack() {
        onStep?.invoke(
            mCurrentType?.drawName,
            mCurrentDraw?.getCurrentPointCount(),
            mCurrentDraw?.setTotalPointCount(),
            mCurrentDraw?.isFinish
        )
    }

    val line = Line().apply {
        this.lineWidth = dp2()
        this.color = context.getColor(R.color.c007fff)
    }

    var eyesShow:((show:Boolean)->Unit)? = null

    var showBottomLayout:((show:Boolean)->Unit)? = null

    var deleteAll:(()->Unit)? = null

    fun getLock():Boolean{
        return mCurrentDraw?.line?.isLock == true
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        initObserver()
    }

    fun getCurrentType(): DrawToolsType?{
        return mCurrentType
    }

    private fun initObserver() {
        DrawShapeUtils.drawShapeLock.value = false
        DrawShapeUtils.drawShapeCanDelete.value = false
    }

    fun deleteAll(){
        mCurrentDraw = null
        drawDataManager?.deleteAll(pairName,businessType) {
            deleteAll?.invoke()
            showBottomLayout?.invoke(false)
            invalidate()
        }
    }

    fun deleteOne(): Boolean {
        if (mCurrentDraw == null) {
            return false
        }
        if(mCurrentDraw?.isDrawingNotStarted() == true) {
            showBottomLayout?.invoke(false)
            return false
        }
        drawDataManager?.deleteOne(mCurrentDraw) {
            showBottomLayout?.invoke(false)
            mCurrentDraw = null
            invalidate()
        }
        return true
    }

    fun getNotFinish(): Boolean {
        return mCurrentDraw?.isFinish == false
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        release()
    }

    fun setKChartView(kChartView: KChartView) {
        this.mKChartView = kChartView
        kChartView.setMkChartRect { left, top, right, bottom, startIndex, startOffset, stopIndex, stopOffset ->
            this.let {
                Log.d("Felix" ,"=left==$left=${right-left}==right=$right==top==$top==bottom===$bottom")
                it.setDrawRect(
                    0f,
                    kChartView.mainRect.top.toFloat(),
                    kChartView.width.toFloat(),
                    kChartView.mainRect.bottom.toFloat()
                )
                it.setValueFormatter(kChartView.valueFormatter)
                it.view = kChartView
                it.setDrawRight(right)
                it.setDrawTop(top)
                it.setDrawBottom(bottom)
                it.setDrawLeft(left)
            }
        }
    }

    fun initConfig(drawShape: DrawToolShape?) {
        DrawShapeUtils.init(drawShape)
    }

    private var drawConfigSave: ((DrawToolShape) -> Unit?)? = null
    fun setDrawConfigSave(action: ((DrawToolShape) -> Unit?)?) {
        this.drawConfigSave = action
    }
}