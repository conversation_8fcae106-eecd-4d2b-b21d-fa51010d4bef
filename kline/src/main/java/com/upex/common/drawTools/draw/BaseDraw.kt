package com.upex.common.drawTools.draw

import android.graphics.*
import android.text.TextPaint
import android.util.Log
import androidx.core.content.ContextCompat
import com.example.myapplication.R
import com.github.tifezh.kchartlib.chart.formatter.ValueFormatter
import com.github.tifezh.kchartlib.chart.impl.IValueFormatter
import com.github.tifezh.kchartlib.utils.DpConstant
import com.github.tifezh.kchartlib.utils.DpConstant.dp5
import com.upex.common.drawTools.*
import com.upex.common.drawTools.db.*
import com.upex.common.utils.TimeUtils.getKlineTradeDate
import com.upex.common.utils.app.ApplicationUtil.Companion.context
import com.upex.common.utils.display.*
import com.upex.common.utils.display.DensityUtil
import com.upex.common.utils.numFormat
import com.upex.common.utils.toLongCatching

abstract class BaseDraw {

    val SELECT_DISTANCE = DisplayUtils.dp2px(12f)
    var digits = 2
    var roundR = DensityUtil.dp2px(4f).toFloat()
    var padding = DensityUtil.dp2px(3f).toFloat()
    var margin = DensityUtil.dp2px(6f).toFloat()

    private var mValueFormatter: IValueFormatter? = null
    var priceBgRect: RectF? = null

    fun setValueFormatter(valueFormatter: IValueFormatter?) {
        this.mValueFormatter = valueFormatter
    }

    fun formatValue(value: Float): String? {
        if (mValueFormatter == null) {
            setValueFormatter(ValueFormatter(digits))
        }
        return mValueFormatter?.format(value)
    }

    var show = true

    var line: Line? = null

    var mPointList = ArrayList<DrawPoint>()

    private val mPaint = Paint()

    private val mDashPathEffectPaint = Paint()
    private var pointFillColor = ContextCompat.getColor(context, R.color.cFF1E1E1E)

    fun setLightTheme(lightTheme: Boolean) {
        pointFillColor = ContextCompat.getColor(context, if (lightTheme) { R.color.cEBFFFFFF } else { R.color.cFF1E1E1E })
    }

    fun getPaint(): Paint {
        line?.apply {
            mPaint.color = color
            mPaint.strokeWidth = lineWidth.toFloat()
            mPaint.style = Paint.Style.STROKE
            mPaint.isAntiAlias = true
            mPaint.pathEffect = DashPathEffect(DrawToolsLineStyle.convertEnum(lineStyle).dash, 0f)
        }
        return mPaint
    }

    fun getDashPathEffectPaint(): Paint {
        line?.apply {
            mDashPathEffectPaint.color = color
            mDashPathEffectPaint.strokeWidth = DrawToolsLineWidth.LineWidth0.width.toFloat()
            mDashPathEffectPaint.style = Paint.Style.STROKE
            mDashPathEffectPaint.isAntiAlias = true
            mDashPathEffectPaint.pathEffect = DashPathEffect(DrawToolsLineStyle.LineStyle2.dash, 0f)
        }
        return mDashPathEffectPaint
    }

    fun getTextPaint(): Paint {
        line?.apply {
            mTextPaint.color = color
            mTextPaint.isAntiAlias = true
            mTextPaint.textSize = DensityUtil.dp2px(12f).toFloat()
        }
        return mTextPaint
    }

    private val mPointPaint = Paint()

    val mTextPaint = TextPaint()

    private val mTimeAndPriceTextPaint = TextPaint()

    fun getTimeAndPriceTextPaint(): TextPaint {
        mTimeAndPriceTextPaint.color = Color.WHITE
        mTimeAndPriceTextPaint.textSize = DensityUtil.dp2px(12f).toFloat()
        mTimeAndPriceTextPaint.isAntiAlias = true
        return mTimeAndPriceTextPaint
    }

    private val mTimeAndPriceTextBgPaint = Paint()

    private fun getTimeAndPriceTextBgPaint(): Paint {
        mTimeAndPriceTextBgPaint.color = Color.GRAY
        mTimeAndPriceTextBgPaint.isAntiAlias = true
        return mTimeAndPriceTextBgPaint
    }

    private val mFillPaint = Paint()

    fun getFillPaint(): Paint {
        line?.apply {
            if (fillColor != Color.TRANSPARENT) {
                mFillPaint.color = fillColor
            } else {
                mFillPaint.color = setTransparency(fillColor, 0)
            }
            mFillPaint.style = Paint.Style.FILL
        }
        return mFillPaint
    }

    private val path = Path()
    private val pathB = Path()
    private var oneDownX: Float = 0f
    private var oneDownY: Float = 0f

    val fillPath = Path()

    private var mCurrentPoint: DrawPoint? = null
    private var mSelectedPoint: DrawPoint? = null

    abstract fun setTotalPointCount(): Int

    open fun isBezier() = false

    open fun isHighlighter() = false

    var isFinish = false

    fun getCurrentPointCount(): Int {
        return mPointList.size
    }

    open fun isInSelectArea(x: Float, y: Float, mRect: RectF): Boolean {
        return false
    }

    open fun onEditPoint(editIndex: Int, x: Float, y: Float) {}

    open fun onUpPoint(index: Int) {}

    open fun isSamePre() = false

    open fun isHasFill() = false

    var isSelect = false

    open fun onSelect(x: Float, y: Float, mRect: RectF): Boolean {
        mPointList.forEach { drawPoint ->
            val select = isInCircle(x, y, drawPoint.x, drawPoint.y, SELECT_DISTANCE.toFloat())
            if (select) {
                return true
            }
            val inSelectArea = isInSelectArea(x, y, mRect)
            if (inSelectArea) {
                return true
            }
        }
        return false
    }

    private var isContains = true

    fun onDown(x: Float, y: Float, mRect: RectF) {
        if (!isFinish) {
            isContains = true
            if (isBezier()) {
                createNewPoint()
                mCurrentPoint?.let { setPosition(x, y, it) }
                mCurrentPoint?.let { mPointList.add(it) }
                return
            }
            createNewPoint()
            mCurrentPoint?.let { setPosition(x, y, it) }
            mCurrentPoint?.let { mPointList.add(it) }
        } else {
            onEditDown(x, y,mRect)
        }
    }

    fun onMove(x: Float, y: Float, distanceX: Float, distanceY: Float): Boolean {
        if (!isContains) {
            return false
        }
        if (!isFinish) {
            if (isBezier()) {
                createNewPoint()
                mCurrentPoint?.let { setPosition(x, y, it) }
                mCurrentPoint?.let { mPointList.add(it) }
                return true
            }
            mCurrentPoint?.let { setPosition(x, y, it) }
            return true
        } else {
            return onEdit(x, y, distanceX, distanceY)
        }
    }

    fun onUp(x: Float, y: Float) {
        if (!isContains) {
            return
        }
        if (!isFinish) {
            if (isBezier()) {
                createNewPoint()
                mCurrentPoint?.let { setPosition(x, y, it) }
                mCurrentPoint?.let { mPointList.add(it) }
                isFinish = true
                return
            }
            mCurrentPoint?.let { setPosition(x, y, it) }
            onUpPoint(mPointList.indexOf(mCurrentPoint))
            Log.v("DrawView", "size:${mPointList.size}, setTotalPointCount:${setTotalPointCount()}, this:$this")
            if (mPointList.size == setTotalPointCount()) {
//                ToastUtil.show(CommonLanguageUtil.getValue(Keys.Markets_Kline_draw_already_complate))
                isFinish = true
            }
        }
    }

    fun isDrawingNotStarted(): Boolean {
        return mPointList.isEmpty()
    }

    fun onEdit(x: Float, y: Float, distanceX: Float, distanceY: Float): Boolean {
        if (isSelect && line?.isLock == false) {
            if (editIndex != -1) {
                setPosition(x, y, mPointList[editIndex])
                onEditPoint(editIndex, x, y)
                return true
            } else if (editAll) {
                mPointList.forEach { drawPoint ->
                    drawPoint.x = drawPoint.x.plus(-distanceX)
                    drawPoint.y = drawPoint.y.plus(-distanceY)
                }
            }
        }
        return false
    }

    private var editIndex = -1
    private var editAll = false

    private fun onEditDown(x: Float, y: Float,mRect: RectF) {
        if (isBezier()) {
            editIndex = -1
            mPointList.forEachIndexed { index, drawPoint ->
                val selectLine = isInCircle(x, y, drawPoint.x, drawPoint.y, SELECT_DISTANCE.toFloat())
                if (selectLine) {
                    editAll = true
                    return
                } else {
                    editAll = false
                }
            }
            return
        }
        mPointList.forEachIndexed { index, drawPoint ->
            val selectPoint = isInCircle(x, y, drawPoint.x, drawPoint.y, SELECT_DISTANCE.toFloat())
            if (selectPoint) {
                editIndex = index
                mSelectedPoint = drawPoint
                return
            }
        }
        editIndex = -1
        mSelectedPoint = null

        val selectLine = isInSelectArea(x, y, mRect)
        if (selectLine) {
            editAll = true
            return
        } else {
            editAll = false
        }
    }


    fun onDraw(canvas: Canvas?, mRect: RectF, clipRect: RectF) {
        canvas?.let {
            // TODO: 再看
            if(isSelect) {//最先画，小圆点后画，防止虚线穿小圆点
                mSelectedPoint?.let { currentP ->
                    drawSelectedTimeAndPriceLine(currentP, it, mRect)
                }
            }
            it.save()
            it.clipRect(clipRect)
            Log.d("onDraw","==${this.javaClass.name}===$isSelect")
            if (isFinish) {
                if (isSamePre()) {
                    drawPreLines(canvas)
                } else if (isBezier()) {
                    drawBezier(canvas)
                } else {
                    onFinalDraw(canvas,mRect)
                }
            } else {
                if (isBezier()) {
                    drawBezier(canvas)
                } else {
                    drawPreLines(canvas)
                }
            }
            if (isSelect) {
                if (isBezier()) {
                    drawBezierPoint(it)
                } else {
                    drawAllPoint(it)
                }
            }
            it.restore()
            if (isSelect) {//最后画价格时间文案，防止线遮挡文案
                drawAllTimeAndPrice(it, mRect)
            }
        }
    }

    open fun onFinalDraw(canvas: Canvas, mRect: RectF) {}

    private fun drawAllPoint(canvas: Canvas) {
        mPointList.forEach { drawPoint(it, canvas) }
    }

    private fun drawAllTimeAndPrice(canvas: Canvas, mRect: RectF) {
        if(isBezier()) {
            drawTimeAndPrice(mPointList.first(), canvas, mRect)
            drawTimeAndPrice(mPointList.last(), canvas, mRect)
        } else {
            mPointList.forEach { drawTimeAndPrice(it, canvas, mRect) }
        }
    }

    private fun drawTimeAndPrice(point: DrawPoint, canvas: Canvas, mRect: RectF) {
        Log.d("drawTimeAndPrice","==point===${this.javaClass.name}")
        val timeStr = getKlineTradeDate((point.timeStr ?: "0").toLongCatching(0), "dd/MM HH:mm")
        val timeRect = getTextRect(timeStr, getTimeAndPriceTextPaint())
        var timeRight = point.x + timeRect.width() / 2f + padding
        if (timeRight > mRect.width()) {
            timeRight = mRect.width()
        }
        if (timeRight < timeRect.width() + 2 * padding) {
            timeRight = timeRect.width() + 2 * padding
        }
        val timeBgRect = RectF(timeRight - timeRect.width() - 2 * padding, mRect.bottom, timeRight, mRect.bottom + timeRect.height() + 2 * padding)
        canvas.drawRoundRect(timeBgRect, roundR, roundR, getTimeAndPriceTextBgPaint())
//        val timeStrRect = RectF(timeBgRect.left + padding, mRect.bottom + padding, timeBgRect.right - padding, mRect.bottom + timeRect.height())
        canvas.drawText(timeStr, timeBgRect.left + padding, timeBgRect.bottom - padding - DpConstant.dp1(), getTimeAndPriceTextPaint())

        val price = point.price.numFormat(digits, true)
        val priceRect = getTextRect(price, getTimeAndPriceTextPaint())
        var priceTop = point.y - priceRect.height() / 2f - padding
        if (priceTop < mRect.top) {
            priceTop = mRect.top
        }
        if (priceTop > mRect.bottom - priceRect.height() - 2 * padding) {
            priceTop = mRect.bottom - priceRect.height() - 2 * padding
        }
        priceBgRect = RectF(mRect.right - priceRect.width() - 2 * padding, priceTop, mRect.right, priceTop + priceRect.height() + 2 * padding)
        priceBgRect?.let {
            canvas.drawRoundRect(it, roundR, roundR, getTimeAndPriceTextBgPaint())
            canvas.drawText(price, it.left + padding, it.bottom - padding, getTimeAndPriceTextPaint())
        }
    }

    private fun drawSelectedTimeAndPriceLine(point: DrawPoint, canvas: Canvas, mRect: RectF) {
        val pathH = Path()
        pathH.moveTo(mRect.left, point.y)
        pathH.lineTo(priceBgRect?.left?:mRect.right, point.y)
        canvas.drawPath(pathH, getDashPathEffectPaint())

        val pathV = Path()
        pathV.moveTo(point.x, mRect.top)
        pathV.lineTo(point.x, mRect.bottom)
        canvas.drawPath(pathV, getDashPathEffectPaint())

    }

    private fun drawBezierPoint(canvas: Canvas) {
        if (mPointList.size > 1) {
            drawPoint(mPointList[0], canvas)
            drawPoint(mPointList[mPointList.size - 1], canvas)
        }
    }


    private fun drawBezier(canvas: Canvas) {
        canvas.save()
        if (mPointList.size > 0) {
            pathB.reset()
            if (isHighlighter()){
                mPaint.setStrokeCap(Paint.Cap.ROUND)
            }
            mPointList.forEachIndexed { index, drawPoint ->

                if (index == 0) {
                    oneDownX = drawPoint.x
                    oneDownY = drawPoint.y
                    pathB.moveTo(drawPoint.x, drawPoint.y)
                } else {
                    val endX = (oneDownX + drawPoint.x) / 2f
                    val endY = (oneDownY + drawPoint.y) / 2f
                    pathB.quadTo(oneDownX, oneDownY, endX, endY)
                    oneDownX = drawPoint.x
                    oneDownY = drawPoint.y
                }
            }
//           if (isHighlighter()){
//               drawHighlighterPoint(canvas)
//           }
        }
        canvas.drawPath(pathB, getPaint())

    }

    private fun drawHighlighterPoint(canvas: Canvas) {
        canvas.save()
        mPointPaint
        if (mPointList.size > 0) {
            val point = mPointList[0]

            line?.apply {
                val rect = RectF(point.x - lineWidth/2f,point.y-lineWidth/2f,point.x+lineWidth/2f,point.y+lineWidth)
                mPointPaint.color = color
                canvas.drawArc(rect,0f,180f,true, mPointPaint)
            }
        }
        if (mPointList.size > 1) {
            val point = mPointList[mPointList.size-1]
            line?.apply {
                mPointPaint.color = color
                canvas.drawCircle(point.x, point.y, lineWidth/2f, mPointPaint)
            }
        }

    }

    private fun drawPreLines(canvas: Canvas) {
        if (mPointList.size > 0) {
            path.reset()
            mPointList.forEachIndexed { index, drawPoint ->
                if (index == 0) {
                    path.moveTo(drawPoint.x, drawPoint.y)
                } else {
                    path.lineTo(drawPoint.x, drawPoint.y)
                }
            }
            mCurrentPoint?.let { path.lineTo(it.x, it.y) }
            canvas.drawPath(path, getPaint())
        }
    }

    private fun drawPoint(point: DrawPoint, canvas: Canvas) {
        line?.apply {
            mPointPaint.style = Paint.Style.FILL
            mPointPaint.color = pointFillColor
            val dp5 = dp5().toFloat()
            canvas.drawCircle(point.x, point.y, dp5, mPointPaint)
            mPointPaint.style = Paint.Style.STROKE
            mPointPaint.color = color
            mPointPaint.strokeWidth = DensityUtil.dp2px(1.5f).toFloat()
            canvas.drawCircle(point.x, point.y, dp5, mPointPaint)
        }
    }

    private fun setPosition(x: Float, y: Float, point: DrawPoint) {
        point.x = x
        point.y = y
    }

    init {
        line?.apply {
            mPaint.color = color
            mPaint.isAntiAlias = true
            mPaint.strokeWidth = lineWidth.toFloat()
            mPaint.style = Paint.Style.STROKE

            mPointPaint.color = color
            mPointPaint.isAntiAlias = true
            mPointPaint.strokeWidth = lineWidth.toFloat()
            mPointPaint.style = Paint.Style.FILL

            mFillPaint.color = fillColor
            mFillPaint.isAntiAlias = true
            mFillPaint.style = Paint.Style.FILL

        }
    }

    fun setColor(color: Int) {
        line?.color = color
    }

    fun setLineWidth(width: Int) {
        line?.lineWidth = width
    }

    fun setLineStyle(index: Int) {
//        mPaint.pathEffect = DashPathEffect(dashLengths[2], 0f)
    }


    private fun createNewPoint() {
        if (mPointList.size < setTotalPointCount()) {
            val drawPoint = DrawPoint()
            mCurrentPoint = drawPoint
            mSelectedPoint = drawPoint
        }
    }

    fun getTextRect(text: String, textPaint: Paint): Rect {
        val rect = Rect()
        textPaint.getTextBounds(text, 0, text.length, rect)
        return rect
    }
}