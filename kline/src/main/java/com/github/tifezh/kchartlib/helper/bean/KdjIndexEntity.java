package com.github.tifezh.kchartlib.helper.bean;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

@Keep
public class KdjIndexEntity extends BaseIndexEntity {
    // 主图 boll
    float k;
    float d;
    float j;

    @Override
    public void reset() {
        super.reset();
        k = 0f;
        d = 0f;
        j = 0f;
    }

    public void setKdj(float k, float d, float j) {
        this.k = k;
        this.d = d;
        this.j = j;
        setComputed(true);
    }

    public float getK() {
        return k;
    }

    public float getD() {
        return d;
    }

    public float getJ() {
        return j;
    }

    @NonNull
    @Override
    public String toString() {
        return "KDJIndexEntity{" +
                "k=" + k +
                ", d=" + d +
                ", j=" + j +
                '}';
    }
}