package com.github.tifezh.kchartlib.helper.bean

enum class KlineOtherEnum(private val showName: String, private val value: String) : KlineEnum {
    NONE("", ""),
    VOL("VOL", "vol"),
    MACD("MACD", "macd"),
    KDJ("KDJ", "kdj"),
    RSI("RSI", "rsi"),
    CCI("CCI", "cci"),
    KD("KD", "kd"),
    WR("WR", "wr"),
    DMI("DMI", "dmi"),
    ROC("ROC", "roc");

    override fun getShowName(): String {
        return showName
    }

    override fun getValue(): String {
        return value
    }
}

class KlineOtherEnumUtils {
    companion object {
        @JvmStatic
        fun value2KlineOtherEnum(value: String?): KlineOtherEnum {
            val lowerValue = value?.lowercase() ?: return KlineOtherEnum.NONE
            return when (lowerValue) {
                KlineOtherEnum.VOL.getValue() -> KlineOtherEnum.VOL
                KlineOtherEnum.MACD.getValue() -> KlineOtherEnum.MACD
                KlineOtherEnum.KDJ.getValue() -> KlineOtherEnum.KDJ
                KlineOtherEnum.RSI.getValue() -> KlineOtherEnum.RSI
                KlineOtherEnum.CCI.getValue() -> KlineOtherEnum.CCI
                KlineOtherEnum.KD.getValue() -> KlineOtherEnum.KD
                KlineOtherEnum.WR.getValue() -> KlineOtherEnum.WR
                KlineOtherEnum.DMI.getValue() -> KlineOtherEnum.DMI
                KlineOtherEnum.ROC.getValue() -> KlineOtherEnum.ROC
                else -> KlineOtherEnum.NONE
            }
        }
    }
}