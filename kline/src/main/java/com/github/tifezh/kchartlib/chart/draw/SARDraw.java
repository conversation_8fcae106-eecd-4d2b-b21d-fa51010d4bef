package com.github.tifezh.kchartlib.chart.draw;

import android.content.Context;
import android.graphics.*;

import androidx.annotation.*;
import androidx.core.content.ContextCompat;

import com.example.myapplication.R;
import com.github.tifezh.kchartlib.chart.EntityImpl.SARImpl;
import com.github.tifezh.kchartlib.chart.impl.*;

/**
 * author：lvy
 * date：2025/02/28
 * desc：主图 SAR 指标
 */
public class SARDraw extends SimpleChartDraw<SARImpl> {

    private final Paint mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private final Context mContext;
    private final Rect sarRect = new Rect();

    public SARDraw(Context context) {
        mContext = context;
        float textSize = context.getResources().getDimension(R.dimen.chart_zhibiao_text_size);
        float strokeWidth = context.getResources().getDimension(R.dimen.chart_line_width);
        int color = ContextCompat.getColor(context, R.color.kchartlib_ma1);

        mPaint.setColor(color);
        mPaint.setStrokeWidth(strokeWidth);
//        mSarPaint.setStyle(Paint.Style.FILL);
        mPaint.setStrokeCap(Paint.Cap.ROUND);
        mPaint.setTextSize(textSize);
    }

    @Override
    public void setTypeFace(Typeface typeFace) {
        if (typeFace == null) return;
        mPaint.setTypeface(typeFace);
    }

    @Override
    public void drawTranslated(@Nullable SARImpl lastPoint, @NonNull SARImpl curPoint, float lastX, float curX, @NonNull Canvas canvas, @NonNull IKChartView view, int position) {

    }

    @Override
    public void onDrawScreenLine(@Nullable SARImpl lastPoint, @NonNull SARImpl curPoint, float lastX, float curX, @NonNull Canvas canvas, @NonNull IKChartView view, int position) {
        if (lastPoint == null || curPoint == null) {
            return;
        }

        // 获取 SAR 值的坐标
//        float lastY = view.getMainY(lastPoint.getSar());
        float curY = view.getMainY(curPoint.getSar());

        // 绘制 SAR 点
        mPaint.setStyle(Paint.Style.STROKE);
        canvas.drawRect(curX - 5, curY - 5, curX + 5, curY + 5, mPaint);
//        canvas.drawCircle(curX, curY, 5, mSarPaint); // 5 是点的半径，可以根据需要调整

//        // 如果需要连接相邻的 SAR 点，可以绘制线段
//        canvas.drawLine(lastX, lastY, curX, curY, mSarPaint);
    }

    @Override
    public void drawText(@NonNull Canvas canvas, @NonNull IKChartView view, int position, float x, float y) {
        x += MainDraw.mTextMargin;
        SARImpl point = (SARImpl) view.getItem(position);

        String text = "SAR:" + view.formatValue(point.getSar());
        mPaint.getTextBounds(text, 0, text.length(), sarRect);
        mPaint.setStyle(Paint.Style.FILL);
        canvas.drawText(text, x, y, mPaint);
    }

    @Override
    public float getMaxValue(SARImpl point, int positon) {
        return Math.max(Float.MIN_VALUE, point.getSar());
    }

    @Override
    public float getMinValue(SARImpl point, int positon) {
        return Math.min(Float.MAX_VALUE, point.getSar());
    }

    @Override
    public int getTextH() {
        return sarRect.height();
    }


    public void setColor(@ColorInt int color) {
        mPaint.setColor(color);
    }

    public void setTextSize(float textSize) {
        mPaint.setTextSize(textSize);
    }

    public void setStrokeWidth(int strokeWidth) {
        mPaint.setStrokeWidth(strokeWidth);
    }
}
