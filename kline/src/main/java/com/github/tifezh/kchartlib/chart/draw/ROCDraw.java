package com.github.tifezh.kchartlib.chart.draw;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.util.LruCache;
import android.util.Pair;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.example.myapplication.R;
import com.github.tifezh.kchartlib.chart.EntityImpl.KLineImpl;
import com.github.tifezh.kchartlib.chart.EntityImpl.ROCImpl;
import com.github.tifezh.kchartlib.chart.impl.IKChartView;
import com.github.tifezh.kchartlib.chart.impl.IValueFormatter;
import com.github.tifezh.kchartlib.chart.impl.SimpleChartDraw;
import com.github.tifezh.kchartlib.utils.ViewUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * BOLL实现类
 * Created by tifezh on 2016/6/19.
 */

public class ROCDraw extends SimpleChartDraw<ROCImpl> {

    private final Paint mROCPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private final Paint mROCMAPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    private final Context mContext;
    private int rocNValue = 12;
    private int rocMValue = 6;

    private final List<Paint> paintList = new ArrayList<>();

    public ROCDraw(Context context) {
        mContext = context;
        float lineWidth = context.getResources().getDimension(R.dimen.chart_line_width);
        float textSize = context.getResources().getDimension(R.dimen.chart_zhibiao_text_size);

        mROCPaint.setColor(ContextCompat.getColor(context, R.color.kchartlib_ma1));
        mROCPaint.setStrokeWidth(lineWidth);
        mROCPaint.setStrokeCap(Paint.Cap.ROUND);
        mROCPaint.setTextSize(textSize);

        mROCMAPaint.setColor(ContextCompat.getColor(context, R.color.kchartlib_ma2));
        mROCMAPaint.setStrokeWidth(lineWidth);
        mROCMAPaint.setStrokeCap(Paint.Cap.ROUND);
        mROCMAPaint.setTextSize(textSize);

        paintList.add(mROCPaint);
        paintList.add(mROCMAPaint);
    }

    @Override
    public void drawTranslated(@Nullable ROCImpl lastPoint, @NonNull ROCImpl curPoint, float lastX, float curX, @NonNull Canvas canvas, @NonNull IKChartView view, int position) {

    }

    private float[] mROCLines;
    private float[] mROCMALines;

    @Override
    public void drawOnScreen(Canvas canvas, @NonNull IKChartView view) {
//        super.drawOnScreen(canvas, view);
        List<Pair<Float, KLineImpl>> mPointList = view.getPointList();
        if (mPointList == null || mPointList.isEmpty()) {
            return;
        }
        int pointSize = mPointList.size();
        int m5Size = 0;
        int m10Size = 0;
        boolean isROCDraw = false;
        boolean isROCMADraw = false;
        int idx5 = 0;
        int idx10 = 0;
        for (int i = 1; i < pointSize; i++) {
            int position = mStartIndex + i - 1;//因为前面多加了一个 last
            if (position < rocNValue && position < rocNValue + rocMValue) {
                continue;
            }
            Pair<Float, KLineImpl> lastPointPair = mPointList.get(i - 1);
            Pair<Float, KLineImpl> cuxPointPair = mPointList.get(i);
            Float lastX = lastPointPair.first;
            ROCImpl lastPoint = lastPointPair.second;
            Float curX = cuxPointPair.first;
            ROCImpl curPoint = cuxPointPair.second;

            if (lastPoint == null || curPoint == null) {
                continue;
            }
            if (position >= rocNValue) {
                if (m5Size == 0) {
                    m5Size = (pointSize - i) * 4;
                }
                if (mROCLines == null || mROCLines.length != m5Size) {
                    mROCLines = new float[m5Size];
                }
                mROCLines[idx5++] = lastX;
                mROCLines[idx5++] = getChildY(lastPoint.getRoc());
                mROCLines[idx5++] = curX;
                mROCLines[idx5++] = getChildY(curPoint.getRoc());
                isROCDraw = true;
            }
            if (position >= rocNValue + rocMValue) {
                if (m10Size == 0) {
                    m10Size = (pointSize - i) * 4;
                }
                if (mROCMALines == null || mROCMALines.length != m10Size) {
                    mROCMALines = new float[m10Size];
                }
                mROCMALines[idx10++] = lastX;
                mROCMALines[idx10++] = getChildY(lastPoint.getRocMA());
                mROCMALines[idx10++] = curX;
                mROCMALines[idx10++] = getChildY(curPoint.getRocMA());
                isROCMADraw = true;
            }
        }
        if (isROCDraw) {
            if (mROCLines.length > idx5) {
                canvas.drawLines(mROCLines, 0, idx5, mROCPaint);
            } else {
                canvas.drawLines(mROCLines, mROCPaint);
            }
        }
        if (isROCMADraw) {
            if (mROCMALines.length > idx10) {
                canvas.drawLines(mROCMALines, 0, idx10, mROCMAPaint);
            } else {
                canvas.drawLines(mROCMALines, mROCMAPaint);
            }
        }
    }

    @Override
    public void onDrawScreenLine(@Nullable ROCImpl lastPoint, @NonNull ROCImpl curPoint, float lastX, float curX, @NonNull Canvas canvas, @NonNull IKChartView view, int position) {
        if (position >= rocNValue ) {
            if (lastPoint != null) {
                drawChildLine(canvas, mROCPaint, lastX, lastPoint.getRoc(), curX, curPoint.getRoc());
            }
        }
        if (position >= rocNValue + rocMValue) {
            if (lastPoint != null) {
                drawChildLine(canvas, mROCMAPaint, lastX, lastPoint.getRocMA(), curX, curPoint.getRocMA());
            }
        }
    }
    private final LruCache<String, Float> rocTextCache = new LruCache<>(16);

    private Float getROCTextWidth(String text){
        if(TextUtils.isEmpty(text)){
            return 0f;
        }
        Float width = rocTextCache.get(text);
        if(width==null || width<=0f){
            width =  mROCPaint.measureText(text);
            rocTextCache.put(text,width);
        }
        return width;
    }

    @Override
    public void drawText(@NonNull Canvas canvas, @NonNull IKChartView view, int position, float x, float y) {
        x += ViewUtil.Dp2Px(mContext, 10);

        String text = String.format("ROC(%d,%d)", rocNValue, rocMValue);
        canvas.drawText(text, x, y, mROCPaint);
        x += getROCTextWidth(text) + MainDraw.mTextMargin;
        ROCImpl point = (ROCImpl) view.getItem(position);
        if (position >= rocNValue - 1) {
            text = "ROC:" + view.formatValue(point.getRoc());
            canvas.drawText(text, x, y, mROCPaint);
        }
        if (position >= rocNValue + rocMValue - 1) {
            x += getROCTextWidth(text)+ MainDraw.mTextMargin;
            text = "ROCMA:" + view.formatValue(point.getRocMA());
            canvas.drawText(text, x, y, mROCMAPaint);
        }
    }

    @Override
    public void setValueFormatter(IValueFormatter valueFormatter) {
    }

    @Override
    public IValueFormatter getValueFormatter() {
        return null;
    }

    @Override
    public float getMaxValue(ROCImpl point, int position) {
        if (position < rocNValue) {
            return 0;
        }
        return Math.max(point.getRoc(), point.getRocMA());
    }

    @Override
    public float getMinValue(ROCImpl point, int position) {
        if (position < rocNValue) {
            return 0;
        }

        return Math.min(point.getRoc(), point.getRocMA());
    }

    /**
     * 设置roc颜色
     */
    public void setROCColor(int color) {
        mROCPaint.setColor(color);
    }

    /**
     * 设置rocma颜色
     */
    public void setROCMAColor(int color) {
        mROCMAPaint.setColor(color);
    }

    public void setLineWidth(int lineWidth) {
        mROCPaint.setStrokeWidth(lineWidth);
        mROCMAPaint.setStrokeWidth(lineWidth);
    }

    public void setRocNValue(int rocNValue) {
        this.rocNValue = rocNValue;
    }

    public void setRocMValue(int rocMValue) {
        this.rocMValue = rocMValue;
    }

    @Override
    public void setTypeFace(Typeface typeFace) {
        if (typeFace==null)return;
        for (Paint paint:paintList){
            paint.setTypeface(typeFace);
        }
    }
}
