package com.github.tifezh.kchartlib.chart;

import android.database.DataSetObservable;
import android.database.DataSetObserver;
import android.util.Log;

import com.github.tifezh.kchartlib.chart.impl.IAdapter;

import java.lang.reflect.Field;
import java.util.List;

/**
 * k线图的数据适配器
 * Created by tifezh on 2016/6/9.
 */

public abstract class BaseKChartAdapter implements IAdapter {

    private final DataSetObservable mDataSetObservable = new DataSetObservable();

    public void notifyDataSetChanged() {
//        Log.i("wj", "notifyDataSetChanged: getCount()="+getCount()+", mDataSetObservable="+getObserverSize(mDataSetObservable));
        if (getCount() > 0) {
            mDataSetObservable.notifyChanged();
        } else {
            mDataSetObservable.notifyInvalidated();
        }
    }

    private int getObserverSize(DataSetObservable observer) {
        if (observer != null) {
            try {
                // 从父类 Observable 中获取 mObservers 字段
                Class<?> superClass = observer.getClass().getSuperclass();
                Field observersField = superClass.getDeclaredField("mObservers");
                observersField.setAccessible(true);

                Object observers = observersField.get(observer);
                if (observers instanceof List) {
                    return ((List<?>) observers).size();
                } else {
                    Log.e("wj", "mObservers is not a List: " + observers.getClass().getName());
                    return -1;
                }
            } catch (Throwable e) {
                e.printStackTrace();
                Log.e("wj", "Failed to get observer size", e);
                return -2;
            }
        }
        return -1;
    }

    public void notifyInvalidated() {
        mDataSetObservable.notifyInvalidated();
    }

    public void notifyChanged() {
        mDataSetObservable.notifyChanged();
    }


    @Override
    public void registerDataSetObserver(DataSetObserver observer) {
        mDataSetObservable.registerObserver(observer);
    }

    @Override
    public void unregisterDataSetObserver(DataSetObserver observer) {
        if (observer != null) {
            mDataSetObservable.unregisterObserver(observer);
        }
    }
}
