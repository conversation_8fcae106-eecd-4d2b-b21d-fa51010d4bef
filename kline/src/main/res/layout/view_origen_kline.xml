<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:weightSum="2">


    <FrameLayout
        android:id="@+id/flayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1.3">

<!--        <com.guoziwei.klinelib.chart.CustomCombinedChart-->
<!--            android:id="@+id/price_chart"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="match_parent" />-->


<!--        <com.guoziwei.klinelib.chart.KLineChartInfoView-->
<!--            android:id="@+id/k_info"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:visibility="gone" />-->


        <RelativeLayout
            android:id="@+id/ma30_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="20dp"
            android:layout_marginTop="5dp">


            <TextView
                android:id="@+id/value_5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="20px"
                android:textColor="#FFE69D"
                android:textSize="11sp" />

            <TextView
                android:id="@+id/value_20"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10px"
                android:layout_toRightOf="@+id/value_5"
                android:textColor="#A76DFF"
                android:textSize="11sp" />

            <TextView
                android:id="@+id/value_30"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10px"
                android:layout_toRightOf="@+id/value_20"
                android:textColor="#03C389"
                android:textSize="11sp" />


        </RelativeLayout>

    </FrameLayout>

<!--    <com.guoziwei.klinelib.chart.CustomCombinedChart-->
<!--        android:id="@+id/vol_chart"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="0dp"-->
<!--        android:layout_weight="0.35" />-->

    <RelativeLayout
        android:id="@+id/rlLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="0.35">


<!--        <com.guoziwei.klinelib.chart.CustomCombinedChart-->
<!--            android:id="@+id/macd_chart"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="match_parent" />-->

<!--        <com.guoziwei.klinelib.chart.CustomCombinedChart-->
<!--            android:id="@+id/kdj_chart"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="match_parent" />-->

<!--        <com.guoziwei.klinelib.chart.CustomCombinedChart-->
<!--            android:id="@+id/rsi_chart"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="match_parent"-->

<!--            />-->
    </RelativeLayout>




</LinearLayout>
