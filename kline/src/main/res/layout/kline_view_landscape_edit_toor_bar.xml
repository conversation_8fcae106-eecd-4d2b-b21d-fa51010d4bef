<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clToolBar"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingHorizontal="10dp"
    android:paddingVertical="10dp"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <ImageView
        android:id="@+id/ivHandle"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/ic_full_screen"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivPaintColor"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="10dp"
        android:src="@drawable/ic_full_screen"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivHandle"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivLineThickness"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="10dp"
        android:src="@drawable/ic_full_screen"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivPaintColor"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivLineDashed"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="10dp"
        android:src="@drawable/ic_full_screen"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivLineThickness"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivLock"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="10dp"
        android:src="@drawable/ic_full_screen"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivLineDashed"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivDelete"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="10dp"
        android:src="@drawable/ic_full_screen"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivLock"
        app:layout_constraintTop_toTopOf="parent" />

</merge>