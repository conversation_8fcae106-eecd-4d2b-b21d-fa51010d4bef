<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:padding="3dp"
    android:id="@+id/item"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingDefaultResource">

<!--    <com.upex.common.widget.view.BaseTextView-->
<!--        app:isFontText="true"-->
<!--        app:normalColor="@color/color_4A4A4A"-->
<!--        app:corner="2dp"-->
<!--        android:id="@+id/color_text"-->
<!--        android:gravity="center"-->
<!--        android:textSize="15sp"-->
<!--        android:textColor="?attr/colorTitle"-->
<!--        android:layout_width="18dp"-->
<!--        android:layout_height="18dp" />-->

<!--    <ImageView-->
<!--        android:src="?attr/drawable_draw_tools_not_fill"-->
<!--        android:visibility="gone"-->
<!--        android:id="@+id/image"-->
<!--        android:scaleType="fitXY"-->
<!--        android:layout_width="18dp"-->
<!--        android:layout_height="18dp"/>-->
</LinearLayout>