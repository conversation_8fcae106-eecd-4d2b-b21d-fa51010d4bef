<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/favourite" />

    <TextView
        android:id="@+id/tvCount"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="@dimen/margin_top_title"
        android:text="@string/favourite_strategies"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        tools:text="Favourite Strategies (4)" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/mRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="6dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/mRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:overScrollMode="never"
            app:layoutManager="cn.com.vau.common.view.WrapContentLinearLayoutManager"
            tools:listitem="@layout/item_st_follow_list" />
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>
</LinearLayout>