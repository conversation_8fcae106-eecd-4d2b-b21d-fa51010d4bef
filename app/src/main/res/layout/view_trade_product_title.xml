<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="32dp"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivTradeProduct"
        android:layout_width="13.33dp"
        android:layout_height="14.17dp"
        android:contentDescription="@string/app_name"
        android:src="@drawable/draw_bitmap2_trade_product_ca61e1e1e_c99ffffff"
        app:layout_constraintBottom_toBottomOf="@+id/tvGoProduct"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvGoProduct"
        tools:ignore="RtlSymmetry" />

    <TextView
        android:id="@+id/tvGoProduct"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical|start"
        android:paddingStart="7.3dp"
        android:paddingEnd="4dp"
        android:text="--"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivTradeProduct"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpUsage,HardcodedText"
        tools:text="USDCAD" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivKline"
        android:layout_width="15.8dp"
        android:layout_height="18.3dp"
        android:src="@drawable/draw_bitmap2_product_chart_ca61e1e1e_c99ffffff"
        app:layout_constraintBottom_toBottomOf="@id/tvGoProduct"
        app:layout_constraintStart_toEndOf="@+id/tvGoProduct"
        app:layout_constraintTop_toTopOf="@id/tvGoProduct" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clAccountInfo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1"
        app:layout_constraintStart_toEndOf="@+id/ivKline"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvCurrentInfoTitle"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:text="@string/equity"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@+id/ivTriangleDown"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage"
            tools:style="@style/medium_font"
            tools:text="1000000" />

        <ImageView
            android:id="@+id/ivTriangleDown"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:contentDescription="@string/app_name"
            android:src="@drawable/icon2_expand_up"
            app:layout_constraintBottom_toBottomOf="@+id/tvCurrentInfoTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tvCurrentInfoTitle"
            app:layout_constraintTop_toTopOf="@+id/tvCurrentInfoTitle" />

        <ImageView
            android:id="@+id/ivRisk"
            android:layout_width="14dp"
            android:layout_height="12dp"
            android:layout_marginEnd="4dp"
            android:contentDescription="@string/app_name"
            android:src="@drawable/img_low_risk"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/tvCurrentInfo"
            app:layout_constraintEnd_toStartOf="@+id/tvCurrentInfo"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvCurrentInfo"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvCurrentInfo"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:text="..."
            android:textAlignment="viewEnd"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@+id/ivTriangleDown"
            app:layout_constraintStart_toEndOf="@+id/ivRisk"
            app:layout_constraintTop_toBottomOf="@+id/tvCurrentInfoTitle"
            tools:ignore="SpUsage,HardcodedText"
            tools:text="10001000100010" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</merge>