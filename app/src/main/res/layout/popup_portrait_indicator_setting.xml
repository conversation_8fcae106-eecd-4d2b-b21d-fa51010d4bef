<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/draw_shape_cffffff_c1a1d20_top_r20"
    android:paddingBottom="20dp"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clMainView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginEnd="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvIndicators"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="@string/indicators"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="18dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/tvMainTitle"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_horizontal_base"
            android:text="@string/main_indicators"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvIndicators"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/mainRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="3"
            tools:itemCount="6"
            tools:listitem="@layout/item_popup_setting_indicator"
            app:layout_constraintTop_toBottomOf="@id/tvMainTitle"/>

        <TextView
            android:id="@+id/tvSubTitle"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_vertical_base"
            android:text="@string/sub_indicators"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/mainRecyclerView"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/subRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="3"
            tools:itemCount="6"
            tools:listitem="@layout/item_popup_setting_indicator"
            app:layout_constraintTop_toBottomOf="@id/tvSubTitle"/>


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>