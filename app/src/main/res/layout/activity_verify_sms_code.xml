<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_endIcon="?attr/icon1Cs"
        app:hb_titleText="@string/verification"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvLoginType"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_top_title"
        android:text="@string/the_verification_code_has_been_sent_to"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar" />

    <TextView
        android:id="@+id/tvAccount"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="@dimen/margin_top_title"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvLoginType"
        tools:text="+95 ********" />

    <TextView
        android:id="@+id/tvChange"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="8dp"
        android:text="@string/kyc_change"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/tvAccount"
        app:layout_constraintStart_toEndOf="@+id/tvAccount"
        app:layout_constraintTop_toTopOf="@+id/tvAccount" />

    <cn.com.vau.common.view.PasswordView
        android:id="@+id/passwordView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvAccount" />

    <TextView
        android:id="@+id/tvReSend"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:text="@string/resend"
        android:textColor="@color/ce35728"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/passwordView" />

    <TextView
        android:id="@+id/tvNotReceiveCodeTips"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="6dp"
        android:drawablePadding="4dp"
        android:lineSpacingMultiplier="1.2"
        android:paddingVertical="6dp"
        android:text="@string/did_not_receive_the_otp"
        android:textColor="?color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvReSend" />

    <TextView
        android:id="@+id/tvOr"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:text="@string/or"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvNotReceiveCodeTips" />

    <TextView
        android:id="@+id/tvSendEms"
        style="@style/login_tvNext_style"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:text="@string/send_otp_via_sms"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvOr" />

    <LinearLayout
        android:id="@+id/llWhatsApp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="16dp"
        android:background="@drawable/shape_c3325d366_r100"
        android:gravity="center_horizontal"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvSendEms"
        app:layout_goneMarginTop="@dimen/margin_vertical_button"
        tools:visibility="visible">

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/height_button_main"
            android:drawablePadding="8dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/margin_horizontal_base"
            android:text="@string/send_otp_via_whatsapp"
            android:textColor="@color/cffffff"
            android:textSize="16dp"
            app:drawableEndCompat="@drawable/img_whatsapp"
            app:layout_constraintEnd_toStartOf="@+id/ivWhatsApp"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="@+id/llWhatsApp"
            app:layout_constraintTop_toTopOf="@id/llWhatsApp" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>