<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/price_alerts"
        app:layout_constraintTop_toTopOf="parent" />

    <cn.com.vau.common.view.tablayout.DslTabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="33dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mHeaderBar" />

    <View
        android:id="@+id/viewTopLine"
        style="@style/TabLayoutBottomLineStyle"
        app:layout_constraintBottom_toBottomOf="@+id/tabLayout"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/tvSelectAll"
        app:layout_constraintTop_toBottomOf="@id/viewTopLine" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSelectAll"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:drawableStart="@drawable/draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14"
        android:drawablePadding="6dp"
        android:paddingHorizontal="@dimen/margin_horizontal_base"
        android:paddingVertical="6dp"
        android:text="@string/select_all"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDelete"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:gravity="center"
        android:paddingHorizontal="@dimen/margin_horizontal_base"
        android:text="@string/delete"
        android:textColor="@color/ce35728"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/tvSelectAll"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvSelectAll" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupBottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvSelectAll,tvDelete"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupTab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tabLayout,viewPager,viewTopLine" />

    <ViewStub
        android:id="@+id/mVsNoData"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout="@layout/vs_layout_no_data"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar" />
</androidx.constraintlayout.widget.ConstraintLayout>