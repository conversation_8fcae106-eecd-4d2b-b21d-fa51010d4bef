<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".trade.activity.CloseByOrderActivity">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_endIcon="?attr/icon1Info"
        app:hb_titleText="@string/close_by"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar" />

    <include
        android:id="@+id/includeOrder"
        layout="@layout/item_recycler_open_trades_other"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar" />

    <TextView
        android:id="@+id/tvIntro"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/color_c0a1e1e1e_c0affffff"
        android:padding="@dimen/margin_horizontal_base"
        android:text="@string/please_select_the_close_by"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        android:gravity="start"
        android:textAlignment="viewStart"
        app:layout_constraintTop_toBottomOf="@+id/includeOrder"
        tools:ignore="SpUsage"
        style="@style/gilroy_500" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/mSmartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="10dp"
        app:layout_constraintBottom_toTopOf="@+id/tvEstimatedTotalPnlTitle"
        app:layout_constraintTop_toBottomOf="@+id/tvIntro">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/mRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layoutManager="cn.com.vau.common.view.WrapContentLinearLayoutManager"/>

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <TextView
        android:id="@+id/tvEstimatedTotalPnlTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="@string/estimated_total_pnl"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toTopOf="@+id/tvNext"
        app:layout_constraintEnd_toStartOf="@+id/tvEstimatedTotalPnl"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        style="@style/gilroy_500" />

    <TextView
        android:id="@+id/tvEstimatedTotalPnl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/cf44040"
        android:textDirection="ltr"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvEstimatedTotalPnlTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvEstimatedTotalPnlTitle"
        app:layout_constraintTop_toTopOf="@+id/tvEstimatedTotalPnlTitle"
        style="@style/gilroy_500"
        tools:text=" -0.23" />

    <TextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:text="@string/close__position"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:ignore="SpUsage" />

</androidx.constraintlayout.widget.ConstraintLayout>