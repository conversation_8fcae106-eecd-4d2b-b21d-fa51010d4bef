<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="12dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivSelect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/ivIcon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ivIcon" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivIcon"
        android:layout_width="11dp"
        android:layout_height="8dp"
        android:layout_marginStart="12dp"
        android:src="@drawable/img_price_up"
        app:layout_constraintBottom_toBottomOf="@id/tvAlterName"
        app:layout_constraintStart_toEndOf="@+id/ivSelect"
        app:layout_constraintTop_toTopOf="@id/tvAlterName"
        app:layout_goneMarginStart="0dp" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAlterName"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toStartOf="@+id/ivEnable"
        app:layout_constraintStart_toEndOf="@id/ivIcon"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Sell price falls to 152.768" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFrequency"
        :style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="@id/tvAlterName"
        app:layout_constraintTop_toBottomOf="@id/tvAlterName"
        tools:text="Once only" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTrigged"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginStart="8dp"
        android:background="@drawable/shape_c1fe35728_r100"
        android:gravity="center"
        android:paddingHorizontal="4dp"
        android:text="@string/trigged"
        android:textColor="@color/ce35728"
        android:textSize="6dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tvFrequency"
        app:layout_constraintStart_toEndOf="@+id/tvFrequency"
        app:layout_constraintTop_toTopOf="@+id/tvFrequency" />

    <cn.com.vau.util.widget.SwitchButton
        android:id="@+id/mSwitchButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>