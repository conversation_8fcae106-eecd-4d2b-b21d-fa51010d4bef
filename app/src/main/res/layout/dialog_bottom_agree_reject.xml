<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:background="@drawable/draw_shape_cffffff_c1a1d20_r20"
    android:paddingHorizontal="12dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDetail"
        style="@style/DialogBottomContentStyle"
        android:layout_width="match_parent"
        android:textAlignment="viewStart"
        android:gravity="start"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/selecting_auto_reject_are_auto_reject"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSubTitle"
        style="@style/DialogBottomSubTitleStyle"
        android:layout_marginTop="12dp"
        app:layout_goneMarginTop="0dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvDetail"
        tools:text="Select Account"
        tools:visibility="gone" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSubContent"
        style="@style/DialogBottomSubContentStyle"
        android:layout_width="match_parent"
        android:layout_marginTop="4dp"
        app:layout_goneMarginTop="12dp"
        android:textAlignment="viewStart"
        android:gravity="start"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvSubTitle"
        tools:text="@string/selecting_auto_reject_are_auto_reject"
        tools:visibility="gone" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAgree"
        style="@style/DialogAgreeStyle"
        android:gravity="start"
        android:textAlignment="viewStart"
        android:layout_marginTop="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvSubContent"
        tools:text="@string/don_t_show_this_again" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAgreeDescribe"
        style="@style/DialogBottomSubContentStyle"
        android:layout_marginTop="8dp"
        android:gravity="start"
        android:textAlignment="viewStart"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvAgree"
        tools:text="(Opt-out NDB will reduce credit and balance, all open trades being immediately closed)"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvStart"
        style="@style/DialogBottomLeftButtonStyle"
        app:layout_constraintEnd_toStartOf="@+id/tvEnd"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvEnd"
        tools:visibility="gone" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvEnd"
        style="@style/DialogBottomRightButtonStyle"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvStart"
        app:layout_constraintTop_toBottomOf="@id/tvAgreeDescribe" />

</androidx.constraintlayout.widget.ConstraintLayout>