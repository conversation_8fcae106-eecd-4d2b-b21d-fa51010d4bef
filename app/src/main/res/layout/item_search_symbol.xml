<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tv_name"
        style="@style/regular_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="10dp"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="TSLA" />

    <TextView
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:id="@+id/tv_content"
        style="@style/regular_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        tools:text="Tesla"
        android:textSize="10dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:layout_marginBottom="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <ImageView
        android:id="@+id/iv_follow"
        android:layout_width="32dp"
        android:layout_height="wrap_content"
        android:padding="8dp"
        android:layout_marginEnd="4dp"
        tools:src="@drawable/bitmap2_favorite_cf44040"
        android:contentDescription="@string/app_name"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone"
        tools:visibility="visible"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        style="@style/cut_off_line"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>