<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_title_bar"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:contentDescription="@string/app_name"
            android:src="?attr/imgLogoMark"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvAccount"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="Copy Trading"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="@id/ivLogo"
            app:layout_constraintStart_toEndOf="@id/ivLogo"
            app:layout_constraintTop_toTopOf="@id/ivLogo"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/tvAccountId"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="12dp"
            app:layout_constraintBaseline_toBaselineOf="@id/tvAccount"
            app:layout_constraintStart_toEndOf="@id/tvAccount"
            tools:style="@style/gilroy_400"
            tools:text="*********" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivArrow"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_marginStart="4dp"
            android:layout_marginTop="3dp"
            android:src="@drawable/bitmap2_expand_down12x12_c731e1e1e_c61ffffff"
            app:layout_constraintBottom_toBottomOf="@id/tvAccount"
            app:layout_constraintStart_toEndOf="@id/tvAccountId"
            app:layout_constraintTop_toTopOf="@id/tvAccount" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clCollapsingAccountInfo"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="8dp"
            android:background="?attr/mainLayoutBg"
            android:clickable="true"
            android:focusable="true"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ivProductSearch"
            app:layout_constraintStart_toEndOf="@id/ivLogo"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tvCollapseAccountType"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="9dp"
                android:background="@drawable/shape_c007fff_r2"
                android:paddingHorizontal="6dp"
                android:paddingVertical="1.8dp"
                android:text="Copy Trading"
                android:textColor="@color/cffffff"
                android:textSize="6.6dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/tvCollapseAccountId"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="@id/tvCollapseAccountType"
                app:layout_constraintStart_toEndOf="@id/tvCollapseAccountType"
                app:layout_constraintTop_toTopOf="@id/tvCollapseAccountType"
                tools:text="*********" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivCollapseArrow"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_marginStart="4dp"
                android:src="@drawable/bitmap2_expand_down12x12_c731e1e1e_c61ffffff"
                app:layout_constraintBottom_toBottomOf="@id/tvCollapseAccountId"
                app:layout_constraintStart_toEndOf="@id/tvCollapseAccountId"
                app:layout_constraintTop_toTopOf="@id/tvCollapseAccountId" />

            <TextView
                android:id="@+id/tvCollapsePnl"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="@string/floating_pnl"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvCollapseAccountType" />

            <TextView
                android:id="@+id/tvCollapsePnlValue"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="@id/tvCollapsePnl"
                app:layout_constraintStart_toEndOf="@id/tvCollapsePnl"
                app:layout_constraintTop_toTopOf="@id/tvCollapsePnl"
                tools:text="+25324.23" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tvConnecting"
            style="@style/gilroy_500"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:background="?attr/mainLayoutBg"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ivProductSearch"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Connecting..." />

        <ImageView
            android:id="@+id/ivMessage"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:contentDescription="@string/app_name"
            android:padding="12dp"
            android:src="?attr/icon1Msg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/ivRedDot"
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:background="@color/ce35728"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/ivMessage"
            app:layout_constraintCircle="@id/ivMessage"
            app:layout_constraintCircleAngle="45"
            app:layout_constraintCircleRadius="11dp"
            app:layout_constraintEnd_toEndOf="@id/ivMessage"
            app:layout_constraintStart_toStartOf="@id/ivMessage"
            app:layout_constraintTop_toTopOf="@id/ivMessage"
            app:shapeAppearanceOverlay="@style/circleImageStyle"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/ivProductSearch"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="end"
            android:contentDescription="@string/app_name"
            android:padding="12dp"
            android:src="@drawable/draw_bitmap1_search_c1e1e1e_cebffffff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/ivMessage"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/mRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/titleBar">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/appBarLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/transparent"
                app:elevation="0dp"
                app:layout_behavior="cn.com.vau.common.view.AppBarLayoutBehavior">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/topInfo"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_top_title"
                        app:layout_constraintTop_toTopOf="parent">

                        <!-- 账户KYC验证 -->
                        <ViewStub
                            android:id="@+id/viewStubKycGuide"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inflatedId="@+id/inflatedKycLayout"
                            android:layout="@layout/layout_kyc_verify_guide"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:visibility="visible" />

                        <!-- CopyTrading Equity 为0时蓝条布局 或 已入金 -->
                        <LinearLayout
                            android:id="@+id/llAccountInfo"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:paddingBottom="0dp"
                            android:visibility="visible"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/inflatedKycLayout"
                            tools:visibility="visible">

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:id="@+id/ctlAccountInfo"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:paddingBottom="12dp">

                                <TextView
                                    android:id="@+id/tvTotalAssets"
                                    style="@style/gilroy_500"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/margin_horizontal_base"
                                    android:text="@string/equity"
                                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                                    android:textSize="14dp"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />

                                <ImageView
                                    android:id="@+id/ivEquityTip"
                                    android:layout_width="36dp"
                                    android:layout_height="29dp"
                                    android:layout_marginEnd="3dp"
                                    android:contentDescription="@string/app_name"
                                    android:paddingHorizontal="@dimen/margin_horizontal_base"
                                    android:paddingTop="5dp"
                                    android:paddingBottom="@dimen/margin_horizontal_base"
                                    android:src="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintTop_toTopOf="@id/tvTotalAssets" />

                                <TextView
                                    android:id="@+id/tvTotalEquity"
                                    style="@style/gilroy_600"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                                    android:textDirection="ltr"
                                    android:textSize="26dp"
                                    app:layout_constraintStart_toStartOf="@+id/tvTotalAssets"
                                    app:layout_constraintTop_toBottomOf="@+id/tvTotalAssets"
                                    tools:text="-123456" />

                                <TextView
                                    android:id="@+id/tvCurrency"
                                    style="@style/gilroy_600"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="6dp"
                                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                                    android:textSize="14dp"
                                    app:layout_constraintBaseline_toBaselineOf="@id/tvTotalEquity"
                                    app:layout_constraintBottom_toBottomOf="@+id/tvTotalEquity"
                                    app:layout_constraintStart_toEndOf="@+id/tvTotalEquity"
                                    tools:text="USD" />

                                <TextView
                                    android:id="@+id/tvCopyTradingTitle"
                                    style="@style/gilroy_500"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="12dp"
                                    android:text="@string/copy_trading"
                                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                                    android:textSize="14dp"
                                    app:layout_constraintStart_toStartOf="@+id/tvTotalEquity"
                                    app:layout_constraintTop_toBottomOf="@+id/tvTotalEquity" />

                                <ImageView
                                    android:id="@+id/ivCopyTradingQuestion"
                                    android:layout_width="18dp"
                                    android:layout_height="18dp"
                                    android:layout_marginStart="2dp"
                                    android:layout_marginTop="1dp"
                                    android:contentDescription="@string/app_name"
                                    android:padding="3dp"
                                    android:src="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                                    app:layout_constraintBottom_toBottomOf="@+id/tvCopyTradingTitle"
                                    app:layout_constraintStart_toEndOf="@+id/tvCopyTradingTitle"
                                    app:layout_constraintTop_toTopOf="@+id/tvCopyTradingTitle" />

                                <TextView
                                    android:id="@+id/tvCopyTradingEquity"
                                    style="@style/gilroy_500"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="12dp"
                                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                                    android:textSize="16dp"
                                    app:layout_constraintStart_toStartOf="@+id/tvCopyTradingTitle"
                                    app:layout_constraintTop_toBottomOf="@+id/tvCopyTradingTitle"
                                    tools:text="$123456" />

                                <TextView
                                    android:id="@+id/tvCopyTradingFloatingPnL"
                                    style="@style/gilroy_500"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:textDirection="ltr"
                                    android:textSize="16dp"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintStart_toStartOf="@+id/tvCopyTradingEquity"
                                    app:layout_constraintTop_toBottomOf="@+id/tvCopyTradingEquity"
                                    tools:text="-12345678" />

                                <TextView
                                    android:id="@+id/tvManualTradingTitle"
                                    style="@style/gilroy_500"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/manual_trading"
                                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                                    android:textSize="14dp"
                                    app:layout_constraintEnd_toStartOf="@id/ivManualTradingQuestion"
                                    app:layout_constraintTop_toTopOf="@id/tvCopyTradingTitle" />

                                <ImageView
                                    android:id="@+id/ivManualTradingQuestion"
                                    android:layout_width="18dp"
                                    android:layout_height="18dp"
                                    android:layout_marginTop="1dp"
                                    android:layout_marginEnd="@dimen/margin_horizontal_base"
                                    android:contentDescription="@string/app_name"
                                    android:padding="3dp"
                                    android:src="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                                    app:layout_constraintBottom_toBottomOf="@+id/tvManualTradingTitle"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintTop_toTopOf="@+id/tvManualTradingTitle" />

                                <TextView
                                    android:id="@+id/tvManualTradingEquity"
                                    style="@style/gilroy_500"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="@dimen/margin_horizontal_base"
                                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                                    android:textSize="16dp"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintTop_toTopOf="@id/tvCopyTradingEquity"
                                    tools:text="$123456" />

                                <TextView
                                    android:id="@+id/tvManualTradingFloatingPnL"
                                    style="@style/gilroy_500"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:layout_marginEnd="@dimen/margin_horizontal_base"
                                    android:textDirection="ltr"
                                    android:textSize="16dp"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintTop_toBottomOf="@+id/tvManualTradingEquity"
                                    tools:text="12345678" />

                                <!--CopyTrading 为0-->
                                <androidx.constraintlayout.widget.ConstraintLayout
                                    android:id="@+id/clFollowEquityEmpty"
                                    android:layout_width="match_parent"
                                    android:layout_height="40dp"
                                    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                                    android:layout_marginTop="12dp"
                                    android:background="@drawable/shape_c1f007fff_r4"
                                    android:paddingHorizontal="@dimen/margin_horizontal_base"
                                    android:visibility="gone"
                                    app:layout_constraintTop_toBottomOf="@+id/tvCopyTradingEquity"
                                    tools:visibility="visible">

                                    <TextView
                                        android:id="@+id/tvCopyNow"
                                        style="@style/gilroy_600"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:gravity="center_vertical"
                                        android:paddingHorizontal="8dp"
                                        android:text="@string/discover_strategies"
                                        android:textColor="@color/c007fff"
                                        android:textSize="14dp"
                                        app:layout_constraintBottom_toBottomOf="parent"
                                        app:layout_constraintEnd_toStartOf="@id/ivCopyTradingArrow"
                                        app:layout_constraintTop_toTopOf="parent" />

                                    <ImageView
                                        android:id="@+id/ivCopyTradingArrow"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="1dp"
                                        android:contentDescription="@string/app_name"
                                        android:src="@drawable/icon_source2_arrow_end_10x10"
                                        app:layout_constraintBottom_toBottomOf="@id/tvCopyNow"
                                        app:layout_constraintEnd_toEndOf="parent"
                                        app:layout_constraintTop_toTopOf="@id/tvCopyNow"
                                        app:tint="@color/c007fff" />

                                    <TextView
                                        android:id="@+id/tvCopyEmptyTip"
                                        style="@style/gilroy_500"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:text="@string/trade_like_a_master"
                                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                                        android:textSize="14dp"
                                        app:layout_constraintBottom_toBottomOf="parent"
                                        app:layout_constraintEnd_toStartOf="@id/tvCopyNow"
                                        app:layout_constraintStart_toStartOf="parent"
                                        app:layout_constraintTop_toTopOf="parent" />

                                </androidx.constraintlayout.widget.ConstraintLayout>

                            </androidx.constraintlayout.widget.ConstraintLayout>

                            <View
                                style="@style/signal_profile_cut_off_line"
                                android:layout_width="match_parent" />
                        </LinearLayout>

                        <!-- CopyTrading 未入金 -->
                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/clAssetsEmpty"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:visibility="gone"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/inflatedKycLayout"
                            tools:visibility="gone">

                            <TextView
                                android:id="@+id/tvEquity"
                                style="@style/gilroy_500"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/margin_horizontal_base"
                                android:text="@string/equity"
                                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                                android:textSize="14dp"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <ImageView
                                android:id="@+id/ivEquityTip2"
                                android:layout_width="36dp"
                                android:layout_height="29dp"
                                android:layout_marginEnd="3dp"
                                android:contentDescription="@string/app_name"
                                android:paddingHorizontal="@dimen/margin_horizontal_base"
                                android:paddingTop="5dp"
                                android:paddingBottom="@dimen/margin_horizontal_base"
                                android:src="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="@id/tvEquity" />

                            <TextView
                                android:id="@+id/tvZeroEquity"
                                style="@style/gilroy_600"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="..."
                                android:textColor="?attr/color_c1e1e1e_cebffffff"
                                android:textSize="26dp"
                                app:layout_constraintStart_toStartOf="@+id/tvEquity"
                                app:layout_constraintTop_toBottomOf="@+id/tvEquity"
                                tools:ignore="HardcodedText" />

                            <TextView
                                android:id="@+id/tvZeroCurrency"
                                style="@style/gilroy_600"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="6dp"
                                android:textColor="?attr/color_c1e1e1e_cebffffff"
                                android:textSize="14dp"
                                app:layout_constraintBaseline_toBaselineOf="@id/tvZeroEquity"
                                app:layout_constraintBottom_toBottomOf="@+id/tvZeroEquity"
                                app:layout_constraintStart_toEndOf="@+id/tvZeroEquity" />

                            <TextView
                                android:id="@+id/tvGuideTip"
                                style="@style/gilroy_700"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                                android:layout_marginTop="12dp"
                                android:text="@string/deposit_to_start_journey"
                                android:textColor="?attr/color_c1e1e1e_cebffffff"
                                android:textSize="20dp"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/tvZeroEquity" />

                            <TextView
                                android:id="@+id/tvGuideOperation"
                                style="@style/gilroy_500"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/try_with_a_bite_sized_capital"
                                android:textColor="@color/ce35728"
                                android:textSize="16dp"
                                app:layout_constraintStart_toStartOf="@id/tvGuideTip"
                                app:layout_constraintTop_toBottomOf="@id/tvGuideTip" />

                            <TextView
                                android:id="@+id/tvDeposit"
                                style="@style/gilroy_600"
                                android:layout_width="match_parent"
                                android:layout_height="40dp"
                                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                                android:layout_marginTop="16dp"
                                android:background="@drawable/draw_shape_c1e1e1e_cebffffff_r100"
                                android:gravity="center"
                                android:paddingHorizontal="10dp"
                                android:text="@string/deposit"
                                android:textColor="?attr/color_cebffffff_c1e1e1e"
                                android:textSize="16dp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/tvGuideOperation" />

                            <View
                                android:id="@+id/viewSplit"
                                style="@style/signal_profile_cut_off_line"
                                android:layout_width="match_parent"
                                android:layout_marginTop="16dp"
                                android:visibility="invisible"
                                app:layout_constraintTop_toBottomOf="@id/tvDeposit"
                                tools:visibility="visible" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <!-- 广告位 -->
                    <com.youth.banner.Banner
                        android:id="@+id/mBanner"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:visibility="gone"
                        app:banner_indicator_height="0dp"
                        app:banner_indicator_normal_width="0dp"
                        app:banner_loop_time="4000"
                        app:layout_constraintDimensionRatio="h,393:72"
                        app:layout_constraintTop_toBottomOf="@id/topInfo"
                        tools:visibility="visible" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivBannerClose"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="5dp"
                        android:padding="7dp"
                        android:src="?attr/icon2CloseCircle12x12"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="@id/mBanner"
                        app:layout_constraintTop_toTopOf="@id/mBanner"
                        tools:visibility="gone" />

                    <cn.com.vau.common.view.custom.IndicatorNumView
                        android:id="@+id/indicator"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/margin_horizontal_base"
                        android:layout_marginBottom="4dp"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="@id/mBanner"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <View
                        android:id="@+id/indicatorSplit"
                        style="@style/signal_profile_cut_off_line"
                        android:layout_width="match_parent"
                        app:layout_constraintTop_toBottomOf="@id/mBanner" />

                    <!-- CopyTrading -->
                    <include
                        android:id="@+id/layoutCopyTrading"
                        layout="@layout/include_copytrading_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_top_title_card"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/mBanner"
                        tools:visibility="gone" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.appbar.AppBarLayout>

            <!-- 行情列表 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clSymbol"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_behavior="cn.com.vau.common.view.RecyclerViewBehavior">

                <cn.com.vau.common.view.tablayout.DslTabLayout
                    android:id="@+id/mTabLayout"
                    android:layout_width="match_parent"
                    android:layout_height="33dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <include
                    android:id="@+id/layoutSymbolTitle"
                    layout="@layout/include_deal_product_attr_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toBottomOf="@id/mTabLayout" />

                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/mViewPager"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/layoutSymbolTitle" />

                <ViewStub
                    android:id="@+id/mVsGuide1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout="@layout/vs_layout_trades_guide_1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layoutSymbolTitle" />

                <ViewStub
                    android:id="@+id/mVsGuide2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout="@layout/vs_layout_trades_guide_2"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layoutSymbolTitle" />

                <ViewStub
                    android:id="@+id/mVsNoDataScroll"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout="@layout/vs_layout_no_data_scroll"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/layoutSymbolTitle" />

            </androidx.constraintlayout.widget.ConstraintLayout>
            <!-- 行情列表 结束 -->

            <ViewStub
                android:id="@+id/mViewStubMarketMaintenance"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="-6dp"
                android:layout="@layout/layout_market_maintenance_new"
                app:layout_behavior="@string/appbar_scrolling_view_behavior" />

        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</LinearLayout>
