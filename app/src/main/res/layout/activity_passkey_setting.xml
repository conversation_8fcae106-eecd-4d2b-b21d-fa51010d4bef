<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <ViewStub
        android:id="@+id/passkeyIntroduceVs"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="64dp"
        android:layout_marginBottom="18dp"
        android:layout="@layout/layout_passkey_introduce"
        app:layout_constraintBottom_toTopOf="@id/tvNext"
        app:layout_constraintTop_toBottomOf="@id/mHeaderBar" />

    <ViewStub
        android:id="@+id/passkeyListVs"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout="@layout/layout_passkey_list"
        app:layout_constraintBottom_toTopOf="@id/tvNext"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mHeaderBar" />

    <TextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_width="0dp"
        android:layout_marginHorizontal="12dp"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:text="@string/add_passkey"
        android:textColor="?attr/color_cebffffff_c1e1e1e"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>