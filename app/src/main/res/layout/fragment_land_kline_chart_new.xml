<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true">

    <LinearLayout
        android:id="@+id/llBoard"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:layout_marginTop="12dp"
        android:background="@drawable/draw_shape_stroke_c331e1e1e_c33ffffff_r100"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/margin_horizontal_base"
        app:layout_constraintStart_toStartOf="@id/nestedScrollView"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvName"
            style="@style/bold_semi_font"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="16dp"
            tools:text="USDCAD" />

        <TextView
            android:id="@+id/tvBoardSellPrice"
            style="@style/bold_semi_font"
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:textAlignment="viewEnd"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="16dp"
            tools:text="+1833.20397" />

        <TextView
            android:id="@+id/tvBoardDiff"
            style="@style/bold_semi_font"
            android:layout_width="140dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:paddingEnd="5dp"
            android:textAlignment="viewEnd"
            android:textColor="@color/c00c79c"
            android:textDirection="ltr"
            android:textSize="14dp"
            tools:text="+0.0073 (+97.29%)" />

        <TextView
            android:id="@+id/tv_board_time"
            style="@style/regular_font"
            android:layout_width="50dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            tools:text="13:50:22" />
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvOrders"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:paddingHorizontal="12dp"
        android:paddingVertical="6dp"
        android:text="@string/orders"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/llBoard"
        app:layout_constraintStart_toEndOf="@id/llBoard"
        app:layout_constraintTop_toTopOf="@id/llBoard" />

    <ImageView
        android:id="@+id/ivBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginEnd="21dp"
        android:contentDescription="@string/app_name"
        android:padding="15dp"
        android:src="@drawable/draw_bitmap2_close16x16_c731e1e1e_c61ffffff"
        app:layout_constraintBottom_toBottomOf="@id/llBoard"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/llBoard" />

    <ImageView
        android:id="@+id/ivSetting"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:padding="15dp"
        android:src="?attr/icon1Setting"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/ivBack"
        app:layout_constraintEnd_toStartOf="@id/ivBack"
        app:layout_constraintTop_toTopOf="@id/ivBack" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivKNewGuide"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toStartOf="@id/ivSetting"
        android:padding="15dp"
        app:layout_constraintBottom_toBottomOf="@id/ivSetting"
        app:layout_constraintEnd_toStartOf="@id/ivSetting"
        app:layout_constraintTop_toTopOf="@id/ivSetting"
        app:srcCompat="?attr/icon1Guide" />

    <!--region K线时间设置         -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/intervalRecyclerView"
        android:layout_width="0dp"
        android:layout_height="30dp"
        android:layout_marginTop="@dimen/margin_horizontal_base"
        app:layout_constraintEnd_toStartOf="@id/klineSideControlSideView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/llBoard"
        tools:itemCount="6"
        tools:listitem="@layout/item_kline_tab_interval_new"
        tools:visibility="visible" />
    <!--endregion-->

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nestedScrollView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/klineSideControlSideView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/intervalRecyclerView">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/chartConstraintLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.github.tifezh.kchartlib.helper.chart.KChartView
                android:id="@+id/kCharView"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <com.upex.common.drawTools.DrawView
                android:id="@+id/drawView"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/kCharView"
                app:layout_constraintEnd_toEndOf="@id/kCharView"
                app:layout_constraintStart_toStartOf="@id/kCharView"
                app:layout_constraintTop_toTopOf="@id/kCharView" />

            <ViewStub
                android:id="@+id/vsChartTipFloat"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout="@layout/include_land_kline_chart_tips_layout"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <cn.com.vau.common.view.kchart.toolbar.DragLayout
        android:id="@+id/dlToolbar"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginHorizontal="12dp"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="12dp"
        android:visibility="gone"
        android:layoutDirection="ltr"
        app:layout_constraintBottom_toBottomOf="@id/nestedScrollView"
        app:layout_constraintEnd_toEndOf="@id/nestedScrollView"
        app:layout_constraintStart_toStartOf="@id/nestedScrollView"
        app:layout_constraintTop_toTopOf="@id/nestedScrollView"
        tools:visibility="visible">

        <cn.com.vau.common.view.kchart.toolbar.KLineDrawingToolbar
            android:id="@+id/viewToolbar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </cn.com.vau.common.view.kchart.toolbar.DragLayout>

    <cn.com.vau.page.setting.activity.LandKLineControlsSideView
        android:id="@+id/klineSideControlSideView"
        android:layout_width="@dimen/kline_land_side_width"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivBack" />

    <include
        android:id="@+id/layoutLandKlineDrawingTips"
        layout="@layout/include_land_kline_drawing_tips" />

    <!-- Drag the box to set Stop Loss/Take Profit -->
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDragTip"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginBottom="24dp"
        android:background="@drawable/shape_c007fff_r6"
        android:drawablePadding="12dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="8dp"
        android:text="@string/drag_the_box_loss_take_profit"
        android:textColor="@color/cffffff"
        android:textSize="14dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible" />

    <!-- 无数据 -->
    <ViewStub
        android:id="@+id/mVsNoData"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout="@layout/vs_layout_no_data"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/nestedScrollView"
        app:layout_constraintEnd_toEndOf="@id/nestedScrollView"
        app:layout_constraintStart_toStartOf="@id/nestedScrollView"
        app:layout_constraintTop_toTopOf="@id/nestedScrollView"
        app:ndv_icon="?attr/icNoConnection"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>