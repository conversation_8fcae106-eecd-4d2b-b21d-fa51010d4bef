<?xml version="1.0" encoding="utf-8"?><!-- 互抵平仓 / 反向开仓 / 批量平仓 -->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:id="@+id/ctlParent"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <!--  互抵平仓、批量平仓使用  -->
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivSelect"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tvProdName"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvProdName"
        app:srcCompat="@drawable/icon2_cb_tick_circle_c15b374"
        tool:visibility="visible" />

    <TextView
        android:id="@+id/tvProdName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="16dp"
        app:layout_constraintStart_toEndOf="@+id/ivSelect"
        app:layout_constraintTop_toTopOf="parent"
        tool:ignore="SpUsage"
        style="@style/gilroy_600"
        tool:text="VAU-TEST" />

    <ImageView
        android:id="@+id/ivKLine"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="2dp"
        android:contentDescription="@string/app_name"
        android:padding="2dp"
        android:src="@drawable/draw_bitmap2_view_chart_ca61e1e1e_c99ffffff"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tvProdName"
        app:layout_constraintStart_toEndOf="@+id/tvProdName"
        app:layout_constraintTop_toTopOf="@+id/tvProdName"
        tool:visibility="visible" />

    <TextView
        android:id="@+id/tvPnlTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:maxLines="1"
        android:text="@string/pnl"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvProdName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvProdName"
        tool:ignore="SpUsage"
        style="@style/gilroy_500" />

    <TextView
        android:id="@+id/tvOrderType"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:layout_marginTop="4dp"
        android:background="@drawable/shape_c1f00c79c_r100"
        android:gravity="center_vertical"
        android:paddingHorizontal="8dp"
        android:textColor="@color/c00c79c"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="@+id/tvProdName"
        app:layout_constraintTop_toBottomOf="@+id/tvProdName"
        tool:ignore="Smalldp,SpUsage"
        style="@style/gilroy_500"
        tool:text="Buy" />

    <TextView
        android:id="@+id/tvOrderId"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvOrderType"
        app:layout_constraintStart_toEndOf="@+id/tvOrderType"
        tool:ignore="SpUsage"
        style="@style/gilroy_400"
        tool:text="#4535356565" />

    <TextView
        android:id="@+id/tvPnl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textColor="@color/cf44040"
        android:textDirection="ltr"
        android:textSize="18dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvOrderType"
        app:layout_constraintBottom_toBottomOf="@+id/tvOrderType"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvOrderType"
        tool:ignore="SpUsage"
        style="@style/gilroy_600"
        tool:text="-0.12345678" />

    <TextView
        android:id="@+id/tvVolTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="4dp"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:maxLines="1"
        android:text="@string/volume"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@+id/guideline_v33"
        app:layout_constraintStart_toStartOf="@+id/tvProdName"
        app:layout_constraintTop_toBottomOf="@+id/tvOrderType"
        tool:ignore="SpUsage"
        style="@style/gilroy_400" />

    <TextView
        android:id="@+id/tvVolume"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="12dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toTopOf="@+id/offView"
        app:layout_constraintStart_toStartOf="@+id/tvProdName"
        app:layout_constraintTop_toBottomOf="@+id/tvVolTitle"
        app:layout_goneMarginBottom="12dp"
        tool:ignore="SpUsage"
        style="@style/gilroy_600"
        tool:text="0.20" />

    <TextView
        android:id="@+id/tvOpenPriceTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4dp"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:text="@string/open_price"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvVolTitle"
        app:layout_constraintEnd_toStartOf="@+id/guideline_v66"
        app:layout_constraintStart_toEndOf="@+id/guideline_v33"
        tool:ignore="SpUsage"
        style="@style/gilroy_400" />

    <TextView
        android:id="@+id/tvOpenPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvVolume"
        app:layout_constraintEnd_toStartOf="@+id/guideline_v66"
        app:layout_constraintStart_toEndOf="@+id/guideline_v33"
        tool:ignore="SpUsage"
        style="@style/gilroy_600"
        tool:text="1.2345" />

    <TextView
        android:id="@+id/tvCurrentPriceTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="center_vertical|end"
        android:maxLines="1"
        android:text="@string/current_price"
        android:textAlignment="viewEnd"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvOpenPriceTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/guideline_v66"
        tool:ignore="SpUsage"
        style="@style/gilroy_400" />

    <TextView
        android:id="@+id/tvCurrentPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvOpenPrice"
        app:layout_constraintEnd_toEndOf="parent"
        tool:ignore="SpUsage"
        style="@style/gilroy_600"
        tool:text="1.3456" />

    <View
        android:id="@+id/offView"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvVolume"
        tool:background="?attr/color_c0a1e1e1e_c0affffff" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_v33"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.33" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_v66"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.66" />

</androidx.constraintlayout.widget.ConstraintLayout>
