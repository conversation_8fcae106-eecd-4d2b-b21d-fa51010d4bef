<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingBottom="10dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <cn.com.vau.util.widget.SwitchButton
        android:id="@+id/sbSlippageProtection"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:layout_marginVertical="10dp"
        app:layout_constraintBottom_toTopOf="@id/sbLotRoundUp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"/>

    <cn.com.vau.util.widget.SwitchButton
        android:id="@+id/sbLotRoundUp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        app:layout_constraintBottom_toTopOf="@id/sbCopyOpenTrade"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sbSlippageProtection"
        app:layout_constraintVertical_chainStyle="packed"/>

    <cn.com.vau.util.widget.SwitchButton
        android:id="@+id/sbCopyOpenTrade"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        app:switch_auto_change="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sbLotRoundUp"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSlippageProtection"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:maxLines="2"
        android:text="@string/slippage_protection"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintBottom_toBottomOf="@id/sbSlippageProtection"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/sbSlippageProtection"
        app:layout_constraintEnd_toStartOf="@id/ivSlippageProtectionIcon"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivSlippageProtectionIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        app:srcCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
        app:layout_constraintTop_toTopOf="@id/tvSlippageProtection"
        app:layout_constraintBottom_toBottomOf="@id/tvSlippageProtection"
        app:layout_constraintStart_toEndOf="@id/tvSlippageProtection"
        app:layout_constraintEnd_toStartOf="@id/sbSlippageProtection"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLotRoundUp"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:maxLines="2"
        android:text="@string/lot_round_up"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintBottom_toBottomOf="@id/sbLotRoundUp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/sbLotRoundUp"
        app:layout_constraintEnd_toStartOf="@id/ivLotRoundUpIcon"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivLotRoundUpIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        app:srcCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
        app:layout_constraintTop_toTopOf="@id/tvLotRoundUp"
        app:layout_constraintBottom_toBottomOf="@id/tvLotRoundUp"
        app:layout_constraintStart_toEndOf="@id/tvLotRoundUp"
        app:layout_constraintEnd_toStartOf="@id/sbLotRoundUp"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCopyOpenTrade"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:maxLines="2"
        android:text="@string/copy_opened_trades"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintBottom_toBottomOf="@id/sbCopyOpenTrade"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/sbCopyOpenTrade"
        app:layout_constraintEnd_toStartOf="@id/ivCopyOpenTradeIcon" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivCopyOpenTradeIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        app:srcCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
        app:layout_constraintTop_toTopOf="@id/tvCopyOpenTrade"
        app:layout_constraintBottom_toBottomOf="@id/tvCopyOpenTrade"
        app:layout_constraintStart_toEndOf="@id/tvCopyOpenTrade"
        app:layout_constraintEnd_toStartOf="@id/sbCopyOpenTrade"/>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupSlippageProtection"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvSlippageProtection,ivSlippageProtectionIcon,sbSlippageProtection"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupCopyOpenTrade"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="tvCopyOpenTrade,ivCopyOpenTradeIcon,sbCopyOpenTrade"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>