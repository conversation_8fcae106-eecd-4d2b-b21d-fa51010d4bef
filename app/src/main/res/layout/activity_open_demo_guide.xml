<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_endIcon="@drawable/draw_bitmap2_close16x16_c731e1e1e_c61ffffff"
        app:hb_endIcon1="?attr/icon1Cs"
        app:layout_constraintTop_toTopOf="parent"/>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/llBottom"
        app:layout_constraintTop_toBottomOf="@id/mHeaderBar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tvDemoDesc"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:text="@string/free_10000000_demo"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="18dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvAccoConf"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="@dimen/margin_horizontal_base"
                android:text="@string/account_configuration"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvDemoDesc" />

            <TextView
                android:id="@+id/tvAccoPlatTip"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:gravity="center"
                android:layout_marginTop="6dp"
                android:drawablePadding="4dp"
                android:text="@string/choose_account_platform"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvAccoConf" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rcyAccoPlat"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                app:layout_constraintTop_toBottomOf="@+id/tvAccoPlatTip"
                app:spanCount="2"
                tools:itemCount="2"
                tools:listitem="@layout/item_open_lv1_acco_plat" />

            <View
                android:id="@+id/viewSplitPlat"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="20dp"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintTop_toBottomOf="@id/rcyAccoPlat" />

            <TextView
                android:id="@+id/tvAccoType"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:drawablePadding="4dp"
                android:text="@string/choose_account_type"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/viewSplitPlat" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rcyAccoType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:layout_constraintTop_toBottomOf="@id/tvAccoType"
                app:spanCount="2"
                tools:itemCount="4"
                tools:listitem="@layout/item_open_lv1_acco_type" />

            <View
                android:id="@+id/viewSplitType"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintTop_toBottomOf="@id/rcyAccoType" />

            <TextView
                android:id="@+id/tvAccoCurrency"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="12dp"
                android:text="@string/choose_account_currency"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/viewSplitType" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rcyAccoCurrency"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:layout_constraintTop_toBottomOf="@+id/tvAccoCurrency"
                app:spanCount="4"
                tools:itemCount="12"
                tools:listitem="@layout/item_open_lv1_acco_currency" />

            <!-- Asic才显示这个文案 -->
            <TextView
                android:id="@+id/tvAsicPrompt"
                style="@style/gilroy_400"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="20dp"
                android:gravity="center"
                android:lineSpacingMultiplier="1.2"
                android:text="@string/please_note_the_for_there_differences_in_features"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="11dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/rcyAccoCurrency"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <LinearLayout
        android:id="@+id/llBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tvNext"
            style="@style/main_bottom_button_theme"
            android:layout_marginHorizontal="12dp"
            android:layout_marginVertical="16dp"
            android:text="@string/next" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>