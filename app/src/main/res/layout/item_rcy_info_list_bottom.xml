<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tvInfoTitle"
        style="@style/DialogBottomSubTitleStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpUsage"
        tools:text="Cumulative Earned Profits" />

    <TextView
        android:id="@+id/tvInfoContent"
        style="@style/DialogBottomSubContentStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textAlignment="viewStart"
        android:gravity="start"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvInfoTitle"
        app:layout_goneMarginTop="12dp"
        tools:ignore="SpUsage"
        tools:text="The cumulative sum of profits you have earned after every weekly settlement." />

</androidx.constraintlayout.widget.ConstraintLayout>
