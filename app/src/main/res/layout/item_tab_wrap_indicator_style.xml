<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    tools:ignore="SpUsage,UseCompoundDrawables,UselessParent">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="8dp"
        android:paddingVertical="2.5dp">

        <TextView
            android:id="@+id/tvTab"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="12dp"
            tools:text="Watchlist" />

        <ImageView
            android:id="@+id/ivArrow"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_marginStart="2dp"
            android:contentDescription="@string/app_name"
            android:src="@drawable/icon_source2_triangle_down_tab"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>
</FrameLayout>