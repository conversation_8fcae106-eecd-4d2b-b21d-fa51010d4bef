<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/viewBg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewLine"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivSelect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/margin_horizontal_base"
        android:paddingVertical="@dimen/margin_horizontal_base"
        android:src="@drawable/draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tvProdName"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvProdName" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvProdName"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="16dp"
        app:layout_constraintStart_toEndOf="@+id/ivSelect"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginStart="@dimen/margin_horizontal_base"
        tools:text="VAU-TEST" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivExtent"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:paddingHorizontal="@dimen/margin_horizontal_base"
        app:layout_constraintBottom_toBottomOf="@+id/tvProdName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvProdName"
        tools:src="@drawable/draw_bitmap2_arrow_top10x10_c1e1e1e_cebffffff" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvBidTitle"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/sell"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="@id/tvProdName"
        app:layout_constraintTop_toBottomOf="@id/tvProdName" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvBid"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@id/tvBidTitle"
        app:layout_constraintStart_toEndOf="@id/tvBidTitle"
        app:layout_constraintTop_toTopOf="@id/tvBidTitle"
        tools:text="153.263" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvRate"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@id/tvBidTitle"
        app:layout_constraintStart_toEndOf="@id/tvBid"
        app:layout_constraintTop_toTopOf="@id/tvBidTitle"
        tools:text="+0.37%" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAskTitle"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:text="@string/buy"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@id/tvBidTitle"
        app:layout_constraintStart_toEndOf="@id/tvRate"
        app:layout_constraintTop_toTopOf="@id/tvBidTitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAsk"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@id/tvBidTitle"
        app:layout_constraintStart_toEndOf="@id/tvAskTitle"
        app:layout_constraintTop_toTopOf="@id/tvBidTitle"
        tools:text="153.232" />

    <View
        android:id="@+id/viewLine"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintTop_toBottomOf="@id/tvBidTitle" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvList"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layoutManager="cn.com.vau.common.view.WrapContentLinearLayoutManager"
        app:layout_constraintTop_toBottomOf="@id/viewLine" />

</androidx.constraintlayout.widget.ConstraintLayout>