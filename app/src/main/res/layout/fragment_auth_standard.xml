<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="50dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitleLv1"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:text="@string/lv1_account_opening"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvLv1TitleReq"
        style="@style/bold_semi_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/requirements"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="@id/tvTitleLv1"
        app:layout_constraintTop_toBottomOf="@id/tvTitleLv1" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLv1VerifiedStatus"
        style="@style/regular_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:background="@drawable/draw_shape_ce8e8e8_c414348_r100"
        android:drawablePadding="4dp"
        android:gravity="center"
        android:paddingHorizontal="8dp"
        android:paddingVertical="2dp"
        android:text="@string/unverified"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="@id/tvLv1TitleReq"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvLv1TitleReq"
        tools:text="Unverified" />

    <TextView
        android:id="@+id/tvLv1Re1"
        style="@style/medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:drawablePadding="15dp"
        android:gravity="center"
        android:text="@string/basic_information"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableStartCompat="@drawable/draw_bitmap_circle_right_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="@id/tvLv1TitleReq"
        app:layout_constraintTop_toBottomOf="@id/tvLv1TitleReq" />

    <TextView
        android:id="@+id/tvLv1Re2"
        style="@style/medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:drawablePadding="15dp"
        android:gravity="center"
        android:text="@string/account_configuration"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableStartCompat="@drawable/draw_bitmap_circle_right_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="@id/tvLv1Re1"
        app:layout_constraintTop_toBottomOf="@id/tvLv1Re1" />

    <TextView
        android:id="@+id/tvLv1Re3"
        style="@style/medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:drawablePadding="15dp"
        android:gravity="center"
        android:text="@string/personal_declaration"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableStartCompat="@drawable/draw_bitmap_circle_right_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="@id/tvLv1Re2"
        app:layout_constraintTop_toBottomOf="@id/tvLv1Re2" />

    <View
        android:id="@+id/viewLineLv1"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginTop="15dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvLv1Re3" />

    <TextView
        android:id="@+id/tvLv1TitlePer"
        style="@style/bold_semi_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/permissions"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="@+id/tvLv1TitleReq"
        app:layout_constraintTop_toBottomOf="@+id/viewLineLv1" />

    <TextView
        android:id="@+id/tvLv1Per1"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:drawablePadding="10dp"
        android:gravity="center"
        android:text="@string/trading_account"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        android:textStyle="normal"
        app:drawableStartCompat="@drawable/img_source_permission_account"
        app:layout_constraintStart_toStartOf="@+id/tvLv1TitlePer"
        app:layout_constraintTop_toBottomOf="@+id/tvLv1TitlePer" />

    <TextView
        android:id="@+id/tvLv1Per2"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:drawablePadding="10dp"
        android:gravity="center"
        android:text="@string/deposit"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        android:textStyle="normal"
        app:drawableStartCompat="@drawable/img_source_permission_deposit"
        app:layout_constraintStart_toStartOf="@+id/tvLv1Per1"
        app:layout_constraintTop_toBottomOf="@+id/tvLv1Per1" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLv1VerifyNow"
        style="@style/bold_semi_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:gravity="center"
        android:paddingHorizontal="20dp"
        android:paddingVertical="8dp"
        android:text="@string/verify"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        android:textStyle="normal"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvLv1Per2"
        tools:visibility="visible" />

    <View
        android:id="@+id/viewBottomLv1"
        android:layout_width="wrap_content"
        android:layout_height="8dp"
        android:layout_marginTop="20dp"
        android:background="?attr/color_c0a1e1e1e_c0affffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvLv1VerifyNow"
        app:layout_goneMarginTop="18dp" />

    <TextView
        android:id="@+id/tvTitleLv2"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:text="@string/lv2_id_authentication"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        android:textStyle="normal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/viewBottomLv1" />

    <TextView
        android:id="@+id/tvTitleLv2TitleReq"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:text="@string/requirements"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="@id/tvTitleLv2"
        app:layout_constraintTop_toBottomOf="@+id/tvTitleLv2" />

    <TextView
        android:id="@+id/tvTitleLv2VerifiedStatus"
        style="@style/regular_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:background="@drawable/draw_shape_ce8e8e8_c414348_r100"
        android:drawablePadding="4dp"
        android:gravity="center"
        android:paddingHorizontal="8dp"
        android:paddingVertical="2dp"
        android:text="@string/unverified"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="@id/tvTitleLv2TitleReq"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvTitleLv2TitleReq"
        tools:text="Unverified" />

    <TextView
        android:id="@+id/tvTitleLv2Re1"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:drawablePadding="15dp"
        android:gravity="center"
        android:text="@string/id_information"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableStartCompat="@drawable/draw_bitmap_circle_right_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="@id/tvTitleLv2TitleReq"
        app:layout_constraintTop_toBottomOf="@id/tvTitleLv2TitleReq" />

    <TextView
        android:id="@+id/tvTitleLv2Re2"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:drawablePadding="15dp"
        android:gravity="center"
        android:text="@string/id_photo"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableStartCompat="@drawable/draw_bitmap_circle_right_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="@id/tvTitleLv2Re1"
        app:layout_constraintTop_toBottomOf="@id/tvTitleLv2Re1" />

    <View
        android:id="@+id/viewLineLv2"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginTop="15dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitleLv2Re2" />

    <TextView
        android:id="@+id/tvTitleLv2TitlePer"
        style="@style/bold_semi_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/permissions"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="@+id/tvTitleLv2TitleReq"
        app:layout_constraintTop_toBottomOf="@+id/viewLineLv2" />

    <TextView
        android:id="@+id/tvTitleLv2Trade"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:drawablePadding="10dp"
        android:gravity="center"
        android:text="@string/signal_trade"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        android:textStyle="normal"
        app:drawableStartCompat="@drawable/img_source_permission_trade"
        app:layout_constraintStart_toStartOf="@+id/tvTitleLv2TitlePer"
        app:layout_constraintTop_toBottomOf="@+id/tvTitleLv2TitlePer" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLv2VerifyNow"
        style="@style/bold_semi_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:gravity="center"
        android:paddingHorizontal="20dp"
        android:paddingVertical="8dp"
        android:text="@string/verify"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        android:textStyle="normal"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitleLv2Trade" />

    <View
        android:id="@+id/viewBottomLv2"
        android:layout_width="wrap_content"
        android:layout_height="8dp"
        android:layout_marginTop="20dp"
        android:background="?attr/color_c0a1e1e1e_c0affffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvLv2VerifyNow" />
</androidx.constraintlayout.widget.ConstraintLayout>