<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivTitleSelect"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginStart="4dp"
        android:padding="8dp"
        android:src="@drawable/draw_shape_stroke_c731e1e1e_c61ffffff_r2_s14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/take_profit"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivTitleSelect"
        app:layout_constraintStart_toEndOf="@id/ivTitleSelect"
        app:layout_constraintTop_toTopOf="@+id/ivTitleSelect" />

    <TextView
        android:id="@+id/tvTitleAction"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:drawablePadding="8dp"
        android:textAllCaps="false"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableEndCompat="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/ivTitleSelect"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ivTitleSelect"
        tools:text="@string/pnl" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clPriceEdit"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/select_login_et_bg"
        app:layout_constraintEnd_toStartOf="@+id/clSetting"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintHorizontal_weight="225"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivTitleSelect">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivPriceEditSub"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:padding="12dp"
            android:src="?attr/icon2Sub"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvPriceEditTitle"
            style="@style/gilroy_500"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAlignment="center"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/etPriceEdit"
            app:layout_constraintEnd_toStartOf="@+id/ivPriceEditAdd"
            app:layout_constraintStart_toEndOf="@+id/ivPriceEditSub"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="@string/price"
            tools:visibility="visible" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/etPriceEdit"
            style="@style/gilroy_500"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@null"
            android:hint="@string/not_set"
            android:inputType="numberDecimal"
            android:singleLine="true"
            android:textAlignment="center"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ivPriceEditAdd"
            app:layout_constraintStart_toEndOf="@+id/ivPriceEditSub"
            app:layout_constraintTop_toBottomOf="@+id/tvPriceEditTitle"
            app:layout_goneMarginTop="0dp"
            tools:text="1.3881" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivPriceEditAdd"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:padding="12dp"
            android:src="@drawable/draw_bitmap2_add_c1e1e1e_cebffffff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clSetting"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginEnd="12dp"
        android:background="@drawable/select_login_et_bg"
        app:layout_constraintBottom_toBottomOf="@+id/clPriceEdit"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="142"
        app:layout_constraintStart_toEndOf="@+id/clPriceEdit"
        app:layout_constraintTop_toTopOf="@+id/clPriceEdit">

        <TextView
            android:id="@+id/tvComputeModeEditTitle"
            style="@style/gilroy_500"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:drawablePadding="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAlignment="center"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constraintBottom_toTopOf="@+id/etComputeMode"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="@string/pnl" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/etComputeMode"
            style="@style/gilroy_500"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@null"
            android:hint="@string/not_set"
            android:inputType="numberSigned|numberDecimal"
            android:singleLine="true"
            android:textAlignment="center"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvComputeModeEditTitle"
            app:layout_goneMarginTop="0dp"
            tools:text="1.3881" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupEdit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tvTitleAction,clPriceEdit,clSetting" />

    <TextView
        android:id="@+id/tvTips"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:layout_marginTop="8dp"
        android:lineSpacingExtra="6dp"
        android:minHeight="48dp"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clPriceEdit"
        tools:text="When the last price reaches 105,498.71 USD, it will trigger a Market order, and the estimated PnL will be -1.00 USD (-9.49%)" />

</merge>