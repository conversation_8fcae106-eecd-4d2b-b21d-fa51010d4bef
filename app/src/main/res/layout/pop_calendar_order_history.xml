<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/mainLayoutBg">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_backClickAutoFinishDisallow="true"
        app:hb_titleText="@string/select_date"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ctlDate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_top_title"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r10"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar">

        <TextView
            android:id="@+id/tvStartDateLabel"
            style="@style/medium_font"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="@string/start_date"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="14dp"
            app:layout_constraintEnd_toStartOf="@+id/guideline_v50"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage" />

        <!--    有数据显示 colorCodeCheckText    -->
        <TextView
            android:id="@+id/tvStartDate"
            style="@style/medium_font"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="20dp"
            android:text="@string/select_date"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline_v50"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvStartDateLabel"
            tools:ignore="SpUsage" />

        <View
            android:layout_width="0.5dp"
            android:layout_height="0dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:background="?attr/color_c0a1e1e1e_c262930"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/guideline_v50"
            app:layout_constraintStart_toStartOf="@+id/guideline_v50"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvEndDateLabel"
            style="@style/medium_font"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="@string/end_date"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="14dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/guideline_v50"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage" />

        <TextView
            android:id="@+id/tvEndDate"
            style="@style/medium_font"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="20dp"
            android:text="@string/select_date"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/guideline_v50"
            app:layout_constraintTop_toBottomOf="@+id/tvEndDateLabel"
            tools:ignore="SpUsage" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_v50"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.50" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mConstraintLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/margin_vertical_base"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ctlDate">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rcvCalendar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/margin_horizontal_base"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tvConfirm"
        style="@style/main_bottom_button_theme"
        android:layout_width="0dp"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginStart="26dp"
        android:layout_marginEnd="26dp"
        android:text="@string/done"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>