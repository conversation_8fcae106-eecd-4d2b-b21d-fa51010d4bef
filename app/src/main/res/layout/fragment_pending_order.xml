<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/flParent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="cn.com.vau.trade.fragment.order.PendingOrderFragment">

    <!--解决 AppBarLayout滑动问题 -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/mScrollView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivHideOther"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingVertical="8dp"
                android:paddingStart="12dp"
                android:src="?attr/icon2CbSquareUncheck"
                app:layout_constraintBottom_toBottomOf="@id/tvHideOther"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/tvHideOther"
                tools:ignore="RtlSymmetry" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvHideOther"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:paddingHorizontal="4dp"
                android:paddingVertical="8dp"
                android:text="@string/hide_other_symbols"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:layout_constraintStart_toEndOf="@id/ivHideOther"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/line"
                android:layout_width="match_parent"
                android:layout_height="@dimen/cut_off_line_height"
                android:layout_marginTop="5dp"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                android:visibility="visible"
                app:layout_constraintTop_toBottomOf="@+id/tvHideOther" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <cn.com.vau.common.view.system.MyRecyclerView
        android:id="@+id/mRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layoutManager="cn.com.vau.common.view.WrapContentLinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mScrollView"
        tools:ignore="SpeakableTextPresentCheck" />

    <ViewStub
        android:id="@+id/mVsNoDataScroll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout="@layout/vs_layout_no_data_scroll"
        app:layout_constraintTop_toBottomOf="@id/mScrollView" />

</androidx.constraintlayout.widget.ConstraintLayout>
