<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".trade.activity.ModifiedCloseConfigurationActivity">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTip"
        android:layout_width="0dp"
        android:layout_height="45dp"
        android:background="?attr/color_c0a1e1e1e_c0affffff"
        android:gravity="center_vertical|start"
        android:paddingHorizontal="12dp"
        android:text="@string/filter_orders_based_criteria_below"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        style="@style/gilroy_500"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mHeaderBar" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/mRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toTopOf="@id/tvNext"
        app:layout_constraintTop_toBottomOf="@id/tvTip" />

    <TextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:text="@string/next"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:ignore="SpUsage"/>

</androidx.constraintlayout.widget.ConstraintLayout>