<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tvContent"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:layout_marginTop="16dp"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:text="@string/pnl_currency_displayed_you_losses_incurred"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mHeaderBar" />

</androidx.constraintlayout.widget.ConstraintLayout>