<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_top_title"
        android:text="@string/become_a_signal_provider"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mHeaderBar" />

    <TextView
        android:id="@+id/tv_task_title"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:text="@string/please_complete_the_tasks"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <TextView
        android:id="@+id/tv_task1"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginEnd="5dp"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_task1_commit"
        app:layout_constraintEnd_toStartOf="@id/tv_task1_commit"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_task1_commit"
        tools:text="@string/single_deposit_of_x_trading_account" />

    <TextView
        android:id="@+id/tv_task2"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginEnd="5dp"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:text="@string/read_and_accept_declaration"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_task2_commit"
        app:layout_constraintEnd_toStartOf="@id/tv_task2_commit"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_task2_commit" />

    <TextView
        android:id="@+id/tv_task1_commit"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:gravity="center"
        android:minWidth="80dp"
        android:paddingHorizontal="13dp"
        android:paddingVertical="8dp"
        android:text="@string/go"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_task_title"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/iv_task1_complete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="40dp"
        android:src="@drawable/bitmap_img_source_tick11x8_c00c79c"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/tv_task1_commit"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_task1_commit" />

    <TextView
        android:id="@+id/tv_task2_commit"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:gravity="center"
        android:minWidth="80dp"
        android:paddingHorizontal="13dp"
        android:paddingVertical="8dp"
        android:text="@string/accept"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_task1_commit"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/iv_task2_complete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="40dp"
        android:src="@drawable/bitmap_img_source_tick11x8_c00c79c"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/tv_task2_commit"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_task2_commit" />

    <View
        android:id="@+id/view_split"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="30dp"
        android:background="?attr/color_c331e1e1e_c33ffffff"
        app:layout_constraintTop_toBottomOf="@id/tv_task2_commit" />

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingVertical="24dp"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@id/tvNext"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_split">

        <TextView
            android:id="@+id/tv_accept_text"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/margin_horizontal_base"
            android:lineSpacingExtra="@dimen/line_spacing_extra"
            android:text="As a Signal Provider, I hereby declare and confirm that I possess extensive trading experience, knowledge, and skills, which establish my demonstrated capability to effectively serve in this role.\n\n
I acknowledge and agree to abide by the terms and conditions of copy trading. By agreeing to these terms, I accept the rules and guidelines that govern the practice of copy trading and the relationship of all parties involved.\n\n
I also pledge to conduct myself in an ethical manner, adhering to industry best practices and conducting trades with integrity. I will prioritize the interests of my copiers and strive to utilizing my expertise responsibly."
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp" />
    </ScrollView>

    <TextView
        android:id="@+id/tvNext"
        style="@style/gilroy_600"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginVertical="@dimen/margin_vertical_base"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center"
        android:text="@string/submit"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/scrollView" />
</androidx.constraintlayout.widget.ConstraintLayout>