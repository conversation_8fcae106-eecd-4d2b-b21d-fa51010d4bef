<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="16dp"
        android:paddingHorizontal="@dimen/margin_horizontal_base"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@id/tvNext"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <cn.com.vau.common.view.OpenAccountEditText
                android:id="@+id/viewEtFirstName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:can_edit="false"
                app:hint_text="@string/first_name"
                app:layout_constraintTop_toTopOf="parent"
                app:must_fill="true" />

            <cn.com.vau.common.view.OpenAccountEditText
                android:id="@+id/viewEtMiddleName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                app:can_edit="false"
                app:hint_text="@string/middle_name"
                app:layout_constraintTop_toBottomOf="@id/viewEtFirstName"
                app:must_fill="false" />

            <cn.com.vau.common.view.OpenAccountEditText
                android:id="@+id/viewEtLastName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                app:can_edit="false"
                app:hint_text="@string/last_name"
                app:layout_constraintTop_toBottomOf="@id/viewEtMiddleName"
                app:must_fill="true" />

            <TextView
                android:id="@+id/tvNameWarnTip"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                android:text="@string/the_name_need_id_not_abbreviated"
                android:textColor="@color/ce35728"
                android:textSize="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/viewEtLastName" />

            <cn.com.vau.common.view.OpenAccountEditText
                android:id="@+id/viewEtNationality"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                app:can_edit="false"
                app:hint_text="@string/nationality"
                app:layout_constraintTop_toBottomOf="@id/tvNameWarnTip"
                app:must_fill="true"
                app:show_arrow="true" />

            <cn.com.vau.common.view.OpenAccountEditText
                android:id="@+id/viewEtUnit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                app:hint_text="@string/unit_apt"
                app:layout_constraintTop_toBottomOf="@id/viewEtNationality"
                app:must_fill="true" />

            <cn.com.vau.common.view.OpenAccountEditText
                android:id="@+id/viewEtAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                app:hint_text="@string/address"
                app:layout_constraintTop_toBottomOf="@id/viewEtUnit"
                app:must_fill="true" />

            <cn.com.vau.common.view.OpenAccountEditText
                android:id="@+id/viewEtState"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                app:hint_text="@string/state"
                app:layout_constraintTop_toBottomOf="@id/viewEtAddress"
                app:must_fill="true" />

            <cn.com.vau.common.view.OpenAccountEditText
                android:id="@+id/viewEtCity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                app:hint_text="@string/city"
                app:layout_constraintTop_toBottomOf="@id/viewEtState"
                app:must_fill="true" />

            <cn.com.vau.common.view.OpenAccountEditText
                android:id="@+id/viewEtPostcode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                app:hint_text="@string/postcode"
                app:layout_constraintTop_toBottomOf="@id/viewEtCity"
                app:must_fill="true" />

            <cn.com.vau.common.view.OpenAccountDropDown
                android:id="@+id/viewDdIdType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                app:hint_text="@string/please_select_id_type"
                app:layout_constraintTop_toBottomOf="@id/viewEtPostcode"
                app:must_fill="true"
                app:title_text="@string/id_type" />

            <cn.com.vau.common.view.OpenAccountEditText
                android:id="@+id/viewEtIdSerialNum"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                app:hint_text="@string/id_serial_number"
                app:layout_constraintTop_toBottomOf="@id/viewDdIdType"
                app:must_fill="true" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/groupId3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="viewEtUnit, viewEtAddress, viewEtState, viewEtCity, viewEtPostcode" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginBottom="16dp"
        android:text="@string/next"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>