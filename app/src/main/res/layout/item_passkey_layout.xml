<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="12dp">

    <TextView
        android:id="@+id/passkeyNameTv"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@id/editIv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Passkey1 on iPhone 12 Pro New" />

    <TextView
        android:id="@+id/timeTv"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/deviceTv"
        tools:text="Added: 12/08/2024 12:46:55" />

    <TextView
        android:id="@+id/deviceTv"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/passkeyNameTv"
        tools:text="@string/current_device" />

    <ImageView
        android:id="@+id/editIv"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="12dp"
        android:contentDescription="@string/app_name"
        android:padding="4dp"
        android:src="?attr/icon2Edit16x16"
        app:layout_constraintBottom_toBottomOf="@id/deviceTv"
        app:layout_constraintEnd_toStartOf="@id/deleteIv"
        app:layout_constraintTop_toTopOf="@id/passkeyNameTv" />

    <ImageView
        android:id="@+id/deleteIv"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:contentDescription="@string/app_name"
        android:padding="4dp"
        android:src="@drawable/icon_source2_delete_optional"
        app:layout_constraintBottom_toBottomOf="@id/deviceTv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/passkeyNameTv"
        app:tint="?attr/color_c1e1e1e_cebffffff" />
</androidx.constraintlayout.widget.ConstraintLayout>