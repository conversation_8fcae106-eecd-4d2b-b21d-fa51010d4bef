<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    
    <TextView
        android:id="@+id/tvTab"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:paddingHorizontal="12dp"
        android:gravity="center_vertical"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Tick"/>

    <LinearLayout
        android:id="@+id/llPlaceHolder"
        android:layout_width="4dp"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        app:layout_constraintStart_toEndOf="@id/tvTab"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <View
            android:id="@+id/lineChartType"
            android:layout_width="0.75dp"
            android:layout_height="9dp"
            android:background="?attr/color_c1f1e1e1e_c1fffffff"
            android:visibility="gone"
            tools:visibility="visible"/>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>