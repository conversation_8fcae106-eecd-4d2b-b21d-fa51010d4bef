<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tvRiskManagement"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_horizontal_base"
        android:text="@string/risk_management"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivRiskManagementTip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:src="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
        app:layout_constraintBottom_toBottomOf="@id/tvRiskManagement"
        app:layout_constraintStart_toEndOf="@id/tvRiskManagement"
        app:layout_constraintTop_toTopOf="@id/tvRiskManagement" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivRiskManagementArrow"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:padding="10dp"
        android:src="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@id/tvRiskManagement"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvRiskManagement" />

    <TextView
        android:id="@+id/tvSLTitle"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvRiskManagement"
        tools:text="SL:" />

    <TextView
        android:id="@+id/tvSLValue"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:textColor="@color/ce35728"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@id/tvSLTitle"
        app:layout_constraintStart_toEndOf="@id/tvSLTitle"
        app:layout_constraintTop_toTopOf="@id/tvSLTitle"
        tools:text="35%" />

    <TextView
        android:id="@+id/tvTPTitle"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="40dp"
        android:layout_marginTop="8dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toEndOf="@id/tvSLTitle"
        app:layout_constraintTop_toBottomOf="@id/tvRiskManagement"
        tools:text="TP:" />

    <TextView
        android:id="@+id/tvTPValue"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:textColor="@color/c00c79c"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@id/tvTPTitle"
        app:layout_constraintStart_toEndOf="@id/tvTPTitle"
        app:layout_constraintTop_toTopOf="@id/tvTPTitle"
        tools:text="35%" />

    <TextView
        android:id="@+id/tvGSUserRiskTip"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:drawablePadding="4dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        android:visibility="gone"
        android:textAlignment="viewStart"
        android:gravity="start"
        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvSLTitle"
        tools:text="@string/to_qualify_for_the_above_x"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clSLandTP"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tvRiskManagement"
        tools:visibility="gone">

        <TextView
            android:id="@+id/tvStopLossTitle"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/stop_loss"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <cn.com.vau.common.view.EditTextVerifyComponent
            android:id="@+id/etStopLoss"
            style="@style/gilroy_500"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:inputType="number"
            android:maxLines="1"
            android:paddingStart="@dimen/padding_horizontal_base"
            android:paddingEnd="48dp"
            android:singleLine="true"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="14dp"
            app:layout_constraintTop_toBottomOf="@id/tvStopLossTitle"
            tools:ignore="Autofill,LabelFor"
            tools:text="35" />

        <TextView
            android:id="@+id/tvStopLossUnit"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/margin_horizontal_base"
            android:text="%"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="@id/etStopLoss"
            app:layout_constraintEnd_toEndOf="@id/etStopLoss"
            app:layout_constraintTop_toTopOf="@id/etStopLoss" />

        <TextView
            android:id="@+id/tvEstimatedLoss"
            style="@style/gilroy_400"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/etStopLoss"
            tools:text="Estimated Loss: 18.00 USD" />

        <cn.com.vau.common.view.CustomSeekBar
            android:id="@+id/seekStopLoss"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_vertical_base"
            app:layout_constraintTop_toBottomOf="@id/tvEstimatedLoss"
            app:most_color="@color/ce35728"
            app:seek_progress_drawable="@drawable/seekbar_progress_cff8e5c_background_c1f1e1e1e_c1fffffff"
            app:seek_thumb="@drawable/shape_oval_solid_cffffff_stroke_cff8e5c" />

        <cn.com.vau.util.widget.SwitchButton
            android:id="@+id/sbTakeProfit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="26dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/seekStopLoss"/>

        <TextView
            android:id="@+id/tvTakeProfitTitle"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/take_profit"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="@id/sbTakeProfit"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/sbTakeProfit" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clTakeProfitMask"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_vertical_base"
            android:background="?attr/mainLayoutBg"
            app:layout_constraintTop_toBottomOf="@id/sbTakeProfit">

            <cn.com.vau.common.view.EditTextVerifyComponent
                android:id="@+id/etTakeProfit"
                style="@style/gilroy_500"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:inputType="number"
                android:maxLines="1"
                android:paddingStart="@dimen/padding_horizontal_base"
                android:paddingEnd="48dp"
                android:singleLine="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
                android:textSize="14dp"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="Autofill,LabelFor"
                tools:text="35" />

            <TextView
                android:id="@+id/tvTakeProfitUnit"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:text="%"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="@id/etTakeProfit"
                app:layout_constraintEnd_toEndOf="@id/etTakeProfit"
                app:layout_constraintTop_toTopOf="@id/etTakeProfit" />

            <TextView
                android:id="@+id/tvEstimatedProfit"
                style="@style/gilroy_400"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/etTakeProfit"
                tools:text="Estimated Profit: 18.00 USD" />

            <cn.com.vau.common.view.CustomSeekBar
                android:id="@+id/seekTakeProfit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base"
                app:layout_constraintTop_toBottomOf="@id/tvEstimatedProfit"
                app:most_color="@color/c00c79c"
                app:seek_progress_drawable="@drawable/seekbar_progress_c00c79c_background_c1f1e1e1e_c1fffffff"
                app:seek_thumb="@drawable/shape_oval_solid_cffffff_stroke_c00c79c" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupSLandTP"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="tvSLTitle,tvSLValue,tvTPTitle,tvTPValue"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>