<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/new_order"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvNetworkStatus"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="38dp"
        android:paddingHorizontal="@dimen/margin_horizontal_base"
        android:background="@color/c1fff8e5c"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/mHeaderBar"
        tools:text="Network disconnected. Please try again later."
        tools:visibility="visible"/>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/mSmartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvNetworkStatus">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="193dp">
                
                <TextView
                    android:id="@+id/tvGoProduct"
                    style="@style/gilroy_600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                    android:gravity="center_vertical|start"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textDirection="ltr"
                    android:textSize="20dp"
                    android:drawablePadding="8dp"
                    app:drawableEndCompat="@drawable/draw_bitmap2_triangle_down_tab_c1e1e1e_cebffffff"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="SpUsage"
                    tools:text="USDCAD" />

<!--                <androidx.appcompat.widget.AppCompatImageView-->
<!--                    android:id="@+id/ivGoProduct"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="0dp"-->
<!--                    android:contentDescription="@string/app_name"-->
<!--                    android:paddingStart="8dp"-->
<!--                    android:src="@drawable/draw_bitmap2_triangle_down_tab_c1e1e1e_cebffffff"-->
<!--                    app:layout_constraintBottom_toBottomOf="@id/tvGoProduct"-->
<!--                    app:layout_constraintStart_toEndOf="@id/tvGoProduct"-->
<!--                    app:layout_constraintTop_toTopOf="@id/tvGoProduct"-->
<!--                    tools:ignore="RtlSymmetry" />-->

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivKline"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:paddingHorizontal="12dp"
                    android:src="@drawable/draw_bitmap2_view_chart_ca61e1e1e_c99ffffff"
                    app:layout_constraintBottom_toBottomOf="@id/tvGoProduct"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvGoProduct" />

                <LinearLayout
                    android:id="@+id/llSell"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r10"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingVertical="6dp"
                    app:layout_constraintEnd_toStartOf="@id/tvSpread"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvGoProduct">

                    <TextView
                        android:id="@+id/tvSell"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/sell"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="16dp" />

                    <TextView
                        android:id="@+id/tvSellPrice"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="4dp"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="18dp"
                        tools:text="109.575" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tvSpread"
                    style="@style/gilroy_500"
                    android:layout_width="48dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:singleLine="true"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="10dp"
                    app:layout_constraintBottom_toBottomOf="@id/llSell"
                    app:layout_constraintEnd_toStartOf="@id/llBuy"
                    app:layout_constraintStart_toEndOf="@id/llSell"
                    app:layout_constraintTop_toTopOf="@id/llSell"
                    tools:text="100" />

                <LinearLayout
                    android:id="@+id/llBuy"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="12dp"
                    android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingVertical="6dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/tvSpread"
                    app:layout_constraintTop_toBottomOf="@id/tvGoProduct">

                    <TextView
                        android:id="@+id/tvBuy"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/buy"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="16dp" />

                    <TextView
                        android:id="@+id/tvBuyPrice"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="4dp"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="18dp"
                        tools:text="109.575" />

                </LinearLayout>

                <View
                    android:id="@+id/viewMarketExecution"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                    android:layout_marginTop="12dp"
                    android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r10"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/llSell" />

                <TextView
                    android:id="@+id/tvMarketExecution"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_marginStart="12dp"
                    android:gravity="center_vertical"
                    android:text="@string/market_execution"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14sp"
                    android:drawablePadding="4dp"
                    app:layout_constraintBottom_toBottomOf="@id/viewMarketExecution"
                    app:layout_constraintStart_toStartOf="@id/viewMarketExecution"
                    app:layout_constraintTop_toTopOf="@id/viewMarketExecution"
                    tools:ignore="RtlSymmetry" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivMarketIntroduce"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp"
                    android:paddingStart="4dp"
                    android:paddingEnd="8dp"
                    android:src="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                    app:layout_constraintBottom_toBottomOf="@id/viewMarketExecution"
                    app:layout_constraintStart_toEndOf="@id/tvMarketExecution"
                    app:layout_constraintTop_toTopOf="@id/viewMarketExecution" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivMarketDown"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="12dp"
                    android:src="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
                    app:layout_constraintBottom_toBottomOf="@id/viewMarketExecution"
                    app:layout_constraintEnd_toEndOf="@id/viewMarketExecution"
                    app:layout_constraintTop_toTopOf="@id/viewMarketExecution" />


<!--                <View-->
<!--                    android:id="@+id/line6"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="0.5dp"-->
<!--                    android:layout_marginTop="12dp"-->
<!--                    android:background="?attr/color_c1f1e1e1e_c1fffffff"-->
<!--                    app:layout_constraintTop_toBottomOf="@id/viewMarketExecution" />-->


                <androidx.constraintlayout.widget.Group
                    android:id="@+id/gpAtPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="clAtPrice,tvAtPriceTip,tvDistanceTitle,tvDistance,tvExpirationTitle,tvExpiration"
                    tools:visibility="visible"/>



                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clAtPrice"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginHorizontal="12dp"
                    android:layout_marginTop="12dp"
                    android:background="@drawable/select_login_et_bg"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/viewMarketExecution">

                    <TextView
                        android:id="@+id/tvAtPriceTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:text="@string/at_price"
                        android:textColor="?attr/color_c731e1e1e_c61ffffff"
                        android:textSize="10dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toTopOf="@id/etAtPrice"
                        app:layout_constraintVertical_chainStyle="packed"/>

                    <TextView
                        android:id="@+id/tvAtPriceRange"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="?attr/color_c731e1e1e_c61ffffff"
                        android:textSize="10dp"
                        android:layout_marginStart="4dp"
                        app:layout_constraintBaseline_toBaselineOf="@id/tvAtPriceTitle"
                        app:layout_constraintStart_toEndOf="@id/tvAtPriceTitle"
                        tools:text="1.39003" />

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/etAtPrice"
                        style="@style/gilroy_500"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:background="@null"
                        android:hint="@string/not_set"
                        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
                        android:inputType="numberDecimal"
                        android:singleLine="true"
                        android:text="0.01"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        android:layout_marginEnd="40dp"
                        android:paddingTop="4dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/ivAtPriceSub"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvAtPriceTitle"
                        tools:text="1.38810" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivAtPriceAdd"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="6dp"
                        android:padding="6dp"
                        android:src="@drawable/draw_bitmap2_add_c1e1e1e_cebffffff"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:id="@+id/line7"
                        android:layout_width="0.5dp"
                        android:layout_height="20dp"
                        android:layout_marginHorizontal="6dp"
                        android:background="?attr/color_c331e1e1e_c33ffffff"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/ivAtPriceAdd"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivAtPriceSub"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="6dp"
                        android:padding="6dp"
                        android:src="?attr/icon2Sub"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/line7"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/tvAtPriceTip"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="8dp"
                    android:textColor="@color/cf44040"
                    android:textSize="12dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/clAtPrice"
                    tools:text="Max value: 250000.00 USD"/>

                <TextView
                    android:id="@+id/tvDistanceTitle"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/distance"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:layout_constraintStart_toStartOf="@id/clAtPrice"
                    app:layout_constraintTop_toBottomOf="@id/clAtPrice"
                    tools:layout_constraintTop_toBottomOf="@id/tvAtPriceTip"/>

                <TextView
                    android:id="@+id/tvDistance"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="12dp"
                    app:layout_constraintBaseline_toBaselineOf="@id/tvDistanceTitle"
                    app:layout_constraintEnd_toEndOf="@id/clAtPrice"
                    tools:text="12 points" />

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/atPriceBarrier"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="tvDistanceTitle,tvAtPriceTip"/>


                <androidx.constraintlayout.widget.Group
                    android:id="@+id/gpStopLimitPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="clStopLimitPrice,tvStopLimitTip"
                    tools:visibility="visible"/>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clStopLimitPrice"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginHorizontal="12dp"
                    android:layout_marginTop="12dp"
                    android:background="@drawable/select_login_et_bg"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/atPriceBarrier">

                    <TextView
                        android:id="@+id/tvStopLimitPriceTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:text="@string/stop_limit_price"
                        android:textColor="?attr/color_c731e1e1e_c61ffffff"
                        android:textSize="10dp"
                        app:layout_constraintVertical_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toTopOf="@id/etStopLimitPrice"/>

                    <TextView
                        android:id="@+id/tvStopLimitPriceRange"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="?attr/color_c731e1e1e_c61ffffff"
                        android:textSize="10dp"
                        android:layout_marginStart="4dp"
                        app:layout_constraintBaseline_toBaselineOf="@id/tvStopLimitPriceTitle"
                        app:layout_constraintStart_toEndOf="@id/tvStopLimitPriceTitle"
                        tools:text="1.39003" />

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/etStopLimitPrice"
                        style="@style/gilroy_500"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:background="@null"
                        android:hint="@string/not_set"
                        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
                        android:inputType="numberDecimal"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        android:singleLine="true"
                        android:layout_marginEnd="40dp"
                        android:paddingTop="4dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/ivStopLimitPriceSub"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvStopLimitPriceTitle"
                        tools:text="1.38810" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivStopLimitPriceAdd"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="6dp"
                        android:padding="6dp"
                        android:src="@drawable/draw_bitmap2_add_c1e1e1e_cebffffff"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:id="@+id/line9"
                        android:layout_width="0.5dp"
                        android:layout_height="20dp"
                        android:layout_marginHorizontal="6dp"
                        android:background="?attr/color_c1f1e1e1e_c1fffffff"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/ivStopLimitPriceAdd"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivStopLimitPriceSub"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="6dp"
                        android:padding="6dp"
                        android:src="?attr/icon2Sub"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/line9"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/tvStopLimitTip"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="8dp"
                    android:visibility="gone"
                    android:textColor="@color/cf44040"
                    android:textSize="12dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/clStopLimitPrice"
                    tools:text="Max value: 250000.00 USD"/>

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/stopLimitBarrier"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="tvStopLimitTip,clStopLimitPrice"/>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clVolume"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                    android:layout_marginTop="12dp"
                    android:background="@drawable/select_login_et_bg"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/stopLimitBarrier">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivVolumeDown"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="12dp"
                        android:src="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="@id/clVolume"
                        app:layout_constraintEnd_toEndOf="@id/clVolume"
                        app:layout_constraintTop_toTopOf="@id/clVolume" />

                    <TextView
                        android:id="@+id/tvVolumeTitle"
                        style="@style/gilroy_500"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:text="@string/volume"
                        android:textSize="10dp"
                        android:gravity="start"
                        android:textAlignment="viewStart"
                        android:textColor="?attr/color_c731e1e1e_c61ffffff"
                        app:layout_constraintBottom_toTopOf="@id/etVolume"
                        app:layout_constraintStart_toStartOf="@id/clVolume"
                        app:layout_constraintEnd_toEndOf="@id/etVolume"
                        app:layout_constraintTop_toTopOf="@id/clVolume"
                        app:layout_constraintVertical_chainStyle="packed" />

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/etVolume"
                        style="@style/gilroy_500"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:paddingTop="4dp"
                        android:background="@null"
                        android:inputType="numberDecimal"
                        android:singleLine="true"
                        android:hint="@string/not_set"
                        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
                        android:textSize="14dp"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:layout_marginEnd="20dp"
                        app:layout_constraintBottom_toBottomOf="@id/clVolume"
                        app:layout_constraintEnd_toStartOf="@id/ivVolumeSub"
                        app:layout_constraintHorizontal_bias="0.0"
                        app:layout_constraintStart_toStartOf="@id/clVolume"
                        app:layout_constraintTop_toBottomOf="@id/tvVolumeTitle" />

                    <View
                        android:id="@+id/line1"
                        android:layout_width="0.5dp"
                        android:layout_height="20dp"
                        android:layout_marginEnd="120dp"
                        android:background="?attr/color_c1f1e1e1e_c1fffffff"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="@id/clVolume"
                        app:layout_constraintEnd_toEndOf="@id/clVolume"
                        app:layout_constraintTop_toTopOf="@id/clVolume" />


                    <TextView
                        android:id="@+id/tvVolumeUnit"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginStart="12dp"
                        android:text="@string/lots"
                        android:gravity="center_vertical|start"
                        android:textAlignment="viewStart"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="@id/clVolume"
                        app:layout_constraintStart_toEndOf="@id/line1"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/clVolume" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivVolumeAdd"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:paddingStart="10dp"
                        android:paddingEnd="12dp"
                        android:layout_marginStart="2dp"
                        android:src="@drawable/draw_bitmap2_add_c1e1e1e_cebffffff"
                        app:layout_constraintBottom_toBottomOf="@id/clVolume"
                        app:layout_constraintEnd_toStartOf="@id/line1"
                        app:layout_constraintTop_toTopOf="@id/clVolume" />

<!--                    <View-->
<!--                        android:id="@+id/line"-->
<!--                        android:layout_width="0.5dp"-->
<!--                        android:layout_height="20dp"-->
<!--                        android:background="?attr/color_c1f1e1e1e_c1fffffff"-->
<!--                        app:layout_constraintBottom_toBottomOf="@id/clVolume"-->
<!--                        app:layout_constraintEnd_toStartOf="@id/ivVolumeAdd"-->
<!--                        app:layout_constraintTop_toTopOf="@id/clVolume" />-->

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivVolumeSub"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:paddingStart="12dp"
                        android:paddingEnd="10dp"
                        android:layout_marginEnd="2dp"
                        android:src="?attr/icon2Sub"
                        app:layout_constraintBottom_toBottomOf="@id/clVolume"
                        app:layout_constraintEnd_toStartOf="@id/ivVolumeAdd"
                        app:layout_constraintTop_toTopOf="@id/clVolume" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/tvTransfer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="8dp"
                    android:textSize="12dp"
                    android:textColor="?color_cebffffff_c1e1e1e"
                    android:background="@drawable/draw_shape_c1e1e1e_cebffffff_r4"
                    app:layout_constraintStart_toStartOf="@id/clVolume"
                    app:layout_constraintBottom_toTopOf="@id/clVolume"
                    android:visibility="gone"
                    tools:text="≈ 1000 USD"
                    tools:visibility="visible"/>

                <TextView
                    android:id="@+id/tvVolumeTip"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="8dp"
                    android:textColor="@color/cf44040"
                    android:textSize="12dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/clVolume"
                    tools:text="Max value: 250000.00"/>

                <cn.com.vau.common.view.custom.VolSeekBar
                    android:id="@+id/volSeekBar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="12dp"
                    android:layout_marginTop="-8dp"
                    android:paddingBottom="9dp"
                    app:layout_constraintTop_toBottomOf="@id/tvVolumeTip" />

                <TextView
                    android:id="@+id/tvMinOpen"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    style="@style/gilroy_500"
                    android:textSize="10dp"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    app:layout_constraintTop_toBottomOf="@id/volSeekBar"
                    app:layout_constraintStart_toStartOf="@id/volSeekBar"/>

                <TextView
                    android:id="@+id/tvMaxOpen"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="10dp"
                    style="@style/gilroy_500"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    app:layout_constraintTop_toBottomOf="@id/volSeekBar"
                    app:layout_constraintEnd_toEndOf="@id/volSeekBar"
                    tools:text="Max open 2.50"/>

                <TextView
                    android:id="@+id/tvMarginTitle"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_horizontal_base"
                    android:layout_marginTop="6dp"
                    android:paddingTop="6dp"
                    android:drawablePadding="4dp"
                    android:text="@string/margin"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvMinOpen" />

                <TextView
                    android:id="@+id/tvMargin"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="12dp"
                    android:textDirection="ltr"
                    app:layout_constraintBaseline_toBaselineOf="@id/tvMarginTitle"
                    app:layout_constraintEnd_toStartOf="@id/line"
                    tools:text="2.00 USD" />

                <TextView
                    android:id="@+id/line"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    style="@style/gilroy_400"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="12dp"
                    app:layout_constraintBaseline_toBaselineOf="@id/tvMarginTitle"
                    app:layout_constraintEnd_toStartOf="@id/tvFreeMargin"
                    android:text="/"
                    tools:ignore="HardcodedText" />

                <TextView
                    android:id="@+id/tvFreeMargin"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="12dp"
                    android:textDirection="ltr"
                    app:layout_constraintBaseline_toBaselineOf="@id/tvMarginTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    tools:text="500.00 USD" />

                <TextView
                    android:id="@+id/tvMarginLevelTitle"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_horizontal_base"
                    android:layout_marginTop="12dp"
                    android:drawablePadding="4dp"
                    android:text="@string/margin_level_after_trading"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvMarginTitle" />

                <TextView
                    android:id="@+id/tvMarginLevel"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="12dp"
                    android:textDirection="ltr"
                    app:layout_constraintBaseline_toBaselineOf="@id/tvMarginLevelTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    tools:text="-43229.62%" />

                <TextView
                    android:id="@+id/tvExpirationTitle"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:text="@string/expiration"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:layout_constraintStart_toStartOf="@id/clAtPrice"
                    app:layout_constraintTop_toBottomOf="@id/tvMarginLevel"/>
                <TextView
                    android:id="@+id/tvExpiration"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/good_till_cancelled"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="12dp"
                    app:layout_constraintBaseline_toBaselineOf="@id/tvExpirationTitle"
                    app:layout_constraintEnd_toEndOf="@id/clAtPrice" />

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/volumeBarrier"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="tvMarginLevelTitle,tvExpirationTitle"/>

                <View
                    android:id="@+id/line2"
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginTop="12dp"
                    android:background="?attr/color_c1f1e1e1e_c1fffffff"
                    app:layout_constraintTop_toBottomOf="@id/volumeBarrier" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivTakeProfitChecked"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_marginStart="4dp"
                    android:layout_marginTop="6.5dp"
                    android:padding="8dp"
                    android:src="@drawable/draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/line2" />

                <TextView
                    android:id="@+id/tvTakeProfitTitle"
                    style="@style/gilroy_600"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingTop="12dp"
                    android:text="@string/take_profit"
                    android:gravity="start"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/ivTakeProfitChecked"
                    app:layout_constraintTop_toBottomOf="@id/line2" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/gpTakeProfit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="clTakeProfit,tvTakeProfitTip,tvEstimatedProfitTitle,tvEstimatedProfit"
                    tools:visibility="visible" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clTakeProfit"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginHorizontal="12dp"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/select_login_et_bg"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvTakeProfitTitle">

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/etTakeProfit"
                        style="@style/gilroy_500"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginStart="12dp"
                        android:background="@null"
                        android:hint="@string/not_set"
                        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
                        android:inputType="numberDecimal"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        android:singleLine="true"
                        android:layout_marginEnd="40dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/ivTakeProfitSub"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="1.38810" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivTakeProfitAdd"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="6dp"
                        android:padding="6dp"
                        android:src="@drawable/draw_bitmap2_add_c1e1e1e_cebffffff"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:id="@+id/line3"
                        android:layout_width="0.5dp"
                        android:layout_height="20dp"
                        android:layout_marginHorizontal="6dp"
                        android:background="?attr/color_c1f1e1e1e_c1fffffff"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/ivTakeProfitAdd"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivTakeProfitSub"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="6dp"
                        android:padding="6dp"
                        android:src="?attr/icon2Sub"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/line3"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/tvTakeProfitTip"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="8dp"
                    android:textColor="@color/cf44040"
                    android:textSize="12dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/clTakeProfit"
                    tools:visibility="visible"
                    tools:text="Max value: 250000.00 USD"/>

                <TextView
                    android:id="@+id/tvEstimatedProfitTitle"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/estimated_profit"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/clTakeProfit"
                    app:layout_constraintTop_toBottomOf="@id/clTakeProfit"
                    tools:layout_constraintTop_toBottomOf="@id/tvTakeProfitTip"/>

                <TextView
                    android:id="@+id/tvEstimatedProfit"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="12dp"
                    android:textDirection="ltr"
                    app:layout_constraintBaseline_toBaselineOf="@id/tvEstimatedProfitTitle"
                    app:layout_constraintEnd_toEndOf="@id/clTakeProfit"
                    tools:text="1.39 USD" />

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/takeProfitBarrier"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="tvEstimatedProfitTitle,tvTakeProfitTip"/>

<!--                <View-->
<!--                    android:id="@+id/line4"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="0.5dp"-->
<!--                    android:layout_marginTop="12dp"-->
<!--                    android:background="?attr/color_c1f1e1e1e_c1fffffff"-->
<!--                    app:layout_constraintTop_toBottomOf="@id/takeProfitBarrier" />-->

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivStopLessChecked"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_marginStart="4dp"
                    android:layout_marginTop="10.5dp"
                    android:padding="8dp"
                    android:src="@drawable/draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/takeProfitBarrier" />

                <TextView
                    android:id="@+id/tvStopLessTitle"
                    style="@style/gilroy_600"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingTop="16dp"
                    android:text="@string/stop_loss"
                    android:gravity="start"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:layout_constraintStart_toEndOf="@id/ivStopLessChecked"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/takeProfitBarrier" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/gpStopLoss"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="clStopLoss,tvStopLossTip,tvEstimatedLossTitle,tvEstimatedLoss"
                    tools:visibility="visible" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clStopLoss"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginHorizontal="12dp"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/select_login_et_bg"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvStopLessTitle">

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/etStopLoss"
                        style="@style/gilroy_500"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginStart="12dp"
                        android:background="@null"
                        android:hint="@string/not_set"
                        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
                        android:inputType="numberDecimal"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        android:singleLine="true"
                        android:layout_marginEnd="40dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/ivStopLossSub"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="1.38810" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivStopLossAdd"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="6dp"
                        android:padding="6dp"
                        android:src="@drawable/draw_bitmap2_add_c1e1e1e_cebffffff"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:id="@+id/line5"
                        android:layout_width="0.5dp"
                        android:layout_height="20dp"
                        android:layout_marginHorizontal="6dp"
                        android:background="?attr/color_c1f1e1e1e_c1fffffff"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/ivStopLossAdd"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivStopLossSub"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="6dp"
                        android:padding="6dp"
                        android:src="?attr/icon2Sub"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/line5"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/tvStopLossTip"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="8dp"
                    android:visibility="gone"
                    android:textColor="@color/cf44040"
                    android:textSize="12dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/clStopLoss"
                    tools:text="Max value: 250000.00 USD"/>

                <TextView
                    android:id="@+id/tvEstimatedLossTitle"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/estimated_loss"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/clStopLoss"
                    app:layout_constraintTop_toBottomOf="@id/clStopLoss"
                    tools:layout_constraintTop_toBottomOf="@id/tvStopLossTip" />

                <TextView
                    android:id="@+id/tvEstimatedLoss"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="12dp"
                    android:textDirection="ltr"
                    app:layout_constraintBaseline_toBaselineOf="@id/tvEstimatedLossTitle"
                    app:layout_constraintEnd_toEndOf="@id/clStopLoss"
                    tools:text="1.39 USD" />

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/stopLossBarrier"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="tvEstimatedLossTitle,tvStopLossTip"/>

                <View
                    android:id="@+id/line10"
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginTop="12dp"
                    android:background="?attr/color_c1f1e1e1e_c1fffffff"
                    app:layout_constraintTop_toBottomOf="@id/stopLossBarrier" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/mainLayoutBg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <cn.com.vau.trade.view.TimeSharingChartView
            android:id="@+id/mTimeChartView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="@id/tvNext"
            tools:visibility="gone"/>

        <TextView
            android:id="@+id/tvNext"
            style="@style/gilroy_600"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginHorizontal="@dimen/margin_horizontal_base"
            android:layout_marginBottom="16dp"
            android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
            android:gravity="center"
            android:text="@string/buy"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>