<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/c1e1e1e"
    android:paddingHorizontal="@dimen/padding_horizontal_base">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/mImageFilterView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="35dp"
        android:maxHeight="80dp"
        android:src="@drawable/img_asic_refresh"
        app:layout_constraintBottom_toTopOf="@+id/tvTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/bold_semi_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:text="@string/uploading_documents"
        android:textColor="@color/cffffff"
        android:textSize="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvOpenAccountHint"
        style="@style/regular_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:gravity="center"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:text="@string/this_may_take_a_moment"
        android:textColor="@color/cffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <TextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_button_main"
        android:background="@drawable/shape_cffffff_r100"
        android:text="@string/ok"
        android:textColor="@color/c1e1e1e"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_h50"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.5" />

</androidx.constraintlayout.widget.ConstraintLayout>