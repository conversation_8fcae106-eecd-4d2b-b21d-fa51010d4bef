<?xml version="1.0" encoding="utf-8"?>
<com.scwang.smart.refresh.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mRefreshLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    app:srlEnableLoadMore="false">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingBottom="@dimen/margin_bottom_layout">

            <ViewStub
                android:id="@+id/mVsBanner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout="@layout/vs_layout_discover_copy_trading_banner"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

            <include
                android:id="@+id/layoutCopyTrading"
                layout="@layout/include_copytrading_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base_new"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/mVsBanner"
                tools:visibility="visible" />

            <ViewStub
                android:id="@+id/mVsNoDataScroll"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout="@layout/vs_layout_no_data_scroll"
                app:layout_constraintTop_toTopOf="parent" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</com.scwang.smart.refresh.layout.SmartRefreshLayout>