<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <cn.com.vau.common.view.ClearAndHideEditText
        android:id="@+id/etNickname"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_top_title"
        app:etFontFamily="regular"
        app:etTextSize="14"
        app:hint="@string/enter_4_x_characters"
        app:inputType="text"
        app:is_show="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar"
        app:textColor="?attr/color_c1e1e1e_cebffffff"
        app:textColorHint="?attr/color_c731e1e1e_c61ffffff" />

    <TextView
        android:id="@+id/tvNickLength"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="8dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="@id/etNickname"
        app:layout_constraintTop_toBottomOf="@id/etNickname"
        tools:text="100/100" />

    <TextView
        android:id="@+id/tvSave"
        style="@style/main_bottom_button_theme"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_button_main"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:gravity="center"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="24dp"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="16dp"
        android:text="@string/save"
        app:layout_constraintTop_toBottomOf="@id/tvNickLength" />

</androidx.constraintlayout.widget.ConstraintLayout>
