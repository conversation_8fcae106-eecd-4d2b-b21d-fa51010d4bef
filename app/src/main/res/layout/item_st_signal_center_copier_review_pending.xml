<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="@dimen/padding_card_base"
    tools:ignore="ContentDescription">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivAvatar"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="2dp"
        android:scaleType="centerCrop"
        android:src="@mipmap/ic_launcher"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvNick"
        app:shapeAppearanceOverlay="@style/circleImageStyle" />

    <TextView
        android:id="@+id/tvNick"
        style="@style/gilroy_600"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivAvatar"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Signal Provider Name" />

    <TextView
        android:id="@+id/tvTime"
        style="@style/gilroy_400"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:layout_marginBottom="-2dp"
        android:gravity="start"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tvNick"
        app:layout_constraintTop_toBottomOf="@+id/tvNick"
        tools:text="Applied on 13/03/2024 11:12:00" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/mRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="vertical"
        android:overScrollMode="never"
        app:layoutManager="cn.com.vau.common.view.WrapContentLinearLayoutManager"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTime"
        tools:itemCount="3"
        tools:listitem="@layout/item_st_signal_center_copier_review_pending_child" />

    <TextView
        android:id="@+id/tvViewMore"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="4dp"
        android:paddingVertical="8dp"
        android:text="@string/view_x_more"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        android:visibility="visible"
        app:layout_constraintEnd_toStartOf="@+id/ivArrow"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mRecyclerView" />

    <ImageView
        android:id="@+id/ivArrow"
        android:layout_width="10dp"
        android:layout_height="6dp"
        android:src="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/tvViewMore"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvViewMore"
        app:layout_constraintTop_toTopOf="@+id/tvViewMore" />

    <TextView
        android:id="@+id/tvReject"
        style="@style/gilroy_600"
        android:layout_width="0px"
        android:layout_height="32dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="6dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center"
        android:text="@string/reject"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tvApprove"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvViewMore"
        app:layout_goneMarginTop="12dp" />

    <TextView
        android:id="@+id/tvApprove"
        style="@style/gilroy_600"
        android:layout_width="0px"
        android:layout_height="32dp"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center"
        android:text="@string/approve"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvReject"
        app:layout_constraintTop_toTopOf="@+id/tvReject" />
</androidx.constraintlayout.widget.ConstraintLayout>