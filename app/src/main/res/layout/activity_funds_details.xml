<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        style="@style/cut_off_line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="@dimen/margin_top_title" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/margin_horizontal_base">

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp">

                <TextView
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/name_user"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/tv_Name"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    tools:text="@string/name_user" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/account"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/tv_Account"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textAlignment="viewEnd"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    tools:text="@string/account" />

            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp">

                <TextView
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/time"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/tvTime"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    tools:text="@string/time" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp">

                <TextView
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/amount"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/tv_Amount"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:text="0.00"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp">

                <TextView
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/currency"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/tv_Currency"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:text=""
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    tools:text="AUD" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvFundMethod"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/payment_method"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/tv_PaymentMethod"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:gravity="end|center_vertical"
                    android:singleLine="true"
                    android:text="-"
                    android:textAlignment="viewEnd"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <TextView
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    android:layout_weight="1"
                    android:text="@string/status"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/tv_Status"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textAlignment="viewEnd"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp" />
            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="10dp"
                android:paddingBottom="10dp">

                <TextView
                    android:id="@+id/tvFail"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/processed_notes"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_ProcessedNotes"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:gravity="end"
                    android:text="-"
                    android:textAlignment="viewEnd"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/tvFail"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp">

                <TextView
                    android:id="@+id/tvTransactionsTitle"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/transaction_number"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="SpUsage" />

                <TextView
                    android:id="@+id/tv_Code"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:text="0000000000000"
                    android:textAlignment="viewEnd"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/tvTransactionsTitle"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="SpUsage" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

    </ScrollView>

    <TextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:text="@string/cancel"
        android:visibility="invisible"
        tools:visibility="visible" />

</LinearLayout>
