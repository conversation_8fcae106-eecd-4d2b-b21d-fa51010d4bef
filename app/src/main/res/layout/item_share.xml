<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivIcon"
        android:layout_width="56dp"
        android:layout_height="44dp"
        android:paddingHorizontal="6dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="16dp"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:ellipsize="end"
        android:gravity="bottom|center_horizontal"
        android:maxLines="1"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="@+id/ivIcon"
        app:layout_constraintStart_toStartOf="@+id/ivIcon"
        app:layout_constraintTop_toBottomOf="@id/ivIcon"
        tools:text="Copy Link" />
</androidx.constraintlayout.widget.ConstraintLayout>