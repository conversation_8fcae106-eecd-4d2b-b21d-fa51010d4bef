<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/help_center"
        app:layout_constraintTop_toTopOf="parent" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/mSmartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv1"
                style="@style/gilroy_600"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="@dimen/margin_top_title"
                android:gravity="start"
                android:text="@string/hello_welcome_to_x"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="18dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv2"
                style="@style/gilroy_400"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="4dp"
                android:gravity="start"
                android:text="@string/please_choose_an_list_below"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="10dp"
                app:layout_constraintTop_toBottomOf="@+id/tv1" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ctlFaqs"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:paddingHorizontal="@dimen/margin_horizontal_base"
                app:layout_constraintTop_toBottomOf="@+id/tv2">

                <ImageView
                    android:id="@+id/ivFaq"
                    android:layout_width="20dp"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/app_name"
                    android:src="?attr/imgHelpFaqs"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/faqs"
                    style="@style/gilroy_600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_horizontal_base"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/ivFaq"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="FAQs" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ctlCs"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:paddingHorizontal="@dimen/margin_horizontal_base"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@+id/ctlFaqs"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/ivCs"
                    android:layout_width="20dp"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/app_name"
                    android:scaleType="fitXY"
                    android:src="?attr/icon1Cs"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/margin_horizontal_base"
                    android:layout_toEndOf="@id/ivCs"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/ivCs"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/cs"
                        style="@style/gilroy_600"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="start"
                        android:textAlignment="viewStart"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="16dp"
                        tools:text="24-hour Customer Support" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/csHint"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:lines="1"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="12dp"
                        tools:text="(Mon-Fri 00:00-23:59 GMT+10)" />
                </LinearLayout>

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ctlContactUs"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:paddingHorizontal="@dimen/margin_horizontal_base"
                app:layout_constraintTop_toBottomOf="@+id/ctlCs">

                <ImageView
                    android:id="@+id/ivContactUs"
                    android:layout_width="20dp"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/app_name"
                    android:src="?attr/imgHelpContactUs"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/contactUs"
                    style="@style/gilroy_600"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_horizontal_base"
                    android:gravity="start"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/ivContactUs"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="Contact Us" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>