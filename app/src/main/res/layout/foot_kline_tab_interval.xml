<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingHorizontal="8dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tvTab"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:text="@string/more"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivArrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="3dp"
        android:src="@drawable/draw_bitmap2_triangle_down_tab_ca61e1e1e_c99ffffff"
        app:layout_constraintStart_toEndOf="@id/tvTab"
        app:layout_constraintTop_toTopOf="@id/tvTab"
        app:layout_constraintBottom_toBottomOf="@id/tvTab"/>

</androidx.constraintlayout.widget.ConstraintLayout>