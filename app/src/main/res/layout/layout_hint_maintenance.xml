<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:parentTag="androidx.core.widget.NestedScrollView">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="@dimen/margin_horizontal_base"
        android:paddingEnd="@dimen/margin_horizontal_base"
        android:paddingBottom="50dp"
        android:translationZ="10dp">

        <ImageView
            android:id="@+id/ivMaintenance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="112dp"
            android:contentDescription="@string/app_name"
            android:scaleType="centerCrop"
            android:src="?attr/bgServerMaintenance"
            app:layout_constraintBottom_toTopOf="@+id/tvTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ivMaintenance"
            tools:text="@string/server_maintenance" />

        <TextView
            android:id="@+id/tvContent"
            style="@style/gilroy_400"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:layout_marginTop="9dp"
            android:layout_marginEnd="15dp"
            android:gravity="center"
            android:lineSpacingExtra="@dimen/line_spacing_extra"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle"
            tools:text="@string/maintenance_dialog_content_1" />

        <TextView
            android:id="@+id/tvMaintenanceTime"
            style="@style/gilroy_400"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="15dp"
            android:gravity="center"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvContent"
            tools:text="9th Sep 05:00 to 10th Sep 05:00 (GMT+8)" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>
