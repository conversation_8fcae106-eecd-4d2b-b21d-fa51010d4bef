<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/padding_horizontal_base">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:paddingTop="4dp"
        android:paddingBottom="@dimen/padding_vertical_base"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.cardview.widget.CardView
            android:id="@+id/mCardView"
            android:layout_width="112dp"
            android:layout_height="70dp"
            app:cardBackgroundColor="?attr/color_c1f1e1e1e_c1fffffff"
            app:cardCornerRadius="10dp"
            app:cardElevation="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/mImageView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="0.5dp"
                android:background="@drawable/draw_shape_stroke_c331e1e1e_c33ffffff_r10"
                android:scaleType="fitXY"
                app:shapeAppearanceOverlay="@style/roundImageStyle10"
                tools:src="@drawable/shape_placeholder" />
        </androidx.cardview.widget.CardView>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            style="@style/gilroy_600"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/padding_horizontal_base"
            android:ellipsize="end"
            android:gravity="center_vertical|start"
            android:maxLines="1"
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/mCardView"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/your_account_must_leverag" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvInfo"
            style="@style/gilroy_500"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tvTitle"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvHistoryDate"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tvInfo"
            app:layout_constraintTop_toBottomOf="@+id/tvInfo"
            tools:text="22/10/2021 00:20"
            tools:visibility="visible" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvVideoLength"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            android:visibility="gone"
            app:layout_constraintBaseline_toBaselineOf="@+id/tvHistoryDate"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="32.35"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/cut_off_line_height"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
