<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <cn.com.vau.util.widget.HeaderBar
            android:id="@+id/mHeaderBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:hb_endIcon="?attr/icon1Cs"
            app:hb_titleText="@string/deposit_details"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clOrderInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/margin_horizontal_base"
            android:layout_marginTop="@dimen/margin_top_title"
            app:layout_constraintTop_toBottomOf="@id/mHeaderBar">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivStatus"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginTop="72dp"
                android:visibility="invisible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="?attr/imgAlertFail"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvOrderAmount"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="26dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="26dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ivStatus"
                tools:text="50.00 USD" />

            <TextView
                android:id="@+id/tvOrderStatus"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvOrderAmount"
                tools:text="Deposit processing" />

            <TextView
                android:id="@+id/tvOrderExpired"
                style="@style/gilroy_400"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="@dimen/padding_vertical_base"
                android:gravity="center"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvOrderStatus"
                tools:text="Order Expired (3hrs), Auto Failed" />

            <TextView
                android:id="@+id/tvOrderExpiredTime"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="26dp"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvOrderExpired"
                tools:text="21/06/2023 11:56:03" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clDepositInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/margin_horizontal_base"
            android:layout_marginTop="@dimen/padding_vertical_base"
            android:background="@drawable/draw_main_card"
            app:layout_constraintTop_toBottomOf="@id/clOrderInfo">

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/gl35"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.35" />

            <View
                android:id="@+id/split1"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="51dp"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvMethodTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:gravity="start"
                android:maxWidth="100dp"
                android:maxLines="2"
                android:text="@string/method"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintBottom_toBottomOf="@id/split1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:gravity="center_vertical|end"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="@id/split1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/tvMethodTitle"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivMethodIcon"
                    android:layout_width="wrap_content"
                    android:layout_height="20dp"
                    android:layout_marginHorizontal="5dp"
                    tools:src="@drawable/img_visa_card" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvMethod"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/margin_horizontal_base"
                    android:ellipsize="end"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textAlignment="viewEnd"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    tools:text="Credit/Debit Credit/Debit Credit/Debit Credit/Debit Credit/Debit Credit/Debit " />

            </LinearLayout>

            <View
                android:id="@+id/split2"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="51dp"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintTop_toBottomOf="@id/split1" />

            <TextView
                android:id="@+id/tvOrderNumberTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:text="@string/order_number"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintBottom_toBottomOf="@id/split2"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/split1" />

            <TextView
                android:id="@+id/tvOrderNumber"
                style="@style/gilroy_500"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:ellipsize="end"
                android:gravity="end"
                android:maxLines="1"
                android:textAlignment="viewEnd"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="@id/split2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvOrderNumberTitle"
                app:layout_constraintTop_toTopOf="@id/split1"
                tools:text="VTSG********2023062115602" />

            <View
                android:id="@+id/split3"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="51dp"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintTop_toBottomOf="@id/split2" />

            <TextView
                android:id="@+id/tvUserNameTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:text="@string/username"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintBottom_toBottomOf="@id/split3"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/split2" />

            <TextView
                android:id="@+id/tvUserNeme"
                style="@style/gilroy_500"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:ellipsize="end"
                android:gravity="end"
                android:maxLines="1"
                android:textAlignment="viewEnd"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="@id/split3"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvUserNameTitle"
                app:layout_constraintTop_toTopOf="@id/split2"
                tools:text="Test" />

            <View
                android:id="@+id/split4"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="51dp"
                app:layout_constraintTop_toBottomOf="@id/split3" />

            <TextView
                android:id="@+id/tvAccountTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:text="@string/account"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintBottom_toBottomOf="@id/split4"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/split3" />

            <TextView
                android:id="@+id/tvAccount"
                style="@style/gilroy_500"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:ellipsize="end"
                android:gravity="end"
                android:maxLines="1"
                android:textAlignment="viewEnd"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="@id/split4"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvAccountTitle"
                app:layout_constraintTop_toTopOf="@id/split3"
                tools:text="********" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tvNext"
            style="@style/main_bottom_button_theme"
            android:layout_marginHorizontal="@dimen/margin_horizontal_base"
            android:onClickListener="@{onClickListener}"
            android:text="@string/retry"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>