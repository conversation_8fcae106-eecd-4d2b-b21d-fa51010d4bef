<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingHorizontal="@dimen/margin_horizontal_base">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="18dp"
            tools:text="Title 1" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivData"
            android:layout_width="match_parent"
            android:layout_height="260dp"
            android:layout_marginTop="@dimen/margin_vertical_base"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:shapeAppearance="@style/roundImageStyle10" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvData"
            style="@style/gilroy_400"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_vertical_base"
            android:gravity="start"
            android:lineSpacingExtra="@dimen/line_spacing_extra"
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp"
            tools:text="Get spoilt with trading choices that you can't find elsewhere. Explore from our comprehensive list of more than 20 indices (including Cash and Futures), all within one app. Get spoilt with trading choices that you can't find elsewhere. Explore from our from" />
    </LinearLayout>
</androidx.core.widget.NestedScrollView>

