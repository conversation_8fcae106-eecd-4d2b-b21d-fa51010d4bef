<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ctlParent"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:background="?attr/color_cffffff_c262930"
    android:minWidth="406dp"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:minWidth="406dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clExtraLine"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/margin_horizontal_base"
            android:paddingBottom="15dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvLineTitle"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:text="@string/extra_lines"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="18dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/groupLine"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="visible"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvLineTitle"
                tools:visibility="visible">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guide_l50"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.5" />

                <CheckBox
                    android:id="@+id/cbAsk"
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_horizontal_base"
                    android:layout_marginTop="10dp"
                    android:background="@null"
                    android:button="@null"
                    android:drawableStart="@drawable/select_checkbox_agreement"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical|start"
                    android:padding="4dp"
                    android:text="@string/ask_price_line"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toStartOf="@id/guide_l50"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <CheckBox
                    android:id="@+id/cbBid"
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:button="@null"
                    android:drawableStart="@drawable/select_checkbox_agreement"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical|start"
                    android:padding="4dp"
                    android:text="@string/bid_price_line"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/guide_l50"
                    app:layout_constraintTop_toTopOf="@id/cbAsk" />

                <CheckBox
                    android:id="@+id/cbTake"
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_horizontal_base"
                    android:layout_marginTop="10dp"
                    android:background="@null"
                    android:button="@null"
                    android:drawableStart="@drawable/select_checkbox_agreement"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical|start"
                    android:padding="4dp"
                    android:text="@string/take_profit_line"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toStartOf="@id/guide_l50"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/cbAsk" />

                <CheckBox
                    android:id="@+id/cbStop"
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:background="@null"
                    android:button="@null"
                    android:drawableStart="@drawable/select_checkbox_agreement"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical|start"
                    android:padding="4dp"
                    android:text="@string/stop_loss_line"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/guide_l50"
                    app:layout_constraintTop_toBottomOf="@id/cbBid" />

                <CheckBox
                    android:id="@+id/cbOpen"
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_horizontal_base"
                    android:layout_marginTop="10dp"
                    android:background="@null"
                    android:button="@null"
                    android:drawableStart="@drawable/select_checkbox_agreement"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical|start"
                    android:padding="4dp"
                    android:text="@string/open_position_line"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/cbTake" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>
