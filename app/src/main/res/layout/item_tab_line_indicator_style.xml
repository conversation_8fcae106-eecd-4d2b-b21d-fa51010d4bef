<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tvTab"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Trade"
        tools:textColor="?attr/color_c1e1e1e_cebffffff" />

    <TextView
        android:id="@+id/tvCount"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="2dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        android:visibility="gone"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvTab"
        app:layout_constraintStart_toEndOf="@+id/tvTab"
        app:layout_goneMarginStart="0dp"
        tools:text="99+"
        tools:visibility="visible" />

    <View
        android:id="@+id/viewDot"
        android:layout_width="4dp"
        android:layout_height="4dp"
        android:layout_marginStart="5dp"
        android:background="@drawable/shape_ce35728_r100"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@+id/tvTab"
        app:layout_constraintTop_toTopOf="@+id/tvTab"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>