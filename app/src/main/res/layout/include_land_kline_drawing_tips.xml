<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardDrawingTipsContainer"
    android:layout_width="wrap_content"
    android:layout_height="50dp"
    android:minWidth="284dp"
    android:layout_marginTop="16dp"
    android:visibility="gone"
    app:cardBackgroundColor="?attr/color_cffffff_c262930"
    app:cardCornerRadius="4dp"
    app:cardElevation="6dp"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    tools:visibility="visible">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingHorizontal="24dp"
        android:gravity="center"
        android:layout_gravity="center"
        android:orientation="vertical">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTipsSelectedDrawingType"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp"
            tools:text="矩形 已选" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTipsDrawingStep"
            style="@style/gilroy_300"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="12dp"
            tools:text="点击2个锚点，完成画线(1/2)" />
    </LinearLayout>
</androidx.cardview.widget.CardView>