<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/c1fff8e5c"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <cn.com.vau.common.view.system.LinkSpanTextView
        android:id="@+id/linkTvNotificationEnableTip"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="start"
        android:padding="12dp"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@+id/ivCloseTip"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/unable_to_receive_app_notifications" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivCloseTip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/margin_horizontal_base"
        android:src="@drawable/bitmap2_close10x10_ce35728"
        app:layout_constraintBottom_toBottomOf="@id/viewTips"
        app:layout_constraintEnd_toEndOf="@id/viewTips"
        app:layout_constraintTop_toTopOf="@id/viewTips" />

</LinearLayout>