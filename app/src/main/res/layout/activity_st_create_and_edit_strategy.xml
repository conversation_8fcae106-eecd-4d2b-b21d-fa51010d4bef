<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_backClickAutoFinishDisallow="true" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="@dimen/margin_horizontal_base"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivArrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
                    app:layout_constraintBottom_toBottomOf="@id/ivAvatar"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/ivAvatar" />

                <com.google.android.material.imageview.ShapeableImageView
                    android:id="@+id/ivAvatar"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginTop="@dimen/margin_top_title"
                    android:layout_marginEnd="@dimen/margin_horizontal_base"
                    android:scaleType="centerCrop"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/ivArrow"
                    app:layout_constraintTop_toTopOf="parent"
                    app:shapeAppearanceOverlay="@style/roundImageStyle10"
                    tools:src="@mipmap/ic_launcher" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvPic"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:gravity="center"
                    android:text="@string/profile_picture"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintBottom_toBottomOf="@id/ivAvatar"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/ivAvatar" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvIDTitle"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingVertical="14dp"
                    android:text="@string/strategy_id"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ivAvatar"
                    tools:ignore="HardcodedText" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvID"
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginStart="@dimen/margin_horizontal_base"
                    android:drawablePadding="8dp"
                    android:ellipsize="end"
                    android:gravity="end|center_vertical"
                    android:hint="@string/enter"
                    android:maxLines="1"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:drawableEndCompat="?attr/icon2EditStrategyFunds"
                    app:layout_constraintBottom_toBottomOf="@id/tvIDTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/tvIDTitle"
                    app:layout_constraintTop_toTopOf="@id/tvIDTitle" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/groupId"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="tvIDTitle,tvID" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvNameTitle"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingVertical="14dp"
                    android:text="@string/name_nick"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvIDTitle" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvName"
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginStart="@dimen/margin_horizontal_base"
                    android:drawablePadding="8dp"
                    android:ellipsize="end"
                    android:gravity="end|center_vertical"
                    android:hint="@string/enter"
                    android:maxLines="1"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:drawableEndCompat="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
                    app:layout_constraintBottom_toBottomOf="@id/tvNameTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/tvNameTitle"
                    app:layout_constraintTop_toTopOf="@id/tvNameTitle" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvDescriptionTitle"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingVertical="14dp"
                    android:text="@string/description"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvNameTitle" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvDescription"
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginStart="@dimen/margin_horizontal_base"
                    android:drawablePadding="8dp"
                    android:ellipsize="end"
                    android:gravity="end|center_vertical"
                    android:hint="@string/enter_optional"
                    android:maxLines="1"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:drawableEndCompat="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
                    app:layout_constraintBottom_toBottomOf="@id/tvDescriptionTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/tvDescriptionTitle"
                    app:layout_constraintTop_toTopOf="@id/tvDescriptionTitle" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:background="?attr/color_c0a1e1e1e_c0affffff" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="@dimen/margin_horizontal_base">

                <FrameLayout
                    android:id="@+id/flSourceAccountTitle"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toStartOf="@+id/tvSourceAccount"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvSourceAccountTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="12dp"
                        android:drawablePadding="4dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:paddingVertical="@dimen/padding_vertical_base"
                        android:text="@string/source_account"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="16dp"
                        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff" />
                </FrameLayout>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvSourceAccount"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:drawablePadding="8dp"
                    android:gravity="end|center_vertical"
                    android:hint="@string/select"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:drawableEndCompat="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
                    app:layout_constraintBottom_toBottomOf="@id/flSourceAccountTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/flSourceAccountTitle" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvPaymentAccountTitle"
                    style="@style/gilroy_500"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:drawablePadding="4dp"
                    android:ellipsize="end"
                    android:gravity="start"
                    android:maxLines="1"
                    android:paddingVertical="14dp"
                    android:text="@string/payment_account"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintEnd_toStartOf="@+id/tvPaymentAccount"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/flSourceAccountTitle" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvPaymentAccount"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:drawablePadding="8dp"
                    android:ellipsize="end"
                    android:gravity="end|center_vertical"
                    android:hint="@string/select"
                    android:maxLines="1"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:drawableEndCompat="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
                    app:layout_constraintBottom_toBottomOf="@id/tvPaymentAccountTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvPaymentAccountTitle" />

                <FrameLayout
                    android:id="@+id/flProfitSharingRatioTitle"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toStartOf="@+id/tvProfitSharingRatio"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvPaymentAccountTitle">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvProfitSharingRatioTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="12dp"
                        android:drawablePadding="4dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:paddingVertical="14dp"
                        android:text="@string/profit_sharing_ratio"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="16dp"
                        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff" />
                </FrameLayout>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvProfitSharingRatio"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:drawablePadding="8dp"
                    android:gravity="end|center_vertical"
                    android:hint="@string/select"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:drawableEndCompat="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
                    app:layout_constraintBottom_toBottomOf="@id/flProfitSharingRatioTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/flProfitSharingRatioTitle"
                    tools:ignore="HardcodedText"
                    tools:text="0%" />

                <FrameLayout
                    android:id="@+id/flSettlementFrequencyTitle"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toStartOf="@+id/tvSettlementFrequency"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/flProfitSharingRatioTitle">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvSettlementFrequencyTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="12dp"
                        android:drawablePadding="4dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:paddingVertical="14dp"
                        android:text="@string/settlement_frequency"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="16dp"
                        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff" />
                </FrameLayout>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvSettlementFrequency"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:drawablePadding="8dp"
                    android:gravity="end|center_vertical"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:drawableEndCompat="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
                    app:layout_constraintBottom_toBottomOf="@id/flSettlementFrequencyTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/flSettlementFrequencyTitle"
                    tools:text="@string/weekly" />

                <FrameLayout
                    android:id="@+id/flCurrentSettlementFrequencyTitle"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toStartOf="@+id/tvCurrentSettlementFrequency"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/flSettlementFrequencyTitle">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvCurrentSettlementFrequencyTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="12dp"
                        android:drawablePadding="4dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:paddingVertical="14dp"
                        android:text="@string/current_settlement_frequency"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="16dp"
                        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff" />
                </FrameLayout>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvCurrentSettlementFrequency"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:drawablePadding="8dp"
                    android:gravity="end|center_vertical"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:layout_constraintBottom_toBottomOf="@id/flCurrentSettlementFrequencyTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/flCurrentSettlementFrequencyTitle"
                    tools:text="@string/weekly" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvNextSettlementFrequencyTitle"
                    style="@style/gilroy_500"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:drawablePadding="4dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:paddingVertical="14dp"
                    android:text="@string/next_settlement_frequency"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintEnd_toStartOf="@+id/tvNextSettlementFrequency"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/flCurrentSettlementFrequencyTitle" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvNextSettlementFrequency"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:drawablePadding="8dp"
                    android:gravity="end|center_vertical"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:drawableEndCompat="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
                    app:layout_constraintBottom_toBottomOf="@id/tvNextSettlementFrequencyTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvNextSettlementFrequencyTitle"
                    tools:text="@string/weekly" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvFrequencyTimePrompt"
                    style="@style/gilroy_400"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:lineSpacingExtra="2dp"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:layout_constraintTop_toBottomOf="@+id/tvNextSettlementFrequencyTitle"
                    tools:text="New frequency will take effect on 07/01/2024." />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/groupSelectFrequency"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:constraint_referenced_ids="flSettlementFrequencyTitle,tvSettlementFrequency" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/groupNewFrequency"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="flCurrentSettlementFrequencyTitle,tvCurrentSettlementFrequency,tvNextSettlementFrequency,tvNextSettlementFrequencyTitle,tvFrequencyTimePrompt" />

                <FrameLayout
                    android:id="@+id/flCopierReviewTitle"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toStartOf="@+id/mSwitchButton"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvFrequencyTimePrompt">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvCopierReviewTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="12dp"
                        android:drawablePadding="4dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:paddingVertical="14dp"
                        android:text="@string/copier_review"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="16dp"
                        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff" />
                </FrameLayout>

                <cn.com.vau.util.widget.SwitchButton
                    android:id="@+id/mSwitchButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="@id/flCopierReviewTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/flCopierReviewTitle"
                    app:switch_can_loading="false"
                    app:switch_state="state_false" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvAutoReviewTitle"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawablePadding="4dp"
                    android:paddingVertical="14dp"
                    android:text="@string/auto_review"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/flCopierReviewTitle" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvAutoReview"
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginStart="@dimen/margin_horizontal_base"
                    android:drawablePadding="8dp"
                    android:ellipsize="end"
                    android:gravity="end|center_vertical"
                    android:maxLines="1"
                    android:text="@string/auto_approve"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:drawableEndCompat="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
                    app:layout_constraintBottom_toBottomOf="@id/tvAutoReviewTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/tvAutoReviewTitle"
                    app:layout_constraintTop_toTopOf="@id/tvAutoReviewTitle" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvAutoReviewPrompt"
                    style="@style/gilroy_400"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/margin_vertical_button"
                    android:lineSpacingExtra="2dp"
                    android:text="@string/copiers_will_be_approved_of_application"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvAutoReviewTitle" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/groupAuto"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="tvAutoReview,tvAutoReviewPrompt,tvAutoReviewTitle"
                    tools:visibility="visible" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:background="?attr/color_c0a1e1e1e_c0affffff" />

            <include
                android:id="@+id/layoutEditThreshold"
                layout="@layout/layout_st_threshold_for_copiers"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp" />

            <View
                android:id="@+id/viewThresholdInfo"
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:background="?attr/color_c0a1e1e1e_c0affffff" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clThresholdInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="@dimen/margin_horizontal_base"
                android:visibility="gone"
                tools:visibility="visible">

                <FrameLayout
                    android:id="@+id/flUpdateThresholdTitle"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toStartOf="@+id/tvEdit"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvUpdateThresholdTitle"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/margin_horizontal_base"
                        android:drawablePadding="4dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:paddingVertical="@dimen/margin_vertical_button"
                        android:text="@string/threshold_for_copiers"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="18dp"
                        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff" />
                </FrameLayout>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvEdit"
                    style="@style/gilroy_600"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:gravity="center"
                    android:text="@string/edit"
                    android:textColor="@color/ce35728"
                    android:textSize="16dp"
                    app:layout_constraintBottom_toBottomOf="@+id/flUpdateThresholdTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/flUpdateThresholdTitle" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvMinInvestmentTitle"
                    style="@style/gilroy_500"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:drawablePadding="4dp"
                    android:ellipsize="end"
                    android:gravity="start"
                    android:maxLines="1"
                    android:paddingVertical="14dp"
                    android:text="@string/min_investment_per_copy"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintEnd_toStartOf="@+id/tvMinInvestment"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/flUpdateThresholdTitle" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvMinInvestment"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:ellipsize="end"
                    android:gravity="end|center_vertical"
                    android:maxLines="1"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:layout_constraintBottom_toBottomOf="@id/tvMinInvestmentTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvMinInvestmentTitle"
                    tools:text="100000000.00 USD" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvLotsTitle"
                    style="@style/gilroy_500"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:drawablePadding="4dp"
                    android:ellipsize="end"
                    android:gravity="start"
                    android:maxLines="1"
                    android:paddingVertical="14dp"
                    android:text="@string/min_lots_per_order"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintEnd_toStartOf="@+id/tvLots"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvMinInvestmentTitle" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvLots"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:drawablePadding="8dp"
                    android:gravity="end|center_vertical"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:layout_constraintBottom_toBottomOf="@id/tvLotsTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvLotsTitle"
                    tools:text="100000.01" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvMultiplesTitle"
                    style="@style/gilroy_500"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:drawablePadding="4dp"
                    android:ellipsize="end"
                    android:gravity="start"
                    android:maxLines="1"
                    android:paddingVertical="14dp"
                    android:text="@string/min_multiples_per_order"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintEnd_toStartOf="@+id/tvMultiples"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvLotsTitle" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvMultiples"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:drawablePadding="8dp"
                    android:gravity="end|center_vertical"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:layout_constraintBottom_toBottomOf="@id/tvMultiplesTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvMultiplesTitle"
                    tools:text="2" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvEquivalentMarginTitle"
                    style="@style/gilroy_500"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:drawablePadding="4dp"
                    android:ellipsize="end"
                    android:gravity="start"
                    android:maxLines="1"
                    android:paddingVertical="14dp"
                    android:text="@string/min_multiples_for_equivalent_margin"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintEnd_toStartOf="@+id/tvEquivalentMargin"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvMultiplesTitle" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvEquivalentMargin"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:drawablePadding="8dp"
                    android:gravity="end|center_vertical"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:layout_constraintBottom_toBottomOf="@id/tvEquivalentMarginTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvEquivalentMarginTitle"
                    tools:text="2" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <LinearLayout
        android:id="@+id/llSubmit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/margin_horizontal_base"
        android:paddingTop="24dp"
        android:paddingBottom="@dimen/margin_bottom_next_to_layout"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSave"
            style="@style/gilroy_600"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/draw_shape_c1e1e1e_cebffffff_r100"
            android:gravity="center"
            android:paddingVertical="@dimen/padding_vertical_base"
            android:text="@string/save"
            android:textColor="?attr/color_cebffffff_c1e1e1e"
            android:textSize="16dp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvPublish"
            style="@style/gilroy_600"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:layout_weight="1"
            android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
            android:gravity="center"
            android:paddingVertical="@dimen/padding_vertical_base"
            android:text="@string/publish"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="16dp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvUpdate"
            style="@style/main_bottom_button_theme"
            android:layout_width="match_parent"
            android:layout_height="@dimen/height_button_main"
            android:layout_marginBottom="0dp"
            android:layout_weight="1"
            android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
            android:text="@string/update__now"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:visibility="gone" />
    </LinearLayout>
</LinearLayout>