<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="cn.com.vau.page.user.openAccountThird.OpenAccountThirdActivity">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/open_live_account"
        app:hb_endIcon="@drawable/draw_bitmap2_close16x16_c731e1e1e_c61ffffff"
        app:hb_endIcon1="?attr/icon1Cs"
        app:layout_constraintTop_toTopOf="parent"/>

    <cn.com.vau.common.view.StepOpenAccountView
        android:id="@+id/stepView"
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        app:layout_constraintTop_toBottomOf="@id/mHeaderBar"
        app:step_num="4" />

    <TextView
        android:id="@+id/tvSecondTitle"
        style="@style/bold_semi_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="30dp"
        android:text="@string/account_configuration"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/stepView" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@+id/tvSecondTitle">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="300dp">
            <!--  选择交易平台 ( 一个平台不显示,多个平台显示 ) -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ctlAccountPlatform"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tvAccountPlatform"
                    style="@style/regular_font"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                    android:layout_marginTop="10dp"
                    android:drawablePadding="10dp"
                    android:text="@string/choose_account_platform"
                    app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="13dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rcyAccountPlatform"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginEnd="10dp"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvAccountPlatform"
                    app:spanCount="2"
                    tools:itemCount="2"
                    tools:listitem="@layout/item_rcy_account_platform" />

                <View
                    android:id="@+id/view3"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/cut_off_line_height"
                    android:layout_marginStart="@dimen/margin_horizontal_base"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="@dimen/margin_horizontal_base"
                    android:background="?attr/color_c1f1e1e1e_c1fffffff"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/rcyAccountPlatform" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!--  选择账户类型 -->
            <TextView
                android:id="@+id/tvAccountTypeTip"
                style="@style/regular_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="16dp"
                android:drawablePadding="10dp"
                android:text="@string/choose_account_type"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="13dp"
                app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ctlAccountPlatform" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rcyAccountType"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="15dp"
                android:layout_marginEnd="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvAccountTypeTip"
                tools:itemCount="3"
                tools:listitem="@layout/item_acount_type"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rcyAccountTypeSt"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="15dp"
                android:layout_marginEnd="10dp"
                app:layoutManager="cn.com.vau.common.view.WrapContentLinearLayoutManager"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/rcyAccountType" />

            <!--marginTop="4dp" 是因为上边每个item布局中有marginBottom="12dp"的设定-->
            <View
                android:id="@+id/view2"
                android:layout_width="0dp"
                android:layout_height="@dimen/cut_off_line_height"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/rcyAccountTypeSt" />

            <TextView
                android:id="@+id/tvAccountCurrencyDesc"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:text="@string/choose_account_currency"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="13dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/view2" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rcyAccountCurrency"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="15dp"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="50dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvAccountCurrencyDesc" />

            <CheckBox
                android:id="@+id/cbThirdAgreement"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:background="@null"
                android:button="@drawable/select_checkbox_agreement"
                android:paddingTop="16dp"
                android:paddingEnd="10dp"
                android:paddingBottom="12dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/rcyAccountCurrency" />

            <TextView
                android:id="@+id/tvAgreementTitle"
                style="@style/regular_font"
                android:layout_width="253dp"
                android:layout_height="wrap_content"
                android:text="@string/st_agreements_text"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/cbThirdAgreement"
                app:layout_constraintStart_toEndOf="@+id/cbThirdAgreement"
                app:layout_constraintTop_toTopOf="@+id/cbThirdAgreement" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>


    <TextView
        android:id="@+id/tvNext"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:layout_marginBottom="30dp"
        android:background="@drawable/bitmap_icon2_next_active"
        android:gravity="center"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
