<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/clParent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingStart="@dimen/margin_horizontal_base"
    android:paddingEnd="@dimen/margin_horizontal_base">

    <ImageView
        android:id="@+id/ivBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="28dp"
        android:contentDescription="@string/app_name"
        android:paddingStart="0dp"
        android:paddingEnd="20dp"
        android:paddingBottom="20dp"
        android:src="@drawable/draw_bitmap2_arrow_start16x16_c1e1e1e_cebffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nestedScrollView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:paddingBottom="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivBack">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="100dp">

            <TextView
                android:id="@+id/tvTopTip"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/please_take_photo_and_glare"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/ivSamplePic"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:contentDescription="@string/app_name"
                android:src="@drawable/img_asic_sample_passport"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTopTip" />

            <TextView
                android:id="@+id/tvQuickTipsTitle"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/quick_tips"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="13dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ivSamplePic" />

            <ImageView
                android:id="@+id/ivUploadTip1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="25dp"
                android:contentDescription="@string/app_name"
                android:src="@drawable/img_asic_quick_tips_3_months"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvQuickTipsTitle" />

            <TextView
                android:id="@+id/tvUploadTip1"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="25dp"
                android:layout_marginTop="5dp"
                android:text="@string/at_least_x_months_validity"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/ivUploadTip1"
                app:layout_constraintTop_toTopOf="@+id/ivUploadTip1" />

            <ImageView
                android:id="@+id/ivUploadTip2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="25dp"
                android:contentDescription="@string/app_name"
                android:src="@drawable/img_asic_quick_tips_passport"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ivUploadTip1" />

            <TextView
                android:id="@+id/tvUploadTip2"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="25dp"
                android:layout_marginTop="5dp"
                android:text="@string/must_show_full_clearly"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/ivUploadTip2"
                app:layout_constraintTop_toTopOf="@+id/ivUploadTip2" />

            <ImageView
                android:id="@+id/ivUploadTip3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="25dp"
                android:contentDescription="@string/app_name"
                android:src="@drawable/img_asic_quick_tips_barcode"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ivUploadTip2" />

            <TextView
                android:id="@+id/tvUploadTip3"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="25dp"
                android:layout_marginTop="5dp"
                android:text="@string/must_show_passport_clearly"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/ivUploadTip3"
                app:layout_constraintTop_toTopOf="@+id/ivUploadTip3" />

            <TextView
                android:id="@+id/tvBottomTip"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="28dp"
                android:text="@string/the_personal_information_we_parties"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ivUploadTip3" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <ImageView
        android:id="@+id/tvNext"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginBottom="30dp"
        android:background="@drawable/img_asic_upload"
        android:contentDescription="@string/app_name"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>