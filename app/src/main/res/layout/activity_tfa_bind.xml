<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_backClickAutoFinishDisallow="false"
        app:hb_endIcon="?attr/icon1Cs"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivPwd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:src="?attr/img2faPwdSelect"
        app:layout_constraintEnd_toStartOf="@id/iv2FA"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv2FA" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv2FA"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:src="?attr/img2faUnselect"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivPwd"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvPwd"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/password"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="10dp"
        app:layout_constraintEnd_toEndOf="@id/ivPwd"
        app:layout_constraintStart_toStartOf="@+id/ivPwd"
        app:layout_constraintTop_toBottomOf="@+id/ivPwd" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv2FA"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="2FA"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        app:layout_constraintEnd_toEndOf="@id/iv2FA"
        app:layout_constraintStart_toStartOf="@+id/iv2FA"
        app:layout_constraintTop_toBottomOf="@+id/iv2FA" />

    <View
        android:id="@+id/line1"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginHorizontal="8dp"
        android:background="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@id/iv2FA"
        app:layout_constraintEnd_toStartOf="@id/iv2FA"
        app:layout_constraintStart_toEndOf="@id/ivPwd"
        app:layout_constraintTop_toTopOf="@+id/iv2FA" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupTop"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="ivPwd,iv2FA,tvPwd,tv2FA,line1"
        tools:visibility="visible" />

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/fragment"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="32dp"
        app:defaultNavHost="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvPwd"
        app:layout_goneMarginTop="8dp"
        app:navGraph="@navigation/tfa_bind_navigation"
        tools:ignore="MissingConstraints" />
</androidx.constraintlayout.widget.ConstraintLayout>