<com.scwang.smart.refresh.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/refreshLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/margin_vertical_base"
            android:paddingBottom="@dimen/margin_bottom_layout">

            <TextView
                android:id="@+id/tvDealingTitle"
                style="@style/gilroy_600"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:gravity="center_vertical|start"
                android:text="@string/dealing"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="18dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvSpreadTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/spread"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvDealingTitle" />

            <TextView
                android:id="@+id/tvSpread"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:text="@string/floating"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvSpreadTitle"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle" />

            <TextView
                android:id="@+id/tvDigitsTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/digits"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvSpreadTitle" />

            <TextView
                android:id="@+id/tvDigits"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvDigitsTitle"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                tools:text="4" />

            <TextView
                android:id="@+id/tvStopsLevelTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/stops_level"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvDigitsTitle" />

            <TextView
                android:id="@+id/tvStopsLevel"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvStopsLevelTitle"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                tools:text="4" />

            <TextView
                android:id="@+id/tvPendingsGTCTitle"
                style="@style/gilroy_500"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="6dp"
                android:gravity="center_vertical|start"
                android:text="@string/pendings_GTC"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toStartOf="@+id/tvPendingsGTC"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvStopsLevelTitle" />

            <TextView
                android:id="@+id/tvPendingsGTC"
                style="@style/gilroy_500"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginStart="6dp"
                android:gravity="center_vertical|end"
                android:textAlignment="viewEnd"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                app:layout_constraintStart_toEndOf="@+id/tvPendingsGTCTitle"
                app:layout_constraintTop_toTopOf="@+id/tvPendingsGTCTitle"
                tools:text="Yes" />

            <TextView
                android:id="@+id/tvContractSizeTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/contract_size"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvPendingsGTCTitle" />

            <TextView
                android:id="@+id/tvContractSize"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvContractSizeTitle"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                tools:text="10K" />

            <TextView
                android:id="@+id/tvMinimumVolumeTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/minimum_volume"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvContractSizeTitle" />

            <TextView
                android:id="@+id/tvMinimumVolume"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvMinimumVolumeTitle"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                tools:text="0.01" />

            <TextView
                android:id="@+id/tvMaximumVolumeTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/maximum_volume"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvMinimumVolumeTitle" />

            <TextView
                android:id="@+id/tvMaximumVolume"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvMaximumVolumeTitle"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                tools:text="10" />

            <TextView
                android:id="@+id/tvProfitCalculationTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/profit_calculation"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvMaximumVolumeTitle" />

            <TextView
                android:id="@+id/tvProfitCalculation"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvProfitCalculationTitle"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                tools:text="Forex" />

            <View
                android:id="@+id/viewDealing"
                android:layout_width="match_parent"
                android:layout_height="0.75dp"
                android:layout_marginTop="12dp"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvProfitCalculation" />

            <TextView
                android:id="@+id/tvMarginTitle"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/margin"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="18dp"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/viewDealing" />

            <TextView
                android:id="@+id/tvMarginCalculationTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/margin_calculation"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvMarginTitle" />

            <TextView
                android:id="@+id/tvMarginCalculation"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvMarginCalculationTitle"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                tools:text="Hello" />

            <TextView
                android:id="@+id/tvMarginInitialTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/margin_initial"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvMarginCalculationTitle" />

            <TextView
                android:id="@+id/tvMarginInitial"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvMarginInitialTitle"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                tools:text="1000000.00" />

            <TextView
                android:id="@+id/tvMarginHedgeTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/margin_hedge"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvMarginInitialTitle" />

            <TextView
                android:id="@+id/tvMarginHedge"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvMarginHedgeTitle"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                tools:text="0.00" />

            <TextView
                android:id="@+id/tvMarginPercentageTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/margin_percentage"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvMarginHedgeTitle" />

            <TextView
                android:id="@+id/tvMarginPercentage"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvMarginPercentageTitle"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                tools:text="1%" />

            <TextView
                android:id="@+id/tvMarginCurrencyTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/margin_currency"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvMarginPercentageTitle" />

            <TextView
                android:id="@+id/tvMarginCurrency"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvMarginCurrencyTitle"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                tools:text="XFL" />

            <View
                android:id="@+id/viewMargin"
                android:layout_width="match_parent"
                android:layout_height="0.75dp"
                android:layout_marginTop="12dp"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvMarginCurrency" />

            <TextView
                android:id="@+id/tvSwapTitle"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/swap"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="18dp"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/viewMargin" />

            <TextView
                android:id="@+id/tvSwapTypeTitle"
                style="@style/gilroy_500"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="6dp"
                android:gravity="center_vertical|start"
                android:text="@string/swap_type"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toStartOf="@+id/tvSwapType"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvSwapTitle" />

            <TextView
                android:id="@+id/tvSwapType"
                style="@style/gilroy_500"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginStart="6dp"
                android:gravity="center_vertical|end"
                android:textAlignment="viewEnd"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvSwapTypeTitle"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                app:layout_constraintStart_toEndOf="@+id/tvSwapTypeTitle"
                tools:text="Spot" />

            <TextView
                android:id="@+id/tvSwapLongTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/swap_long"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvSwapType" />

            <TextView
                android:id="@+id/tvSwapLong"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvSwapLongTitle"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                tools:text="0.69" />

            <TextView
                android:id="@+id/tvSwapShortTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/swap_short"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvSwapLongTitle" />

            <TextView
                android:id="@+id/tvSwapShort"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvSwapShortTitle"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                tools:text="-0.37" />

            <TextView
                android:id="@+id/tvDaySwapTitle"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/three_day_swap"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvSwapShortTitle" />

            <TextView
                android:id="@+id/tvDaySwap"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvDaySwapTitle"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                tools:text="Friday" />

            <View
                android:id="@+id/viewSwap"
                android:layout_width="match_parent"
                android:layout_height="0.75dp"
                android:layout_marginTop="12dp"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvDaySwapTitle" />

            <TextView
                android:id="@+id/tvTradingTimeGMT"
                style="@style/gilroy_500"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/trading_time"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="18dp"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/viewSwap" />

            <TextView
                android:id="@+id/tvMon"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/mon"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvTradingTimeGMT" />

            <TextView
                android:id="@+id/tvMonDate"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toTopOf="@+id/tvMon"
                tools:text="Friday" />

            <TextView
                android:id="@+id/tvTues"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="start"
                android:text="@string/tues"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvMon"
                app:layout_constraintTop_toBottomOf="@+id/tvMonDate" />

            <TextView
                android:id="@+id/tvTuesDate"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toTopOf="@+id/tvTues"
                tools:text="tvTuesDate" />

            <TextView
                android:id="@+id/tvWed"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/wed"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvMon"
                app:layout_constraintTop_toBottomOf="@+id/tvTuesDate" />

            <TextView
                android:id="@+id/tvWedDate"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toTopOf="@+id/tvWed"
                tools:text="tvWedDate" />

            <TextView
                android:id="@+id/tvThur"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/thur"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvMon"
                app:layout_constraintTop_toBottomOf="@+id/tvWedDate" />

            <TextView
                android:id="@+id/tvThurDate"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toTopOf="@+id/tvThur"
                tools:text="tvThurDate" />

            <TextView
                android:id="@+id/tvFri"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/fri"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvMon"
                app:layout_constraintTop_toBottomOf="@+id/tvThurDate" />

            <TextView
                android:id="@+id/tvFriDate"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toTopOf="@+id/tvFri"
                tools:text="tvFriDate" />

            <TextView
                android:id="@+id/tvSat"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:text="@string/sat"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvMon"
                app:layout_constraintTop_toBottomOf="@+id/tvFriDate" />

            <TextView
                android:id="@+id/tvSatDate"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toTopOf="@+id/tvSat" />

            <TextView
                android:id="@+id/tvSun"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/sun"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvMon"
                app:layout_constraintTop_toBottomOf="@+id/tvSatDate" />

            <TextView
                android:id="@+id/tvSunDate"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toTopOf="@+id/tvSun" />

            <View
                android:id="@+id/viewTradingTime"
                android:layout_width="match_parent"
                android:layout_height="0.75dp"
                android:layout_marginTop="12dp"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvSunDate" />

            <TextView
                style="@style/gilroy_400"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:gravity="center_vertical|start"
                android:text="@string/the_company_reserves_conditions"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="@+id/tvDealingTitle"
                app:layout_constraintStart_toStartOf="@+id/tvDealingTitle"
                app:layout_constraintTop_toBottomOf="@+id/viewTradingTime" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</com.scwang.smart.refresh.layout.SmartRefreshLayout>