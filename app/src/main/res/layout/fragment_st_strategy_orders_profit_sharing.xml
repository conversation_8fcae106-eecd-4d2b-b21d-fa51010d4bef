<?xml version="1.0" encoding="utf-8"?>
<com.scwang.smart.refresh.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mRefreshLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:srlEnableLoadMore="false">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="@dimen/margin_horizontal_base">

                <TextView
                    android:id="@+id/tvProfitSharingSummaryTitle"
                    style="@style/gilroy_600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:drawableEnd="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                    android:drawablePadding="4dp"
                    android:text="@string/profit_sharing_summary"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="UseCompatTextViewDrawableXml" />

                <TextView
                    android:id="@+id/tvEligibleProfitsForSharingTitle"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:layout_marginEnd="6dp"
                    android:ellipsize="end"
                    android:gravity="center_vertical|start"
                    android:maxLines="1"
                    android:text="@string/eligible_profits_for_sharing"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:layout_constraintEnd_toStartOf="@+id/guideline_t_v50"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvProfitSharingSummaryTitle" />

                <TextView
                    android:id="@+id/tvEligibleProfitsForSharing"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="6dp"
                    android:gravity="center_vertical|start"
                    android:text="0.00"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textDirection="ltr"
                    android:textSize="16dp"
                    app:layout_constraintEnd_toStartOf="@+id/guideline_t_v50"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvEligibleProfitsForSharingTitle"
                    tools:ignore="HardcodedText,SpUsage" />

                <TextView
                    android:id="@+id/tvTotalSharedProfitTitle"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:ellipsize="end"
                    android:gravity="center_vertical|end"
                    android:maxLines="1"
                    android:text="@string/total_shared_profit"
                    android:textAlignment="viewEnd"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/tvEligibleProfitsForSharingTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/guideline_t_v50" />

                <TextView
                    android:id="@+id/tvTotalSharedProfit"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:gravity="center_vertical|end"
                    android:text="0.00"
                    android:textAlignment="viewEnd"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textDirection="ltr"
                    android:textSize="16dp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/tvEligibleProfitsForSharing"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/guideline_t_v50"
                    tools:ignore="HardcodedText,SpUsage" />

                <TextView
                    android:id="@+id/tvHighWaterMarkTitle"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:layout_marginEnd="6dp"
                    android:ellipsize="end"
                    android:gravity="center_vertical|start"
                    android:maxLines="1"
                    android:text="@string/high_water_mark"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:layout_constraintEnd_toStartOf="@+id/guideline_t_v50"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvEligibleProfitsForSharing" />

                <TextView
                    android:id="@+id/tvHighWaterMark"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="6dp"
                    android:gravity="center_vertical|start"
                    android:text="0.00"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textDirection="ltr"
                    android:textSize="16dp"
                    app:layout_constraintEnd_toStartOf="@+id/guideline_t_v50"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvHighWaterMarkTitle"
                    tools:ignore="HardcodedText,SpUsage" />

                <TextView
                    android:id="@+id/tvProfitSharingRatioTitle"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:ellipsize="end"
                    android:gravity="center_vertical|end"
                    android:maxLines="1"
                    android:text="@string/profit_sharing_ratio"
                    android:textAlignment="viewEnd"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/tvHighWaterMarkTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/guideline_t_v50" />

                <TextView
                    android:id="@+id/tvProfitSharingRatio"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:gravity="center_vertical|end"
                    android:text="0.00"
                    android:textAlignment="viewEnd"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textDirection="ltr"
                    android:textSize="16dp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/tvHighWaterMark"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/guideline_t_v50"
                    tools:ignore="HardcodedText,SpUsage" />

                <TextView
                    android:id="@+id/tvSettlementFrequencyTitle"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:layout_marginEnd="6dp"
                    android:ellipsize="end"
                    android:gravity="center_vertical|start"
                    android:maxLines="1"
                    android:text="@string/settlement_frequency"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:layout_constraintEnd_toStartOf="@+id/guideline_t_v50"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvHighWaterMark" />

                <TextView
                    android:id="@+id/tvSettlementFrequency"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="6dp"
                    android:gravity="center_vertical|start"
                    android:text="@string/weekly"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintEnd_toStartOf="@+id/guideline_t_v50"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvSettlementFrequencyTitle"
                    tools:ignore="HardcodedText,SpUsage" />

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline_t_v50"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.50" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tvViewStatement"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="20dp"
                android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
                android:paddingHorizontal="28dp"
                android:paddingVertical="8dp"
                android:text="@string/view_statement"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp" />

            <View
                android:id="@+id/offView"
                android:layout_width="match_parent"
                android:layout_height="12dp"
                android:background="?attr/color_c0a1e1e1e_c0affffff"
                app:layout_constraintBottom_toBottomOf="parent" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</com.scwang.smart.refresh.layout.SmartRefreshLayout>