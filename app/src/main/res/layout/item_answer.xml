<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/llAnswer"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/margin_horizontal_base"
    android:gravity="center_vertical">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivAnswer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAnswer"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:gravity="start"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="14dp"
        tools:text="text" />

</LinearLayout>
