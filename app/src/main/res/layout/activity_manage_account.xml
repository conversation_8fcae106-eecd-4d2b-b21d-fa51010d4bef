<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/flTop"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 这个view是因为有的oppo 一加 手机 会让x号按钮自动获取焦点，有一个阴影，这里设置一个invisible的view获取焦点能避免这个问题 -->
        <View
            android:layout_width="wrap_content"
            android:layout_height="1dp"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:visibility="invisible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvUserName"
            style="@style/gilroy_600"
            android:layout_width="0dp"
            android:layout_height="52dp"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:layout_marginEnd="@dimen/margin_horizontal_base"
            android:drawablePadding="8dp"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="16dp"
            android:visibility="gone"
            app:drawableStartCompat="?attr/imgLogoMark"
            app:layout_constraintEnd_toStartOf="@+id/ivTopClose"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Hi, YimengHi, YimengHi, YimengHi, YimengHi, YimengHi, YimengHi, Yimeng"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/ivTopClose"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="24dp"
            android:contentDescription="@string/app_name"
            android:padding="4dp"
            android:src="@drawable/draw_bitmap2_close16x16_c731e1e1e_c61ffffff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clTitlaBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/ivLeftBack"
                android:layout_width="wrap_content"
                android:layout_height="52dp"
                android:contentDescription="@string/app_name"
                android:padding="@dimen/margin_horizontal_base"
                android:src="@drawable/draw_bitmap2_arrow_start16x16_c1e1e1e_cebffffff"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvTitle"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="20dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/ivLeftBack"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Manage Accounts" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/smartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/llBottom"
        app:layout_constraintTop_toBottomOf="@id/flTop">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nestedScrollView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/margin_vertical_base">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clOpenAccountError"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                    android:background="@drawable/draw_shape_stroke_c331e1e1e_c33ffffff_r10"
                    android:paddingHorizontal="@dimen/padding_horizontal_base"
                    android:paddingVertical="@dimen/padding_horizontal_base"
                    android:visibility="gone"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvErrorTitle"
                        style="@style/gilroy_600"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="5dp"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintEnd_toStartOf="@id/tvErrorBtn"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="ID Verification Unsuccessful" />

                    <TextView
                        android:id="@+id/tvErrorInfo"
                        style="@style/gilroy_500"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="5dp"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="12dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/tvErrorBtn"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvErrorTitle"
                        tools:text="Please reupload ID to proceed" />

                    <TextView
                        android:id="@+id/tvErrorBtn"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="32dp"
                        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
                        android:gravity="center"
                        android:paddingHorizontal="15dp"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Reupload" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clAsicPrompt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                    android:layout_marginTop="@dimen/margin_vertical_base"
                    android:background="@drawable/draw_shape_stroke_c331e1e1e_c33ffffff_r10"
                    android:padding="@dimen/margin_horizontal_base"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/clOpenAccountError"
                    tools:visibility="visible">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvAsicPromptTitle"
                        style="@style/gilroy_600"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="5dp"
                        android:text="@string/complete_the_client_suitability_questionnaire"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintEnd_toStartOf="@+id/tvStart"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/gilroy_400"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@string/you_are_not_suitability_questionnaire"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="12dp"
                        app:layout_constraintEnd_toEndOf="@+id/tvAsicPromptTitle"
                        app:layout_constraintStart_toStartOf="@+id/tvAsicPromptTitle"
                        app:layout_constraintTop_toBottomOf="@+id/tvAsicPromptTitle" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvStart"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="32dp"
                        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:maxWidth="100dp"
                        android:maxLines="1"
                        android:paddingHorizontal="15dp"
                        android:text="@string/link_start"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/tvCopyTradingGroupTitle"
                    style="@style/gilroy_600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_horizontal_base"
                    android:layout_marginTop="@dimen/margin_vertical_base"
                    android:text="@string/copy_trading_account"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="18dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/clAsicPrompt"
                    app:layout_goneMarginTop="0dp"
                    tools:visibility="visible" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/copyTradeRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                    app:layoutManager="cn.com.vau.common.view.WrapContentLinearLayoutManager"
                    app:layout_constraintTop_toBottomOf="@id/tvCopyTradingGroupTitle" />

                <TextView
                    android:id="@+id/tvLiveGroupTitle"
                    style="@style/gilroy_600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_horizontal_base"
                    android:layout_marginTop="@dimen/margin_vertical_base"
                    android:text="@string/live_account"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="18dp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toTopOf="@id/liveRecyclerView"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/copyTradeRecyclerView"
                    tools:visibility="visible" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/liveRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                    app:layoutManager="cn.com.vau.common.view.WrapContentLinearLayoutManager"
                    app:layout_constraintBottom_toTopOf="@id/tvDemoGroupTitle"
                    app:layout_constraintTop_toBottomOf="@id/tvLiveGroupTitle" />

                <TextView
                    android:id="@+id/tvDemoGroupTitle"
                    style="@style/gilroy_600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_horizontal_base"
                    android:layout_marginTop="@dimen/margin_vertical_base"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="18dp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toTopOf="@id/demoRecyclerView"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/liveRecyclerView"
                    tools:text="@string/demo_account"
                    tools:visibility="visible" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/demoRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                    android:overScrollMode="never"
                    android:scrollbars="none"
                    app:layoutManager="cn.com.vau.common.view.WrapContentLinearLayoutManager"
                    app:layout_constraintTop_toBottomOf="@id/tvDemoGroupTitle" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <LinearLayout
        android:id="@+id/llBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingVertical="@dimen/padding_vertical_base"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tvNewLiveAccount"
            style="@style/gilroy_600"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginHorizontal="@dimen/margin_horizontal_base"
            android:background="@drawable/draw_shape_c1e1e1e_cebffffff_r100"
            android:gravity="center"
            android:text="@string/new_live_account"
            android:textColor="?attr/color_cebffffff_c1e1e1e"
            android:textSize="16dp"
            android:visibility="visible"
            app:layout_constraintTop_toBottomOf="@id/copyTradeRecyclerView"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvOpenDemoAccount"
            style="@style/gilroy_600"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginHorizontal="@dimen/margin_horizontal_base"
            android:layout_marginTop="@dimen/margin_vertical_button"
            android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
            android:gravity="center"
            android:text="@string/new_demo_account"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="16dp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/tvNewLiveAccount"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvLinkYourAccount"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_vertical_button"
            android:padding="10dp"
            android:text="@string/link_your_account"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvOpenDemoAccount"
            tools:visibility="visible" />

    </LinearLayout>

    <ViewStub
        android:id="@+id/mVsNoDataScroll"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout="@layout/vs_layout_no_data_scroll"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/flTop" />

</androidx.constraintlayout.widget.ConstraintLayout>