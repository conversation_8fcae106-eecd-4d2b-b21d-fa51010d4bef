<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/select_country"
        app:layout_constraintTop_toTopOf="parent" />

    <include layout="@layout/merge_search" />

    <ExpandableListView
        android:id="@+id/elvResidenceList"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:dividerHeight="0dp"
        android:groupIndicator="@null"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/rcyBigLetter"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etSearch" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcyBigLetter"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginTop="0dp"
        android:layout_marginBottom="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/elvResidenceList"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/elvResidenceList"
        tools:listitem="@layout/item_nav_letter" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ctlSearch"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="?attr/mainLayoutBg"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etSearch">

        <cn.com.vau.common.view.system.MyRecyclerView
            android:id="@+id/searchRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent" />

        <ViewStub
            android:id="@+id/mVsNoData"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout="@layout/vs_layout_no_data"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
