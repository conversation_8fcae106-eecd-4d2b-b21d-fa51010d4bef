<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/mRefreshLayout"
        app:srlEnableLoadMore="false"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <cn.com.vau.common.view.system.MyRecyclerView
                app:layout_constraintTop_toTopOf="parent"
                android:id="@+id/mRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <ViewStub
                android:id="@+id/mVsNoDataScroll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout="@layout/vs_layout_no_data_scroll"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>