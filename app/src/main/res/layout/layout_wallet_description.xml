<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_600"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:textSize="16dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:text="@string/open_crypto_wallet"
        app:layout_constraintTop_toTopOf="parent" />
    
    <ImageView
        android:id="@+id/ivWallet"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:src="@drawable/img_wallet"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvDescription"
        app:layout_constraintBottom_toBottomOf="@id/tvDescription"/>

    <TextView
        android:id="@+id/tvDescription"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_horizontal_base"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:lineSpacingExtra="3dp"
        android:textSize="12dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:text="@string/now_you_can_you_deposit_and_funds_more_in_the_future"
        app:layout_constraintTop_toBottomOf="@id/tvTitle"
        app:layout_constraintStart_toEndOf="@id/ivWallet"
        app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>