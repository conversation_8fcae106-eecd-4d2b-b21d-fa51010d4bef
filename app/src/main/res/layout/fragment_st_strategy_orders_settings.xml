<?xml version="1.0" encoding="utf-8"?>
<com.scwang.smart.refresh.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mRefreshLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:srlEnableLoadMore="false">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp">

            <TextView
                android:id="@+id/tvCopyModeTitle"
                style="@style/gilroy_400"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginEnd="6dp"
                android:gravity="center_vertical|start"
                android:text="@string/copy_mode"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toStartOf="@+id/guideline_v50"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvCopyMode"
                style="@style/gilroy_600"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:textAlignment="viewEnd"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvCopyModeTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/guideline_v50"
                tools:text="Fixed Lots" />

            <TextView
                android:id="@+id/tvLotsPerOrderTitle"
                style="@style/gilroy_400"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="6dp"
                android:gravity="center_vertical|start"
                android:text="@string/lots_per_order"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toStartOf="@+id/guideline_v50"
                app:layout_constraintStart_toStartOf="@+id/tvCopyModeTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvCopyModeTitle" />

            <TextView
                android:id="@+id/tvLotsPerOrder"
                style="@style/gilroy_600"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:gravity="center_vertical|end"
                android:textAlignment="viewEnd"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvLotsPerOrderTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/guideline_v50"
                tools:text="0.01 Lots" />

            <TextView
                android:id="@+id/tvStopLossTitle"
                style="@style/gilroy_400"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="6dp"
                android:gravity="center_vertical|start"
                android:text="@string/stop_loss"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toStartOf="@+id/guideline_v50"
                app:layout_constraintStart_toStartOf="@+id/tvCopyModeTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvLotsPerOrderTitle" />

            <TextView
                android:id="@+id/tvStopLoss"
                style="@style/gilroy_600"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:gravity="center_vertical|end"
                android:textAlignment="viewEnd"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvStopLossTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/guideline_v50"
                tools:text="35%" />

            <TextView
                android:id="@+id/tvTakeProfitTitle"
                style="@style/gilroy_400"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="6dp"
                android:gravity="center_vertical|start"
                android:text="@string/take_profit"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toStartOf="@+id/guideline_v50"
                app:layout_constraintStart_toStartOf="@+id/tvCopyModeTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvStopLossTitle" />

            <TextView
                android:id="@+id/tvTakeProfit"
                style="@style/gilroy_600"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:gravity="center_vertical|end"
                android:textAlignment="viewEnd"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvTakeProfitTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/guideline_v50"
                tools:text="35%" />

            <TextView
                android:id="@+id/tvLotRoundUpTitle"
                style="@style/gilroy_400"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="6dp"
                android:gravity="center_vertical|start"
                android:text="@string/lot_round_up"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toStartOf="@+id/guideline_v50"
                app:layout_constraintStart_toStartOf="@+id/tvCopyModeTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvTakeProfitTitle" />

            <TextView
                android:id="@+id/tvLotRoundUp"
                style="@style/gilroy_600"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:gravity="center_vertical|end"
                android:textAlignment="viewEnd"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvLotRoundUpTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/guideline_v50"
                tools:text="@string/yes_confirm" />

            <TextView
                android:id="@+id/tvCopyOpenTradesTitle"
                style="@style/gilroy_400"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="6dp"
                android:gravity="center_vertical|start"
                android:text="@string/copy_opened_trades"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toStartOf="@+id/guideline_v50"
                app:layout_constraintStart_toStartOf="@+id/tvCopyModeTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvLotRoundUpTitle" />

            <TextView
                android:id="@+id/tvCopyOpenTrades"
                style="@style/gilroy_600"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:gravity="center_vertical|end"
                android:textAlignment="viewEnd"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvCopyOpenTradesTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/guideline_v50"
                tools:text="No" />

            <View
                android:id="@+id/offView"
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:layout_marginTop="20dp"
                android:background="?attr/color_c0a1e1e1e_c0affffff"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvCopyOpenTradesTitle" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline_v50"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.50" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

</com.scwang.smart.refresh.layout.SmartRefreshLayout>