<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ifvItemIcon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/draw_bitmap_img_source_permission_account_c1e1e1e_cffffff" />

    <TextView
        android:id="@+id/tvItemTitle"
        style="@style/medium_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:maxLines="2"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ifvItemIcon"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Trading Account Trading Account Trading Account Trading Account Trading Account Trading Account Trading Account Trading" />

    <TextView
        android:id="@+id/tvItemContent"
        style="@style/regular_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:maxLines="2"
        android:textAlignment="viewStart"
        android:textSize="10dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tvItemTitle"
        app:layout_constraintTop_toBottomOf="@+id/tvItemTitle"
        tools:text="(Once Account verified will enable)(Once Account verified will enable)(Once Account verified will enable)(Once Account verified will enable)(Once Account verified will enable)(Once Account verified will enable)" />

</androidx.constraintlayout.widget.ConstraintLayout>