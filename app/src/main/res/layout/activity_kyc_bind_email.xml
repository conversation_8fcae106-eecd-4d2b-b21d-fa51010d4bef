<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_endIcon="?attr/icon1Cs"
        app:hb_titleText="@string/link_your_email_address" />

    <TextView
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/c1f007fff"
        android:lineSpacingMultiplier="1.2"
        android:padding="12dp"
        android:text="@string/the_email_you_login_email"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp" />

    <TextView
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="12dp"
        android:text="@string/login_email"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp" />

    <cn.com.vau.common.view.login.LoginInputContentView
        android:id="@+id/emailView"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        app:icv_hintText="@string/email"
        app:icv_isInputEmail="true" />

    <TextView
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="12dp"
        android:text="@string/login_password"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp" />

    <cn.com.vau.common.view.login.LoginInputPwdView
        android:id="@+id/pwdView"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        app:ipv_hintText="@string/_8_16_characters" />

    <TextView
        android:id="@+id/tvNext"
        style="@style/login_tvNext_style"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="24dp"
        android:text="@string/send_otp_via_email" />
</LinearLayout>