<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/llAll"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/margin_horizontal_base">

    <TextView
        android:id="@+id/tvAll"
        style="@style/regular_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:text="@string/select_all"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_gravity="bottom"
        android:background="?attr/color_c1f1e1e1e_c1fffffff" />
</FrameLayout>
