<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/margin_horizontal_base">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="172dp">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivGuideImage"
            android:layout_width="match_parent"
            android:layout_height="172dp"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.core.widget.NestedScrollView>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:layout_weight="1">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvGuideTitle"
                style="@style/gilroy_600"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center_horizontal"
                android:maxLines="1"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBottom_toTopOf="@+id/tvGuideContent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                tools:text="[New feature] TradingView Indicators" />

            <TextView
                android:id="@+id/tvGuideContent"
                style="@style/gilroy_400"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:gravity="center_horizontal"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvGuideTitle"
                tools:text="Smarter tools, smarter trades! Tap on the chart to explore new variety of charting tools and indicators to elevate your trades.Tap on the chart to explore new variety of charting tools and indicators to elevate your trades." />

        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.core.widget.NestedScrollView>

</LinearLayout>