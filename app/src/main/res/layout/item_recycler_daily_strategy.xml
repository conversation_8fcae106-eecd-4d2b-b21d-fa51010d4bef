<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/padding_horizontal_base">

    <androidx.cardview.widget.CardView
        android:id="@+id/mCardView"
        android:layout_width="96dp"
        android:layout_height="67dp"
        app:cardBackgroundColor="?attr/color_c1f1e1e1e_c1fffffff"
        app:cardCornerRadius="10dp"
        app:cardElevation="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/mImageView"
            android:layout_width="96dp"
            android:layout_height="67dp"
            android:layout_margin="0.5dp"
            android:background="@drawable/draw_shape_stroke_c331e1e1e_c33ffffff_r10"
            android:scaleType="centerCrop"
            app:shapeAppearanceOverlay="@style/roundImageStyle10"
            tools:src="@drawable/shape_placeholder" />
    </androidx.cardview.widget.CardView>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:maxLines="2"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toStartOf="@+id/mCardView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Lorem ipsum dolor sit amet elit consectetur adipiscin." />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDate"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="@id/mCardView"
        app:layout_constraintStart_toStartOf="@+id/tvTitle"
        tools:text="01/03/2018" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvViews"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:drawablePadding="4dp"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="10dp"
        app:drawableStartCompat="@drawable/img_source2_eye_12x12"
        app:drawableTint="?attr/color_c331e1e1e_c33ffffff"
        app:layout_constraintEnd_toEndOf="@+id/tvTitle"
        app:layout_constraintTop_toTopOf="@+id/tvDate"
        tools:text="9999999" />

</androidx.constraintlayout.widget.ConstraintLayout>
