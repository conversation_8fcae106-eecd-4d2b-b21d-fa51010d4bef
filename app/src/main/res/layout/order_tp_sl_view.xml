<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivSwitch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="?attr/icon2CbSquareUncheck"
        android:paddingTop="12dp"
        android:paddingStart="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="RtlSymmetry" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTpSl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tp_sl"
        android:paddingHorizontal="4dp"
        android:textSize="12dp"
        android:paddingTop="11dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintStart_toEndOf="@id/ivSwitch"
        app:layout_constraintTop_toTopOf="parent"/>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gpSwitch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="ivSwitch,tvTpSl"/>

    <cn.com.vau.trade.view.OrderInputView
        android:id="@+id/inputViewTp"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginTop="8dp"
        android:layout_marginStart="12dp"
        android:background="@drawable/select_open_order_et_bg"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/inputViewSl"
        app:layout_constraintTop_toBottomOf="@id/tvTpSl"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clTpEstimated"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:orientation="horizontal"
        android:layout_marginBottom="2dp"
        android:background="@drawable/draw_shape_c262930_cffffff_r4"
        app:layout_constraintStart_toStartOf="@id/inputViewTp"
        app:layout_constraintBottom_toTopOf="@id/inputViewTp"
        android:maxWidth="160dp"
        android:visibility="gone"
        tools:visibility="gone"
        tools:ignore="HardcodedText">
        <TextView
            android:id="@+id/tvTpEstimated"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:paddingStart="6dp"
            android:textSize="10dp"
            android:gravity="center"
            android:textAlignment="center"
            android:singleLine="true"
            android:textColor="?attr/color_cebffffff_c1e1e1e"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tvTpEstimatedValue"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constrainedWidth="true"
            android:text="@string/estimated_pnl"
            tools:ignore="RtlSymmetry" />

        <TextView
            android:id="@+id/tvTpEstimatedValue"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:textSize="10dp"
            android:paddingEnd="6dp"
            android:gravity="center"
            android:textDirection="ltr"
            android:textAlignment="center"
            android:textColor="@color/c00c79c"
            android:text=" 00 USD"
            app:layout_constrainedWidth="true"
            app:layout_constraintStart_toEndOf="@id/tvTpEstimated"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBaseline_toBaselineOf="@id/tvTpEstimated"
            tools:ignore="RtlSymmetry" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <cn.com.vau.trade.view.OrderInputView
        android:id="@+id/inputViewSl"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="12dp"
        android:background="@drawable/select_open_order_et_bg"
        app:layout_constraintStart_toEndOf="@id/inputViewTp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTpSl"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clSlEstimated"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:paddingHorizontal="6dp"
        android:layout_marginBottom="2dp"
        android:maxWidth="160dp"
        android:background="@drawable/draw_shape_c262930_cffffff_r4"
        app:layout_constraintStart_toStartOf="@id/inputViewSl"
        app:layout_constraintBottom_toTopOf="@id/inputViewSl"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tvSlEstimated"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:textSize="10dp"
            android:gravity="center"
            android:textAlignment="center"
            android:singleLine="true"
            android:layout_marginBottom="2dp"
            android:textColor="?attr/color_cebffffff_c1e1e1e"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/tvSlEstimatedValue"
            android:text="@string/estimated_pnl"
            tools:visibility="visible"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/tvSlEstimatedValue"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:textSize="10dp"
            android:gravity="center"
            android:textDirection="ltr"
            android:textAlignment="center"
            android:layout_marginBottom="2dp"
            android:textColor="@color/c00c79c"
            android:text="  1000 USD"
            app:layout_constrainedWidth="true"
            app:layout_constraintStart_toEndOf="@id/tvSlEstimated"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBaseline_toBaselineOf="@id/tvSlEstimated"
            tools:visibility="visible"
            tools:ignore="HardcodedText" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tvTPTip"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        style="@style/gilroy_500"
        android:gravity="start"
        android:textAlignment="viewStart"
        app:layout_constraintTop_toBottomOf="@id/inputViewTp"
        app:layout_constraintStart_toStartOf="@id/inputViewTp"
        app:layout_constraintEnd_toEndOf="@id/inputViewTp"
        android:textSize="12dp"
        android:visibility="gone"
        android:textColor="@color/cf44040"
        tools:text="Price ≤ 92,323.12"
        tools:visibility="visible"/>

    <TextView
        android:id="@+id/tvSLTip"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        style="@style/gilroy_500"
        android:gravity="start"
        android:textAlignment="viewStart"
        app:layout_constraintTop_toBottomOf="@id/inputViewSl"
        app:layout_constraintStart_toStartOf="@id/inputViewSl"
        app:layout_constraintEnd_toEndOf="@id/inputViewSl"
        android:textSize="12dp"
        android:visibility="gone"
        android:textColor="@color/cf44040"
        tools:text="Price ≤ 92,323.12"
        tools:visibility="visible"/>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gpTpSl"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="inputViewSl,inputViewTp"
        tools:visibility="visible"/>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gpOther"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvTPTip,tvSLTip,clTpEstimated,clSlEstimated"
        tools:visibility="visible"/>

</merge>