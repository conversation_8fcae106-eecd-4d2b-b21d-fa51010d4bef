<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvDateYM"
        style="@style/bold_semi_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_horizontal_base"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:paddingStart="8dp"
        android:paddingTop="3dp"
        android:paddingEnd="8dp"
        android:paddingBottom="3dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="09/2021" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="20dp"
        android:layout_weight="1"
        android:gravity="start"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="14dp"
        app:layout_constraintEnd_toStartOf="@+id/tvMoney"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvDateYM"
        app:layout_goneMarginTop="@dimen/margin_horizontal_base"
        tools:text="入金/出金" />

    <TextView
        android:id="@+id/tvMoney"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="10dp"
        android:gravity="end|center_vertical"
        android:textAlignment="viewEnd"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvDateYM"
        tools:text="0.00 USD" />

    <TextView
        android:id="@+id/tvDate"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        tools:text="22/09/2021 00:20:29" />

    <TextView
        android:id="@+id/tvState"
        style="@style/medium_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:gravity="end"
        android:textAlignment="viewEnd"
        android:textColor="@color/ce35728"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="@+id/tvMoney"
        app:layout_constraintStart_toEndOf="@+id/tvDate"
        app:layout_constraintTop_toTopOf="@+id/tvDate"
        tools:text="SuccessfulSuccessfulSuccessfulSuccessfulSuccessfulSuccessfulSuccessfulSuccessfulSuccessfulSuccessfulSuccessfulSuccessfulSuccessfulSuccessfulSuccessfulSuccessfulSuccessfulSuccessfulSuccessfulSuccessful" />

    <View
        style="@style/cut_off_line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="12dp"
        app:layout_constraintTop_toBottomOf="@+id/tvState" />

</androidx.constraintlayout.widget.ConstraintLayout>