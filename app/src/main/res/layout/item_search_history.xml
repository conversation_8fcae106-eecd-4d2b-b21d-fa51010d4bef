<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_ProductName"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:gravity="center_vertical"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        tools:text="NZDJPY" />

    <View
        style="@style/cut_off_line"
        android:layout_gravity="bottom" />

</LinearLayout>