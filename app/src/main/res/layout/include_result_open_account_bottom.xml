<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/padding_horizontal_base"
    android:paddingBottom="36dp">

    <TextView
        android:id="@+id/tvPermissionTitle"
        style="@style/bold_semi_font"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:gravity="center_horizontal"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Congratulations" />

    <TextView
        android:id="@+id/tvPermissionContent"
        style="@style/medium_font"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:gravity="center_horizontal"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toBottomOf="@+id/tvPermissionTitle"
        tools:text="Your Live account application passed.Completed Lv2 ID authentication to start trading" />

    <View
        android:id="@+id/permissionView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/cut_off_line_height"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintTop_toBottomOf="@+id/tvPermissionContent" />

    <TextView
        android:id="@+id/tvPermission"
        style="@style/bold_semi_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginTop="16dp"
        android:ellipsize="end"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:gravity="center_vertical|start"
        android:textAlignment="viewStart"
        android:maxLines="2"
        android:text="@string/permissions"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toStartOf="@+id/guideline_v78"
        app:layout_constraintStart_toEndOf="@+id/guideline_v22"
        app:layout_constraintTop_toBottomOf="@+id/permissionView" />

    <cn.com.vau.common.view.OpenAccountPermissionText
        android:id="@+id/permissionTradingAccount"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/guideline_v22"
        app:layout_constraintTop_toBottomOf="@+id/tvPermission"
        app:type="1" />

    <cn.com.vau.common.view.OpenAccountPermissionText
        android:id="@+id/permissionDeposit"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/guideline_v22"
        app:layout_constraintTop_toBottomOf="@+id/permissionTradingAccount"
        app:type="2" />

    <cn.com.vau.common.view.OpenAccountPermissionText
        android:id="@+id/permissionTrade"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/guideline_v22"
        app:layout_constraintTop_toBottomOf="@+id/permissionDeposit"
        app:type="3" />

    <cn.com.vau.common.view.OpenAccountPermissionText
        android:id="@+id/permissionWithdrawal"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/guideline_v22"
        app:layout_constraintTop_toBottomOf="@+id/permissionTrade"
        app:type="4" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_v22"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.22" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_v78"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.78" />

</androidx.constraintlayout.widget.ConstraintLayout>