<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:ignore="HardcodedText">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_endIcon="?attr/icon1Faq"
        app:hb_endIcon1="?attr/icon1Signals" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/mRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0px"
        android:layout_weight="1"
        app:srlEnableLoadMore="false"
        app:srlEnableRefresh="true">

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.appbar.AppBarLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/transparent"
                android:orientation="vertical"
                app:elevation="0px">

                <!-- 需要被推走的布局 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_scrollFlags="scroll">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/clPersonal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_top_title"
                        android:paddingHorizontal="@dimen/padding_card_base">

                        <com.google.android.material.imageview.ShapeableImageView
                            android:id="@+id/ivAvatar"
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:scaleType="centerCrop"
                            android:src="@mipmap/ic_launcher"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:shapeAppearanceOverlay="@style/circleImageStyle" />

                        <TextView
                            android:id="@+id/tvNick"
                            style="@style/gilroy_600"
                            android:layout_width="0px"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_marginTop="2dp"
                            android:layout_marginEnd="8dp"
                            android:ellipsize="end"
                            android:gravity="start"
                            android:maxLines="1"
                            android:textAlignment="viewStart"
                            android:textColor="?attr/color_c1e1e1e_cebffffff"
                            android:textSize="18dp"
                            app:layout_constraintEnd_toStartOf="@+id/ivEndArrow"
                            app:layout_constraintStart_toEndOf="@+id/ivAvatar"
                            app:layout_constraintTop_toTopOf="@+id/ivAvatar"
                            tools:text="Signal Provider Name" />

                        <TextView
                            android:id="@+id/tvProviderId"
                            style="@style/gilroy_400"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="2dp"
                            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                            android:textDirection="ltr"
                            android:textSize="12dp"
                            app:layout_constraintBottom_toBottomOf="@+id/ivAvatar"
                            app:layout_constraintStart_toStartOf="@+id/tvNick"
                            tools:text="Provider ID：1234567" />

                        <ImageView
                            android:id="@+id/ivEndArrow"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:contentDescription="@string/app_name"
                            android:src="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginTop="12dp"
                        android:background="?attr/color_c331e1e1e_c33ffffff" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/padding_card_base">

                        <TextView
                            android:id="@+id/tvKey1"
                            style="@style/gilroy_500"
                            android:layout_width="0px"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:gravity="start"
                            android:maxLines="1"
                            android:text="@string/strategies"
                            android:textAlignment="viewStart"
                            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                            android:textSize="12dp"
                            app:layout_constraintEnd_toStartOf="@+id/tvKey2"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tvStrategies"
                            style="@style/gilroy_600"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:textColor="?attr/color_c1e1e1e_cebffffff"
                            android:textSize="16dp"
                            app:layout_constraintStart_toStartOf="@+id/tvKey1"
                            app:layout_constraintTop_toBottomOf="@+id/tvKey1"
                            tools:text="3/10" />

                        <TextView
                            android:id="@+id/tvKey2"
                            style="@style/gilroy_500"
                            android:layout_width="0px"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:gravity="end"
                            android:maxLines="1"
                            android:text="@string/active_copiers"
                            android:textAlignment="viewEnd"
                            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                            android:textSize="12dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/tvKey1"
                            app:layout_constraintTop_toTopOf="@+id/tvKey1" />

                        <TextView
                            android:id="@+id/tvCopiers"
                            style="@style/gilroy_600"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="?attr/color_c1e1e1e_cebffffff"
                            android:textSize="16dp"
                            app:layout_constraintEnd_toEndOf="@+id/tvKey2"
                            app:layout_constraintTop_toTopOf="@+id/tvStrategies"
                            tools:text="100" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="8dp"
                        android:layout_marginBottom="8dp"
                        android:background="?attr/color_c0a1e1e1e_c0affffff" />
                </LinearLayout>

                <!-- 需要被卡住的布局 -->
                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <cn.com.vau.common.view.tablayout.DslTabLayout
                        android:id="@+id/mTabLayout"
                        android:layout_width="match_parent"
                        android:layout_height="33dp" />

                    <View
                        style="@style/TabLayoutBottomLineStyle"
                        android:layout_gravity="bottom" />
                </FrameLayout>
            </com.google.android.material.appbar.AppBarLayout>

            <!-- 底部可滑动布局 -->
            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/viewPager2"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_behavior="@string/appbar_scrolling_view_behavior" />
        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <TextView
        android:id="@+id/tvCreateStrategy"
        style="@style/main_bottom_button_theme"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginVertical="@dimen/margin_vertical_base"
        android:text="@string/create_new_strategy" />
</LinearLayout>