<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="cn.com.vau.page.common.selectArea.SelectAreaCodeActivity">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_endIcon="?attr/icon1Cs"
        app:hb_titleText="@string/select_nationality"
        app:layout_constraintTop_toTopOf="parent" />

    <include layout="@layout/merge_search" />

    <FrameLayout
        android:id="@+id/clFilterContainer"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="5dp"
        android:background="?attr/mainLayoutBg"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etSearch">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/mRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:dividerHeight="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </FrameLayout>

    <ViewStub
        android:id="@+id/mVsNoData"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout="@layout/vs_layout_no_data"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etSearch" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clContainer"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etSearch">

        <TextView
            android:id="@+id/titlePopular"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:gravity="center_vertical"
            android:paddingTop="@dimen/padding_vertical_base"
            android:textColor="@color/ce35728"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Popular" />

        <ExpandableListView
            android:id="@+id/expandListView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:dividerHeight="0dp"
            android:groupIndicator="@null"
            android:paddingTop="16dp"
            android:scrollbars="none"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/rcyNavLetter"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titlePopular" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rcyNavLetter"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginTop="0dp"
            android:layout_marginBottom="0dp"
            app:layout_constraintBottom_toBottomOf="@+id/expandListView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/expandListView"
            app:layout_constraintTop_toTopOf="@+id/expandListView"
            tools:listitem="@layout/item_nav_letter" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
