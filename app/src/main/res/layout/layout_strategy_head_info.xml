<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivAvatar"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:scaleType="centerCrop"
        android:src="@mipmap/ic_launcher"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/roundImageStyle10" />

    <TextView
        android:id="@+id/tvNick"
        style="@style/gilroy_600"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="8dp"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivAvatar"
        app:layout_constraintTop_toTopOf="@+id/ivAvatar"
        tools:text="Strategy NameStrategy NameStrategy NameStrategy NameStrategy Name" />

    <TextView
        android:id="@+id/tvIdKey"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivAvatar"
        app:layout_constraintStart_toStartOf="@+id/tvNick"
        tools:ignore="HardcodedText"
        tools:text="Strategy ID：" />

    <TextView
        android:id="@+id/tvStrategyId"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvIdKey"
        app:layout_constraintStart_toEndOf="@+id/tvIdKey"
        tools:text="1234567" />

    <TextView
        android:id="@+id/tvRoi"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:text="@string/return_another"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@id/gl33"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivAvatar" />

    <TextView
        android:id="@+id/tvRoiRate"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:text="-"
        android:textColor="@color/c00c79c"
        android:textDirection="ltr"
        android:textSize="18dp"
        app:layout_constraintEnd_toStartOf="@id/gl33"
        app:layout_constraintStart_toStartOf="@+id/tvRoi"
        app:layout_constraintTop_toBottomOf="@+id/tvRoi"
        tools:text="120.07%" />

    <!--    此LinearLayout为了下面带图标的TextView能正常显示在规定的区域内-->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:gravity="center"
        app:layout_constraintEnd_toStartOf="@id/gl66"
        app:layout_constraintStart_toEndOf="@id/gl33"
        app:layout_constraintTop_toBottomOf="@id/ivAvatar">

        <TextView
            android:id="@+id/tvProfitSharing"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawablePadding="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/profit_sharing"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvProfitSharingRate"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="6dp"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="-"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="18dp"
        app:layout_constraintEnd_toStartOf="@id/gl66"
        app:layout_constraintStart_toEndOf="@id/gl33"
        app:layout_constraintTop_toBottomOf="@+id/tvRoi"
        tools:text="20.07%" />

    <TextView
        android:id="@+id/tvSettlement"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:drawablePadding="4dp"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:text="@string/settlement"
        android:textAlignment="viewEnd"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/gl66"
        app:layout_constraintTop_toBottomOf="@id/ivAvatar" />

    <TextView
        android:id="@+id/tvSettlementValue"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:text="-"
        android:textAlignment="viewEnd"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/gl66"
        app:layout_constraintTop_toBottomOf="@+id/tvRoi"
        tools:text="Weekly" />

    <View
        style="@style/cut_off_line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvRoiRate" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl33"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.333" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl66"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.666" />

</androidx.constraintlayout.widget.ConstraintLayout>