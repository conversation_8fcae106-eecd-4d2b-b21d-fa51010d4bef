<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="16dp"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@id/tvFinish"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clPassportPhoto"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:paddingVertical="@dimen/padding_vertical_base"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tvBankPhoto"
                    style="@style/bold_semi_font"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="@string/bank_statement_photo" />

                <TextView
                    android:id="@+id/tvTip1"
                    style="@style/gilroy_500"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:drawablePadding="8dp"
                    android:text="@string/shows_full_name_and_address"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:drawableStartCompat="@drawable/bitmap_img_source_tick11x8_c00c79c"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvBankPhoto" />

                <ImageView
                    android:id="@+id/ivTip2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/app_name"
                    android:src="@drawable/bitmap_img_source_tick11x8_c00c79c"
                    app:layout_constraintBottom_toBottomOf="@+id/tvTip2"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tvTip2" />

                <cn.com.vau.common.view.system.LinkSpanTextView
                    android:id="@+id/tvTip2"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginTop="8dp"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/ivTip2"
                    app:layout_constraintTop_toBottomOf="@id/tvTip1"
                    tools:text="@string/document_issued_within_x" />

                <ImageView
                    android:id="@+id/ivPhoto"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:contentDescription="@string/app_name"
                    android:src="?attr/imgBankStatement"
                    android:visibility="invisible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvTip2"
                    tools:visibility="visible" />

                <TextView
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="40dp"
                    android:layout_marginTop="16dp"
                    android:gravity="center"
                    android:text="@string/upload_a_clear_poa_or_blur"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="11dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ivPhoto" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="9dp"
                android:layout_marginEnd="6dp"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:layout_constraintTop_toBottomOf="@+id/clPassportPhoto"
                app:spanCount="3"
                tools:itemCount="5"
                tools:listitem="@layout/item_grid_upload" />

            <LinearLayout
                android:id="@+id/llTip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="4dp"
                android:orientation="vertical"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/recyclerView">

                <TextView
                    android:id="@+id/tvMaxUploadWarnTip"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/maximum_6_photo_to_upload"
                    android:textColor="@color/ce35728"
                    android:textSize="10dp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvSupportTip"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="@string/supported_file_types"
                    android:textColor="?attr/color_c731e1e1e_c61ffffff"
                    android:textSize="10dp" />

                <TextView
                    android:id="@+id/tvSupportTip2"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="@string/maximum_upload_file_size"
                    android:textColor="?attr/color_c731e1e1e_c61ffffff"
                    android:textSize="10dp" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/tvFinish"
        style="@style/main_bottom_button_theme"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginBottom="16dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:clickable="false"
        android:text="@string/finish"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>