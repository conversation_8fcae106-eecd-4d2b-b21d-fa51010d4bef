<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:paddingBottom="6dp"
    android:clipToPadding="false">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        app:cardBackgroundColor="?attr/color_cffffff_c262930"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:cardCornerRadius="10dp"
        app:cardElevation="6dp"
        app:cardPreventCornerOverlap="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/margin_horizontal_base">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:src="?attr/imgInApp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTitle"
                style="@style/gilroy_600"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="48dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="@+id/icon"
                app:layout_constraintEnd_toStartOf="@id/ivClose"
                app:layout_constraintStart_toEndOf="@+id/icon"
                app:layout_constraintTop_toTopOf="@+id/icon"
                tools:text="Dividend Announcement" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivClose"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:padding="12dp"
                android:src="@drawable/draw_bitmap2_close10x10_c1e1e1e_cebffffff"
                app:layout_constraintBottom_toBottomOf="@id/tvTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/tvTitle" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvIntro"
                style="@style/gilroy_400"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="6dp"
                android:lineSpacingMultiplier="1.2"
                android:maxLines="2"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:gravity="start"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTitle"
                tools:text="@string/x_x_lots_x_at_x" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

</FrameLayout>
