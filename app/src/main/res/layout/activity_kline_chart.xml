<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:id="@+id/llRoot"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <View
        android:id="@+id/viewStatusbar"
        android:layout_width="1dp"
        android:layout_height="match_parent" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="50dp">

            <LinearLayout
                android:id="@+id/llTopInfo"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/margin_horizontal_base"
                android:layout_marginEnd="5dp"
                android:layout_marginBottom="7dp"
                android:background="@drawable/draw_shape_stroke_c331e1e1e_c33ffffff_r100"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tv_prod_name"
                    style="@style/gilroy_600"
                    android:layout_width="110dp"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:paddingStart="12dp"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    tool:text="USDCAD" />

                <TextView
                    android:id="@+id/tv_board_sellprice"
                    style="@style/gilroy_600"
                    android:layout_width="82dp"
                    android:layout_height="match_parent"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    tool:text="1232.20397" />

                <TextView
                    android:id="@+id/tv_board_diff"
                    style="@style/gilroy_500"
                    android:layout_width="130dp"
                    android:layout_height="match_parent"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:textColor="@color/c00c79c"
                    android:textDirection="ltr"
                    android:textSize="14dp"
                    tool:text="+2333.0073(+99.29%)" />

                <TextView
                    android:id="@+id/tv_board_time"
                    style="@style/gilroy_400"
                    android:layout_width="52dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="12dp"
                    android:ellipsize="end"
                    android:gravity="center_vertical|end"
                    android:maxLines="1"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    tool:text="23:59:22" />
            </LinearLayout>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvOrders"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
                android:paddingHorizontal="12dp"
                android:paddingVertical="6dp"
                android:text="@string/orders"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintTop_toTopOf="@id/llTopInfo"
                app:layout_constraintBottom_toBottomOf="@id/llTopInfo"
                app:layout_constraintStart_toEndOf="@id/llTopInfo"/>

            <ImageView
                android:id="@+id/iv_back1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginEnd="15dp"
                android:contentDescription="@string/app_name"
                android:padding="15dp"
                android:src="@drawable/draw_bitmap2_close16x16_c731e1e1e_c61ffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <ImageView
                android:id="@+id/ivSetting"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@id/iv_back1"
                android:contentDescription="@string/app_name"
                android:padding="15dp"
                android:src="?attr/icon1Setting"
                app:layout_constraintEnd_toStartOf="@id/iv_back1"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivKNewGuide"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@id/ivSetting"
                android:padding="15dp"
                app:srcCompat="?attr/icon1Guide"
                app:layout_constraintEnd_toStartOf="@id/ivSetting"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="28dp"
            android:layout_marginEnd="16dp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_chart_type"
                android:layout_width="match_parent"
                android:layout_height="28dp"
                android:layout_marginEnd="15dp"
                android:visibility="visible" />

        </FrameLayout>

        <!-- 所有图表区域 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layoutDirection="ltr">

            <!--主、副图容器-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <!--主图外容器-->
                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/rl_scale"
                            android:layout_width="match_parent"
                            android:layout_height="20dp"
                            android:layout_marginEnd="55dp"
                            android:gravity="center_vertical"
                            app:layout_constraintBottom_toBottomOf="parent">

                            <TextView
                                android:id="@+id/scale_left"
                                style="@style/gilroy_400"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                                android:textSize="9dp"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintBottom_toBottomOf="parent"
                                tool:text="12/05 08:00"/>

                            <TextView
                                android:id="@+id/scale_middle"
                                style="@style/gilroy_400"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                                android:textSize="9dp"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintBottom_toBottomOf="parent"
                                tool:text="12/05 08:00" />

                            <TextView
                                android:id="@+id/scale_right"
                                style="@style/gilroy_400"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                                android:textSize="9dp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintBottom_toBottomOf="parent"
                                tool:text="12/05 08:00" />
                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <!-- 主图 -->
                        <cn.com.vau.common.view.kchart.views.ChartViewImp
                            android:id="@+id/chartview"
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            android:layout_marginEnd="@dimen/margin_horizontal_base"
                            app:layout_constraintBottom_toTopOf="@id/rl_scale"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:layout_marginEnd="16dp">

                        <LinearLayout
                            android:id="@+id/llChartType"
                            android:layout_width="wrap_content"
                            android:layout_height="18dp"
                            android:layout_marginStart="@dimen/margin_horizontal_base"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent">

                            <TextView
                                android:id="@+id/tvMainChartTitle"
                                style="@style/gilroy_500"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="4dp"
                                android:textColor="?attr/color_c1e1e1e_cebffffff"
                                android:textSize="9dp"
                                android:visibility="visible"
                                tool:text="@string/ma"
                                tool:visibility="visible"/>

                            <TextView
                                android:id="@+id/tvMainChartInfo"
                                style="@style/gilroy_500"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:singleLine="true"
                                android:textColor="?attr/color_c1e1e1e_cebffffff"
                                android:textSize="9dp"
                                android:visibility="visible"
                                tool:text="@string/ma" />

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llCurrentOrderInfo"
                            android:layout_width="wrap_content"
                            android:layout_height="18dp"
                            android:layout_marginStart="5dp"
                            android:layout_marginTop="2dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:visibility="gone"
                            app:layout_constraintStart_toEndOf="@id/llChartType"
                            app:layout_constraintTop_toTopOf="@id/llChartType"
                            app:layout_constraintBottom_toBottomOf="@id/llChartType"
                            tool:visibility="visible">


                        </LinearLayout>

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </FrameLayout>

                <!--副图 & 交易量图-->
                <FrameLayout
                    android:id="@+id/fl_subchart"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <cn.com.vau.common.view.kchart.views.ChartViewImp
                        android:id="@+id/chart_sub_view"
                        android:layout_width="match_parent"
                        android:layout_height="100dp"
                        android:layout_marginEnd="@dimen/margin_horizontal_base" />

                    <!--交易量图-->
                    <cn.com.vau.common.view.kchart.views.ChartViewImp
                        android:id="@+id/volume_chart"
                        android:layout_width="match_parent"
                        android:layout_height="100dp"
                        android:layout_marginEnd="@dimen/margin_horizontal_base"
                        android:visibility="gone" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:layout_marginEnd="16dp">

                        <LinearLayout
                            android:id="@+id/llSubChartType"
                            android:layout_width="wrap_content"
                            android:layout_height="18dp"
                            android:layout_marginStart="5dp"
                            android:layout_marginTop="5dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:paddingHorizontal="8dp">

                            <TextView
                                android:id="@+id/tvSubChartTitle"
                                style="@style/gilroy_500"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/macd"
                                android:textColor="?attr/color_c1e1e1e_cebffffff"
                                android:textDirection="ltr"
                                android:textSize="9dp"
                                android:visibility="visible" />

                            <TextView
                                android:id="@+id/tvSubChartInfo"
                                style="@style/gilroy_500"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="4dp"
                                android:singleLine="true"
                                android:textColor="?attr/color_c1e1e1e_cebffffff"
                                android:textSize="9dp"
                                android:visibility="visible"
                                tool:text="@string/ma" />

                        </LinearLayout>

                    </LinearLayout>

                </FrameLayout>

            </LinearLayout>

            <!-- 分时图外容器 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/timeChartContainer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:background="?attr/mainLayoutBg"
                android:visibility="invisible">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clTimeScale"
                    android:layout_width="match_parent"
                    android:layout_height="20dp"
                    android:gravity="center_vertical"
                    app:layout_constraintBottom_toBottomOf="parent">

                    <TextView
                        android:id="@+id/scale_time_left"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="9dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        tool:text="12/05 08:00" />

                    <TextView
                        android:id="@+id/scale_time_middle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="9dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        tool:text="12/05 08:00" />

                    <TextView
                        android:id="@+id/scale_time_right"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="9dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        tool:text="12/05 08:00" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!-- 分时图 -->
                <cn.com.vau.common.view.kchart.views.ChartViewImp
                    android:id="@+id/chart_time_view"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toTopOf="@id/clTimeScale"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Drag the box to set Stop Loss/Take Profit -->
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvDragTip"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_gravity="bottom|center_horizontal"
                android:layout_marginBottom="24dp"
                android:background="@drawable/shape_c007fff_r6"
                android:drawablePadding="12dp"
                android:gravity="center_vertical"
                android:paddingHorizontal="8dp"
                android:text="@string/drag_the_box_loss_take_profit"
                android:textColor="@color/cffffff"
                android:textSize="14dp"
                android:visibility="gone" />

            <!-- 空布局 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/rl_empty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone"
                tool:visibility="visible">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/img_kline_refresh"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 十字线显示板 -->
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:layoutDirection="ltr"
                android:paddingStart="30dp"
                android:paddingEnd="58dp"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/rl_charttip_float"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:minWidth="90dp"
                    android:layoutDirection="locale"
                    android:background="@drawable/draw_shape_cf3f3f3_c262930_r4"
                    android:padding="4dp"
                    android:visibility="gone"
                    tool:visibility="visible">

                    <TextView
                        android:id="@+id/tv_charttip_time"
                        style="@style/gilroy_500"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="9dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tool:text="31/08/2023 14:30" />

                    <TextView
                        android:id="@+id/tv_charttip_open"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:text="@string/open__quotation"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="9dp"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_charttip_time"
                        app:layout_constraintEnd_toStartOf="@id/tv_charttip_open_value" />

                    <TextView
                        android:id="@+id/tv_charttip_open_value"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:minWidth="50dp"
                        android:ellipsize="end"
                        android:gravity="end"
                        android:textDirection="ltr"
                        android:textAlignment="viewEnd"
                        android:maxLines="1"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="9dp"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintBottom_toBottomOf="@id/tv_charttip_open"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/tv_charttip_open"
                        app:layout_constraintTop_toTopOf="@id/tv_charttip_open"
                        tool:text="2383.823" />

                    <TextView
                        android:id="@+id/tv_charttip_high"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="3dp"
                        android:text="@string/high_most"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="9dp"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        app:layout_constraintEnd_toStartOf="@id/tv_charttip_high_value"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_charttip_open" />

                    <TextView
                        android:id="@+id/tv_charttip_high_value"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:minWidth="50dp"
                        android:ellipsize="end"
                        android:gravity="end"
                        android:textDirection="ltr"
                        android:textAlignment="viewEnd"
                        android:maxLines="1"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="9dp"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintBottom_toBottomOf="@id/tv_charttip_high"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/tv_charttip_high"
                        app:layout_constraintTop_toTopOf="@id/tv_charttip_high"
                        tool:text="2383.823" />

                    <TextView
                        android:id="@+id/tv_charttip_low"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="3dp"
                        android:text="@string/low_most"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="9dp"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        app:layout_constraintEnd_toStartOf="@id/tv_charttip_low_value"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_charttip_high" />

                    <TextView
                        android:id="@+id/tv_charttip_low_value"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:minWidth="50dp"
                        android:ellipsize="end"
                        android:gravity="end"
                        android:textDirection="ltr"
                        android:textAlignment="viewEnd"
                        android:maxLines="1"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="9dp"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintBottom_toBottomOf="@id/tv_charttip_low"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/tv_charttip_low"
                        app:layout_constraintTop_toTopOf="@id/tv_charttip_low"
                        tool:text="2383.823" />

                    <TextView
                        android:id="@+id/tv_charttip_close"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="3dp"
                        android:text="@string/close__quotation"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="9dp"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        app:layout_constraintEnd_toStartOf="@id/tv_charttip_close_value"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_charttip_low" />

                    <TextView
                        android:id="@+id/tv_charttip_close_value"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:minWidth="50dp"
                        android:ellipsize="end"
                        android:gravity="end"
                        android:textDirection="ltr"
                        android:textAlignment="viewEnd"
                        android:maxLines="1"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="9dp"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintBottom_toBottomOf="@id/tv_charttip_close"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/tv_charttip_close"
                        app:layout_constraintTop_toTopOf="@id/tv_charttip_close"
                        tool:text="2383.823" />

                    <!-- 涨跌额 -->
                    <TextView
                        android:id="@+id/tv_charttip_change_amount"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="3dp"
                        android:text="@string/kline_tips_change"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="9dp"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        app:layout_constraintEnd_toStartOf="@id/tv_charttip_change_amount_value"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_charttip_close" />

                    <TextView
                        android:id="@+id/tv_charttip_change_amount_value"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:minWidth="50dp"
                        android:ellipsize="end"
                        android:gravity="end"
                        android:textDirection="ltr"
                        android:textAlignment="viewEnd"
                        android:maxLines="1"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="9dp"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintBottom_toBottomOf="@id/tv_charttip_change_amount"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/tv_charttip_change_amount"
                        app:layout_constraintTop_toTopOf="@id/tv_charttip_change_amount"
                        tool:text="2383.823" />

                    <!-- 涨跌幅 -->
                    <TextView
                        android:id="@+id/tv_charttip_change_percent"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="3dp"
                        android:text="@string/percent_change"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="9dp"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        app:layout_constraintEnd_toStartOf="@id/tv_charttip_change_percent_value"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_charttip_change_amount" />

                    <TextView
                        android:id="@+id/tv_charttip_change_percent_value"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:minWidth="50dp"
                        android:ellipsize="end"
                        android:gravity="end"
                        android:textDirection="ltr"
                        android:textAlignment="viewEnd"
                        android:maxLines="1"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="9dp"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        app:layout_constraintBottom_toBottomOf="@id/tv_charttip_change_percent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/tv_charttip_change_percent"
                        app:layout_constraintTop_toTopOf="@id/tv_charttip_change_percent"
                        tool:text="2383.823" />

                    <!-- 振幅 -->
                    <TextView
                        android:id="@+id/tv_charttip_amplitude"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="3dp"
                        android:text="@string/fluctuation"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="9dp"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/tv_charttip_amplitude_value"
                        app:layout_constraintTop_toBottomOf="@id/tv_charttip_change_percent" />

                    <TextView
                        android:id="@+id/tv_charttip_amplitude_value"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:minWidth="50dp"
                        android:ellipsize="end"
                        android:gravity="end"
                        android:textDirection="ltr"
                        android:textAlignment="viewEnd"
                        android:maxLines="1"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="9dp"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintBottom_toBottomOf="@id/tv_charttip_amplitude"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/tv_charttip_amplitude"
                        app:layout_constraintTop_toTopOf="@id/tv_charttip_amplitude"
                        tool:text="2383.823" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </FrameLayout>


        </FrameLayout>
    </LinearLayout>

</LinearLayout>
