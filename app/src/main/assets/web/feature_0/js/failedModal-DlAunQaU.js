import{P as o}from"./vant-vendor-D8PsFlrJ.js";import{_ as e,r as s}from"./index-M11nEPjl.js";import{M as a,K as t,L as n,S as r}from"./vue-vendor-DjIN0JG5.js";const c={props:{value:{type:Boolean}},computed:{show:{get(){return this.$props.value},set(o){this.$emit("update:value",o)}}},methods:{close(){this.show=!1,s("106")},back(){this.show=!1,s("106")}}},i={class:"modal_btn"};const l=e(c,[["render",function(e,s,c,l,u,d){const p=o;return t(),a(p,{show:d.show,"onUpdate:show":s[1]||(s[1]=o=>d.show=o),onClose:d.close},{default:n((()=>[s[2]||(s[2]=r("div",{class:"modal_msg space_2"},[r("p",null," Unfortunately, you have failed the quiz. You are welcome to try again in 7 days. Our Education (https://www.vantagemarkets.com/en-au/professional-trading-account/) page is a great resource to improve your product and trading knowledge. Please contact us on 1300 945 517 <NAME_EMAIL> for any questions. ")],-1)),r("div",i,[r("div",{class:"btn flex-center btn_next",onClick:s[0]||(s[0]=(...o)=>d.back&&d.back(...o))},"OK")])])),_:1},8,["show","onClose"])}]]);export{l as F};
