import{s as e,E as s}from"./vant-vendor-D8PsFlrJ.js";import{n as o}from"./navbar-DfgFEjpa.js";import{n as t}from"./footer-BLTvWGoM.js";import{i as a,j as i,s as r,r as l,_ as n}from"./index-M11nEPjl.js";import{K as d}from"./vendor-CwRwASPO.js";import{x as A}from"./xssIgnoreConfig-EdhXgEyG.js";import{g as c}from"./news-Bq96hVLI.js";import{J as m,K as g,S as p,A as u,I as f,j as h,M as k,V as w,F as v,Y as C}from"./vue-vendor-DjIN0JG5.js";import{s as j}from"./skeletonAnalysis-DF5fKjSL.js";import y from"./empty_dark-BVM0wdOH.js";import x from"./empty_light-t-w1hjKQ.js";const T={data:()=>({detail:"",loading:!0,isNoData:!1}),methods:{formatTime:a,filterXssCode:e=>d(e,A),goBack(){l("501")},getDetail(){const s={id:this.$route.params.id};c(s).then((s=>{this.loading=!1;const{resultCode:o,data:t,msgInfo:a}=s;if("V00000"===o){this.detail=t?t.obj:{};const e=Object.keys(this.detail);this.isNoData=!e.length}else e({message:a,wordBreak:"break-word"}),this.isNoData=!0})).catch((s=>{this.loading=!1,this.isNoData=!0;const{message:o}=s;e({message:o,wordBreak:"break-word"})}))},setHeaderStyle(){if(this.detail.imgUrl)return{background:`url(${this.detail.imgUrl}) no-repeat center center`}},setNavStyle:()=>i()?"isIos":"isAndroid"},created(){this.getDetail()},mounted(){r({code:"250",title:this.$t("Newsletter")})},beforeCreate(){},beforeDestroy(){i()&&document.body.removeAttribute("style")}},Q=["src"];const B={class:"container"},I={key:1,class:"newsletter"},U={key:1},S={class:"header"},b={class:"info"},D={class:"title"},E={class:"time"},V={class:"banner"},N=["src"],z={class:"content"},L=["innerHTML"];const M=n({components:{newFooter:t,navBar:o,backTop:n({data:()=>({goTop_dark:"data:image/webp;base64,UklGRhQCAABXRUJQVlA4WAoAAAAQAAAAIwAAIwAAQUxQSAYBAAABgCIAbNxGyEjjHe8d3qCgoCAjISPhYf1hjwge3fD+UBw8bFysHbZk7wURMQFgfhomSswpzgNC48PC5nho8UzspoMnjNx0DCaM3JjQgMTNCSuBuGMMpZG7joUDd979oV4EAAeuburcKnwAiLXsybUFkOvy6nypMQ6Gsmxfwt5h9khWzeKZokOyqmoWByWbZC1msSU2S1b9UH1XzWKyS1ZdRVVW1SymZLmrrvyHV9XNksjy9li5xOvj0xInS7HknIf/MOB/QFj6RYC9h+8be/cAQB4/AQDseu3/wNjnBsUQe1AoAVI7QqgjtYoI1jC2uQVw7slHO2i4j7ZlD43xOsfEnGi6IpgBVlA4IOgAAABQBgCdASokACQAPk0gjUQioiEY7mQAKATEtIACH2/5Tys6gHQAGMg4CopWu8TuotBjDWUMvJ1VfjT7wAD+/xIOf4b/k3xL+VBf7dW5EhJRzCEIQOg9nu5WL/FrdH7ae73KI3+VFZ/5Tk/aC94eIyf5B98P6ARCm9/0xX4kC/mvzUS3CTiPTfTXFE6iOHfUlsMJgsU/iYkm63siXUqQlzlxGw2h3trfViDkB71VD+MU/+Shd4TEV3O80DQFw8f04QVeOvR1ApOLwS3MNS90zpxpB+pxGvzxf/5ecfDwz5McImmkz/hgoAAA",goTop_light:"data:image/webp;base64,UklGRjICAABXRUJQVlA4WAoAAAAQAAAAIwAAIwAAQUxQSAkBAAABgBoAcBDnHKgwARPE8Qs8qjgcVcSdiztZeSN0BGRl6zpC64qsjHx5G5DcJT9BREwAqN30gpEohnUaoLLfSB18jRHJjN7S36nq3KuGQJVxUAxI1XEo9EgNQ5+7U9M546nxeMFWCACeiqcYzwJ5gL2ULKm0wUBlfje+lchNijyf/0zW22rhJJLYsuwGTiIiiQ0YdZwkm1gXSc1J5FPkSySxSs9J5GARPkQSq6LmV+SgCx0ipyai5uNxUI6Ox7cmLJpszrhOz3Bzz+BgaxcAvIV+/sjqAQAtdgQAGFv5C8xtZsh2ewvscuCwHjooO6wVHGi7uc7cgdGjDUeo6IPu1UNlN60hEkVcJgdqAFZQOCACAQAAUAcAnQEqJAAkAD5NIIxEIqIhGO5kACgExLSAAiC/+Q8zD2AYyDgKila7wrfEqao9zBnC+LySvAkCAoSx1e6ZnqtoEAD++uZz+c/xPxdeIa/rocWGGr5YZTA7kumMyaw+W50pOuHU3P5rcf8bA+Djq+9x/9TEeCyrMV4P+Vm859o6Eq845kgnjr6dklrHCbXGAxo+dNoJq/Fc7uVi/+9c8xZPSEQDydE3CKoAE7tXW/p75lfUKn/yCHq0DYW5VUL7VHNeuK9PzBLosC/ejyqiW+nhewUnIjPoiNvvB/7Md8d6CLeWh97ylDasBf5b4A6uURSlfBVfRai9DCdaTf99zAAA",scrollTop:0}),computed:{theme(){return this.$pinia.state.value.params.themeTxt}},props:{gap:{type:Number,default:200}},mounted(){window.addEventListener("scroll",this.scrollTops,!0)},beforeDestroy(){window.removeEventListener("scroll",this.scrollTops)},methods:{scrollTops(){this.scrollTop=document.documentElement.scrollTop},toTop(){const e=document.documentElement.scrollTop||document.body.scrollTop,s=Math.floor(e/1.3);e<=0||(window.scrollTo(0,s),window.requestAnimationFrame(this.toTop))}}},[["render",function(e,s,o,t,a,i){return g(),m("div",{onClick:s[0]||(s[0]=e=>i.toTop()),class:"goto-back",style:u({display:a.scrollTop<o.gap?"none":""})},[p("img",{src:"dark"===i.theme?a.goTop_dark:a.goTop_light},null,8,Q)],4)}],["__scopeId","data-v-d0142511"]]),skeletonAnalysis:j},mixins:[T],computed:{noDataImg(){return"dark"===this.$pinia.state.value.params.themeTxt?y:x}}},[["render",function(e,o,t,a,i,r){var l,n,d;const A=f("nav-bar"),c=f("skeletonAnalysis"),u=s,j=f("newFooter"),y=f("back-top");return g(),m("div",B,[h(A,{leftText:e.$t("Newsletter"),onClickLeft:e.goBack},null,8,["leftText","onClickLeft"]),e.loading?(g(),k(c,{key:0})):(g(),m("div",I,[e.isNoData?(g(),k(u,{key:0,class:"no_data",image:r.noDataImg,description:e.$t("no_records_found"),"image-size":"80"},null,8,["image","description"])):(g(),m("div",U,[p("div",S,[p("div",b,[p("div",D,w(e.detail.title),1),p("div",E,w(e.detail.createTime),1)]),p("div",V,[p("img",{src:e.detail.imgUrl,alt:""},null,8,N)])]),p("div",z,[(g(!0),m(v,null,C(e.detail.htmlList,(s=>(g(),m("div",{key:s,class:"html-rich",innerHTML:e.filterXssCode(s)},null,8,L)))),128))]),h(j,{class:"content",views:(null==(l=e.detail)?void 0:l.newsSource)||0,time:null==(n=e.detail)?void 0:n.createTime,source:(null==(d=e.detail)?void 0:d.newsSourceName)||""},null,8,["views","time","source"])]))])),h(y,{gap:150})])}],["__scopeId","data-v-637d33ff"]]);export{M as default};
