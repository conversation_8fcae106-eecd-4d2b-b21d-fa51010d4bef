package cn.com.vau.util.widget.dialog.base

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.os.Build
import android.view.*
import androidx.viewbinding.ViewBinding
import com.lxj.xpopup.impl.FullScreenPopupView

@SuppressLint("ViewConstructor")
open class FullScreenDialog<VB : ViewBinding>(
    context: Context,
    viewBinding: ((LayoutInflater, ViewGroup, Boolean) -> VB),
    private val onCreateListener: ((VB) -> Unit)? = null,
    private val onDismissListener: (() -> Unit)? = null,
) : FullScreenPopupView(context), IDialog<VB> {

    private val inflater = LayoutInflater.from(context)

    protected var mContentBinding = viewBinding.invoke(inflater, fullPopupContainer, false)

    override fun addInnerContent() {
        fullPopupContainer.addView(mContentBinding.root)
    }

    override fun onCreate() {
        super.onCreate()
        onCreateListener?.invoke(mContentBinding)
        processBackPress()
        initView()
    }

    private fun initView() {
        setContentView()
    }

    /**
     * 设置内容区域
     */
    open fun setContentView() {

    }

    private fun processBackPress() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU && dialog != null) {
            dialog.onBackInvokedDispatcher.registerOnBackInvokedCallback(0) {
                if (onBackPressed()) {
                    return@registerOnBackInvokedCallback
                }
                if (popupInfo.isDismissOnBackPressed &&
                    (popupInfo.xPopupCallback == null || !popupInfo.xPopupCallback.onBackPressed(this))) {
                    dismissOrHideSoftInput()
                }
            }
        }
    }

    override fun onDismiss() {
        super.onDismiss()
        onDismissListener?.invoke()
    }

    override fun getContentViewBinding(): VB {
        return mContentBinding
    }

    override fun showDialog() {
        if (popupInfo == null) {
            return
        }
        super.show()
    }

    override fun dismissDialog() {
        super.dismiss()
    }

    override fun isShowDialog(): Boolean {
        return super.isShow()
    }

    @Suppress("unused")
    class Builder<VB : ViewBinding>(activity: Activity) :
        IBuilder<VB, Builder<VB>>(activity) {

        override fun build(): FullScreenDialog<VB> {
            return super.build() as FullScreenDialog
        }

        override fun createDialog(context: Context): IDialog<VB> {
            return FullScreenDialog<VB>(
                context,
                config.viewBinding,
                config.onCreateListener,
                config.onDismissListener,
            )
        }
    }
}