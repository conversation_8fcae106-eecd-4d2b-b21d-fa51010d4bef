package cn.com.vau.util.opt

import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Build
import android.os.Bundle
import android.os.Process
import android.os.SystemClock
import cn.com.vau.common.storage.SpManager.getAppStartFirst
import cn.com.vau.util.opt.PerfTraceUtil.addActivity
import cn.com.vau.util.opt.PerfTraceUtil.splash
import com.google.firebase.perf.metrics.AppStartTrace
import java.util.concurrent.TimeUnit
import kotlin.concurrent.Volatile

class AppStartMonitor private constructor(context: Context) : Application.ActivityLifecycleCallbacks {

    private var appContext: Context

    private var processStartTime: Long = 0

    //首个Activity onCreate 时间
    var onCreateTime: Long = 0L

    var isStartedFromBackground: Boolean = false

    //是否进行统计
    var isStartTraceStopped: Boolean = false

    val isStartFirst: Boolean

    private var isRegisteredForLifecycleCallbacks = false


    init {
        appContext = context.applicationContext
        //是否安装之后的首次启动
        isStartFirst = getAppStartFirst(true)
        //只支持Android 7.0
        isStartTraceStopped = Build.VERSION.SDK_INT < Build.VERSION_CODES.N
        //获取进程启动时间
        processStartTime = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            Process.getStartElapsedRealtime()
        } else {
            0L
        }
    }

    @Synchronized
    fun registerActivityLifecycleCallbacks(context: Context) {
        if (isRegisteredForLifecycleCallbacks) {
            return
        }
        val appContext = context.applicationContext
        if (appContext is Application) {
            appContext.registerActivityLifecycleCallbacks(this)
            isRegisteredForLifecycleCallbacks = true
            this.appContext = appContext
        }
    }

    @Synchronized
    fun unregisterActivityLifecycleCallbacks() {
        if (!isRegisteredForLifecycleCallbacks) {
            return
        }
        (appContext as Application).unregisterActivityLifecycleCallbacks(this)
        isRegisteredForLifecycleCallbacks = false
    }


    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        addActivity(activity)
        if (onCreateTime != 0L) { // An activity already called onCreate()) {
            return
        }
        val isForeground = AppStartTrace.isAnyAppProcessInForeground(appContext)
        onCreateTime = SystemClock.elapsedRealtime()
        val launchActivity = activity.javaClass.simpleName
        if (isStartFirst //首次启动不统计
            || (onCreateTime - processStartTime > MAX_LATENCY_BEFORE_UI_INIT_MILLIS) //参照firebaase逻辑，时间超1分钟不统计
            || !launchActivity.contains(splash) //首次不是闪屏页面，不统计
            || isStartedFromBackground //通过handler判断是不是后台启动
            || !isForeground
        ) { //不是前台启动
            isStartTraceStopped = true
        }
    }

    override fun onActivityStarted(activity: Activity) {
    }

    override fun onActivityResumed(activity: Activity) {

    }

    override fun onActivityPaused(activity: Activity) {
    }

    override fun onActivityStopped(activity: Activity) {
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
    }

    override fun onActivityDestroyed(activity: Activity) {
    }

    class StartFromBackgroundRunnable(private val monitor: AppStartMonitor?) : Runnable {
        override fun run() {
            // if no activity has ever been created.
            if (monitor != null && monitor.onCreateTime == 0L) {
                monitor.isStartedFromBackground = true
            }
        }
    }

    companion object {
        @Volatile
        private var instance: AppStartMonitor? = null

        val MAX_LATENCY_BEFORE_UI_INIT_MILLIS = TimeUnit.MINUTES.toMillis(1)
        val MAX_LATENCY_BEFORE_UI_INIT_MICROS = TimeUnit.MINUTES.toMicros(1)

        fun getInstance(context: Context): AppStartMonitor? {
            if (instance == null) {
                synchronized(AppStartMonitor::class.java) {
                    if (instance == null) {
                        instance = AppStartMonitor(context)
                    }
                }
            }
            return instance
        }
    }
}
