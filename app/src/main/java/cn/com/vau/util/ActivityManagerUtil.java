package cn.com.vau.util;

import android.app.Activity;
import android.os.Process;

import androidx.annotation.Nullable;

import java.util.ArrayList;
import java.util.List;

import cn.com.vau.MainActivity;
import cn.com.vau.common.base.activity.BaseActivity;

/**
 * ActivityManagerUtil
 * Activity管理工具类
 */
public class ActivityManagerUtil {

    private final List<Activity> activityStack;
    private static ActivityManagerUtil instance = null;

    private ActivityManagerUtil() {
        activityStack = new ArrayList<>();
    }

    /**
     * @return ActivityManagerUtil
     */
    public static ActivityManagerUtil getInstance() {
        if(instance == null) {
            instance = new ActivityManagerUtil();
        }
        return instance;
    }

    /**
     * 添加activity
     */
    public void addActivity(Activity activity) {
        if(!activityStack.contains(activity)) {
            activityStack.add(activity);
        }
    }

    /**
     * 当执行onDestroy方法时移除改activity
     */
    public void removeActivity(Activity activity) {
        activityStack.remove(activity);
    }

    /**
     * finishActivity 结束指定类名的Activity
     */
    public void finishActivity(Class<?> cls) {
        for (Activity activity : activityStack) {
            if(activity != null && activity.getClass().equals(cls)) {
                activity.finish();
                activity = null;
                break;
            }
        }
    }

    /**
     * finishActivity 结束所有相同指定类名的Activity
     */
    public void finishAllSameActivity(Class<?> cls) {
        for (Activity activity : activityStack) {
            if (activity != null && activity.getClass().equals(cls)) {
                activity.finish();
                activity = null;
            }
        }
    }

    /**
     * remainActivity  保留唯一的Activity
     */
    public void finishOtherActivities(Class<?> clazz) {
        for (Activity activity : activityStack) {
            if(null != activity) {
                if(!activity.getClass().equals(clazz)) {
                    activity.finish();
                }
            }
        }
    }

    /**
     * @return Activity
     * 获取当前Activity（堆栈中最后一个压入的）
     */
    public Activity getSecondActivity() {
        if(activityStack.size() > 2)
            return activityStack.get(activityStack.size() - 2);
        else return activityStack.get(activityStack.size() - 1);
    }

    public boolean hasActivity(Class<?> cls) {
        for (Activity activity : activityStack) {
            if(activity != null && activity.getClass().equals(cls)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 栈内包含几个指定activity
     */
    public int getActivityCount(Class<?> cls) {
        int count = 0;
        for (Activity activity : activityStack) {
            if(activity != null && activity.getClass().equals(cls)) {
                count++;
            }
        }
        return count;
    }

    /**
     * remainActivity  保留指定的某些 Activity
     */
    public void remainActivityList(List<Class<?>> clazzList) {
        out:
        for (Activity activity : activityStack) {
            if(null != activity) {
                for (Class<?> clazz : clazzList) {
                    if(activity.getClass().equals(clazz)) {
                        continue out;
                    }
                }
                activity.finish();
            }
        }
        activityStack.clear();
    }

    /**
     * finishAllActivity  结束所有Activity
     */
    public void finishAllActivity() {
        for (Activity activity : activityStack) {
            if(null != activity) {
                activity.finish();
            }
        }
        activityStack.clear();
    }

    /**
     * 结束所有还未销毁的activity.
     *
     * @param uncaughtExceptionThread uncaughtExceptionThread处理器
     */
    public void finishAllActivity(@Nullable Thread uncaughtExceptionThread) {
        boolean wait = false;
        for (final Activity activity : activityStack) {
            if(activity instanceof MainActivity)
                continue;
            final boolean isMainThread = uncaughtExceptionThread == activity.getMainLooper().getThread();
            final Runnable finisher = new Runnable() {
                @Override
                public void run() {
                    activity.finish();
                }
            };

            if(isMainThread) {
                finisher.run();
            } else {
                // A crashed activity won't continue its lifecycle. So we only wait if something else crashed
                wait = true;
                activity.runOnUiThread(finisher);
            }
        }
        if(wait) {
            final int timeOut = 100;
            waitForAllActivitiesDestroy(timeOut);
        }
        activityStack.clear();
    }

    /**
     * wait until the last activity is stopped.
     *
     * @param timeOutInMillis timeout for wait
     */
    public void waitForAllActivitiesDestroy(int timeOutInMillis) {
        synchronized (activityStack) {
            long start = System.currentTimeMillis();
            long now = start;
            while (!activityStack.isEmpty() && start + timeOutInMillis > now) {
                try {
                    activityStack.wait(start - now + timeOutInMillis);
                } catch (InterruptedException ignored) {
                }
                now = System.currentTimeMillis();
            }
        }
    }

    public boolean isExists(Class<? extends BaseActivity> clazz) {
        for (Activity activity : ActivityManagerUtil.getInstance().getActivityStack()) {
            if(activity.getClass() == clazz) {
                return true;
            }
        }
        return false;
    }

    public void isExistsAndFinish(Class<? extends Activity> clazz) {
        if(!activityStack.isEmpty()) {
            for (Activity activity : activityStack) {
                if(activity.getClass() == clazz) {
                    activity.finish();
                    activity = null;
                }
            }
        }
    }

    public List<Activity> getActivityStack() {
        return activityStack;
    }

    /**
     * 杀掉服务和进程.
     */
    public void endApplication() {
        killProcessAndExit();
    }

    /**
     * 杀掉进程.
     */
    private void killProcessAndExit() {
        final int exitType = 10;
        Process.killProcess(Process.myPid());
        System.exit(exitType);
    }
}
