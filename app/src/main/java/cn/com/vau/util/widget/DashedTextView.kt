package cn.com.vau.util.widget

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.TypedArray
import android.graphics.*
import android.text.TextUtils
import android.util.AttributeSet
import android.view.Gravity
import android.view.MotionEvent
import androidx.appcompat.widget.AppCompatTextView
import cn.com.vau.R
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.dp2px
import java.util.Locale

/**
 * 自定义虚线TextView
 */
class DashedTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {

    private var dashedColor: Int =
        AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff) // 虚线默认颜色
    private var dashedStrokeWidth: Float = 1f.dp2px()   // 虚线线条默认宽度

    private var textWidth: Float = 0f

    private val paintLine = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        strokeWidth = dashedStrokeWidth // 线条宽度
        pathEffect = DashPathEffect(floatArrayOf(1f.dp2px(), 3f.dp2px()), 0f) // 设置虚线间隔
        strokeCap = Paint.Cap.ROUND
    }

    init {
        attrs?.let {
            val typedArray: TypedArray =
                context.obtainStyledAttributes(it, R.styleable.DashedTextView)
            dashedColor = typedArray.getColor(R.styleable.DashedTextView_dashedColor, dashedColor)
            dashedStrokeWidth = typedArray.getFloat(R.styleable.DashedTextView_dashedStrokeWidth, dashedStrokeWidth)
            typedArray.recycle()
        }
        paint.color = dashedColor
        paintLine.color = dashedColor
    }

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        if (layout != null && !TextUtils.isEmpty(text)) {
            val lineCount: Int = layout.lineCount
            for (i in 0..<lineCount) {
                val rect = RectF(
                    layout.getLineLeft(i),
                    layout.getLineTop(i) + textSize,
                    layout.getLineRight(i),
                    layout.getLineBaseline(i).toFloat()
                )

                canvas.drawLine(
                    rect.left,
                    rect.bottom + resources.displayMetrics.density * 2,  /* 下划线距离文本的距离 */
                    rect.right,
                    rect.bottom + resources.displayMetrics.density * 2,
                    paintLine
                )
            }
        }
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        // 计算文本宽度
        textWidth = paint.measureText(text.toString())
        if (isInTextRect(event?.x?:-1f)) {
            return super.dispatchTouchEvent(event)
        }
       return false
    }

    private fun isInTextRect(x: Float): Boolean {
        val gravity = gravity and Gravity.HORIZONTAL_GRAVITY_MASK // 获取对齐方式
        val isRtl = layoutDirection == LAYOUT_DIRECTION_RTL
                || TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == LAYOUT_DIRECTION_RTL
        // 计算起始 X 坐标
        val startX = when {
            isRtl && (gravity == Gravity.END || gravity == Gravity.RIGHT) -> paddingStart.toFloat()
            isRtl && (gravity == Gravity.START || gravity == Gravity.LEFT) -> width - textWidth - paddingEnd
            gravity == Gravity.CENTER || gravity == Gravity.CENTER_HORIZONTAL -> (width - textWidth) / 2
            gravity == Gravity.END || gravity == Gravity.RIGHT -> width - textWidth - paddingEnd
            else -> paddingStart.toFloat()
        }

        val endX = startX + textWidth
        return (x in startX..endX)
    }

    /**
     * 允许动态修改虚线颜色
     */
    fun setDashedColor(color: Int) {
        dashedColor = color
        paint.color = dashedColor
        invalidate() // 重新绘制
    }
}