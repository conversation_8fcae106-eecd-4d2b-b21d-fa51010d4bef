package cn.com.vau.util.widget.dialog

import android.app.Activity
import android.os.*
import android.view.View
import kotlinx.coroutines.Runnable
import java.lang.ref.SoftReference

object TopInAppPopupUtils {

    private var topPopupWindowRef: SoftReference<TopInAppPopupWindow>? = null
    val handler = Handler(Looper.getMainLooper())
    val hideRunnable = Runnable {
        dismissTopInAppPopupWindow()
    }

    fun showTopPopupWindow(
        activity: Activity?,
        title: CharSequence?,
        content: CharSequence?,
        icon: Int = 0,
        duration: Long = 2000L
    ) {
        if (activity == null || activity.isFinishing || activity.isDestroyed) {
            return
        }

        //调用的时候，必须有decorView
        val decorView: View? = activity.window.decorView
        if (decorView == null) {
            return
        }

        //如果不是主线程，转到主线程调用
        if (!isMainThread()) {
            handler.post {
                showTopPopupWindow(activity, title, content, icon, duration)
            }
            return
        }

        if (decorView.windowToken == null) {
            // Activity 还没有完全创建，延迟显示
            decorView.post {
                showTopPopupWindow(activity, title, content, icon, duration)
            }
            return
        }

        realShow(activity, title, content, icon, duration)
    }

    private fun isMainThread(): Boolean {
        return Looper.myLooper() == Looper.getMainLooper()
    }

    private fun realShow(
        activity: Activity?,
        title: CharSequence?,
        content: CharSequence?,
        icon: Int,
        duration: Long
    ) {
        // val activity = ActivityManagerUtil.getInstance().secondActivity
        if (activity == null || activity.isFinishing || activity.isDestroyed) {
            return
        }

        //多重保障，防止出现 android.view.WindowManager$BadTokenException: Unable to add window -- token null is not valid; is your activity running?
        try {
            handler.removeCallbacks(hideRunnable)
            dismissTopInAppPopupWindow()
            val topPopupWindow = TopInAppPopupWindow.Builder(activity)
                .setTitle(title)
                .setContent(content)
                .setIcon(icon)
                .build()
            topPopupWindow.showDialog()
            topPopupWindow.setOnDismissListener {
                clearTopInAppPopupWindow()
            }
            topPopupWindowRef = SoftReference(topPopupWindow)
            handler.postDelayed(hideRunnable, duration)
        } catch (e: Throwable) {
            e.printStackTrace();
        }
    }

    private fun clearTopInAppPopupWindow() {
        if (topPopupWindowRef != null) {
            val topPopupWindow = topPopupWindowRef?.get()
            if (topPopupWindow != null && topPopupWindow.isShowDialog() == false) {
                topPopupWindowRef?.clear()
                topPopupWindowRef = null
            }
        }
    }

    private fun dismissTopInAppPopupWindow() {
        if (topPopupWindowRef != null) {
            val topPopupWindow = topPopupWindowRef?.get()
            if (topPopupWindow?.isShowDialog() == true) {
                topPopupWindow.dismissDialog()
                topPopupWindowRef?.clear()
                topPopupWindowRef = null
            }
        }
    }

}




