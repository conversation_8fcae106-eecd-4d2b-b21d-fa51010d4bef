package cn.com.vau.util

import android.text.TextUtils
import com.github.tifezh.kchartlib.helper.bean.KLineEntity
import com.google.gson.*
import com.google.gson.reflect.TypeToken
import com.upex.common.utils.TimeUtils.getKlineTradeDate
import org.json.JSONObject
import java.io.Reader
import java.lang.reflect.Type
import java.util.concurrent.ConcurrentHashMap

/**
 * Json 解析工具类， 使用Gson解析
 */
object GsonUtil {

    private const val KEY_DEFAULT = "defaultGson"
    private const val KEY_DELEGATE = "delegateGson"
    private const val KEY_LOG_UTILS = "logUtilsGson"

    private val GSONS: MutableMap<String, Gson?> = ConcurrentHashMap()

    /**
     * Set the delegate of [Gson].
     *
     * @param delegate The delegate of [Gson].
     */
    fun setGsonDelegate(delegate: Gson?) {
        if (delegate == null) return
        GSONS[KEY_DELEGATE] = delegate
    }

    /**
     * Set the [Gson] with key.
     *
     * @param key  The key.
     * @param gson The [Gson].
     */
    fun setGson(key: String, gson: Gson?) {
        if (TextUtils.isEmpty(key) || gson == null) return
        GSONS[key] = gson
    }

    /**
     * Return the [Gson] with key.
     *
     * @param key The key.
     * @return the [Gson] with key
     */
    fun getGson(key: String): Gson? {
        return GSONS[key]
    }

    private val gson: Gson
        get() {
            val gsonDelegate = GSONS[KEY_DELEGATE]
            if (gsonDelegate != null) {
                return gsonDelegate
            }
            var gsonDefault = GSONS[KEY_DEFAULT]
            if (gsonDefault == null) {
                gsonDefault = buildGson()
                GSONS[KEY_DEFAULT] = gsonDefault
            }
            return gsonDefault
        }

    /**
     * Serializes an object into json.
     *
     * @param src       The object to serialize.
     * @param typeOfSrc The specific genericized type of src.
     * @return object serialized into json.
     */
    fun toJson(src: Any?, typeOfSrc: Type): String {
        return toJson(gson, src, typeOfSrc)
    }

    /**
     * Serializes an object into json.
     *
     * @param gson   The gson.
     * @param object The object to serialize.
     * @return object serialized into json.
     */
    fun toJson(gson: Gson, data: Any?): String {
        return gson.toJson(data)
    }

    /**
     * Serializes an object into json.
     *
     * @param gson      The gson.
     * @param src       The object to serialize.
     * @param typeOfSrc The specific genericized type of src.
     * @return object serialized into json.
     */
    fun toJson(gson: Gson, src: Any?, typeOfSrc: Type): String {
        return gson.toJson(src, typeOfSrc)
    }

    /**
     * Converts [String] to given type.
     *
     * @param json The json to convert.
     * @param type Type json will be converted to.
     * @return instance of type
     */
    fun <T> fromJson(json: String?, type: Class<T>): T? {
        try {
            return fromJson(gson, json, type)

        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    /**
     * Converts [String] to given type.
     *
     * @param json the json to convert.
     * @param type type type json will be converted to.
     * @return instance of type
     */
    fun <T> fromJson(json: String?, type: Type): T {
        return fromJson(gson, json, type)
    }

    /**
     * Converts [Reader] to given type.
     *
     * @param reader the reader to convert.
     * @param type   type type json will be converted to.
     * @return instance of type
     */
    fun <T> fromJson(reader: Reader, type: Class<T>): T {
        return fromJson(gson, reader, type)
    }

    /**
     * Converts [Reader] to given type.
     *
     * @param reader the reader to convert.
     * @param type   type type json will be converted to.
     * @return instance of type
     */
    fun <T> fromJson(reader: Reader, type: Type): T {
        return fromJson(gson, reader, type)
    }

    fun <T> fromJson(json: JsonElement, type: Class<T>): T {
        return fromJson(gson, json, type)
    }

    /**
     * Converts [String] to given type.
     *
     * @param gson The gson.
     * @param json The json to convert.
     * @param type Type json will be converted to.
     * @return instance of type
     */
    fun <T> fromJson(gson: Gson, json: String?, type: Class<T>): T {
        return gson.fromJson(json, type)
    }

    /**
     * Converts [String] to given type.
     *
     * @param gson The gson.
     * @param json the json to convert.
     * @param type type type json will be converted to.
     * @return instance of type
     */
    fun <T> fromJson(gson: Gson, json: String?, type: Type): T {
        return gson.fromJson(json, type)
    }

    fun <T> fromJson(gson: Gson, json: JsonElement?, type: Type): T {
        return gson.fromJson(json, type)
    }

    /**
     * Converts [Reader] to given type.
     *
     * @param gson   The gson.
     * @param reader the reader to convert.
     * @param type   type type json will be converted to.
     * @return instance of type
     */
    fun <T> fromJson(gson: Gson, reader: Reader?, type: Class<T>): T {
        return gson.fromJson(reader, type)
    }

    /**
     * Converts [Reader] to given type.
     *
     * @param gson   The gson.
     * @param reader the reader to convert.
     * @param type   type type json will be converted to.
     * @return instance of type
     */
    fun <T> fromJson(gson: Gson, reader: Reader?, type: Type): T {
        return gson.fromJson(reader, type)
    }

    /**
     * Return the type of [List] with the `type`.
     *
     * @param type The type.
     * @return the type of [List] with the `type`
     */
    fun getListType(type: Type): Type {
        return TypeToken.getParameterized(MutableList::class.java, type).type
    }

    /**
     * Return the type of [Set] with the `type`.
     *
     * @param type The type.
     * @return the type of [Set] with the `type`
     */
    fun getSetType(type: Type): Type {
        return TypeToken.getParameterized(MutableSet::class.java, type).type
    }

    /**
     * Return the type of map with the `keyType` and `valueType`.
     *
     * @param keyType   The type of key.
     * @param valueType The type of value.
     * @return the type of map with the `keyType` and `valueType`
     */
    fun getMapType(keyType: Type, valueType: Type): Type {
        return TypeToken.getParameterized(MutableMap::class.java, keyType, valueType).type
    }

    /**
     * Return the type of array with the `type`.
     *
     * @param type The type.
     * @return the type of map with the `type`
     */
    fun getArrayType(type: Type): Type {
        return TypeToken.getArray(type).type
    }

    /**
     * Return the type of `rawType` with the `typeArguments`.
     *
     * @param rawType       The raw type.
     * @param typeArguments The type of arguments.
     * @return the type of map with the `type`
     */
    fun getType(rawType: Type, vararg typeArguments: Type): Type {
        return TypeToken.getParameterized(rawType, *typeArguments).type
    }

    val gson4LogUtils: Gson?
        get() {
            var gson4LogUtils = GSONS[KEY_LOG_UTILS]
            if (gson4LogUtils == null) {
                gson4LogUtils = GsonBuilder().setPrettyPrinting().serializeNulls().create()
                GSONS[KEY_LOG_UTILS] = gson4LogUtils
            }
            return gson4LogUtils
        }

    fun buildGson(): Gson {
        return GsonBuilder()
            .disableHtmlEscaping()
            .registerTypeAdapter(String::class.java, StringAdapter())
            .registerTypeAdapter(Int::class.javaPrimitiveType, IntegerDefault0Adapter())
            .registerTypeAdapter(Int::class.java, IntegerDefault0Adapter())
            .registerTypeAdapter(Double::class.javaPrimitiveType, DoubleDefault0Adapter())
            .registerTypeAdapter(Double::class.java, DoubleDefault0Adapter())
            .registerTypeAdapter(Long::class.javaPrimitiveType, LongDefault0Adapter())
            .registerTypeAdapter(Long::class.java, LongDefault0Adapter())
            .create()
    }

    /**
     * 自定义的 Gson 适配器，用于序列化和反序列化 String 对象。
     * 该适配器处理特殊情况，例如输入可能不是标准的 String 类型。
     */
    private class StringAdapter : JsonSerializer<String?>, JsonDeserializer<String> {

        /**
         * 将 JsonElement 反序列化为 String 对象。
         *
         * @param json 需要反序列化的 JsonElement。
         * @param typeOfT 目标类型（在此为 String）。
         * @param context 反序列化的上下文。
         * @return 反序列化后的 String 值。如果输入不是原始类型，则返回 JSON 字符串表示。
         * @throws JsonParseException 如果 JsonElement 不能反序列化为 String，则抛出此异常。
         */
        @Throws(JsonParseException::class)
        override fun deserialize(json: JsonElement, typeOfT: Type, context: JsonDeserializationContext): String {
            return if (json is JsonPrimitive) {
                // 如果 JsonElement 是原始类型，返回它的 String 表示
                json.asString
            } else {
                // 否则，返回 JSON 元素的字符串表示
                json.toString()
            }
        }

        /**
         * 将 String 对象序列化为 JsonElement。
         *
         * @param src 需要序列化的 String 对象。
         * @param typeOfSrc 源对象的类型（在此为 String）。
         * @param context 序列化的上下文。
         * @return 表示序列化后 String 的 JsonElement。
         */
        override fun serialize(src: String?, typeOfSrc: Type, context: JsonSerializationContext): JsonElement {
            // 返回 String 作为 JsonPrimitive
            return JsonPrimitive(src)
        }
    }

    /**
     * 自定义的 Gson 适配器，用于序列化和反序列化 Integer 对象。
     * 该适配器处理特殊情况，例如输入为 "" 或 "null" 时，将其转换为 0。
     */
    private class IntegerDefault0Adapter : JsonSerializer<Int?>, JsonDeserializer<Int> {

        /**
         * 将 JsonElement 反序列化为 Integer 对象。
         *
         * @param json 需要反序列化的 JsonElement。
         * @param typeOfT 目标类型（在此为 Integer）。
         * @param context 反序列化的上下文。
         * @return 反序列化后的 Integer 值。如果输入为空字符串或 "null"，则返回 0。
         * @throws JsonParseException 如果 JsonElement 不能反序列化为 Integer，则抛出此异常。
         */
        @Throws(JsonParseException::class)
        override fun deserialize(json: JsonElement, typeOfT: Type, context: JsonDeserializationContext): Int {
            try {
                val str = json.asString
                if ("" == str || "null" == str) {
                    // 如果 json 字符串是空字符串或 "null"，返回 0
                    return 0
                }
            } catch (ignore: Exception) {
                // 捕获并忽略异常，如果 json 不是字符串格式，不进行任何处理
            }
            // 正常情况下，返回 json 的整数值
            return json.asInt
        }

        /**
         * 将 Integer 对象序列化为 JsonElement。
         *
         * @param src 需要序列化的 Integer 对象。
         * @param typeOfSrc 源对象的类型（在此为 Integer）。
         * @param context 序列化的上下文。
         * @return 表示序列化后 Integer 的 JsonElement。
         */
        override fun serialize(src: Int?, typeOfSrc: Type, context: JsonSerializationContext): JsonElement {
            // 返回 Integer 作为 JsonPrimitive
            return JsonPrimitive(src)
        }
    }

    /**
     * 自定义的 Gson 适配器，用于序列化和反序列化 Double 对象。
     * 该适配器处理特殊情况，例如输入为 "" 或 "null" 时，将其转换为 0.00。
     */
    private class DoubleDefault0Adapter : JsonSerializer<Double?>, JsonDeserializer<Double> {

        /**
         * 将 JsonElement 反序列化为 Double 对象。
         *
         * @param json 需要反序列化的 JsonElement。
         * @param typeOfT 目标类型（在此为 Double）。
         * @param context 反序列化的上下文。
         * @return 反序列化后的 Double 值。如果输入为空字符串或 "null"，则返回 0.00。
         * @throws JsonParseException 如果 JsonElement 不能反序列化为 Double，则抛出此异常。
         */
        @Throws(JsonParseException::class)
        override fun deserialize(json: JsonElement, typeOfT: Type, context: JsonDeserializationContext): Double {
            try {
                val str = json.asString
                if ("" == str || "null" == str) {
                    // 如果 json 字符串是空字符串或 "null"，返回 0.00
                    return 0.00
                }
            } catch (ignore: Exception) {
                // 捕获并忽略异常，如果 json 不是字符串格式，不进行任何处理
            }
            // 正常情况下，返回 json 的双精度浮点数值
            return json.asDouble
        }

        /**
         * 将 Double 对象序列化为 JsonElement。
         *
         * @param src 需要序列化的 Double 对象。
         * @param typeOfSrc 源对象的类型（在此为 Double）。
         * @param context 序列化的上下文。
         * @return 表示序列化后 Double 的 JsonElement。
         */
        override fun serialize(src: Double?, typeOfSrc: Type, context: JsonSerializationContext): JsonElement {
            // 返回 Double 作为 JsonPrimitive
            return JsonPrimitive(src)
        }
    }

    /**
     * 自定义的 Gson 适配器，用于序列化和反序列化 Long 对象。
     * 该适配器处理特殊情况，例如输入为 "" 或 "null" 时，将其转换为 0L。
     */
    private class LongDefault0Adapter : JsonSerializer<Long?>, JsonDeserializer<Long> {

        /**
         * 将 JsonElement 反序列化为 Long 对象。
         *
         * @param json 需要反序列化的 JsonElement。
         * @param typeOfT 目标类型（在此为 Long）。
         * @param context 反序列化的上下文。
         * @return 反序列化后的 Long 值。如果输入为空字符串或 "null"，则返回 0L。
         * @throws JsonParseException 如果 JsonElement 不能反序列化为 Long，则抛出此异常。
         */
        @Throws(JsonParseException::class)
        override fun deserialize(json: JsonElement, typeOfT: Type, context: JsonDeserializationContext): Long {
            try {
                val str = json.asString
                if ("" == str || "null" == str) {
                    // 如果 json 字符串是空字符串或 "null"，返回 0L
                    return 0L
                }
            } catch (ignore: Exception) {
                // 捕获并忽略异常，如果 json 不是字符串格式，不进行任何处理
            }
            // 正常情况下，返回 json 的长整型数值
            return json.asLong
        }

        /**
         * 将 Long 对象序列化为 JsonElement。
         *
         * @param src 需要序列化的 Long 对象。
         * @param typeOfSrc 源对象的类型（在此为 Long）。
         * @param context 序列化的上下文。
         * @return 表示序列化后 Long 的 JsonElement。
         */
        override fun serialize(src: Long?, typeOfSrc: Type, context: JsonSerializationContext): JsonElement {
            // 返回 Long 作为 JsonPrimitive
            return JsonPrimitive(src)
        }
    }

    public class KlineEntityTypeAdapter : JsonDeserializer<KLineEntity> {
        @Throws(JsonParseException::class)
        override fun deserialize(json: JsonElement, typeOfT: Type, context: JsonDeserializationContext): KLineEntity {
            val kLineEntity: KLineEntity = fromJson(json, KLineEntity::class.java)
            try {
                kLineEntity.timestampLong = kLineEntity.timestamp.toLongCatching(0L)
                kLineEntity.timestampFormat = getKlineTradeDate(kLineEntity.timestampLong + kLineEntity.period * 60 / 2, "dd/MM HH:mm")
            } catch (e: java.lang.Exception) {
                kLineEntity.timestampLong = 0L
                kLineEntity.timestampFormat = ""
            }
            return kLineEntity
        }
    }

    /**
     * 解析为json字符串
     * @param any list/map/javaBean
     * @return
     */
    fun <T> toJson(any: T?): String {
        try {
            return toJson(gson, any)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ""
    }

    /**
     * json对象 => javaBean对象
     * @param json JSONObject对象数据源
     * @param cla javaBean对象的class
     * @return JavaBean
     */
    fun <T> toBean(json: JSONObject?, cla: Class<T>): T? {
        return if (json == null) null else toBean(json.toString(), cla)
    }

    /**
     * json字符串 => javaBean对象
     * @param json json字符串
     * @param cla javaBean对象的class
     * @return
     */
    fun <T> toBean(json: String?, cla: Class<T>): T? {
        try {
            return fromJson(json, cla)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    /**
     * 带默认值的json字符串 ==> javaBean对象
     */
    inline fun <reified T> fromJson(json: String?, def: T?): T? {
        return fromJson(json, null, def)
    }

    /**
     * json字符串 ==> List/Map等
     * @param json json数组字符串
     * @param type 泛型Type：参考 Type type = new TypeToken<List></List><Object>>(){}.getType();
     * @param defValue 默认值
     * @return
     */
    inline fun <reified T> fromJson(json: String?, type: Type?, defValue: T?): T? {
        try {
            json ?: return defValue
            val t = type ?: object : TypeToken<T>() {}.type
            val result = fromJson<T>(json, t)
            if (result != null) {
                return result
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return defValue
    }
}

/**
 * 将json字符串转换为对象
 */
inline fun <reified T> fromJson(json: String?) = GsonUtil.fromJson(json, T::class.java)

/**
 * 将对象转换为json字符串
 */
inline val <reified T> T.json get() = GsonUtil.toJson(this)
