package cn.com.vau.util.widget.dialog

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.databinding.DialogBottomAgreeRejectBinding
import cn.com.vau.util.widget.dialog.base.*

/**
 * 底部弹窗基类
 */
@SuppressLint("ViewConstructor")
@Suppress("unused")
class BottomAgreeRejectDialog private constructor(
    context: Context,
    title: CharSequence? = null,
    private var content: CharSequence? = null,
    private var startText: CharSequence? = null,
    private var endText: CharSequence? = null,
    private var isAgree: Boolean = false,
    private var agreeText: CharSequence? = null,
    private var onStartListener: ((Boolean) -> Unit)? = null,
    private var onEndListener: ((IDialog<DialogBottomAgreeRejectBinding>, Boolean) -> Unit)? = null,
    private var agreeDescribe: CharSequence? = null,
    private var isNeedAgreeOnEndClick: Boolean = false,
    private var subTitle: CharSequence? = null,
    private var subContent: CharSequence? = null,
    onCreateListener: ((DialogBottomAgreeRejectBinding) -> Unit)? = null,
    onDismissListener: (() -> Unit)? = null,
) : BottomDialog<DialogBottomAgreeRejectBinding>(
    context,
    DialogBottomAgreeRejectBinding::inflate,
    title,
    onCreateListener,
    onDismissListener
) {

    private val unSelectDrawable by lazy {
        ContextCompat.getDrawable(
            context,
            R.drawable.draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14
        )?.apply {
            setBounds(0, 0, intrinsicWidth, intrinsicHeight)
        }
    }

    private val selectDrawable by lazy {
        ContextCompat.getDrawable(context, R.drawable.icon2_cb_tick_circle_c15b374)?.apply {
            setBounds(0, 0, intrinsicWidth, intrinsicHeight)
        }
    }

    override fun setContentView() {
        super.setContentView()
        setContent()
        setAgreeView()
        setAgreeDescribeView()
        setButtonView()
        setSubTitleView()
        setSubContentView()
    }

    fun setContent(content: CharSequence?): BottomAgreeRejectDialog {
        this.content = content
        setContent()
        return this
    }

    private fun setContent() {
        mContentBinding.tvDetail.isVisible = !content.isNullOrEmpty()
        mContentBinding.tvDetail.text = content
    }

    fun setSubTitle(subTitle: CharSequence?): BottomAgreeRejectDialog {
        this.subTitle = subTitle
        setSubTitleView()
        return this
    }

    private fun setSubTitleView() {
        mContentBinding.tvSubTitle.isVisible = !subTitle.isNullOrEmpty()
        mContentBinding.tvSubTitle.text = subTitle
    }

    fun setSubContent(subContent: CharSequence?): BottomAgreeRejectDialog {
        this.subContent = subContent
        setSubContentView()
        return this
    }

    private fun setSubContentView() {
        mContentBinding.tvSubContent.isVisible = !subContent.isNullOrEmpty()
        mContentBinding.tvSubContent.text = subContent
    }

    fun setAgreeDescribe(agreeDescribe: CharSequence?): BottomAgreeRejectDialog {
        this.agreeDescribe = agreeDescribe
        setAgreeDescribeView()
        return this
    }

    private fun setAgreeDescribeView() {
        mContentBinding.tvAgreeDescribe.isVisible = !agreeDescribe.isNullOrEmpty()
        mContentBinding.tvAgreeDescribe.text = agreeDescribe
    }

    fun setAgree(isAgree: Boolean): BottomAgreeRejectDialog {
        this.isAgree = isAgree
        setAgreeDrawable()
        return this
    }

    fun setAgreeText(agreeText: CharSequence?): BottomAgreeRejectDialog {
        this.agreeText = agreeText
        setAgreeText()
        return this
    }


    private fun setAgreeView() {
        setAgreeText()
        setAgreeDrawable()
        setAgreeListener()
    }

    private fun setAgreeListener() {
        mContentBinding.tvAgree.setOnClickListener {
            isAgree = !isAgree
            setAgreeDrawable()
        }
    }

    private fun setAgreeText() {
        mContentBinding.tvAgree.isVisible = !agreeText.isNullOrEmpty()
        mContentBinding.tvAgree.text = agreeText
    }

    private fun setAgreeDrawable() {
        if (isAgree) {
            mContentBinding.tvAgree.setCompoundDrawables(selectDrawable, null, null, null)
        } else {
            mContentBinding.tvAgree.setCompoundDrawables(unSelectDrawable, null, null, null)
        }
    }


    fun setStartText(startText: CharSequence?): BottomAgreeRejectDialog {
        if (!startText.isNullOrEmpty()) {
            this.startText = startText
            mContentBinding.tvStart.text = this.startText
        }
        return this
    }

    fun setEndText(endText: CharSequence?): BottomAgreeRejectDialog {
        if (!endText.isNullOrEmpty()) {
            this.endText = endText
            mContentBinding.tvEnd.text = this.endText
        }
        return this
    }

    fun setOnEndListener(onEndListener: ((IDialog<DialogBottomAgreeRejectBinding>,Boolean) -> Unit)?): BottomAgreeRejectDialog {
        this.onEndListener = onEndListener
        setEndListener()
        return this
    }

    fun setOnStartListener(onStartListener: (( Boolean) -> Unit)?): BottomAgreeRejectDialog {
        this.onStartListener = onStartListener
        setStartListener()
        return this
    }

    private fun setButtonView() {
        setButtonText(mContentBinding.tvStart, startText)
        setButtonText(mContentBinding.tvEnd, endText)
        setButtonAction()
    }


    private fun setButtonAction() {
        setStartListener()
        setEndListener()
    }

    private fun setEndListener() {
        mContentBinding.tvEnd.setOnClickListener {
            onEndListener?.invoke(this, isAgree)
            if (!isNeedAgreeOnEndClick) {
                dismissDialog()
                return@setOnClickListener
            }
            if (isAgree) {
                dismissDialog()
            }
        }
    }

    private fun setStartListener() {
        mContentBinding.tvStart.setOnClickListener {
            onStartListener?.invoke(isAgree)
            dismissDialog()
        }
    }

    private fun setButtonText(tvButton: TextView, text: CharSequence?) {
        if (!text.isNullOrEmpty()) {
            tvButton.text = text
        }
    }


    @Suppress("unused")
    class Builder(activity: Activity) :
        IBuilder<DialogBottomAgreeRejectBinding, Builder>(activity) {
        protected var title: CharSequence? = null

        //弹窗内容
        protected var content: CharSequence? = null


        protected var subTitle: CharSequence? = null

        //弹窗内容
        protected var subContent: CharSequence? = null

        //左边文字
        protected var startText: String? = null

        //右边文字
        protected var endText: String? = null

        //底部描述语言
        protected var agreeText: String? = null

        //默认是否选中
        protected var isAgree: Boolean = false

        protected var agreeDescribe: CharSequence? = null

        /**
         * 当用户点击End按钮时，是否需要必须选中
         */
        protected var isNeedAgreeOnEndClick: Boolean = false

        protected var onEndListener: ((IDialog<DialogBottomAgreeRejectBinding>, Boolean) -> Unit)? =
            null
        protected var onStartListener: ((Boolean) -> Unit)? =
            null

        fun setAgreeText(agreeText: String?): Builder {
            this.agreeText = agreeText
            return this
        }

        fun setStartText(startText: String?): Builder {
            this.startText = startText
            return this
        }

        fun setEndText(endText: String?): Builder {
            this.endText = endText
            return this
        }

        fun setTitle(title: CharSequence?) = apply {
            this.title = title
            return this
        }

        fun setSubTitle(subTitle: CharSequence?) = apply {
            this.subTitle = subTitle
            return this
        }

        fun setContent(content: CharSequence?) = apply {
            this.content = content
            return this
        }

        fun setSubContent(subContent: CharSequence?) = apply {
            this.subContent = subContent
            return this
        }

        fun setAgreeDescribe(agreeDescribe: CharSequence?) = apply {
            this.agreeDescribe = agreeDescribe
            return this
        }

        fun setAgree(isAgree: Boolean) = apply {
            this.isAgree = isAgree
            return this
        }

        fun setNeedAgreeOnEndClick(isNeedAgreeOnEndClick: Boolean) = apply {
            this.isNeedAgreeOnEndClick = isNeedAgreeOnEndClick
            return this
        }

        fun setStartListener(onStartListener: ((Boolean) -> Unit)?) =
            apply {
                this.onStartListener = onStartListener
                return this
            }

        fun setEndListener(onEndListener: ((IDialog<DialogBottomAgreeRejectBinding>,Boolean) -> Unit)?) =
            apply {
                this.onEndListener = onEndListener
                return this
            }

        override fun createDialog(context: Context): IDialog<DialogBottomAgreeRejectBinding> {
            return BottomAgreeRejectDialog(
                context,
                title,
                content,
                startText,
                endText,
                isAgree,
                agreeText,
                onStartListener,
                onEndListener,
                agreeDescribe,
                isNeedAgreeOnEndClick,
                subTitle,
                subContent,
                config.onCreateListener,
                config.onDismissListener
            )
        }
    }

}
