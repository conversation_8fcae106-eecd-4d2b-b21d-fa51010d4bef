package cn.com.vau.util.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import cn.com.vau.R
import cn.com.vau.databinding.LayoutCouponProgressBinding
import cn.com.vau.util.ifNull

/**
 * Filename: CouponProgressView
 * Author: GG
 * Date: 2025/3/17
 * Description:
 */
class CouponProgressView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {
    private val mBinding: LayoutCouponProgressBinding by lazy(LazyThreadSafetyMode.NONE) {
        LayoutCouponProgressBinding.inflate(LayoutInflater.from(context), this, true)
    }

    fun setProgress(current: Double?, total: Double?) {
        mBinding.textView.text = buildString {
            append(context.getString(R.string.traded_lots))
            append(": ${current.ifNull()}/${total.ifNull()}")
        }
        mBinding.progressBar.progress = (current.ifNull(0.0) / total.ifNull(0.0) * 100).toInt()
    }

}