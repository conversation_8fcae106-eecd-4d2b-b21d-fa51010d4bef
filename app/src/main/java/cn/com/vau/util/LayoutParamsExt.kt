package cn.com.vau.util

import android.view.View
import android.view.ViewGroup.MarginLayoutParams

fun View?.setMarginLeft(marginLeft:Int) {
    if(this == null || this.layoutParams == null){
        return
    }
    if (this.layoutParams !is MarginLayoutParams) {
        return
    }
    val marginLayoutParams = this.layoutParams as MarginLayoutParams
    if (marginLayoutParams.leftMargin == marginLeft) {
        return
    }
    marginLayoutParams.leftMargin = marginLeft
    this.layoutParams = marginLayoutParams
}