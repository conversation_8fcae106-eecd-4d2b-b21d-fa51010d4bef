package cn.com.vau.util.widget.webview.preload

import android.annotation.SuppressLint
import android.content.Context
import android.content.MutableContextWrapper
import android.net.http.SslError
import android.os.Looper
import android.os.MessageQueue.IdleHandler
import android.text.TextUtils
import android.webkit.SslErrorHandler
import android.webkit.URLUtil
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import cn.com.vau.common.application.VauApplication
import cn.com.vau.common.http.HttpUrl.BaseHtmlUrl
import cn.com.vau.common.http.HttpUrl.htmlUrlPrefix
import cn.com.vau.util.widget.webview.utils.ThreadPoolUtil
import cn.com.vau.util.widget.webview.utils.ThreadPoolUtil.isMainThread
import cn.com.vau.util.widget.webview.utils.WebViewAbUtil
import cn.com.vau.util.widget.webview.utils.WebViewAbUtil.isUserWebViewPool
import cn.com.vau.util.widget.webview.utils.WebViewLogUtil
import java.util.ArrayDeque
import java.util.Deque

@SuppressLint("SetJavaScriptEnabled")
@Suppress("unused")
class PreloadWebViewFactory private constructor() {

    companion object {
        private const val MAX_POOL_SIZE = 3 // 可以根据需要调整池的大小
        private const val BLANK_URL = "about:blank" //空白页面URL

        @Volatile
        private var instance: PreloadWebViewFactory? = null

        fun getInstance(): PreloadWebViewFactory =
            instance ?: synchronized(PreloadWebViewFactory::class.java) {
                instance ?: PreloadWebViewFactory().also { instance = it }
            }
    }

    private val pool: Deque<PreloadWebView> = ArrayDeque(MAX_POOL_SIZE)

    /**
     * webView 空白页
     * test：https://au-one.app-alpha.com:18008/h5/feature/test.html
     * prod： https://h5.vantagemarketapp.com/h5/feature/test.html （待部署）
     */
    private val url = "$BaseHtmlUrl$htmlUrlPrefix/test.html"

    /**
     * 预加载WebView
     */
    fun preload() {
        Looper.getMainLooper().queue.addIdleHandler(object : IdleHandler {
            override fun queueIdle(): Boolean {
                preloadWebView()
                //返回值为 false，即只会执行一次；
                return false
            }
        })
    }

    private fun preloadWebView() {
        //如果池中有WebView，则不创建新的WebView
        if (pool.size > 0) {
            return
        }
        //创建WebView
        val webView = createWebView(VauApplication.context, true)
        if (isUserWebViewPool()) {
            pool.push(webView)
        }
    }

    /**
     * 获取一个WebView实例
     */
    fun acquire(context: Context): PreloadWebView? {
        return getCachedWebView(context)
    }

    private fun getCachedWebView(context: Context): PreloadWebView? {
        if (!isMainThread()) {
            return null
        }
        val webView = if (!pool.isEmpty() && isUserWebViewPool()) {
            pool.pop()
        } else {
            createWebView(context)
        }
        clearDataWebView(webView)
        webView.setWebViewContext(context)
        return webView
    }

    fun release(webView: WebView?) {
        if (webView == null) {
            return
        }
        if (isMainThread()) {
            releaseWebView(webView)
        } else {
            ThreadPoolUtil.executeMain {
                releaseWebView(webView)
            }
        }
    }

    /**
     * 为了防止使用前，webView有内容，需要先做一次清理工作
     */
    private fun clearDataWebView(webView: WebView?) {
        if (webView !is PreloadWebView || !isUserWebViewPool()) {
            return
        }
        try {
            webView.clear()
            webView.setCached()
            webView.webViewClient = object : WebViewClient() {}
            if (!TextUtils.equals(webView.url, BLANK_URL)) {
                webView.loadUrl(BLANK_URL)
                return
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 回收或者销毁WebView
     */
    private fun releaseWebView(webView: WebView) {
        if (webView !is PreloadWebView) {
            return
        }
        try {
            webView.clear()
            if (webView.isShouldDestory()) {
                webView.destroy()
                return
            }
            if (pool.size < MAX_POOL_SIZE && isUserWebViewPool()) {
                webView.setCached()
                //防止JS交互
                webView.getSettings().javaScriptEnabled = false
                //复用的webView
                if (!TextUtils.equals(webView.url, BLANK_URL)) {
                    webView.loadUrl(BLANK_URL)
                }
                pool.push(webView)
            } else {
                webView.destroy()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun createWebView(context: Context, isPreload: Boolean = false): PreloadWebView {
        //直接New出来，避免内存泄漏
        val webView = PreloadWebView(MutableContextWrapper(context))

        if (WebViewAbUtil.isPreloadUrl()) {
            webView.webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    WebViewLogUtil.e("onPageFinished url:$url")
                    clearDataWebView(view)
                }

                override fun onReceivedError(
                    view: WebView?,
                    request: WebResourceRequest?,
                    error: WebResourceError?
                ) {
                    WebViewLogUtil.e("onReceivedError url:${request?.url}")
                    clearDataWebView(view)
                }

                override fun onReceivedHttpError(
                    view: WebView?,
                    request: WebResourceRequest?,
                    errorResponse: WebResourceResponse?
                ) {
                    WebViewLogUtil.e("onReceivedHttpError url:${request?.url}")
                    clearDataWebView(view)
                    super.onReceivedHttpError(view, request, errorResponse)
                }

                override fun onReceivedSslError(
                    view: WebView?,
                    handler: SslErrorHandler?,
                    error: SslError?
                ) {
                    WebViewLogUtil.e("onReceivedSslError")
                    clearDataWebView(view)
                    super.onReceivedSslError(view, handler, error)
                }
            }
            //如果是预加载，则预加载url,提高首次加载速度
            if (isPreload) {
                WebViewLogUtil.e("Preload url:$url")
                //如果是https或者http链接，则加载
                //注意两个判断的顺序，
                //因为大多用的是https,将其放在前面，可以减少判断耗时
                if (URLUtil.isHttpsUrl(url) || URLUtil.isHttpUrl(url)) {
                    webView.loadUrl(url)
                }
            }
        }
        return webView
    }

    fun removeAll() {
        if (isMainThread()) {
            removeAllWebView()
        } else {
            ThreadPoolUtil.executeMain {
                removeAllWebView()
            }
        }
    }

    private fun removeAllWebView() {
        for (webView in pool) {
            webView.clear()
            webView.destroy()
        }
        pool.clear()
    }
}
