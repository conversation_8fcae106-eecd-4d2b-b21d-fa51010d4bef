package cn.com.vau.util.widget.dialog

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import androidx.lifecycle.lifecycleScope
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.databinding.DialogBottomMonthYearBinding
import cn.com.vau.util.TimeUtil
import cn.com.vau.util.toIntCatching
import cn.com.vau.util.widget.dialog.base.BottomDialog
import cn.com.vau.util.widget.dialog.base.IBuilder
import cn.com.vau.util.widget.dialog.base.IDialog
import io.reactivex.Observable
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch

@SuppressLint("ViewConstructor")
class BottomMonthYearDialog private constructor(
    context: Context,
    private val onSelectListener: ((IDialog<DialogBottomMonthYearBinding>, selectYear: String?, selectMonth: String?) -> Unit)? = null,
    onCreateListener: ((DialogBottomMonthYearBinding) -> Unit)? = null,
    onDismissListener: (() -> Unit)? = null,
) : BottomDialog<DialogBottomMonthYearBinding>(
    context,
    DialogBottomMonthYearBinding::inflate,
    null,
    onCreateListener,
    onDismissListener
) {
    private var monthData = mutableListOf<String>()
    private var yearData = mutableListOf<String>()
    private var selectYear: String? = null
    private var selectMonth: String? = null

    init {
        selectYear = TimeUtil.getNowTime("yyyy")
        selectMonth = TimeUtil.getNowTime("MM")
        yearData()
        monthData()
    }

    fun yearData() {
        yearData.clear()
        val currentYear = TimeUtil.getNowTime("yyyy").toIntCatching()
        val isOutMonth = selectMonth.toIntCatching() >= TimeUtil.getNowTime("MM").toIntCatching()
        val startYear = if (isOutMonth) currentYear else currentYear + 1
        val endYear = if (isOutMonth) 10 else 11

        Observable.range(startYear, endYear)
            .subscribe(object : BaseObserver<Int>() {
                override fun onHandleSubscribe(d: Disposable?) {
                }

                override fun onNext(it: Int) {
                    yearData.add(it.toString())
                }

                override fun onComplete() {
                    super.onComplete()
                    disposable.dispose()
                }
            })
    }

    fun monthData() {
        monthData.clear()
        val isCurrentYear =
            selectYear.toIntCatching() == TimeUtil.getNowTime("yyyy").toIntCatching()
        val startMonth = if (isCurrentYear) TimeUtil.getNowTime("MM").toIntCatching() else 1
        val endMonth = if (isCurrentYear) 13 - TimeUtil.getNowTime("MM").toIntCatching() else 12
        Observable.range(startMonth, endMonth)
            .subscribe(object : BaseObserver<Int>() {
                override fun onHandleSubscribe(d: Disposable?) {
                }

                override fun onNext(it: Int) {

                    monthData.add(if (it < 10) "0$it" else it.toString())
                }

                override fun onComplete() {
                    super.onComplete()
                    disposable.dispose()
                }
            })
    }

    override fun setContentView() {
        super.setContentView()
        mContentBinding.pvMonth.setData(monthData)
        mContentBinding.pvYear.setData(yearData)
        selectMonth = monthData.elementAtOrNull(0) ?: ""
        selectYear = yearData.elementAtOrNull(0) ?: ""

        mContentBinding.pvMonth.setSelected(0)
        mContentBinding.pvYear.setSelected(0)

        initListener()
    }

    private fun initListener() {
        mContentBinding.pvMonth.setOnSelectListener { it, position ->
            val isContainToyear1 =
                selectMonth.toIntCatching() >= TimeUtil.getNowTime("MM").toIntCatching()
            val isContainToyear2 = it.toIntCatching() >= TimeUtil.getNowTime("MM").toIntCatching()
            selectMonth = it
            if (isContainToyear1 != isContainToyear2) {
                yearData()
                mContentBinding.pvYear.setData(yearData)
                var position = yearData.indexOf(selectYear)
                if (position == -1) {
                    selectYear = yearData.elementAtOrNull(0) ?: ""
                    position = 0
                }
                mContentBinding.pvYear.setSelected(position)
            }
        }

        mContentBinding.pvYear.setOnSelectListener { it, position ->
            val isContainToyear1 =
                selectYear.toIntCatching() == TimeUtil.getNowTime("yyyy").toIntCatching()
            val isContainToyear2 = it.toIntCatching() == TimeUtil.getNowTime("yyyy").toIntCatching()
            selectYear = it
            if (isContainToyear1 != isContainToyear2) {
                monthData()
                mContentBinding.pvMonth.setData(monthData)

                var position = monthData.indexOf(selectMonth)
                if (position == -1) {
                    selectMonth = monthData.elementAtOrNull(0) ?: ""
                    position = 0
                }
                mContentBinding.pvMonth.setSelected(position)
            }
        }
        mContentBinding.tvFinish.setOnClickListener {
            onSelectListener?.invoke(this,selectYear?.substring(2),selectMonth)
            dismiss()
        }
    }


    @Suppress("unused")
    class Builder(activity: Activity) :
        IBuilder<DialogBottomMonthYearBinding, Builder>(activity) {
        private var onSelectListener: ((IDialog<DialogBottomMonthYearBinding>, selectYear: String?, selectMonth: String?) -> Unit)? =
            null

        fun setSelectListener(onSelectListener: ((IDialog<DialogBottomMonthYearBinding>, selectYear: String?, selectMonth: String?) -> Unit)?): Builder {
            this.onSelectListener = onSelectListener
            return this
        }

        override fun build(): BottomMonthYearDialog {
            return super.build() as BottomMonthYearDialog
        }

        override fun createDialog(context: Context): IDialog<DialogBottomMonthYearBinding> {
            return BottomMonthYearDialog(context, onSelectListener,config.onCreateListener, config.onDismissListener)
        }

    }
}
