package cn.com.vau.util.widget.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.view.popup.bean.HintLocalData

class BottomInfoWithIconListAdapter(
    var mContext: Context,
    var dataList: List<HintLocalData>
) : RecyclerView.Adapter<BottomInfoWithIconListAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder =
        ViewHolder(
            LayoutInflater.from(mContext)
                .inflate(R.layout.item_rcy_info_icon_list_bottom, parent, false)
        )

    @SuppressLint("SetTextI18n", "UseCompatLoadingForDrawables", "CutPasteId")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        val dataBean = dataList.getOrNull(position)
        holder.itemView.findViewById<TextView>(R.id.tvInfoTitle).isVisible = (!TextUtils.isEmpty(dataBean?.title))
        holder.itemView.findViewById<TextView>(R.id.tvInfoContent).isVisible = (!TextUtils.isEmpty(dataBean?.content))
        holder.itemView.findViewById<ImageView>(R.id.ivInfoIcon).isVisible = dataBean?.iconId != 0

        holder.itemView.findViewById<TextView>(R.id.tvInfoTitle).text = dataBean?.title
        holder.itemView.findViewById<TextView>(R.id.tvInfoContent).text = dataBean?.content

        if (dataBean != null && dataBean.iconId != 0) {
            holder.itemView.findViewById<ImageView>(R.id.ivInfoIcon)
                .setImageResource(dataBean.iconId)
        }
    }

    override fun getItemCount(): Int = dataList.size

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view)

}