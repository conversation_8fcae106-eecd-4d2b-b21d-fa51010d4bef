package cn.com.vau.util.tracking

/**
 * author：lvy
 * date：2024/8/19
 * desc：神策埋点常量
 */
object SensorsConstant {

    // 文档地址：https://hytechc.atlassian.net/wiki/spaces/PW/pages/287703052
    object V3500 {
        // 登录页面浏览 -> 登录页面加载完成时触发
        const val LOGIN_PAGE_VIEW = "LoginPage_View"

        // 登录按钮点击 -> 登录页面按钮点击时触发
        const val LOGIN_PAGE_CLICK = "LoginPage_Click"

        // 开户及验证页面浏览 -> 开户验证页面加载完成时触发
        const val OPEN_IDENTITY_PAGE_VIEW = "OpenIdentityPage_View"

        // 开户及验证页面点击 -> 开户验证页面按钮点击成功时触发
        const val OPEN_IDENTITY_PAGE_CLICK = "OpenIdentityPage_Click"

        // 入金页浏览 -> 入金一级页面加载完成时触发
        const val DEPOSIT_PAGE_VIEW = "DepositPage_View"

        // 入金页入金方式点击 -> 入金一级页面入金方式按钮点击时触发
        const val DEPOSIT_PAGE_CLICK = "DepositPage_Click"

        // 入金详情页浏览 -> 入金二级页面加载完成时触发
        const val DEPOSIT_DETAIL_PAGE_VIEW = "DepositDetailPage_View"

        // 入金详情页支付按钮点击 -> 入金二级页面按钮点击时触发
        const val DEPOSIT_DETAIL_PAGE_CLICK = "DepositDetailPage_Click"

        //（k线页面） 交易产品详情页浏览 -> 交易产品详情页加载完成时触发
        const val PRODUCT_DETAIL_PAGE_VIEW = "ProductDetailPage_View"

        //（k线页面） 交易产品详情页点击 -> 交易产品详情页点击时触发 点击按钮时上报button_name信息 点击素材时上报模块&素材信息
        const val PRODUCT_DETAIL_PAGE_CLICK = "ProductDetailPage_Click"

        // 策略详情页浏览 -> 策略详情页加载完成时触发
        const val STRATEGIES_DETAIL_PAGE_VIEW = "StrategiesDetailPage_View"

        // 策略详情页点击 -> 策略详情页按钮点击时触发
        const val STRATEGIES_DETAIL_PAGE_CLICK = "StrategiesDetailPage_Click"

        //（跟单下单页面） 跟单详情页浏览 -> 跟单详情页页面加载完成时触发
        const val COPY_DETAIL_PAGE_VIEW = "CopyDetailPage_View"

        //（跟单下单页面） 跟单详情页点击 -> 跟单详情页按钮点击时触发
        const val COPY_DETAIL_PAGE_CLICK = "CopyDetailPage_Click"

        //（下单页面） 交易详情页按钮点击 -> 交易详情页点击买卖开仓按钮时触发
        const val TRADE_OPEN_SUBMIT = "TradeOpen_Submit"

        // 交易详情页按钮点击 -> 交易详情页点击关闭平仓按钮时触发
        const val TRADE_CLOSE_SUBMIT = "TradeClose_Submit"

        // 分享按钮点击 -> 分享按钮点击时触发
        const val SHARE_BTN_CLICK = "ShareBtn_Click"

        // 分享方式点击 -> 分享方式按钮点击时触发
        const val SHARE_METHOD_CLICK = "ShareMethod_Click"

        // 点击搜索按钮 -> 点击搜索按钮时触发
        const val SEARCH_BTN_CLICK = "SearchBtn_Click"

        // App_Tab点击 -> 点击app内五个tab时触发
        const val APP_TAB_CLICK = "App_Tab_Click"

        // App_Tab 页面浏览 -> app内五个tab页面加载完成时触发
        const val APP_TAB_PAGE_VIEW = "App_TabPage_View"

        // App_交易页Banner点击 -> 点击app交易页Banner位时触发
        const val APP_TRADES_BANNER_CLICK = "App_TradesBanner_Click"

        // App_交易页产品点击 -> 点击app交易页产品时触发
        const val APP_TRADES_PRODUCT_CLICK = "App_TradesProduct_Click"

        // App_我的页面按钮点击 -> App_我的页面按钮点击
        const val APP_PROFILE_PAGE_CLICK = "App_ProfilePage_Click"

        // App_我的页面Banner点击 -> 点击app我的页面Banner位时触发
        const val APP_PROFILE_BANNER_CLICK = "App_ProfileBanner_Click"

        // App_活动页面Banner点击 -> 点击app活动页面Banner时触发
        const val APP_PROMO_BANNER_CLICK = "App_PromoBanner_Click"

        // App_活动列表页面点击 -> 点击app活动列表页面内容时触发
        const val APP_PROMO_LIST_CLICK = "App_Promolist_Click"

        // App_发现页面点击 -> 点击app发现页面内容时触发
        const val APP_DISCOVER_PAGE_CLICK = "App_DiscoverPage_Click"
    }

    // 文档地址：https://pj4w2l1pwuq.sg.larksuite.com/sheets/Nym5smA5GhN6dJt2wP7lCbACgub?sheet=Z6zvT9
    object V3510 {
        // 价格提醒
        // Price Alerts页浏览 -> 用户浏览Price Alerts页时触发
        const val PRICE_ALERT_PAGE_VIEW = "PriceAlertPage_View"

        // Price Alerts页Manage点击 -> 用户点击Price Alerts页Manage按钮时触发
        const val PRICE_ALERT_PAGE_MANAGE_BTN_CLICK = "PriceAlertPage_ManageBtn_Click"

        // 开启push权限弹窗按钮点击 -> 权限开启弹窗，用户点击Enable时触发
        const val PUSH_ACCESS_POPUP_BTN_CLICK = "PushAccessPopUpBtn_Click"

        // 价格提醒编辑页按钮点击 -> 用户点击价格提醒编辑页任一按钮时触发
        const val PRICE_ALERT_EDIT_PAGE_BTN_CLICK = "PriceAlertEditPageBtn_Click"

        // 持仓相关
        // 持倉卡片分享按钮点击 -> 用户点击持仓卡片的分享按钮时触发
        const val POSITION_CARD_SHARE_BTN_CLICK = "PositionCardShareBtn_Click"

        // 持倉Edit按钮点击 -> 用户点击持仓卡片或持仓详情页上Edit按钮时触发
        const val POSITION_PAGE_EDIT_BTN_CLICK = "PositionPage_EditBtn_Click"

        // 持倉编辑选单按钮点击 -> 用户点击持仓编辑弹窗任一按钮时触发
        const val POSITION_EDIT_MENU_BTN_CLICK = "PositionEditMenuBtn_Click"

        // 部份平仓相关
        // 持仓详情页Close History点击 -> 用户点击持仓详情页的Close History按钮时触发
        const val POSITION_DETAIL_PAGE_CLOSE_HISTORY_BTN_CLICK = "PositionDetailPage_CloseHistoryBtn_Click"

        // 历史订单详情页Close History点击 -> 用户点击历史订单页Close History按钮时触发
        const val ORDER_DETAIL_PAGE_CLOSE_HISTORY_BTN_CLICK = "OrderDetailPage_CloseHistoryBtn_Click"

        // 挂单相关
        // 挂单卡片Modify按钮点击 -> 用户点击挂单卡片Modify按钮时触发
        const val ORDER_CARD_MODIFY_BTN_CLICK = "OrderCard_ModifyBtn_Click"

        // 暂停跟单
        // 跟单交易管理选单Stop Copy点击 -> 用户点击Manager Order选单上的Stop Copy按钮时触发
        const val COPY_TRADE_PAGE_MANAGE_MENU_STOP_COPY_BTN_CLICK = "CopyTradePageManageMenu_StopCopyBtn_Click"

        // 跟单交易暂停跟单确认选单按钮点击 -> 用户点击Enable One Click Trading选单上的I agree.../Not Now/Yes按钮时触发
        const val COPY_TRADE_PAGE_ENABLE_OC_MENU_BTN_CLICK = "CopyTradePageEnableOCMenuBtn_Click"

        // 跟单交易暂停确认选单按钮点击 -> 用户点击Enable One Click Trading选单上的Cancel/Confirm按钮时触发
        const val CONFIRM_STOP_COPY_POPUP_BTN_CLICK = "ConfirmStopCopyPopUpBtn_Click"

        // 反向开仓
        // 反向开仓按钮点击 -> 用户点击Close and Reverse按钮时触发
        const val REVERSE_PAGE_CLOSE_AND_REVERSE_BTN_CLICK = "ReversePage_CloseAndReverseBtn_Click"

        // 互抵平仓
        // 互抵平仓按钮点击 -> 用户点击Close按钮时触发
        const val CLOSE_BY_PAGE_CLOSE_BTN_CLICK = "CloseByPage_CloseBtn_Click"

        // 批量平仓
        // 持倉页Close按钮点击 -> 用户点击持仓卡片上Close按钮时触发
        const val POSITION_PAGE_CLOSE_BTN_CLICK = "PositionPage_CloseBtn_Click"

        // 持仓页平仓选单按钮点击 -> 用户点击close all order/close selected order按钮时触发
        const val POSITION_PAGE_CLOSE_MENU_BTN_CLICK = "PositionPageCloseMenuBtn_Click"

        // 批量平仓操作页Step1按钮点击 -> 用户点击close selected order后，到此页面用户有取消任一选项或点击Unselect All或select All按钮时触发
        const val CLOSE_CONFIGUATION_PAGE_STEP1BTN_CLICK = "CloseConfiguationPageStep1Btn_Click"

        // 批量平仓操作页Step1下一步点击 -> 用户点击Next时触发
        const val CLOSE_CONFIGUATION_PAGE_STEP1_NEXT_BTN_CLICK = "CloseConfiguationPageStep1_NextBtn_Click"

        // 批量平仓操作页Step2按钮点击 -> 用户点击Next后，到此页面用户有取消任一选项或点击Close All/Unselect All或返回按钮时触发
        const val CLOSE_CONFIGUATION_PAGE_STEP2_BTN_CLICK = "CloseConfiguationPageStep2Btn_Click"

        // 批量平仓操作页Step2平仓点击 -> 用户点击Close All时触发
        const val CLOSE_CONFIGUATION_PAGE_STEP2_CLOSE_ALL_BTN_CLICK = "CloseConfiguationPageStep2_CloseAllBtn_Click"

        // 批量平仓操作页Step3确认是否平仓选单点击 -> 用户点击Close All后，出现确认是否平仓选单，用户点击Cancel/Confirm时触发
        const val CLOSE_CONFIGUATION_PAGE_STEP3_CLOSE_POSITION_MENU_BTN_CLICK = "CloseConfiguationPageStep3_ClosePositionMenuBtn_Click"

        // 持仓页确认是否平仓选单点击 -> 平仓选单用户点击close all order后，跳出ClosePositionMenu，用户点击Cancel/Confirm时触发
        const val POSITION_PAGE_CLOSE_POSITION_MENU_BTN_CLICK = "PositionPage_ClosePositionMenuBtn_Click"

        // K线页
        // K线页浏览 -> 用户浏览K线页时触发
        const val CANDLESTICK_CHART_PAGE_VIEW = "CandlestickchartPage_View"

        // K线页页面标签点击 -> 用户K线页点击tab标签时触发
        const val CANDLESTICK_CHART_PAGE_PAGE_TAB_CLICK = "CandlestickchartPage_PageTab_Click"

        // K线图时间按钮点击 -> 用户K线页点击时间按钮时触发
        const val CANDLESTICK_CHART_PAGE_TIME_BTN_CLICK = "CandlestickchartPage_TimeBtn_Click"

        // K线图设置按钮点击 -> 用户K线页点击设置时触发
        const val CANDLESTICK_CHART_PAGE_SETTING_BTN_CLICK = "CandlestickchartPage_SettingBtn_Click"

        // K线图切换横屏按钮点击 -> 用户点击切换横屏按钮时触发
        const val CANDLESTICK_CHART_PAGE_SWITH_LANDSCAPE_BTN_CLICK = "CandlestickchartPage_SwithLandscapeBtn_Click"

        // K线图指标栏指标标签点击 -> 用户点击指标按钮时触发
        const val CANDLESTICK_CHART_PAGE_METRICS_TAB_CLICK = "CandlestickchartPage_MetricsTab_Click"

        // K线图价格变化栏点击 -> 用户点击指标变化按钮时触发
        const val CANDLESTICK_CHART_PAGE_PRICE_CHANGE_CLICK = "CandlestickchartPage_PriceChange_Click"

        // 底部买入按钮点击 -> 用户点击卖出按钮时触发
        const val CANDLESTICK_CHART_PAGE_BUY_BTN_CLICK = "CandlestickchartPage_BuyBtn_Click"

        // 底部卖出按钮点击 -> 用户点击卖出按钮时触发
        const val CANDLESTICK_CHART_PAGE_SELL_BTN_CLICK = "CandlestickchartPage_SellBtn_Click"

        // CopyTab下的策略点击 -> 用户点击任一策略时触发
        const val COPY_TAB_PAGE_STRATEGY_CLICK = "CopyTabPage_Strategy_Click"

        // AnalysisTab下的文章点击 -> 用户点击任一文章时触发
        const val ANALYSIS_TAB_PAGE_ARTICLE_CLICK = "AnalysisTabPage_Article_Click"
    }

    // 文档地址：https://pj4w2l1pwuq.sg.larksuite.com/sheets/QY4osqiN1hCfgDt5ZJwltiMRgjc
    object V3520 {
        // 出入金
        // 入金页Back点击 -> 入金任一页面点击Back时触发
        const val DEPOSIT_PAGE_BACK_CLICK = "DepositPage_Back_Click"
    }

    // 文档地址：https://pj4w2l1pwuq.sg.larksuite.com/sheets/Nym5smA5GhN6dJt2wP7lCbACgub?sheet=hQGZxg
    object V3540 {
        // 消息列表
        // 首页消息图标点击 -> 用户在首页点击消息图标时触发
        const val HOMEPAGE_MESSAGES_ICON_CLICK = "HomePage_MessagesIcon_Click"

        // 订单页消息图标点击 -> 用户在订单页点击消息图标时触发
        const val ORDER_PAGE_MESSAGES_ICON_CLICK = "OrderPage_MessagesIcon_Click"

        // 消息页浏览 -> 用户浏览消息页时触发
        const val MESSAGES_PAGE_VIEW = "MessagesPage_View"

        // 消息页消息点击 -> 用户在消息页点击任一消息时触发
        const val MESSAGES_PAGE_MESSAGES_CLICK = "MessagesPage_Messages_Click"

        // 消息页Tab标签点击 -> 用户在消息页点击任一Tab标签时触发
        const val MESSAGES_PAGE_TAB_CLICK = "MessagesPage_Tab_Click"

        // 消息页一键全读点击 -> 用户在消息页点击一键全读时触发
        const val MESSAGES_PAGE_ONE_CLICK_READ_CLICK = "MessagesPage_OneClickRead_Click"

        // 消息页顶部打开提示点击 -> 用户在消息页点击顶部push打开提示的Enable Now时触发
        const val MESSAGES_PAGE_ENABLE_NOW_CLICK = "MessagesPage_EnableNow_Click"

        // 通知设定页push打开引导按钮点击 -> 用户在通知设定页点击push打开引导按钮时触发
        const val NOTIFICATION_SETTINGS_PAGE_DISABLED_CLICK = "NotificationSettingsPage_Disabled_Click"

        // 通知设定页开启Do not disturb模式点击 -> 用户在通知设定页点击开启Do not disturb模式时触发
        const val NOTIFICATION_SETTINGS_PAGE_DO_NOT_DISTURB_CLICK = "NotificationSettingsPage_DoNotDisturb_Click"

        // Profile页
        // Profile页UID复制点击 -> 用户在Profile页点击UID复制时触发
        const val PROFILE_PAGE_UID_COPY_CLICK = "ProfilePage_UIDCopy_Click"

        // 个人Profile页UID点击 -> 用户在个人Profile页点击UID复制时触发
        const val PERSONAL_PROFILE_PAGE_UID_COPY_CLICK = "PersonalProfilePage_UIDCopy_Click"

        // 下单页
        // 交易详情页浏览 -> 用户浏览交易详情页时触发
        const val TRADE_PAGE_VIEW = "TradePage_View"

        // 交易详情页Confirm按钮点击 -> 交易详情页点击买卖开仓按钮后跳出二次确认，点击Confirm时触发
        const val TRADE_OPEN_CONFIRM = "TradeOpen_Confirm"

        // 交易详情页K线按钮点击 -> 用户在交易详情页点击K线按钮时触发
        const val TRADE_PAGE_K_CHART_BTN_CLICK = "TradePage_KchartBtn_Click"

        // 交易详情页切换产品点击 -> 用户在交易详情页点击切换产品按钮时触发
        const val TRADE_PAGE_PRODUCT_CLICK = "TradePage_Product_Click"

        // 交易详情页加减号按钮点击 -> 用户在交易详情页点击加减号时触发
        const val TRADE_PAGE_PLUS_MINUS_BTN_CLICK = "TradePage_PlusMinusBtn_Click"

        // 交易详情页交易量拉杆点击 -> 用户在交易详情页有使用交易量拉杆，拉杆值不为0%时触发
        const val TRADE_PAGE_VOLUME_CONTROL_CLICK = "TradePage_VolumeControl_Click"

        // 交易详情页底部图表点击 -> 用户在交易详情页底部图表收起时，有点击图表使其展开时触发
        const val TRADE_PAGE_BOTTOM_CHART_CLICK = "TradePage_BottomChart_Click"

        // 交易设置页二次确认弹窗开关点击 -> 用户在Trade Setting 页点击二次确认弹窗开关后，接口请求成功时触发
        const val TRADE_SETTING_CONFIRMATION_SWITCH_CLICK = "TradeSettingConfirmation_Switch_Click"

        // 交易二次确认弹窗DoNotShow点击 -> 用户点击二次确认弹窗 Do not show again...时触发
        const val TRADE_CONFIRMATION_POPUP_DO_NOT_SHOW_CLICK = "TradeConfirmationPopUp_DoNotShow_Click"

        // 交易详情页订单类型注释按钮点击 -> 用户在交易详情页点击订单类型注释按钮时触发
        const val TRADE_PAGE_ORDER_TYPES_ANNOTATION_CLICK = "TradePage_OrderTypesAnnotation_Click"

        // 交易详情页保证金注释按钮点击 -> 用户在交易详情页点击保证金注释按钮时触发
        const val TRADE_PAGE_MARGIN_ANNOTATION_CLICK = "TradePage_MarginAnnotation_Click"
    }

    // 文档地址：https://pj4w2l1pwuq.sg.larksuite.com/sheets/Nym5smA5GhN6dJt2wP7lCbACgub?sheet=QHoZAr
    object V3550 {
        // 未登录的Profile页浏览 -> 用户浏览未登录的Profile页时触发
        const val NLIPROFILEPAGE_VIEW = "NLIProfilePage_View"

        // 未登录的Profile页注册或登录按钮点击 -> 用户在未登录的Profile页点击Sign Up/Log In按钮时触发
        const val NLIPROFILEPAGE_SIGNUPLOGIN_CLICK = "NLIProfilePage_SignUpLogIn_Click"

        // 未登录Profile页的设置页浏览 -> 用户浏览未登录Profile页的设置页时触发
        const val NLIPROFILESETTINGPAGE_VIEW = "NLIProfileSettingPage_View"

        // 未登录Profile页的设置页语言切换 -> 用户在未登录Profile页的设置页完成切换语言时触发
        const val NLIPROFILESETTINGPAGE_LANGUAGE_SWITCH = "NLIProfileSettingPage_Language_Switch"

        // 未登录Profile页的设置页主题切换 -> 用户在未登录Profile页的设置页完成切换主题时触发
        const val NLIPROFILESETTINGPAGE_THEME_SWITCH = "NLIProfileSettingPage_Theme_Switch"

        // 未登录Profile页的设置页AboutUs点击 -> 用户在未登录Profile页的设置页点击AboutUs时触发
        const val NLIPROFILESETTINGPAGE_ABOUTUS_CLICK = "NLIProfileSettingPage_AboutUs_Click"
    }

    // 文档地址：https://pj4w2l1pwuq.sg.larksuite.com/sheets/Nym5smA5GhN6dJt2wP7lCbACgub?sheet=HlaJrm
    object V3560 {
        // 列表模式-切换点击
        const val LISTMODETOGGLE_CLICK = "ListModeToggle_Click"

        // 涨跌排序按钮点击
        const val PRICESORTBUTTON_CLICK = "PriceSortButton_Click"
    }

    // 文档地址：https://pj4w2l1pwuq.sg.larksuite.com/sheets/Nym5smA5GhN6dJt2wP7lCbACgub?sheet=rl0Z4S
    // https://pj4w2l1pwuq.sg.larksuite.com/wiki/PkyrwMOj8iprwrkDPzxlH9cegih?sheet=IVfr5B
    object V3610 {
        // 策略点击
        const val COPYTRADINGSTRATEGY_CLICK = "CopyTradingStrategy_Click"

        // 跟单订单提交
        const val COPYTRADINGSUBMIT_CLICK = "CopyTradingSubmit_Click"

        // 用户提交参加活动
        const val COPYTRADINGEVENT_SUBMITCLICK = "CopyTradingEvent_SubmitClick"

        // 用户提交参加活动
        const val COPYTRADINGEVENT_SUBMITRESULT = "CopyTradingEvent_SubmitResult"

        // APP Trade页面浏览
        const val TRADE_GENERALPAGEVIEW = "Trade_GeneralPageView"

        // 用户进入持仓管理选项列表
        const val COPYTRADINGCOPIER_MANAGE_CLICK = "CopyTradingCopier_Manage_Click"

        // 停止跟单回传结果
        const val COPYTRADINGSTOPCOPY_RESULT = "CopyTradingStopCopy_Result"

        //K线图确认关闭止盈止损点击
        const val CANCELTPSL_CONFIRMCLICK = "CancelTPSL_ConfirmClick"

        /**
         * 弹窗曝光时触发
         */
        const val POPUP_EXPOSURE = "Popup_Exposure"

        /**
         * 点击弹窗时触发
         */
        const val POPUP_CLICK = "Popup_Click"
    }

    object ORDER_POSITION {
        // 持仓页入金按钮点击
        const val POSITIONPAGE_DEPOSIT_CLICK = "PositionPage_Deposit_Click"

        //用户资产不为0时在持仓页点击New Order按钮时触发
        const val POSITIONPAGE_NEWORDER_CLICK = "PositionPage_NewOrder_Click"

        //持仓卡片编辑按钮点击
        const val POSITIONCARD_EDITBTN_CLICK = "PositionCard_EditBtn_Click"

        //持仓Close弹窗交易量拉杆点击
        const val POSITIONCLOSEPOPUP_VOLUMECONTROL_CLICK = "PositionClosePopup_VolumeControl_Click"

        //持仓Close弹窗Confirm按钮点击
        const val POSITIONCLOSEPOPUP_CONFIRM = "PositionClosePopup_Confirm"

        //止盈止损弹窗Confirm按钮点击
        const val TP_SLPOPUP_CONFIRM = "TP_SLPopup_Confirm"

        //持倉卡片K线按钮点击
        const val POSITIONCARD_CANDLESTBTN_CLICK = "PositionCard_CandlestBtn_Click"

        //持倉卡片订单复制按钮点击
        const val POSITIONCARD_ORDERIDCOPYTBTN_CLICK = "PositionCard_OrderIDCopytBtn_Click"

    }

    object SymbolSearch {
        const val CANDLESTICKSEARCH_RESULTCLICK = "CandlestickSearch_ResultClick"
    }

    object V3700 {

        /**
         * kyc 认证中心页面显示 standard plus
         */
        const val AUTHENTICATIONCENTER_PAGEVIEW = "AuthenticationCenter_PageView"

        /**
         * kyc 认证中心页面详情点击
         */
        const val AUTHENTICATIONCENTER_DETAILCLICK = "AuthenticationCenter_DetailClick"

        /**
         * kyc等级弹窗显示
         */
        const val KYCVERIFYPOPUP_VIEW = "KYCVerifypopup_View"

        /**
         * kyc等级弹窗点击
         */
        const val KYCVERIFYPOPUP_CLICK = "KYCVerifypopup_Click"

        /**
         * 完善opt验证按钮点击
         */
        const val OTP_VERIFY_CLICK = "OTP_Verify_Click"

        /**
         * 提示按钮点击
         */
        const val UPGRADE_CLICK = "Upgrade_Click"

        /**
         * 注册页面浏览 -> 注册页加载完成时触发
         */
        const val REGISTER_PAGE_VIEW = "RegisterPage_View"

        /**
         * 注册页面按钮点击 -> 注册页面按钮点击时触发
         */
        const val REGISTER_PAGE_CLICK = "RegisterPage_Click"
    }

    // 文档地址：https://pj4w2l1pwuq.sg.larksuite.com/sheets/Nym5smA5GhN6dJt2wP7lCbACgub?sheet=cLyqjM
    object V3710 {
        /**
         * 跟单详情页Portfolio栏位切换 -> 信号源点击切换 Pnl/Daily Change
         */
        const val COPY_TRADING_PORTFOLIO_COLUMN_SWITCH = "CopyTradingPortfolio_Column_Switch"

        /**
         * 跟单详情页tab点击 -> 用户点击 Overview/Portfolio Tab
         */
        const val COPY_TRADING_STRATEGY_TAB_CLICK = "CopyTradingStrategy_Tab_Click"

        /**
         * 跟单策略排序点击 -> 用户点击rating弹框其中1个tab：Rating、Return、Copiers、Win Rate、Risk Band
         */
        const val COPY_TRADING_DISCOVER_SORT_CLICK = "CopyTradingDiscover_Sort_Click"

        /**
         * 跟单策略筛选确认点击 -> 用户筛选完Filter弹框的6个未读的条件后点击confirm按钮
         */
        const val COPY_TRADING_DISCOVER_FILTER_CONFIRM_CLICK = "CopyTradingDiscover_Filter_Confirm_Click"

        /**
         * 优惠券夹页_主页面浏览
         */
        const val COUPONMAINPAGE_VIEW = "CouponMainPage_View"

        /**
         * 优惠券夹页_按钮点击
         */
        const val COUPONPAGEBUTTON_CLICK = "CouponPageButton_Click"

        /**
         * 优惠券夹页_优惠券详情页浏览
         */
        const val COUPONDETAILPAGE_VIEW = "CouponDetailPage_View"

        /**
         * 优惠券夹页_优惠券详情页提交
         */
        const val COUPONDETAILPAGE_SUBMIT = "CouponDetailPage_Submit"

        /**
         * 优惠券夹页_Redeem点击
         */
        const val COUPONPAGEREDEEMBUTTON_CLICK = "CouponPageRedeemButton_Click"

        /**
         * 优惠券夹页_Redeem兑换结果
         */
        const val COUPONPAGEREDEEMBUTTON_RESULT = "CouponPageRedeemButton_Result"

        /**
         * 分享场景浏览
         */
        const val SHAREPOSTER_VIEW = "SharePoster_View"

        /**
         * 分享按钮点击
         */
        const val SHAREPOSTERBUTTON_CLICK = "SharePosterButton_Click"

        /**
         * 分享edit按钮点击
         */
        const val SHAREEDITBUTTON_CLICK = "ShareEditButton_Click"
    }

    // 三方登录。文档地址：https://pj4w2l1pwuq.sg.larksuite.com/wiki/Mf06wAlLmirTKykYLjulXQmCgKF?sheet=ad3d6b
    object ThirdLogin {

        /**
         * Telegram绑定点击 -> 用户点击绑定Telegram
         */
        const val LOGIN_TELEGRAM_CLICK = "Login_Telegram_Click"

        /**
         * Telegram解绑点击 -> 用户点击解绑Telegram
         */
        const val UNLINK_TELEGRAM_CLICK = "Unlink_Telegram_Click"
    }

    // 优化
    object Opt {
        /**
         * H5流程 打开页面、加载成功、加载失败、交互、关闭页面
         */
        const val H5_PROCESS = "h5_process"
    }

    //常量key值
    object Key {
        const val TAB_NAME = "tab_name" // Tab 名称
        const val MODULE_ID = "module_id" // 模块id
        const val MODULE_NAME = "module_name" // 模块名称
        const val MODULE_RANK = "module_rank" // 模块序号
        const val MKT_ID = "mkt_id" // 素材id
        const val MKT_NAME = "mkt_name" // 素材名称
        const val MKT_RANK = "mkt_rank" // 素材排序
        const val TARGET_URL = "target_url" // 跳转链接
        const val BUTTON_NAME = "button_name" // 按钮名称
        const val SYMBOL_ID = "symbol_id" // symbolId
        const val SYMBOL_NAME = "symbol_name" // symbol名称
        const val SYMBOL_RANK = "symbol_rank" // symbol排序
        const val CURRENT_PAGE_NAME = "current_page_name" // 当前页面名称
        const val ACCOUNT_CURRENCY = "account_currency" // 账户币种
        const val DEPOSIT_METHOD = "deposit_method" // 入金方式
        const val IDENTITY_LEVEL = "openidentity_level" // 验证阶段
        const val IDENTITY_STEP = "openidentity_step" // 验证步骤
        const val TRADE_TYPE = "trade_type" // 交易类型
        const val PRODUCT_GROUP = "product_group" // 交易产品组
        const val PRODUCT_SYMBOL = "product_symbol" // 交易产品
        const val IS_LONG_TERM = "is_long_term" // 是否长期活动
        const val ACTIVITY_START_TIME = "activity_start_time" // 活动开始时间
        const val ACTIVITY_END_TIME = "activity_end_time" // 活动结束时间
        const val ACTIVITY_ID = "activity_id" // 活动id
        const val ACTIVITY_NAME = "activity_name" // 活动名称
        const val ACTIVITY_RANK = "activity_rank" // 活动排序
        const val TRADE_DIRECTION = "trade_direction" // 交易方向
        const val IS_PROFIT = "is_profit" // 是否选择止盈
        const val IS_LOSS = "is_loss" // 是否选择止损
        const val TRADE_MODE = "trade_mode" // 交易方式
        const val IS_COUPON = "is_coupon" // 是否使用优惠券
        const val COUPON_ID = "coupon_id" // 优惠券ID
        const val COUPON_NAME = "coupon_name" // 优惠券名称
        const val COUPON_AMOUNT = "coupon_amount" // 优惠券金额
        const val COUPON_TYPE = "coupon_type" // 优惠券类型
        const val IS_ACTIVITY_SHARE = "is_activity_share" // 是否活动分享
        const val SHARE_TYPE = "share_type" // 分享方式
        const val IS_OPTIONAL = "is_optional" // 是否自选
        const val STRATEGY_TITLE = "strategy_title" // 策略标题
        const val PROVIDER_NAME = "provider_name" // 信号源名称
        const val STRATEGY_ID = "strategy_id" // 策略id
        const val PROVIDER_ID = "provider_id" // 信号源id
        const val RETURN_RATIO = "return_ratio" // 收益率
        const val RETURN_TYPE = "return_type" // 收益类型
        const val COPIERS_NUMBER = "copiers_number" // 跟单者数量
        const val RISK_LEVEL = "risk_level" // 风险度
        const val PROFIT_SHARE = "profit_share" // 分润比例
        const val SETTLEMENT = "settlement" // 结算周期
        const val COPY_MODE = "copy_mode" // 跟随模式
        const val INVESTMENT_AMOUNT = "investment_amount" // 投资金额
        const val INVESTMENT_CURRENCY = "investment_currency" // 投资币种
        const val STOP_LOSS = "stop_loss" // 止损百分比
        const val TAKE_PROFIT = "take_profit" // 止盈百分比
        const val IS_LOT_ROUNDUP = "is_lot_roundup" // 是否使用提升助手
        const val IS_COPY_OPENED = "is_copy_opened" // 是否复制持仓订单
        const val BELONG_TAB_NAME = "belong_tab_name" // 所属Tab 名称
        const val BUTTON_LOCATION = "button_location" // 按钮位置
        const val ORDER_ID = "order_id" // 订单ID
        const val ENTRANCE_POSITION = "entrance_position" // 入口位置
        const val ORDER_TYPE = "order_type" // 订单类型
        const val SCREEN_ORIENTATION = "screen_orientation" // 荧幕方向
        const val ARTICLE_TITLE = "article_title" // 文章标题
        const val MESSAGES_STATUS = "messages_status" // 消息状态
        const val IS_LINK = "is_link" // 是否有带链接
        const val FIELD_NAME = "field_name" // 栏位名称
        const val ORDER_UNIT = "order_unit" // 下单单位
        const val SWITCH_STATUS = "switch_status" // 开关状态
        const val POSITION = "position" // 入口位置
        const val TARGET_NAME = "target_name" // 目标名称
        const val IS_SHIELDED_STRATEGY = "is_shielded_strategy" // 参加活动的策略标识
        const val SUBMIT_RESULT = "submit_result" // 提交结果
        const val TP_TYPE = "tp_type" // 止盈设置类型
        const val SL_TYPE = "sl_type" // 止损设置类型
        const val IS_SLECTED_TP = "is_slected_tp" // 是否有选择止盈
        const val IS_SLECTED_SL = "is_slected_sl" // 是否有选择止损
        const val ORIGINAL_KEY_WORD = "original_key_word" //原始搜索关键字
        const val CLICK_PRODUCT = "click_product" //点击商品
        const val POSITION_RANK = "position_rank" //位置序号
        const val IS_DEFAULT = "is_default" //是否为预设商品
        const val PLATFORM_TYPE = "platform_type" // 平台类型
        const val POPUP_TYPE = "popup_type" // 弹窗类型
        const val POPUP_NAME = "popup_name" // 弹窗名称
        const val POPUP_ID = "popup_id" // 弹窗id
        const val ACCOUNT_LEVEL = "account_level" // 账户等级
        const val FUNCTION_NAME = "function_name" // 方法名
        const val COLUMN_NAME = "column_name" // 栏目名称

        // opt 需求key
        const val IS_FIRST = "is_first" // 是否是首次加载
        const val TIMESTAMP = "timestamp" // 时间戳
        const val LOAD_TIME = "load_time" // 加载时长
        const val STEP = "step" // 步骤
        const val JS_VALUE = "js_value" // js交互的值
    }
}