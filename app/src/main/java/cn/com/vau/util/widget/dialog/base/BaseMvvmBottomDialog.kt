package cn.com.vau.util.widget.dialog.base

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelStore
import androidx.lifecycle.ViewModelStoreOwner
import androidx.viewbinding.ViewBinding
import cn.com.vau.common.base.mvvm.ILoading
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.utils.SDKIntervalUtil
import cn.com.vau.common.view.dialog.CommonLoadingDialog
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.util.LogUtil
import cn.com.vau.util.getStatusHeight
import cn.com.vau.util.screenHeight
import com.lxj.xpopup.enums.PopupStatus
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * BottomDialog
 * created by array on 2025/2/14 11:10
 * desc:
 * 1、默认dismiss时销毁ViewModel（使用mViewModelStoreOwner创建ViewModel的话）
 * 2、使用EventBus时，注册和反注册
 * 3、使用SDKIntervalUtil时，添加和移除回调
 * 4、支持loading
 */
@SuppressLint("ViewConstructor")
abstract class BaseMvvmBottomDialog<VB : ViewBinding, VM : BaseViewModel>(
    val activity: FragmentActivity,
    title: CharSequence?,
    viewBinding: ((LayoutInflater, ViewGroup, Boolean) -> VB),
) : BottomDialog<VB>(
    context = activity,
    viewBinding = viewBinding,
    title = title
), ILoading, SDKIntervalCallback {

    val mViewModel by lazy {
        initViewModel()
    }

    abstract fun initViewModel(): VM

    private val mViewModelStoreOwner by lazy {
        MyViewModelStoreOwner()
    }

    private val mLoadingDialog by lazy { CommonLoadingDialog(activity) }

    override fun onCallback() {

    }

    override fun onCreate() {
        if (useEventBus() && !EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        if (useSDKIntervalUtil()) {
            SDKIntervalUtil.instance.addCallBack(this)
        }
        initLoadingChange()
        super.onCreate()
    }

    override fun onDismiss() {
        super.onDismiss()
        mViewModelStoreOwner.viewModelStore.clear()
        if (useEventBus()) {
            EventBus.getDefault().unregister(this)
        }
        if (useSDKIntervalUtil()) {
            SDKIntervalUtil.instance.removeCallBack(this)
        }
        hideLoadDialog()
    }

    override fun setContentView() {
        super.setContentView()
        rootBinding.container.addOnLayoutChangeListener { _, left, top, right, bottom,
                                                          oldLeft, oldTop, oldRight, oldBottom ->
            val newHeight = bottom - top
            val oldHeight = oldBottom - oldTop

            if ((popupStatus == PopupStatus.Show || popupStatus == PopupStatus.Showing) && newHeight != oldHeight) {
                // 处理高度变化
                LogUtil.i("zl_log", "BaseMvvmBottomDialog: addOnLayoutChangeListener: open")
                bottomPopupContainer.open()
            }
        }
    }

    override fun getMaxHeight(): Int {
        return screenHeight - context.getStatusHeight()
    }

    /**
     * VM监听loading变化事件
     */
    private fun initLoadingChange() {
        mViewModel.loadingChange.dialogLiveData.observe(this) {
            if (it) { //显示弹框
                showLoadDialog()
            } else { //关闭弹窗
                hideLoadDialog()
            }
        }

    }

    /**
     * 展示进度框
     */
    override fun showLoadDialog() {
        if (isDismiss || mLoadingDialog.isShowing) return
        if (!mLoadingDialog.isShowing) {
            mLoadingDialog
                .setAnchorView(rootBinding.root)
                .show()
        }
    }

    /**
     * 隐藏进度框
     */
    override fun hideLoadDialog() {
        try {
            if (mLoadingDialog.isShowing) mLoadingDialog.dismiss()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 是否使用EventBus
     */
    open fun useEventBus(): Boolean = false

    /**
     * 是否使用SDKIntervalUtil
     */
    open fun useSDKIntervalUtil(): Boolean = false

    protected fun getMyViewModelStoreOwner(): ViewModelStoreOwner = mViewModelStoreOwner

    @Subscribe(threadMode = ThreadMode.MAIN)
    open fun onMsgEvent(eventTag: String) {

    }

}

private class MyViewModelStore : ViewModelStore()

private class MyViewModelStoreOwner : ViewModelStoreOwner {
    private val mViewModelStore = MyViewModelStore()
    override val viewModelStore: ViewModelStore
        get() = mViewModelStore
}