package cn.com.vau.util.widget.dialog.base

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.databinding.PopupAttachListBinding
import com.lxj.xpopup.R
import com.lxj.xpopup.enums.PopupPosition
import com.lxj.xpopup.util.XPopupUtils

@SuppressLint("ViewConstructor")
class AttachListPopupWindow private constructor(
    context: Context,
    private var adapter: RecyclerView.Adapter<*>? = null,
    width: Int = 0,
    height: Int = 0,
    maxWidth: Int = 0,
    maxHeight: Int = 0,
    onCreateListener: ((PopupAttachListBinding) -> Unit)? = null,
    onDismissListener: (() -> Unit)? = null,
) : AttachPopupWindow<PopupAttachListBinding>(
    context,
    PopupAttachListBinding::inflate,
    onCreateListener,
    onDismissListener,
    width,
    height,
    maxWidth,
    maxHeight
), IDialog<PopupAttachListBinding> {

    override fun initView() {
        super.initView()
        setRecyclerView()
        setBorderRadius()
    }

    private fun setBorderRadius() {
        attachPopupContainer.background = XPopupUtils.createDrawable(
            resources.getColor(
                if (popupInfo.isDarkTheme)
                    R.color._xpopup_dark_color
                else
                    R.color._xpopup_light_color
            ), popupInfo.borderRadius
        )
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setRecyclerView() {
        mContentBinding.recyclerView.layoutManager = WrapContentLinearLayoutManager(context)
        mContentBinding.recyclerView.adapter = adapter
        adapter?.notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setAdapter(adapter: RecyclerView.Adapter<*>?) {
        if (adapter == null) {
            return
        }
        mContentBinding.recyclerView.adapter = adapter
        adapter.notifyDataSetChanged()
    }


    @Suppress("unused")
    class Builder(activity: Activity) :
        IBuilder<PopupAttachListBinding, Builder>(activity) {
        private var adapter: RecyclerView.Adapter<*>? = null

        //设置Adapter
        fun setAdapter(adapter: RecyclerView.Adapter<*>?) = apply {
            this.adapter = adapter
            return this
        }

        private var targetView: View? = null
        private var popupPosition: PopupPosition = PopupPosition.Left

        //如果是Attach则一定要指定在哪个View的基础上弹窗
        fun setTargetView(targetView: View?): Builder {
            this.targetView = targetView
            return this

        }

        fun setPopupPosition(popupPosition: PopupPosition): Builder {
            this.popupPosition = popupPosition
            return this

        }

        override fun getPopupPosition(): PopupPosition {
            return popupPosition
        }

        override fun getTargetView(): View? {
            return targetView
        }

        override fun build(): AttachListPopupWindow {
            return super.build() as AttachListPopupWindow
        }

        override fun createDialog(context: Context): IDialog<PopupAttachListBinding> {
            return AttachListPopupWindow(
                context,
                adapter,
                config.width,
                config.height,
                config.maxWidth,
                config.maxHeight,
                config.onCreateListener,
                config.onDismissListener
            )
        }

    }
}
