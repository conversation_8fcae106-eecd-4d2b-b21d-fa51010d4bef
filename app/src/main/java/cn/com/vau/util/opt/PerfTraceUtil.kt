package cn.com.vau.util.opt

import android.app.Activity
import android.content.Intent
import android.text.TextUtils
import android.view.View
import android.view.ViewTreeObserver
import androidx.annotation.Keep
import cn.com.vau.common.application.VauApplication
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.util.LogUtil
import cn.com.vau.util.opt.AppStartMonitor.Companion.MAX_LATENCY_BEFORE_UI_INIT_MICROS
import cn.com.vau.util.widget.FirebaseManager
import com.google.firebase.ktx.Firebase
import com.google.firebase.perf.ktx.performance
import com.google.firebase.perf.metrics.Trace
import com.google.firebase.perf.util.Clock
import com.google.firebase.perf.util.Timer
import java.util.concurrent.ConcurrentHashMap

object PerfTraceUtil {

    private val traces = ConcurrentHashMap<String, Trace>()
    private val activityList = mutableListOf<String>()
    private val clock = Clock()

    val startUpTagFromFcm = "start_up_from_fcm"
    private const val TAG_PREFIX = "Perf_v6_Start"

    val splash = "SplashActivity"
    private val main = "MainActivity"

    fun addActivity(activity: Activity?) {
        if (activity == null || activityList.size >= 2) {
            return
        }
        activityList.add(activity::class.java.simpleName)
    }

    fun isTraceSplash(): Boolean {
        val isTrace = activityList.size == 1 &&
                activityList[0].contains(splash) &&
                !isStartTraceStopped()
        if (!isTrace) {
            stopStartTrace()
        }
        return isTrace
    }

    fun isTraceMain(): Boolean {
        val isTrace = activityList.size == 2 &&
                activityList[0].contains(splash) &&
                activityList[1].contains(main) &&
                !isStartTraceStopped()
        if (!isTrace) {
            stopStartTrace()
        }
        return isTrace
    }

    fun stopStartTrace() {
        AppStartMonitor.getInstance(VauApplication.context)?.isStartTraceStopped = true
        traces.forEach { (key, trace) ->
            if(key.contains(TAG_PREFIX)){
                traces.remove(key)
            }
        }
    }

    fun startTrace(trace: StartTrace?, intent: Intent? = null) {
        if (trace == null || isStartFirst()) {
            return
        }
        try {
            val name = switchTraceForSt(trace).name
            LogUtil.i("PerfTraceUtil", "startTrace:$name")
            if (name.contains(TAG_PREFIX) && isStartTraceStopped()) {
                return
            }
            val newTrace = Firebase.performance.newTrace(name)
            traces[name] = newTrace
            newTrace.start()
        } catch (e: Exception) {
            FirebaseManager.recordException(Exception("startTrace e" + e.message))
        }
    }

    private fun isStartFirst(): Boolean {
        return AppStartMonitor.getInstance(VauApplication.context)?.isStartFirst == true
    }

    private fun isStartTraceStopped(): Boolean {
        return AppStartMonitor.getInstance(VauApplication.context)?.isStartTraceStopped == true
    }

    fun stopTrace(trace: StartTrace?, intent: Intent? = null) {
        if (trace == null || isStartFirst()) {
            return
        }
        if (!checkNeedTrace(trace.name, intent)) {
            val name = switchTraceForSt(trace).name
            traces.remove(name)
            return
        }
        try {
            val name = switchTraceForSt(trace).name
            if (name.contains(TAG_PREFIX) && isStartTraceStopped()) {
                traces.remove(name)
                return
            }
            val t = traces[name] ?: return
            if (isExceedLimitTime(t)) {
                traces.remove(name)
                return
            }
            LogUtil.e("PerfTraceUtil", "stopTrace :$name")
            t.stop()
            traces.remove(name)
        } catch (e: Exception) {
            FirebaseManager.recordException(Exception("stopTrace e" + e.message))
        }
    }

    private fun isExceedLimitTime(t: Trace): Boolean {
        try {
            val declaredField = t.javaClass.getDeclaredField("startTime")
            declaredField.isAccessible = true
            val startTime = declaredField.get(t) as Timer
            return startTime.getDurationMicros(clock.time) > MAX_LATENCY_BEFORE_UI_INIT_MICROS
        } catch (e: Exception) {
            FirebaseManager.recordException(Exception("isExceedLimitTime e:" + e.message))
        }
        return false
    }

    private fun checkNeedTrace(beforeName: String, intent: Intent?): Boolean {
        // 如果是 appCreate -> splashCreate. 判断启动路径是否符合 application -> splash
        if (beforeName == StartTrace.Perf_v6_Start_AppCreate_SplashCreate.name) {
            if (isTraceSplash() && !isStartUpFromFcmOrDeepLink(intent)) {
                return true
            } else {
                stopStartTrace()
                return false
            }
        }

        // 如果是 appCreate -> tradeCreate. 判断启动路径是否符合 application -> trade
        if (beforeName == StartTrace.Perf_v6_Start_AppCreate_TradeFirst.name) {
            if (isTraceMain() && !isStartUpFromFcmOrDeepLink(intent)) {
                return true
            } else {
                stopStartTrace()
                return false
            }
        }

        return true
    }


    /**
     * 是否通过Fcm或者deepleek启动
     */
    fun isStartUpFromFcmOrDeepLink(intent: Intent?): Boolean {
        if (intent == null) {
            return false
        }
        val startUpTag = intent.getStringExtra(startUpTagFromFcm)

        //是否来自FcmTransitActivity
        val hasStartUpTagFromFcm = TextUtils.equals(startUpTag, startUpTagFromFcm)
        if (hasStartUpTagFromFcm) {
            stopStartTrace()
            return true
        }

        //是否来自FcmPush
        var isFromFcmPush = false
        if (intent.extras?.containsKey(Constants.FCM_TYPE) == true &&
            intent.extras?.containsKey(Constants.FCM_DATA_P) == true
        ) {
            isFromFcmPush = true
        } else if (intent.extras?.containsKey(Constants.FCM_TYPE) == false &&
            intent.extras?.containsKey(Constants.FCM_DATA_P) == true
        ) {
            isFromFcmPush = true
        } else if (intent.data?.toString()?.startsWith("vantage://") == true) {
            isFromFcmPush = true
        } else if (intent.data != null) {
            isFromFcmPush = true
        }
        if (isFromFcmPush) {
            stopStartTrace()
            return true
        }

        //是否来自场景
        var isOtherJump = false
        if (intent.extras?.containsKey("dynamic_links") == true) {
            isOtherJump = true
        }
        // 切换账户
        if (intent.extras?.containsKey("is_switch_account") == true) {
            isOtherJump = true
        }
        // 跳转LoginActivity（登出的ReStart方法需要跳）
        if (intent.extras?.getBoolean("isLaunchLogin", false) == true) {
            isOtherJump = true
        }
        if (intent.getIntExtra("app_link_type", -1) != -1) {
            isOtherJump = true
        }
        if (isOtherJump) {
            stopStartTrace()
            return true
        }
        return false
    }

    fun firstFrameTrace(
        rootView: View?,
        stopTrace: StartTrace?,
        startTrace: StartTrace?,
        intent: Intent? = null
    ) {
        if (isStartFirst() || rootView == null) {
            return
        }

        rootView.viewTreeObserver.addOnDrawListener(object : ViewTreeObserver.OnDrawListener {
            private var isFirstDraw = true
            override fun onDraw() {
                if (isFirstDraw) {
                    isFirstDraw = false
                    stopTrace(stopTrace, intent)
                    startTrace(startTrace, intent)
                    rootView.post {
                        try {
                            rootView.viewTreeObserver.removeOnDrawListener(this)
                        } catch (e: Exception) {
                            FirebaseManager.recordException(Exception("firstFrameTrace Exception:" + e.message))
                        }
                    }
                }
            }
        })
    }

    fun stopTraceOnFirstFrame(
        rootView: View?,
        intent: Intent? = null,
        vararg stopTraces: StartTrace
    ) {
        if (stopTraces.isEmpty() || isStartFirst() || rootView == null) {
            return
        }
        rootView.viewTreeObserver.addOnDrawListener(object : ViewTreeObserver.OnDrawListener {
            private var isFirstDraw = true
            override fun onDraw() {
                if (isFirstDraw) {
                    isFirstDraw = false
                    stopTraces.forEach {
                        stopTrace(it, intent)
                    }
                    stopStartTrace()
                    rootView.post {
                        try {
                            rootView.viewTreeObserver.removeOnDrawListener(this)
                        } catch (e: Exception) {
                            FirebaseManager.recordException(Exception("stopTraceOnFirstFrame Exception:" + e.message))
                        }
                    }
                }
            }
        })
    }

    private fun switchTraceForSt(trace: StartTrace): StartTrace {
        if (!UserDataUtil.isStLogin() || !UserDataUtil.isLogin()) {
            return trace
        }
        return when (trace) {
            StartTrace.Perf_v6_Start_AppCreate_SplashCreate -> StartTrace.Perf_v6_Start_St_AppCreate_SplashCreate
            StartTrace.Perf_v6_Start_SplashCreate_SplashFirst -> StartTrace.Perf_v6_Start_St_SplashCreate_SplashFirst
            StartTrace.Perf_v6_Start_SplashFirst_StartMain -> StartTrace.Perf_v6_Start_St_SplashFirst_StartMain
            StartTrace.Perf_v6_Start_StartMain_MainCreate -> StartTrace.Perf_v6_Start_St_StartMain_MainCreate
            StartTrace.Perf_v6_Start_MainCreate_MainFirst -> StartTrace.Perf_v6_Start_St_MainCreate_MainFirst
            StartTrace.Perf_v6_Start_MainFirst_TradeFirst -> StartTrace.Perf_v6_Start_St_MainFirst_TradeFirst
            StartTrace.Perf_v6_Start_AppCreate_TradeFirst -> StartTrace.Perf_v6_Start_St_AppCreate_TradeFirst

            StartTrace.Perf_v6_InitHelper_Start_Connected -> StartTrace.Perf_v6_InitHelper_St_Start_Connected
            else -> trace
        }

    }

    @Keep
    enum class StartTrace {
        /**
         * AppAttach -> SplashCreate -> SplashFirst -> MainStart -> MainFirst -> TraceFirst -> TraceFinis
         */
        Perf_v6_Start_AppCreate_SplashCreate,
        Perf_v6_Start_SplashCreate_SplashFirst,
        Perf_v6_Start_SplashFirst_StartMain,
        Perf_v6_Start_StartMain_MainCreate,
        Perf_v6_Start_MainCreate_MainFirst,
        Perf_v6_Start_MainFirst_TradeFirst,

        Perf_v6_Start_AppCreate_TradeFirst,

        Perf_v6_Start_St_AppCreate_SplashCreate,
        Perf_v6_Start_St_SplashCreate_SplashFirst,
        Perf_v6_Start_St_SplashFirst_StartMain,
        Perf_v6_Start_St_StartMain_MainCreate,
        Perf_v6_Start_St_MainCreate_MainFirst,
        Perf_v6_Start_St_MainFirst_TradeFirst,

        Perf_v6_Start_St_AppCreate_TradeFirst,

        /**
         * Order
         */
        Perf_v6_Order_Create_First,

        /**
         * StOrder
         */
        Perf_v6_Order_St_Create_First,
        Perf_v6_Order_St_First_Finish,

        /**
         * Signals
         */
        // 策略社区
        Perf_v6_Signals_strategy_Create_First,
        Perf_v6_Signals_strategy_First_Finish,

        /**
         * Promo
         */
        Perf_v6_Promo_Create_First,
        Perf_v6_Promo_First_Finish,

        /**
         * Profile
         */
        Perf_v6_Profile_Create_First,
        Perf_v6_Profile_First_Finish,

        /**
         * Account
         */
        Perf_v6_Account_Create_First,
        Perf_v6_Account_First_Finish,

        /**
         * H5
         */
        Perf_v6_H5_Create_LoadUrl,
        Perf_v6_H5_LoadUrl_Finish,
        Perf_v6_H5_All_Finish,

        Perf_v6_InitHelper_Start_Connected,
        Perf_v6_InitHelper_St_Start_Connected,
    }
}