package cn.com.vau.util

import android.os.Looper
import android.text.TextUtils
import cn.com.vau.BuildConfig
import cn.com.vau.common.AbConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.utils.AbTestUtil
import cn.com.vau.util.widget.FirebaseManager
import java.math.*
import java.text.*
import java.util.Locale


/**
 * 由于调用format.format()  方法是比较耗时的。并且numberFormat 这个方法是线程不安全的。
 * 因此我们就要在子线程中来调用此方法，且需要将这个方法加锁
 * 但是，由于历史遗留问题，项目中已经有很多在UI线程中调用这个方法。由于在子线程中调用这些耗时方法是比较频繁的，例如WebSocket每隔200ml就可能推送一回，线程竞争会很激烈，如果一旦加锁，
 * 那么就会造成UI线程等待锁而阻塞
 * 因此我们目前解决的办法是，新写一个单独的方法，来供子线程中调用。主线程中还是调用原来的方法，不会和主线程出现竞争问题。
 * 这个新写的单独的方法的命名都是xxxx2这种形式
 */

val abOptExpandToThreadLocal by lazy { AbTestUtil.getBoolean(AbConstants.App.AB_KEY_OPT_EXPAND_TO_THREAD_LOCAL) }
/**
 * 通用的数字格式化扩展函数 可以在子线程中调用的
 */
fun Number?.numFormat2(keepNum: Int, isRound: Boolean): String {
    if (this == null)
        return "0.0"

    return numberFormat2(this, keepNum, isRound)
}


/**
 * 根据货币格式化
 */
fun Number?.numCurrencyFormat2(
    currencyType: String = UserDataUtil.currencyType(), isRound: Boolean = true
): String = this.toString().numCurrencyFormat2(currencyType, isRound)


/**
 * 根据货币格式化
 */
fun String?.numCurrencyFormat2(
    currencyType: String = UserDataUtil.currencyType(), isRound: Boolean = true
): String {

    if (TextUtils.isEmpty(this))
        return when (currencyType) {
            "JPY", "USC" -> "0"
            "BTC", "ETH" -> "0.00000000"
            else -> "0.00"
        }

    return formatByCurrency2(this ?: "", currencyType, isRound)
}

/**
 * 根据货币的类型进行不同小数位的格式化
 */
private fun formatByCurrency2(amount: String, currencyType: String, isRound: Boolean = true): String =
    amount.numFormat2(
        when (currencyType) {
            "JPY", "USC" -> 0
            "BTC", "ETH" -> 8
            else -> 2
        },
        isRound
    )

/**
 * 需要四舍五入的第二个参数传 true
 */
fun String?.numFormat2(digitParam: Int, isRound: Boolean = false): String {
    return this.toDoubleCatching().numFormat2(digitParam, isRound)
}

/**
 * 格式化产品价格
 */
fun String?.formatProductPrice2(digits: Int, isRound: Boolean = false): String {
    return this.toDoubleCatching().formatProductPrice2(digits, isRound)
}

/**
 * 扩展函数，用于格式化数字为产品价格的形式
 */
fun Number.formatProductPrice2(keepNum: Int, isRound: Boolean, zeroUI: String = "-"): String {
    // 如果当前数字为 0.0，则返回 "-"，表示没有价格
    if ("0".mathCompTo(this.toString()) == 0) return zeroUI

    return numberFormat2(this, keepNum, isRound)
}

//参考https://blog.csdn.net/Jason_Lewis/article/details/79176984
private var dfs2 = DecimalFormatSymbols.getInstance(Locale.ENGLISH).apply {
    // 设置小数点符号为 '.'，即使用点号作为小数分隔符
    decimalSeparator = '.'
}

//   private static NumberFormat format = NumberFormat.getNumberInstance(Locale.ENGLISH);
private var format2 = (NumberFormat.getInstance(Locale.ENGLISH) as DecimalFormat).apply {
    // 禁用千位分隔符
    isGroupingUsed = false
    decimalFormatSymbols = dfs2
}

private val DECIMAL_FORMAT_THREAD_LOCAL: ThreadLocal<DecimalFormat> = object : ThreadLocal<DecimalFormat>() {

    override fun initialValue(): DecimalFormat{
        return (NumberFormat.getInstance(Locale.ENGLISH) as DecimalFormat).apply {
            // 禁用千位分隔符
            isGroupingUsed = false
            //参考https://blog.csdn.net/Jason_Lewis/article/details/79176984
            decimalFormatSymbols = DecimalFormatSymbols.getInstance(Locale.ENGLISH).apply {
                // 设置小数点符号为 '.'，即使用点号作为小数分隔符
                decimalSeparator = '.'
            }
        }
    }
}

/**
 * 仅这个类调用的 ， 统一的数字格式化函数
 */
private fun numberFormat2(number: Number, keepNum: Int, isRound: Boolean): String {
    if (!abOptExpandToThreadLocal) {
        return numberFormat2Old(number, keepNum, isRound)
    }
    //从ThreadLocal中获取，保证不同的线程，对象不同
    val decimalFormat = DECIMAL_FORMAT_THREAD_LOCAL.get() ?: return "0.0"
    // 如果 keepNum 为负数，则小数位数为 0，否则为 keepNum 指定的位数
    decimalFormat.maximumFractionDigits = keepNum.coerceAtLeast(0)
    decimalFormat.minimumFractionDigits = keepNum.coerceAtLeast(0)

    try { // 根据 isRound 参数决定是否进行四舍五入
        // 转换为 BigDecimal 以更好地控制精度
        var bigDecimal = BigDecimal(number.toString())

        bigDecimal = if (!isRound) { // 使用 setScale 保留指定小数位，直接截取多余的小数位
            bigDecimal.setScale(keepNum.coerceAtLeast(0), RoundingMode.FLOOR)
        } else { // 进行四舍五入
            bigDecimal.setScale(keepNum.coerceAtLeast(0), RoundingMode.HALF_UP)
        }
        return decimalFormat.format(bigDecimal)
    } catch (e: Exception) {
        e.printStackTrace() //上报为不严重类型
        FirebaseManager.recordException(Exception("numberFormat2 is Exception number=$number,keepNum=$keepNum,isRound=$isRound"))
        return "0.0"
    }
}
/**
 * 仅这个类调用的 ， 统一的数字格式化函数
 */
//-------------加锁----------------
@Synchronized
private fun numberFormat2Old(number: Number, keepNum: Int, isRound: Boolean): String { // 设置最大和最小的小数位数
    if (BuildConfig.DEBUG) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            throw RuntimeException("你不应该在主线程中调用此方法，因为可能会因为竞争锁而导致主线程阻塞。如果在主线使用，程应该使用Expand，而不应该使用Expend2")
        }
    }
    // 如果 keepNum 为负数，则小数位数为 0，否则为 keepNum 指定的位数
    format2.maximumFractionDigits = keepNum.coerceAtLeast(0)
    format2.minimumFractionDigits = keepNum.coerceAtLeast(0)

    try { // 根据 isRound 参数决定是否进行四舍五入
        // 转换为 BigDecimal 以更好地控制精度
        var bigDecimal = BigDecimal(number.toString())

        bigDecimal = if (!isRound) { // 使用 setScale 保留指定小数位，直接截取多余的小数位
            bigDecimal.setScale(keepNum.coerceAtLeast(0), RoundingMode.FLOOR)
        } else { // 进行四舍五入
            bigDecimal.setScale(keepNum.coerceAtLeast(0), RoundingMode.HALF_UP)
        }
        return format2.format(bigDecimal)
    } catch (e: Exception) {
        e.printStackTrace() //上报为不严重类型
        FirebaseManager.recordException(Exception("numberFormatOld2 is Exception number=$number,keepNum=$keepNum,isRound=$isRound"))
        return "0.0"
    }
}

private val df2 = DecimalFormat.getInstance(Locale.ENGLISH) as DecimalFormat

private val DECIMAL_FORMAT_ORDINARY_THREAD_LOCAL: ThreadLocal<DecimalFormat> = object : ThreadLocal<DecimalFormat>() {

    override fun initialValue(): DecimalFormat{
        return DecimalFormat.getInstance(Locale.ENGLISH) as DecimalFormat
    }
}

fun String.addComma2(digitParam: Int): String {
    if (!abOptExpandToThreadLocal) {
        return addComma2Old(digitParam)
    }
    var digit = ""
    if (digitParam == -1) {
        val currencyType = UserDataUtil.currencyType()
        digit = when (currencyType) {
            "JPY", "USC" -> ""
            "BTC", "ETH" -> "00000000"
            else -> "00"
        }
    } else {
        for (i in 1..digitParam) {
            digit += "0"
        }
    }
    //从ThreadLocal中获取，保证不同的线程，对象不同
    val decimalFormat = DECIMAL_FORMAT_ORDINARY_THREAD_LOCAL.get() ?: return this
    decimalFormat.isGroupingUsed = false
    decimalFormat.applyPattern(
        if (this.indexOf(".") > 0) {
            "###,##0.$digit"
        } else {
            "###,##0"
        }
    )

    return decimalFormat.format(this.toDoubleCatching())
}

//-------------加锁----------------
@Synchronized
fun String.addComma2Old(digitParam: Int): String {

    var digit = ""
    if (digitParam == -1) {
        val currencyType = UserDataUtil.currencyType()
        digit = when (currencyType) {
            "JPY", "USC" -> ""
            "BTC", "ETH" -> "00000000"
            else -> "00"
        }
    } else {
        for (i in 1..digitParam) {
            digit += "0"
        }
    }
    df2.isGroupingUsed = false
    df2.applyPattern(
        if (this.indexOf(".") > 0) {
            "###,##0.$digit"
        } else {
            "###,##0"
        }
    )

    return df2.format(this.toDoubleCatching())
}
