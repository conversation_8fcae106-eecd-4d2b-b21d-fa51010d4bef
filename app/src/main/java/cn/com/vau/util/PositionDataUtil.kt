package cn.com.vau.util

import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.trade.PositionBean
import cn.com.vau.trade.viewmodel.OrderViewModel
import java.util.concurrent.CopyOnWriteArrayList

object PositionDataUtil {

    /**
     * 获取持仓列表
     * @param isNeedFilter true 获取K线页指定产品(filterSymbol)的持仓
     * @param filterSymbol K线页指定产品
     */
    private fun initPositionShareData(filterSymbol: String? = null, isNeedFilter: Boolean = false): CopyOnWriteArrayList<ShareOrderData> {
        if (isNeedFilter) {
            return getFilterSymbolPositionData(filterSymbol)
        }
        return getPositionData()
    }

    /**
     * 获取持仓
     */
    private fun getPositionData(): CopyOnWriteArrayList<ShareOrderData> {
        val dataList = CopyOnWriteArrayList<ShareOrderData>()
        dataList.addAll(VAUSdkUtil.shareOrderList())
        return dataList
    }

    /**
     * 获取指定产品的持仓
     */
    private fun getFilterSymbolPositionData(filterSymbol: String? = null): CopyOnWriteArrayList<ShareOrderData> {
        val dataList = CopyOnWriteArrayList<ShareOrderData>()
        dataList.addAll(VAUSdkUtil.shareOrderList().filter {
            it.symbol == filterSymbol
        })
        return dataList
    }

    /**
     * 使用公共数据的持仓数据初始化持仓列表数据
     */
    fun initPositionDataFromShareData(filterSymbol: String? = null, isNeedFilter: Boolean = false, positionMap: HashMap<String, PositionBean>): CopyOnWriteArrayList<ShareOrderData> {
        val shareOrderList = initPositionShareData(filterSymbol, isNeedFilter)
        for (shareOrder in shareOrderList) {
            val tpSlStatue = if (shareOrder.takeProfit.mathCompTo("0") != 0 || shareOrder.stopLoss.mathCompTo("0") != 0) 1 else 0
            val positionBean = positionMap[shareOrder.order] ?: PositionBean(tpSlStatus = tpSlStatue)
            if (isAmountOpen()) {
                positionBean.volumeAmount = getAmountFromVolume(shareOrder).addComma()
            } else {
                shareOrder.volumeUI = shareOrder.volume
            }
            shareOrder.positionBean = positionBean
            comma(shareOrder, positionBean)
            positionMap[shareOrder.order ?: ""] = positionBean
        }
        return shareOrderList
    }

    /**
     * 数值千分位处理 运行在主线程
     */
    private fun comma(shareOrderData: ShareOrderData, positionBean: PositionBean) {
        positionBean.openPriceUI = shareOrderData.openPrice.addComma(shareOrderData.digits)
        val profitUI = shareOrderData.profit.numCurrencyFormat().addComma()
        positionBean.profitUI = if (shareOrderData.profit > 0) "+${profitUI}" else profitUI
        positionBean.tpUI = shareOrderData.takeProfit?.addComma(shareOrderData.digits).ifNull()
        positionBean.slUI = shareOrderData.stopLoss?.addComma(shareOrderData.digits).ifNull()

    }

    /**
     * 数值千分位处理 运行在子线程
     */
    private fun comma2(shareOrderData: ShareOrderData, positionBean: PositionBean) {
        positionBean.openPriceUI = shareOrderData.openPrice.addComma2(shareOrderData.digits)
        val profitUI = shareOrderData.profit.numCurrencyFormat().addComma2(-1)
        positionBean.profitUI = if (shareOrderData.profit > 0) "+${profitUI}" else profitUI
        positionBean.tpUI = shareOrderData.takeProfit?.addComma2(shareOrderData.digits).ifNull()
        positionBean.slUI = shareOrderData.stopLoss?.addComma2(shareOrderData.digits).ifNull()
    }

    fun isAmountOpen(): Boolean {
        return OrderViewModel.UNIT_AMOUNT == SpManager.getOpenPositionUnit()
    }

    /**
     * 根据当前价格计算持有量
     */
    private fun getAmountFromVolume(orderData: ShareOrderData): String {
        val productData = VAUSdkUtil.symbolList().firstOrNull {
            it.symbol == orderData.symbol
        } ?: return ""
        return OrderUtil.getAmountFromVolume(productData, orderData.volume ?: "0", if (OrderUtil.isBuyOfOrder(orderData.cmd)) "1" else "0")
    }

    /**
     * 更新仓位信息
     * 注意：此方法运行在子线程的
     */
    fun refreshPositionData(shareOrderList: CopyOnWriteArrayList<ShareOrderData>, positionMap: HashMap<String, PositionBean>) {
        for (shareOrder in shareOrderList) {
            val positionBean = positionMap[shareOrder.order] ?: PositionBean()
            if (isAmountOpen()) {
                positionBean.volumeAmount = getAmountFromVolume(shareOrder).addComma2(-1)
            } else {
                shareOrder.volumeUI = shareOrder.volume
            }
            refreshTpSlStatue(positionBean, if (shareOrder.takeProfit.mathCompTo("0") != 0 || shareOrder.stopLoss.mathCompTo("0") != 0) 1 else 0)
            comma2(shareOrder, positionBean)
            shareOrder.positionBean = positionBean
        }
    }

    /**
     * 更新仓位止盈止损显示状态
     */
    private fun refreshTpSlStatue(positionBean: PositionBean, newStatue: Int) {
        val oldStatue = positionBean.tpSlStatus
        if (oldStatue == 0 && newStatue != 0) {
            positionBean.tpSlStatus = newStatue
            return
        }
        if (oldStatue != 0 && newStatue == 0) {
            positionBean.tpSlStatus = newStatue
        }
    }

    /**
     * 计算预估盈亏
     */
    private fun refreshEstimated(shareOrderData: ShareOrderData) {
        val data = VAUSdkUtil.symbolList().find {
            it.symbol == shareOrderData.symbol
        } ?: return

        if (shareOrderData.takeProfit.mathCompTo("0") != 0) {
            val estimateTp = VAUSdkUtil.getProfitLoss(
                data,
                shareOrderData.openPrice,
                shareOrderData.volume ?: "0",
                shareOrderData.cmd,
                shareOrderData.takeProfit ?: "0"
            ).toString().numCurrencyFormat()
            shareOrderData.positionBean?.estimatedTP = estimateTp
        } else {
            shareOrderData.positionBean?.estimatedTP = "0".numCurrencyFormat()
        }

        if (shareOrderData.stopLoss.mathCompTo("0") != 0) {
            val estimateSl = VAUSdkUtil.getProfitLoss(
                data,
                shareOrderData.openPrice,
                shareOrderData.volume ?: "0",
                shareOrderData.cmd,
                shareOrderData.stopLoss ?: "0"
            ).toString().numCurrencyFormat()
            shareOrderData.positionBean?.estimatedSl = estimateSl
        } else {
            shareOrderData.positionBean?.estimatedSl = "0".numCurrencyFormat()
        }
    }

    private fun getRoi(shareOrderData: ShareOrderData): String {
        val roi = "${"${shareOrderData.profit}".mathMul("100").mathDiv(shareOrderData.margin ?: "0", 2)}%"
        return roi
    }
}