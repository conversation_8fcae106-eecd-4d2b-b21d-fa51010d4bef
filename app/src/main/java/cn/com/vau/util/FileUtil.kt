package cn.com.vau.util

import android.content.ContentResolver
import android.net.Uri
import android.os.Build
import java.io.File
import java.io.FileNotFoundException
import java.io.IOException

/**
 * <pre>
 * author: Blankj
 * blog  : http://blankj.com
 * time  : 2016/05/03
 * desc  : UtilApp about file
</pre> *
 */
object FileUtil {

    /**
     * Return the file by path.
     *
     * @param filePath The path of file.
     * @return the file
     */
    fun getFileByPath(filePath: String): File? {
        return if (StringUtil.isSpace(filePath)) null else File(filePath)
    }

    /**
     * Return whether the file exists.
     *
     * @param file The file.
     * @return `true`: yes<br></br>`false`: no
     */
    fun isFileExists(file: File?): Boolean {
        if (file == null) return false
        if (file.exists()) {
            return true
        }
        return isFileExists(file.getAbsolutePath())
    }

    /**
     * Return whether the file exists.
     *
     * @param filePath The path of file.
     * @return `true`: yes<br></br>`false`: no
     */
    fun isFileExists(filePath: String): Boolean {
        val file: File? = getFileByPath(filePath)
        if (file == null) return false
        if (file.exists()) {
            return true
        }
        return isFileExistsApi29(filePath)
    }

    private fun isFileExistsApi29(filePath: String?): Boolean {
        if (Build.VERSION.SDK_INT >= 29) {
            try {
                val uri = Uri.parse(filePath)
                val cr: ContentResolver = UtilApp.getApp().getContentResolver()
                val afd = cr.openAssetFileDescriptor(uri!!, "r")
                if (afd == null) return false
                try {
                    afd.close()
                } catch (ignore: IOException) {
                }
            } catch (e: FileNotFoundException) {
                return false
            }
            return true
        }
        return false
    }

}
