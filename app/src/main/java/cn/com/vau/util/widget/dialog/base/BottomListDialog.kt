package cn.com.vau.util.widget.dialog.base

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.os.Build
import android.text.TextUtils
import android.view.LayoutInflater
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.databinding.DialogBottomListRootBinding
import cn.com.vau.util.dp2px
import cn.com.vau.util.screenHeight
import com.lxj.xpopup.core.BottomPopupView

@SuppressLint("ViewConstructor")
class BottomListDialog private constructor(
    context: Context,
    private var title: String? = null,
    private var content: CharSequence? = null,
    private var adapter: RecyclerView.Adapter<*>? = null,
    private var contentColor: Int = 0,
    private val onCreateListener: ((DialogBottomListRootBinding) -> Unit)? = null,
    private val onDismissListener: (() -> Unit)? = null,
    private val viewInterval: Int = -1,
    private val width: Int = 0,
    private val height: Int = 0,
    private val maxWidth: Int = 0,
    private val maxHeight: Int = 0
) : BottomPopupView(context), IDialog<DialogBottomListRootBinding> {

    protected val inflater = LayoutInflater.from(context)

    protected var mContentBinding =
        DialogBottomListRootBinding.inflate(inflater, bottomPopupContainer, false)

    private var tvTitle: TextView? = null

    override fun addInnerContent() {
        bottomPopupContainer.addView(mContentBinding.root)
    }

    override fun getPopupWidth(): Int {
        return if (width != 0) width else super.getPopupWidth()
    }

    override fun getPopupHeight(): Int {
        return if (height != 0) height else super.getPopupHeight()
    }

    // 如果需要覆写 getMaxWidth 和 getMaxHeight，可以提供自定义的最大宽高
    override fun getMaxWidth(): Int {
        return if (maxWidth != 0) maxWidth else super.getMaxWidth()
    }

    override fun getMaxHeight():Int {
        return if (maxHeight != 0) maxHeight else super.getMaxWidth()
    }

    override fun onCreate() {
        super.onCreate()
        initView()
        fixNavigationBarPadding()
        onCreateListener?.invoke(mContentBinding)
        processBackPress()
    }

    private fun processBackPress() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU && dialog != null) {
            dialog.onBackInvokedDispatcher.registerOnBackInvokedCallback(0) {
                if (onBackPressed()) {
                    return@registerOnBackInvokedCallback
                }
                if (popupInfo.isDismissOnBackPressed &&
                    (popupInfo.xPopupCallback == null || !popupInfo.xPopupCallback.onBackPressed(this))) {
                    dismissOrHideSoftInput()
                }
            }
        }
    }

    private fun initView() {
        setTitleInner(title)
        setContentInner(content)
        setContentColorInner(contentColor)
        setRecyclerView()
        setViewInterval()
    }

    private fun setViewInterval() {
        if (viewInterval < 0) {
            return
        }
        val params = mContentBinding.recyclerView.layoutParams
        if (params is MarginLayoutParams) {
            params.topMargin = viewInterval.dp2px()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setRecyclerView() {
        mContentBinding.recyclerView.layoutManager = WrapContentLinearLayoutManager(context)
        mContentBinding.recyclerView.adapter = adapter
        adapter?.notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setAdapter(adapter: RecyclerView.Adapter<*>?) {
        if (adapter == null) {
            return
        }
        this.adapter = adapter
        mContentBinding.recyclerView.adapter = adapter
        adapter.notifyDataSetChanged()
    }

    private fun setTitleInner(title: String?) {
        val params = mContentBinding.recyclerView.layoutParams as? MarginLayoutParams
        if (!TextUtils.isEmpty(title)) {
            mContentBinding.tvTitle.isVisible = true
            tvTitle = mContentBinding.root.findViewById<TextView>(R.id.tvTitle)
            tvTitle?.text = title
            params?.topMargin = 4.dp2px()
        } else {
            mContentBinding.tvTitle.isVisible = false
            params?.topMargin = 18.dp2px()
        }
        mContentBinding.recyclerView.layoutParams = params
    }

    private fun setContentInner(content: CharSequence?) {
        if (!TextUtils.isEmpty(content)) {
            mContentBinding.tvContent.isVisible = true
            mContentBinding.tvContent.text = content
        } else {
            mContentBinding.tvContent.isVisible = false
        }
    }

    private fun setContentColorInner(contentColor: Int) {
        if (contentColor <= 0) {
            return
        }
        mContentBinding.tvContent.setTextColor(contentColor)
    }

    override fun onDismiss() {
        super.onDismiss()
        onDismissListener?.invoke()
    }

    override fun getContentViewBinding(): DialogBottomListRootBinding {
        return mContentBinding
    }

    fun setTitle(title: String?) {
        this.title = title
        setTitleInner(this.title)
    }

    fun setContent(content: CharSequence?) {
        this.content = content
        setContentInner(this.content)
    }

    fun setContentColor(@ColorInt color: Int) {
        this.contentColor = color
        setContentColorInner(this.contentColor)
    }

    override fun showDialog() {
        if (popupInfo == null) {
            return
        }
        super.show()
    }

    override fun dismissDialog() {
        super.dismiss()
    }

    override fun isShowDialog(): Boolean {
        return super.isShow()
    }

    @Suppress("unused")
    class Builder(activity: Activity) :
        IBuilder<DialogBottomListRootBinding, Builder>(activity) {
        private var adapter: RecyclerView.Adapter<*>? = null
        private var viewInterval: Int = -1
        private var title: String? = null
        private var content: CharSequence? = null
        private var contentColor: Int = 0

        init {
            setMaxHeight(screenHeight)
        }

        //设置Adapter
        fun setAdapter(adapter: RecyclerView.Adapter<*>?) = apply {
            this.adapter = adapter
            return this
        }

        //内容和标题之间的间隔
        fun setViewInterval(viewInterval: Int) = apply {
            if (viewInterval < 0) {
                return this
            }
            this.viewInterval = viewInterval
            return this
        }

        //默认居中弹窗，需要指定类型
        fun setTitle(title: String?): Builder {
            this.title = title
            return this
        }

        fun setContent(content: CharSequence?): Builder {
            this.content = content
            return this
        }

        fun setContentColor(@ColorInt color: Int): Builder {
            this.contentColor = color
            return this
        }

        override fun build(): BottomListDialog {
            return super.build() as BottomListDialog
        }

        override fun createDialog(context: Context): IDialog<DialogBottomListRootBinding> {
            return BottomListDialog(
                context,
                title,
                content,
                adapter,
                contentColor,
                config.onCreateListener, config.onDismissListener,
                viewInterval,
                config.width, config.height,
                config.maxWidth, config.maxHeight,
            )
        }
    }
}
