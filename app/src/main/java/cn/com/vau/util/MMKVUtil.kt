package cn.com.vau.util

import com.google.gson.reflect.TypeToken
import com.tencent.mmkv.MMKV

@Deprecated("已废弃，禁止使用，请使用SpManager")
object MMKVUtil {

    private val mmkv by lazy { MMKV.defaultMMKV() }

    fun <T> saveObject(key: String?, classZ: T) {
        mmkv.encode(key, GsonUtil.buildGson().toJson(classZ))
    }

    fun <T> getObject(key: String?, clazz: Class<T>): T? {
        val jsonStr = mmkv.decodeString(key)
        return runCatching { GsonUtil.buildGson().fromJson(jsonStr, clazz) }.getOrNull()
    }

    fun <T> getObject(key: String?, classZ: TypeToken<T>): T? {
        val jsonStr = mmkv.decodeString(key)
        return runCatching { GsonUtil.buildGson().fromJson(jsonStr, classZ.type) as? T? }.getOrNull()
    }

    @JvmStatic
    fun saveString(key: String, value: String?) {
        mmkv.encode(key, value)
    }

    @JvmStatic
    @JvmOverloads
    fun getString(key: String, defaultValue: String = ""): String = mmkv.getString(key, defaultValue) ?: ""

    fun saveInt(key: String, value: Int) {
        mmkv.encode(key, value)
    }

    @JvmStatic
    fun getInt(key: String, defaultValue: Int = -1): Int = mmkv.getInt(key, defaultValue)

    fun saveLong(key: String, value: Long) {
        mmkv.encode(key, value)
    }

    fun getLong(key: String, defaultValue: Long = -1): Long = mmkv.getLong(key, defaultValue)

    fun saveFloat(key: String, value: Float) {
        mmkv.encode(key, value)
    }

    fun getFloat(key: String, defaultValue: Float = -1f): Float = mmkv.getFloat(key, defaultValue)

    @JvmStatic
    fun saveBoolean(key: String, value: Boolean) {
        mmkv.encode(key, value)
    }

    @JvmStatic
    fun getBoolean(key: String, defaultValue: Boolean = false): Boolean = mmkv.getBoolean(key, defaultValue)

    fun removeKey(key: String) {
        mmkv.remove(key)
    }

    fun clearAll() {
        mmkv.clearAll()
    }

}