package cn.com.vau.util

import java.util.LinkedHashMap
import kotlin.collections.set
import kotlin.jvm.javaClass


/**
 * 输入描述
 * Created by THINKPAD on 2019/9/19.
 */
object MapUtil {

    /**
     * 将Object对象里面的属性和值转化成Map对象
     *
     * @param obj
     * @return
     */
    fun objectToMap(obj: Any?): Map<String, Any> {
        val map = LinkedHashMap<String, Any>()
        if (obj == null) return map
        try {
            val clazz = obj.javaClass
            println(clazz)
            for (field in clazz.declaredFields) {
                field.isAccessible = true
                val fieldName = field.name
                var value = field.get(obj)
                if (value == null) {
                    value = ""
                }
                map[fieldName] = value
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return map
    }

}