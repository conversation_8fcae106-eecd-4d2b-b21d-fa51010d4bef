package cn.com.vau.util.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.content.Context
import android.content.res.TypedArray
import android.graphics.drawable.GradientDrawable
import android.text.TextUtils
import android.transition.ChangeBounds
import android.transition.ChangeTransform
import android.transition.Fade
import android.transition.TransitionManager
import android.transition.TransitionSet
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import android.view.animation.Animation
import android.view.animation.LinearInterpolator
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.FontRes
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import cn.com.vau.R
import cn.com.vau.util.dp2px
import androidx.core.content.withStyledAttributes
import androidx.core.view.isVisible
import androidx.core.view.setPadding

@Suppress("unused")
class LoadingButtonView : ConstraintLayout {

    private var buttonText: CharSequence? = null
    private var buttonTextSize: Int = 0

    @ColorInt
    private var loadingColor: Int = 0

    @ColorInt
    private var buttonTextColor: Int = 0
    private var buttonFontFamilyRes: Int = 0
    private var buttonTextView: TextView? = null

    @DrawableRes
    private var startIconRes = 0
    private var startIconWidth = 0
    private var startIconHeight = 0
    private var startIconPadding = 0
    private var startIconView: ImageView? = null

    @DrawableRes
    private var endIconRes = 0
    private var endIconWidth = 0
    private var endIconHeight = 0
    private var endIconPadding = 0
    private var endIconView: ImageView? = null

    private var buttonAlpha = 0f

    @ColorInt
    private var buttonBackgroundColor: Int = 0
    private var buttonCorner: Int = 0
    private val buttonBackgroundDrawable by lazy {
        GradientDrawable().apply {
            shape = GradientDrawable.RECTANGLE
        }
    }

    private var loadingWidth = 0
    private var loadingHeight = 0
    private var loadingPadding = 0
    private val loadingDrawable by lazy {
        ContextCompat.getDrawable(context, R.drawable.icon2_button_loading)
    }

    private var currState = STATE_NORMAL

    private var onClickListener: (() -> Unit)? = null

    constructor(context: Context) : super(context) {
        init(context)
        initView()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context)
        initAttrs(attrs)
        initView()
    }

    private fun initAttrs(attrs: AttributeSet?) {
        if (attrs == null) {
            return
        }
        context.withStyledAttributes(attrs, R.styleable.LoadingButtonView) {
            //解析左Icon
            parseStartIcon()
            //解析右Icon
            parseEndIcon()
            // 解析文字属性
            parseButtonText()
            //解析背景
            parseBackground()
            //解析Padding
            parsePadding()
            //解析Loading
            parseLoading()
        }
    }

    private fun TypedArray.parseLoading() {
        loadingColor = getColor(R.styleable.LoadingButtonView_lbv_loading_color, buttonTextColor)
        loadingDrawable?.setTint(loadingColor)

        val defaultWidth = if (endIconRes != 0) { endIconWidth } else startIconWidth
        val defaultHeight = if (endIconRes != 0) { endIconHeight } else startIconHeight
        loadingWidth =
            getDimensionPixelSize(R.styleable.LoadingButtonView_lbv_loading_width, defaultWidth)
        loadingHeight =
            getDimensionPixelSize(R.styleable.LoadingButtonView_lbv_loading_height, defaultHeight)

        val defaultPadding = if (endIconRes != 0) { endIconPadding } else startIconPadding
        loadingPadding =
            getDimensionPixelSize(R.styleable.LoadingButtonView_lbv_loading_padding, defaultPadding)
    }

    private var paddingVertical: Int = 0
    private var paddingHorizontal: Int = 0

    private fun TypedArray.parsePadding() {
        paddingVertical =
            getDimensionPixelSize(R.styleable.LoadingButtonView_lbv_button_padding_vertical, 0)
        paddingHorizontal =
            getDimensionPixelSize(R.styleable.LoadingButtonView_lbv_button_padding_horizontal, 12.dp2px())
    }

    private fun TypedArray.parseBackground() {
        buttonBackgroundColor = getColor(
            R.styleable.LoadingButtonView_lbv_button_background_color,
            ContextCompat.getColor(context, R.color.cf44040)
        )
        buttonCorner =
            getDimensionPixelSize(R.styleable.LoadingButtonView_lbv_button_corner, 100.dp2px())
        buttonAlpha = getFloat(R.styleable.LoadingButtonView_lbv_button_alpha, 0.8f)
    }

    private fun TypedArray.parseEndIcon() {
        endIconRes = getResourceId(R.styleable.LoadingButtonView_lbv_icon_end, 0)
        endIconWidth =
            getDimensionPixelSize(R.styleable.LoadingButtonView_lbv_icon_end_width, 24.dp2px())
        endIconHeight =
            getDimensionPixelSize(R.styleable.LoadingButtonView_lbv_icon_end_height, 24.dp2px())
        endIconPadding =
            getDimensionPixelSize(R.styleable.LoadingButtonView_lbv_icon_end_padding, 4.dp2px())
    }

    private fun TypedArray.parseStartIcon() {
        startIconRes = getResourceId(R.styleable.LoadingButtonView_lbv_icon_start, 0)
        startIconWidth =
            getDimensionPixelSize(R.styleable.LoadingButtonView_lbv_icon_start_width, 24.dp2px())
        startIconHeight =
            getDimensionPixelSize(R.styleable.LoadingButtonView_lbv_icon_start_height, 24.dp2px())
        startIconPadding =
            getDimensionPixelSize(R.styleable.LoadingButtonView_lbv_icon_start_padding, 4.dp2px())
    }

    private fun TypedArray.parseButtonText() {
        buttonText = getText(R.styleable.LoadingButtonView_lbv_button_text)
        buttonTextColor = getColor(
            R.styleable.LoadingButtonView_lbv_button_text_color,
            ContextCompat.getColor(context, R.color.cffffff)
        )
        buttonTextSize = getDimensionPixelSize(
            R.styleable.LoadingButtonView_lbv_button_text_size,
            16.dp2px()
        )
        buttonFontFamilyRes = getResourceId(
            R.styleable.LoadingButtonView_lbv_button_font_family,
            R.font.gilroy_semi_bold
        )
    }

    private fun init(context: Context) {
        inflate(context, R.layout.layout_loading_botton, this)
    }

    private fun initView() {
        startIconView = findViewById(R.id.startIconView)
        endIconView = findViewById(R.id.endIconView)
        buttonTextView = findViewById(R.id.buttonTextView)
        setStartIconView()
        setEndIconView()
        setButtonTextView()
        setButtonBackground()
        setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical)
        setOnClickListener { _: View? -> handleClickAnimation() }
    }

    fun setOnClickListener(listener: () -> Unit) {
        onClickListener = listener
    }

    fun setStartIconRes(@DrawableRes resId: Int) {
        //防止重复设置
        if (resId != startIconRes) {
            startIconRes = resId
            setStartIconView()
        }
        //左右图片同时只会有一个
        if (resId != 0 && endIconRes != 0) {
            endIconRes = 0
            setEndIconView()
        }
    }

    /**
     * 设置左Icon大小,单位是dp
     */
    fun setStartIconSize(width: Int, height: Int) {
        if (width < 0 || height < 0) {
            return
        }
        //防止重复设置
        val w = width.dp2px()
        val h = height.dp2px()
        if (w == startIconWidth && h == startIconHeight) {
            return
        }
        startIconWidth = w
        startIconHeight = h
        setIconSize(startIconView, w, h)
    }

    fun setStartIconPadding(padding: Int) {
        if (padding < 0) {
            return
        }
        //防止重复设置
        val p = padding.dp2px()
        if (p == startIconPadding) {
            return
        }
        startIconPadding = p
        startIconView?.setPadding(startIconPadding)
    }

    /**
     * 设置左Icon大小,单位是dp
     */
    fun setEndIconSize(width: Int, height: Int) {
        if (width < 0 || height < 0) {
            return
        }
        //防止重复设置
        val w = width.dp2px()
        val h = height.dp2px()
        if (w == endIconWidth && h == endIconHeight) {
            return
        }
        endIconWidth = w
        endIconHeight = h
        setIconSize(endIconView, w, h)
    }

    fun setEndIconPadding(padding: Int) {
        if (padding < 0) {
            return
        }
        //防止重复设置
        val p = padding.dp2px()
        if (p == endIconPadding) {
            return
        }
        endIconPadding = p
        endIconView?.setPadding(endIconPadding)
    }

    /**
     * 设置右Icon
     */
    fun setEndIconRes(@DrawableRes resId: Int) {
        //防止重复设置
        if (resId != endIconRes) {
            endIconRes = resId
            setEndIconView()
        }
        //左右图片同时只会有一个
        if (resId != 0 && startIconRes != 0) {
            startIconRes = 0
            setStartIconView()
        }
    }

    private fun setEndIconView() {
        if (endIconRes != 0) {
            endIconView?.setImageResource(endIconRes)
            endIconView?.visibility = VISIBLE
            setIconSize(endIconView, endIconWidth, endIconHeight)
            endIconView?.setPadding(endIconPadding)
            return
        }
        endIconView?.visibility = GONE
    }

    private fun setStartIconView() {
        if (startIconRes != 0) {
            startIconView?.setImageResource(startIconRes)
            startIconView?.visibility = VISIBLE
            setIconSize(startIconView, startIconWidth, startIconHeight)
            startIconView?.setPadding(startIconPadding)
            return
        }
        startIconView?.visibility = GONE
    }

    private fun setIconSize(iconView: ImageView?, iconWidth: Int, iconHeight: Int) {
        if (iconView == null || iconWidth < 0 || iconHeight < 0) {
            return
        }
        val params = iconView.layoutParams
        params.width = iconWidth
        params.height = iconHeight
        iconView.layoutParams = params
    }

    /**
     * 设置 按钮文字
     */
    fun setButtonText(buttonText: CharSequence?) {
        //防止重复设置
        if (TextUtils.equals(buttonText, this.buttonText)) {
            return
        }
        this.buttonText = buttonText
        if (buttonText.isNullOrEmpty()) {
            buttonTextView?.visibility = GONE
            return
        }
        buttonTextView?.text = buttonText
        buttonTextView?.visibility = VISIBLE
    }

    /**
     * buttonTextSize 单位是 dp
     */
    fun setButtonTextSize(buttonTextSize: Int) {
        //统一保存px数据
        val px = buttonTextSize.dp2px()
        //防止重复设置
        if (this.buttonTextSize == px) {
            return
        }
        this.buttonTextSize = px
        buttonTextView?.setTextSize(TypedValue.COMPLEX_UNIT_PX, this.buttonTextSize.toFloat())
    }

    /**
     * 设置按钮文字颜色
     */
    fun setButtonTextColorRes(@ColorRes buttonTextColorRes: Int) {
        setButtonTextColor(ContextCompat.getColor(context, buttonTextColorRes))
    }

    /**
     * 设置按钮文字颜色
     */
    fun setButtonTextColor(@ColorInt buttonTextColor: Int) {
        //防止重复设置
        if (this.buttonTextColor == buttonTextColor) {
            return
        }
        this.buttonTextColor = buttonTextColor
        buttonTextView?.setTextColor(buttonTextColor)
    }

    /**
     * 设置按钮文字颜色
     */
    fun setLoadingColorRes(@ColorRes buttonTextColorRes: Int) {
        setLoadingColor(ContextCompat.getColor(context, buttonTextColorRes))
    }

    /**
     * 设置按钮文字颜色
     */
    fun setLoadingColor(@ColorInt loadingColor: Int) {
        //防止重复设置
        if (this.loadingColor == loadingColor) {
            return
        }
        this.loadingColor = loadingColor
        loadingDrawable?.setTint(loadingColor)
    }

    fun setLoadingSize(width: Int, height: Int) {
        if (width < 0 || height < 0) {
            return
        }
        //防止重复设置
        val w = width.dp2px()
        val h = height.dp2px()
        if (w == loadingWidth && h == loadingHeight) {
            return
        }
        loadingWidth = w
        loadingHeight = h
    }

    fun setLoadingPadding(padding: Int) {
        if (padding < 0) {
            return
        }
        //防止重复设置
        val p = padding.dp2px()
        if (p == loadingPadding) {
            return
        }
        loadingPadding = p
    }

    /**
     * 设置按钮字体
     */
    fun setButtonFontFamilyRes(@FontRes buttonFontFamilyRes: Int) {
        if (this.buttonFontFamilyRes == buttonFontFamilyRes) {
            return
        }
        this.buttonFontFamilyRes = buttonFontFamilyRes
        if (this.buttonFontFamilyRes == 0) {
            return
        }
        buttonTextView?.setTypeface(ResourcesCompat.getFont(context, this.buttonFontFamilyRes))
    }

    private fun setButtonTextView() {
        //如果文字为空，则不可见
        if (buttonText.isNullOrEmpty()) {
            buttonTextView?.visibility = GONE
            return
        }
        buttonTextView?.visibility = VISIBLE
        buttonTextView?.text = buttonText
        //文字大小
        buttonTextView?.setTextSize(TypedValue.COMPLEX_UNIT_PX, buttonTextSize.toFloat())
        //颜色
        buttonTextView?.setTextColor(buttonTextColor)
        //字体
        if (buttonFontFamilyRes == 0) {
            return
        }
        buttonTextView?.setTypeface(ResourcesCompat.getFont(context, buttonFontFamilyRes))
    }

    /**
     * 设置PaddingVertical 单位是dp
     */
    fun setButtonPaddingVertical(paddingVertical: Int) {
        if (paddingVertical < 0) {
            return
        }
        val v = paddingVertical.dp2px()
        if (v == this.paddingVertical) {
            return
        }
        this.paddingVertical = v
        setPadding(
            this.paddingHorizontal,
            this.paddingVertical,
            this.paddingHorizontal,
            this.paddingVertical
        )
    }

    /**
     * 设置PaddingHorizontal 单位是dp
     */
    fun setButtonPaddingHorizontal(paddingHorizontal: Int) {
        if (paddingHorizontal < 0) {
            return
        }
        val h = paddingHorizontal.dp2px()
        if (h == this.paddingHorizontal) {
            return
        }
        this.paddingHorizontal = h
        setPadding(
            this.paddingHorizontal,
            this.paddingVertical,
            this.paddingHorizontal,
            this.paddingVertical
        )
    }

    /**
     * 设置按钮Loading时的透明度
     */
    fun setButtonAlpha(alpha: Float) {
        if (alpha < 0f) {
            return
        }
        this.buttonAlpha = alpha
    }

    /**
     * 按钮背景颜色
     */
    fun setButtonBackgroundColorRes(@ColorRes colorRes: Int) {
        setButtonBackgroundColor(ContextCompat.getColor(context, colorRes))
    }

    /**
     * 按钮背景颜色
     */
    override fun setBackgroundColor(@ColorInt color: Int) {
        setButtonBackgroundColor(color)
    }

    /**
     * 按钮背景颜色
     */
    override fun setBackgroundResource(@ColorRes colorRes: Int) {
        setButtonBackgroundColorRes(colorRes)
    }

    /**
     * 按钮背景颜色
     */
    fun setButtonBackgroundColor(@ColorInt backgroundColor: Int) {
        if (this.buttonBackgroundColor == backgroundColor) {
            return
        }
        this.buttonBackgroundColor = backgroundColor
        buttonBackgroundDrawable.setColor(backgroundColor)
        background = buttonBackgroundDrawable
    }

    /**
     * 按钮圆角,单位是dp
     */
    fun setButtonCorner(buttonCorner: Int) {
        if (buttonCorner < 0) {
            return
        }
        val corner = buttonCorner.dp2px()
        if (corner != this.buttonCorner) {
            this.buttonCorner = corner
            buttonBackgroundDrawable.cornerRadius = this.buttonCorner.toFloat()
            background = buttonBackgroundDrawable
        }
    }

    private fun setButtonBackground() {
        buttonBackgroundDrawable.setColor(buttonBackgroundColor)
        buttonBackgroundDrawable.cornerRadius = buttonCorner.toFloat()
        background = buttonBackgroundDrawable
    }

    fun stopLoading() {
        if (!isLoading()) {
            return
        }
        stopRotationAnimation()
        resetView()
    }

    private fun resetView() {
        if (isHideButtonTextView(buttonTextView)) {
            setButtonTextView()
        } else {
            addFadeAnim(Fade.OUT)
        }
        setStartIconView()
        setEndIconView()
        isEnabled = true
        currState = STATE_NORMAL
        handleAlphaWhenStop()
    }

    private fun handleClickAnimation() {
        if (isLoading()) {
            return
        }
        currState = STATE_LOADING
        isEnabled = false
        val isHide = isHideButtonTextView(buttonTextView)
        //如果展示不下loadingDrawable + 文字，则只展示loading
        if (isHide) {
            buttonTextView?.isVisible = false
        }
        if (startIconRes != 0) {
            //如果有左icon,则直接展示loading动画,delay time为 0
            startRotationAnimation(startIconView, 0L)
        } else if (endIconRes != 0) {
            //如果有右icon,则直接展示loading动画,delay time为 0
            startRotationAnimation(endIconView, 0L)
        } else {
            startAnimation(isHide)
        }
        //点击时，整个View透明度发生变化，100ms渐变
        handleAlphaWhenLoading()
        onClickListener?.invoke()
    }

    private fun isHideButtonTextView(textView: TextView?): Boolean {
        if (textView == null || textView.text.isNullOrEmpty()) {
            return true
        }
        if (buttonText.isNullOrEmpty()) {
            return true
        }
        val textWidth = textView.paint.measureText(buttonText.toString())
        //Loading状态需要的Width = 文字长度 + icon长度 + 边框 + padding
        val needWidth = if (endIconRes != 0) {
            textWidth + endIconWidth + 4.dp2px() + paddingHorizontal * 2
        } else {
            textWidth + startIconWidth + 4.dp2px() + paddingHorizontal * 2
        }
        if (needWidth > width) {
            return true
        }
        return false
    }

    private var alphaAnim: ObjectAnimator? = null

    private fun handleAlphaWhenLoading() {
        alphaAnim?.cancel()
        alphaAnim = ObjectAnimator.ofFloat(this, ANIM_ALPHA, 1f, buttonAlpha).apply {
            setDuration(ANIM_ALPHA_DURATION)
            interpolator = LinearInterpolator()
            start()
        }
    }

    private fun handleAlphaWhenStop() {
        alphaAnim?.cancel()
        alphaAnim = ObjectAnimator.ofFloat(this, ANIM_ALPHA, buttonAlpha, 1f).apply {
            setDuration(ANIM_ALPHA_DURATION)
            interpolator = LinearInterpolator()
            start()
        }
    }

    private fun startAnimation(isHide: Boolean) {
        addFadeAnim(Fade.IN)
        startIconView?.visibility = VISIBLE
        //恢复旋转状态
        lastTarget?.rotation = 0f
        if (isHide) {
            //如果文字不展示，则直接开始loading动画
            startRotationAnimation(startIconView, 0L)
        } else {
            //如果文字展示，则直接延迟300ms开始动画
            startRotationAnimation(startIconView, ANIM_TRANSITION_DURATION)
        }
    }

    private fun addFadeAnim(fadingMode: Int) {
        val transitionSet = TransitionSet().apply {
            //位移动画
            addTransition(ChangeBounds())
            // 添加淡入效果
            addTransition(Fade(fadingMode))
            // 添加形变动画
            addTransition(ChangeTransform())
            duration = ANIM_TRANSITION_DURATION
        }
        TransitionManager.beginDelayedTransition(this, transitionSet)
    }

    private var rotateAnim: ObjectAnimator? = null
    private var lastTarget: ImageView? = null

    private fun startRotationAnimation(target: ImageView?, delay: Long) {
        if (rotateAnim?.isRunning == true) {
            return
        }
        loadingDrawable?.setTint(loadingColor)
        target?.setImageDrawable(loadingDrawable)
        setIconSize(target, loadingWidth, loadingHeight)
        target?.setPadding(loadingPadding)
        if (rotateAnim != null && lastTarget == target) {
            target?.rotation = 0f
            rotateAnim?.start()
            return
        }
        lastTarget = target
        rotateAnim = ObjectAnimator.ofFloat(target, ANIM_ROTATION, 0f, ANIM_ANGLE).apply {
            setDuration(ANIM_ROTATION_DURATION)
            interpolator = LinearInterpolator()
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    if (startIconRes != 0 || endIconRes != 0) {
                        lastTarget?.rotation = 0f
                    }
                }
            })
            repeatCount = Animation.INFINITE
            repeatMode = ObjectAnimator.RESTART
            startDelay = delay
            start()
        }
    }

    private fun stopRotationAnimation() {
        rotateAnim?.cancel()
    }

    fun isLoading(): Boolean {
        return currState == STATE_LOADING
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        //避免内存泄漏
        rotateAnim?.cancel()
        rotateAnim = null
        alphaAnim?.cancel()
        alphaAnim = null
    }

    companion object {
        private const val ANIM_ALPHA = "alpha"
        private const val ANIM_ALPHA_DURATION = 100L
        private const val ANIM_ROTATION = "rotation"
        private const val ANIM_ROTATION_DURATION: Long = 1200
        private const val ANIM_TRANSITION_DURATION: Long = 300
        private const val ANIM_ANGLE = 360f
        const val STATE_LOADING = 1
        const val STATE_NORMAL = 0
    }
}

