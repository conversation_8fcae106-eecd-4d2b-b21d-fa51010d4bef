package cn.com.vau.util

import android.annotation.SuppressLint
import android.util.Base64
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

/**
 * author：lvy
 * date：2025/01/04
 * desc：
 */
@SuppressLint("GetInstance")
object AESUtil {

    const val PWD_AES_KEY = "da4756119d5e7e0f" // 密码的key

    /**
     * 加密AES
     */
    @JvmStatic
    fun encryptAES(content: String?, key: String): String {
        if (content.isNullOrBlank()) return ""
        return runCatching {
            val secretKey = SecretKeySpec(key.toByteArray(), "AES")

            val cipher = Cipher.getInstance("AES/ECB/PKCS5Padding")
            cipher.init(Cipher.ENCRYPT_MODE, secretKey)

            val encryptedData = cipher.doFinal(content.toByteArray())
            String(Base64.encode(encryptedData, Base64.NO_WRAP), Charsets.UTF_8)
        }.getOrDefault("")
    }

    /**
     * 解密AES
     */
    @JvmStatic
    fun decryptAES(encryptedContent: String?, key: String): String {
        if (encryptedContent.isNullOrBlank()) return ""
        return runCatching {
            val secretKey = SecretKeySpec(key.toByteArray(), "AES")

            val cipher = Cipher.getInstance("AES/ECB/PKCS5Padding")
            cipher.init(Cipher.DECRYPT_MODE, secretKey)

            val decodedData = Base64.decode(encryptedContent, Base64.NO_WRAP)
            val decryptedData = cipher.doFinal(decodedData)
            String(decryptedData, Charsets.UTF_8)
        }.getOrDefault("")
    }
}