package cn.com.vau.util

import android.content.Context
import cn.com.vau.R
import com.scwang.smart.refresh.footer.ClassicsFooter
import com.scwang.smart.refresh.header.ClassicsHeader

/**
 * Filename: RefreshUtils
 * Author: GG
 * Date: 2024/5/23
 * Description:
 */
object RefreshUtil {

    @JvmStatic
    fun setHeader(context: Context) {
        ClassicsHeader.REFRESH_HEADER_PULLING = context.getString(R.string.pull_down_to_refresh)
        ClassicsHeader.REFRESH_HEADER_REFRESHING = context.getString(R.string.loading)
        ClassicsHeader.REFRESH_HEADER_LOADING = context.getString(R.string.loading)
        ClassicsHeader.REFRESH_HEADER_RELEASE = context.getString(R.string.release_to_refresh)
        ClassicsHeader.REFRESH_HEADER_UPDATE = "'${context.getString(R.string.last_update)}' M-d HH:mm"
        ClassicsHeader.REFRESH_HEADER_FINISH = context.getString(R.string.successful)
        ClassicsHeader.REFRESH_HEADER_FAILED = context.getString(R.string.failed)
    }

    @JvmStatic
    fun setFooter(context: Context) {
        ClassicsFooter.REFRESH_FOOTER_PULLING = context.getString(R.string.pull_up_to_load_more)
        ClassicsFooter.REFRESH_FOOTER_RELEASE = context.getString(R.string.release_to_refresh)
        ClassicsFooter.REFRESH_FOOTER_LOADING = context.getString(R.string.loading)
        ClassicsFooter.REFRESH_FOOTER_REFRESHING = context.getString(R.string.loading)
        ClassicsFooter.REFRESH_FOOTER_FINISH = ""
        ClassicsFooter.REFRESH_FOOTER_FAILED = context.getString(R.string.failed)
        ClassicsFooter.REFRESH_FOOTER_NOTHING = context.getString(R.string.no_more_data)
    }

}