package cn.com.vau.home.activity

import MainNewComerEventContract
import android.annotation.SuppressLint
import android.graphics.Paint
import android.os.*
import android.view.*
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseFrameActivity
import cn.com.vau.common.constants.UrlConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.VAUStartUtil
import cn.com.vau.data.account.MT4AccountTypeObj
import cn.com.vau.data.discover.WbpStatusData
import cn.com.vau.databinding.ActivityMainNewComerEventBinding
import cn.com.vau.home.adapter.MainNewComerEventRecyclerAdapter
import cn.com.vau.home.model.MainNewComerEventModel
import cn.com.vau.home.presenter.MainNewComerEventPresenter
import cn.com.vau.page.html.*
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*

/**
 * 新手活动
 * TODO  此页面需转 H5
 */
class MainNewComerEventActivity :
    BaseFrameActivity<MainNewComerEventPresenter, MainNewComerEventModel>(),
    MainNewComerEventContract.View {

    private val binding by lazy { ActivityMainNewComerEventBinding.inflate(layoutInflater) }

    private var openAdapter: MainNewComerEventRecyclerAdapter? = null
    private var depositAdapter: MainNewComerEventRecyclerAdapter? = null
    private var tradeAdapter: MainNewComerEventRecyclerAdapter? = null
    private var referAdapter: MainNewComerEventRecyclerAdapter? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
    }

    @SuppressLint("WrongConstant", "ObsoleteSdkInt")
    override fun initView() {
        super.initView()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            window.addFlags(
                WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS
            );
        }

        binding.run {
            tvTc.paint.flags = Paint.UNDERLINE_TEXT_FLAG

            openAdapter =
                MainNewComerEventRecyclerAdapter(context, mPresenter.openEventList, 1)
            depositAdapter = MainNewComerEventRecyclerAdapter(
                context,
                mPresenter.depositEventList,
                2
            )
            tradeAdapter =
                MainNewComerEventRecyclerAdapter(context, mPresenter.tradeEventList, 3)
            referAdapter =
                MainNewComerEventRecyclerAdapter(context, mPresenter.referEventList, 4)

            openRecyclerView.adapter = openAdapter
            depositRecyclerView.adapter = depositAdapter
            tradeRecyclerView.adapter = tradeAdapter
            referRecyclerView.adapter = referAdapter
        }
    }

    override fun initData() {
        super.initData()
        mPresenter.newerGiftActivityGetWBPStatusApi()
    }

    override fun initListener() {
        super.initListener()
        binding.ifvBack.setOnClickListener(this)
        binding.tvTc.setOnClickListener(this)
        openAdapter?.setOnItemClickListener(object :
            MainNewComerEventRecyclerAdapter.OnItemClickListener {
            override fun onItemClick(position: Int, type: Int) {
                goNextActivity(position, type)
            }
        })
        depositAdapter?.setOnItemClickListener(object :
            MainNewComerEventRecyclerAdapter.OnItemClickListener {
            override fun onItemClick(position: Int, type: Int) {
                goNextActivity(position, type)
            }
        })
        tradeAdapter?.setOnItemClickListener(object :
            MainNewComerEventRecyclerAdapter.OnItemClickListener {
            override fun onItemClick(position: Int, type: Int) {
                goNextActivity(position, type)
            }
        })
        referAdapter?.setOnItemClickListener(object :
            MainNewComerEventRecyclerAdapter.OnItemClickListener {
            override fun onItemClick(position: Int, type: Int) {
                goNextActivity(position, type)
            }
        })
    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {
            R.id.ifvBack -> finish()

            R.id.tvTc -> {
                openActivity(HtmlActivity::class.java, Bundle().apply {
                    putInt("tradeType", 3)
                    putString("title", getString(R.string.tcs_event_newcomer))
                    putString("url", mPresenter.wbpDataBean?.newerGiftActivity?.tcLink ?: "")
                })
            }
        }
    }

    @SuppressLint("CheckResult", "NotifyDataSetChanged")
    override fun showNewComerEventView() {

        binding.ctlTimeLimitedPromo.isVisible = "1" == mPresenter.wbpDataBean?.timeLimitActivity?.isShow

        val eventData = mPresenter.wbpDataBean?.newerGiftActivity

        binding.ctlOpenAccount.isVisible = "1" == eventData?.openAccountActivities?.isShow

        binding.ctlDeposit.isVisible = "1" == eventData?.depositActivities?.isShow
        binding.ctlTrade.isVisible = "1" == eventData?.tradeActivities?.isShow
        binding.ctlRefer.isVisible = "1" == eventData?.referActivities?.isShow
        binding.tvOpenAccountInfo.isVisible = ("1" == eventData?.openAccountActivities?.isShow && "0" == eventData.openAccountActivities.isJoinTogether)
        binding.tvDepositInfo.isVisible = ("1" == eventData?.depositActivities?.isShow && "0" == eventData.depositActivities.isJoinTogether)
        binding.tvTradeInfo.isVisible = ("1" == eventData?.tradeActivities?.isShow && "0" == eventData.tradeActivities.isJoinTogether)
        binding.tvReferInfo.isVisible = ("1" == eventData?.referActivities?.isShow && "0" == eventData.referActivities.isJoinTogether)

        ImageLoaderUtil.loadImage(context, eventData?.backgroundImage ?: "", binding.ivEvent)

        openAdapter?.notifyDataSetChanged()
        depositAdapter?.notifyDataSetChanged()
        tradeAdapter?.notifyDataSetChanged()
        referAdapter?.notifyDataSetChanged()

    }

    private fun goNextActivity(position: Int, eventPosition: Int) {

        val currentEvent = when (eventPosition) {
            1 -> mPresenter.openEventList.elementAtOrNull(position)
            2 -> mPresenter.depositEventList.elementAtOrNull(position)
            3 -> mPresenter.tradeEventList.elementAtOrNull(position)
            else -> mPresenter.referEventList.elementAtOrNull(position)
        }

        // ------ 外部跳转 ------
        if ("2" == currentEvent?.linkType) {
            openActivity(HtmlActivity::class.java, Bundle().apply {
                putInt("tradeType", 3)
                putString("title", currentEvent.textContent ?: "")
                putString("url", currentEvent.activityUrl ?: "")
            })
            LogEventUtil.setLogEvent(
                BuryPointConstant.V342.PROMO_REFERRAL_BONUS_PAGE_VIEW, Bundle().apply {
                    putString(BuryPointConstant.PositionType.KEY_POSITION, BuryPointConstant.PositionType.WELCOME_BUNDLE)
                    putString(BuryPointConstant.PositionType.KEY_ELIGIBILITY, "-")
                }
            )
            finish()
            return
        }

        if (!UserDataUtil.isLogin()) {
            openActivity(LoginActivity::class.java)
            finish()
            return
        }

        // ------ 内部跳转 ------
        val eventData = mPresenter.wbpDataBean?.newerGiftActivity
        // 是否完成开户流程，为完成开户流程走开户引导的接口  0 未完成开户 1 已完成开户
        if ("0" == eventData?.openAccountActivities?.isDone) {
            // 已跟Marco确认直接跳H5开户，被忽略覆盖TC页面的逻辑以后优化
            if (SpManager.isV1V2()) {
                VAUStartUtil.openAccountToKyc(context)
            } else {
                mPresenter.queryMT4AccountState(currentEvent)
            }
            return
        }

        val code = currentEvent?.activityUrl

        if ("3" == code || ("4" == code && "0" == eventData?.depositActivities?.isDone)) {
            NewHtmlActivity.openActivity(this, url = UrlConstants.HTML_FUND_DEPOSIT)
            finish()
            return
        }

        if ("4" == code && "0" == eventData?.tradeActivities?.isDone) {
            finish()
            return
        }

        openActivity(HtmlActivity::class.java, Bundle().apply {
            putInt("tradeType", 3)
            putString("title", getString(R.string.tcs_event_newcomer))
            putString("url", mPresenter.wbpDataBean?.newerGiftActivity?.tcLink ?: "")
        })

        LogEventUtil.setLogEvent(
            BuryPointConstant.V342.PROMO_REFERRAL_BONUS_PAGE_VIEW, Bundle().apply {
                putString(BuryPointConstant.PositionType.KEY_POSITION, BuryPointConstant.PositionType.WELCOME_BUNDLE)
                putString(BuryPointConstant.PositionType.KEY_ELIGIBILITY, "-")
            }
        )

        finish()

    }

    override fun skipOpenAccountActivity(objData: MT4AccountTypeObj?, data: WbpStatusData.Activity?) {
        /*
         开户审核中,只打开app
         1：账户未提交 (跳步数)
         2：账户审核，
         3：账户被挂起 {跳第一步}
         4：账户被拒绝(被拒绝，不存在这种情况，被拒绝时被弹出登陆，不能登陆)
         5：账户已通过
         6：账户待审核且身份证明未提交(跳身份证明)  数据不对，也是跳步数
         */
        //跟jk沟通后，只有账户已通过的情况下才跳转到活动规则页面其他情况跳转到开户引导
        // data?.activityUrl ==1时 不需要完全开户就能跳转到活动页面 objData?.applyTpe == 7 是没有上传身份证明 可以通过
        if (objData?.applyTpe == 7 && "1" == data?.activityUrl) {
            if (objData.status == 5) {
                // 跳转活动规则
                openActivity(HtmlActivity::class.java, Bundle().apply {
                    putInt("tradeType", 3)
                    putString("title", getString(R.string.tcs_event_newcomer))
                    putString("url", mPresenter.wbpDataBean?.newerGiftActivity?.tcLink ?: "")
                })
            } else {
                ToastUtil.showToast(getString(R.string.you_have_an_existing_processed))
            }
        } else {
            objData?.let {
                VAUStartUtil.openAccountGuide(this, it)
            }
            finish()
        }

    }

}