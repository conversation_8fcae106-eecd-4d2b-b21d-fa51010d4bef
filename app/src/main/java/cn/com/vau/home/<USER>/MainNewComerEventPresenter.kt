package cn.com.vau.home.presenter

import MainNewComerEventContract
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.data.account.MT4AccountTypeBean
import cn.com.vau.data.discover.WbpStatusData
import cn.com.vau.util.ToastUtil
import io.reactivex.disposables.Disposable

class MainNewComerEventPresenter : MainNewComerEventContract.Presenter() {

    var wbpDataBean: WbpStatusData.Obj? = null

    var openEventList: ArrayList<WbpStatusData.Activity> = arrayListOf()
    var depositEventList: ArrayList<WbpStatusData.Activity> = arrayListOf()
    var tradeEventList: ArrayList<WbpStatusData.Activity> = arrayListOf()
    var referEventList: ArrayList<WbpStatusData.Activity> = arrayListOf()

    override fun newerGiftActivityGetWBPStatusApi() {
        mView?.showNetDialog()
        val paramMap = hashMapOf<String, Any>()
        if (UserDataUtil.isLogin()) {
            paramMap["token"] = UserDataUtil.loginToken()
        }
        mModel?.newerGiftActivityGetWBPStatusApi(paramMap, object : BaseObserver<WbpStatusData>() {
            override fun onNext(dataBean: WbpStatusData?) {
                mView?.hideNetDialog()
                // V00000
                if (dataBean?.resultCode != "V00000") return
                wbpDataBean = dataBean.data?.obj
                openEventList.clear()
                depositEventList.clear()
                tradeEventList.clear()
                referEventList.clear()
                openEventList.addAll(wbpDataBean?.newerGiftActivity?.openAccountActivities?.activities ?: arrayListOf())
                depositEventList.addAll(wbpDataBean?.newerGiftActivity?.depositActivities?.activities ?: arrayListOf())
                tradeEventList.addAll(wbpDataBean?.newerGiftActivity?.tradeActivities?.activities ?: arrayListOf())
                referEventList.addAll(wbpDataBean?.newerGiftActivity?.referActivities?.activities ?: arrayListOf())
                mView?.showNewComerEventView()
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }

        })
    }

    override fun queryMT4AccountState(data: WbpStatusData.Activity?) {

        mView?.showNetDialog()
        val map = hashMapOf<String, String>()
        map["token"] = UserDataUtil.loginToken()
        mModel?.queryMT4AccountState(map, object : BaseObserver<MT4AccountTypeBean>() {
            override fun onNext(dataBean: MT4AccountTypeBean?) {
                mView?.hideNetDialog()
                if ("V00000" != dataBean?.resultCode) {
                    ToastUtil.showToast(dataBean?.msgInfo ?: "")
                    return
                }
                val obj = dataBean.data?.obj
                mView?.skipOpenAccountActivity(obj,data)
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager?.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.showNetDialog()
            }

        })
    }

}