package cn.com.vau.ui.mine.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.liveData
import cn.com.vau.common.greendao.dbUtils.DbManager
import cn.com.vau.common.greendao.dbUtils.DealLogInfo
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.view.timeSelection.PickerDateUtil
import cn.com.vau.util.AppUtil
import cn.com.vau.util.UriUtil
import cn.com.vau.util.fill0
import cn.com.vau.util.widget.Transformations
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import java.util.Calendar

class AccountActivityViewModel : BaseViewModel() {

    val calendar: Calendar = Calendar.getInstance()

    val mYear by lazy { calendar.get(Calendar.YEAR) }
    val mMonth by lazy { calendar.get(Calendar.MONTH) }
    val mDay by lazy { calendar.get(Calendar.DAY_OF_MONTH) }

    var likeDateStr: String = PickerDateUtil.longTimeToString(System.currentTimeMillis(), "dd/MM/yyyy")

    var logFilePath: String = ""

    private var requestBodyParamLiveData = MutableLiveData<MultipartBody>()

    val dataList: ArrayList<DealLogInfo> by lazy {
        DbManager.getInstance().getDealLogList("${mDay.fill0()}/${(mMonth + 1).fill0()}/$mYear") as ArrayList<DealLogInfo>?
            ?: arrayListOf()
    }

    val addLogLiveData = Transformations.switchMap(requestBodyParamLiveData) {
        liveData {
            val result = try {
                val data = baseService.userLogsAddLog(it)
                Result.success(data)
            } catch (e: Exception) {
                e.printStackTrace()
                Result.failure(e)
            }
            emit(result)
        }
    }

    fun userLogsAddLog() {

        val logsList = DbManager.getInstance().getDealLogList(likeDateStr) as ArrayList<DealLogInfo>

        val logsSb = StringBuffer()
        logsSb.append("Android\n")
        for (log in logsList) {
            logsSb.append("${log.date}  ${log.log} ;\n")
        }

        val logTimeMillis = System.currentTimeMillis()
        logFilePath = "data/data/${AppUtil.getPackageName()}/aLog$logTimeMillis.txt"

        UriUtil.writeTxtToFile(logsSb.toString(), logFilePath)

        val file = File(logFilePath)
        val builder = MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart("token", UserDataUtil.loginToken())
            .addFormDataPart("logContent", "")
        val formBody = file.asRequestBody("multipart/form-data".toMediaTypeOrNull())

        builder.addFormDataPart("logFile", file.name, formBody)

        val parts = builder.build()

        requestBodyParamLiveData.value = parts

    }
}