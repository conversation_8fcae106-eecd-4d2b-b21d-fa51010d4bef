package cn.com.vau.ui.order.perform

import android.annotation.SuppressLint
import android.text.TextUtils
import android.view.View
import androidx.core.view.isGone
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import cn.com.vau.R
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.performance.AbsPerformance
import cn.com.vau.data.enums.EnumInitStep
import cn.com.vau.ui.order.util.LoadingHelper
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.widget.dialog.CenterActionDialog
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.conflate
import kotlinx.coroutines.flow.sample
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class BalanceResetPerformance(val fragment: Fragment, private val mResetBalanceView: View, private val visibleBlock: ((Boolean) -> Unit)? = null) : AbsPerformance() {

    private lateinit var viewModel: BalanceResetViewModel

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        EventBus.getDefault().register(this)
        viewModel = ViewModelProvider(fragment)[BalanceResetViewModel::class.java]
        initView()
        initLoadingChange()
        viewModel.handleMessage()
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        EventBus.getDefault().unregister(this)
    }

    @OptIn(FlowPreview::class)
    private fun initView() {
        /** 接口：账户余额检测重置状态 */
        fragment.lifecycleScope.launch {
            viewModel.showReset.flowWithLifecycle(fragment.lifecycle, Lifecycle.State.STARTED).collect {
                when (it) {
                    is ResetState.Visible -> {
                        showResetLayout(true)
                    }

                    is ResetState.Gone -> {
                        showResetLayout(false)
                    }
                }
            }
        }

        /** 接口：账户余额重置清零 */
        fragment.lifecycleScope.launch {
            viewModel.resetMsgOnSuccess.flowWithLifecycle(fragment.lifecycle, Lifecycle.State.STARTED).collectLatest {
                ToastUtil.showToast(it)
                mResetBalanceView.isGone = true
                InitHelper.initialize(EnumInitStep.ORDER)
//                viewModel.accountBalanceCheckReset()
            }
        }

        /**
         * 短时间内多次调用接口问题
         */
        fragment.lifecycleScope.launch {
            viewModel.messageFlow
                .conflate()
                .sample(500)
                .collect { message ->
                    viewModel.accountBalanceCheckReset()
                }
        }

    }

    /**
     * VM监听loading变化事件
     */
    private fun initLoadingChange() {
        viewModel.loadingChange.dialogLiveData.observe(fragment) {
            if (it) { //显示弹框
                LoadingHelper.showLoading(fragment.requireActivity())
            } else { //关闭弹窗
                LoadingHelper.hideLoading(fragment.requireActivity())
            }
        }

    }

    @SuppressLint("SetTextI18n")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.SWITCH_ACCOUNT -> {// 切换账户
                mResetBalanceView.isGone = true
                viewModel.handleMessage(tag)
            }

            NoticeConstants.WS.CHANGE_OF_FUNDS,// 资金变动通知
            NoticeConstants.Init.DATA_SUCCESS_ORDER,//持仓列表刷新完成
            NoticeConstants.REFRESH_ORDER_DATA_SHARE,//部分平仓（可合并）
            NoticeConstants.WS.CHANGE_OF_OPEN_ORDER,// 持仓变化
            NoticeConstants.WS.CHANGE_OF_PENDING_ORDER -> {// 挂单变化
                viewModel.handleMessage(tag)
            }
        }
    }

    /**
     * 显示重置布局
     */
    private fun showResetLayout(isShow: Boolean) {
        visibleBlock?.invoke(isShow)
        if (!isShow) {
            mResetBalanceView.isGone = true
            return
        }
        mResetBalanceView.isGone = false
        mResetBalanceView.clickNoRepeat {
            LogEventUtil.setLogEvent(BuryPointConstant.V341.ORDERS_RESET_BUTTON_CLICK)
            CenterActionDialog.Builder(fragment.requireActivity())
                .setTitle(fragment.getString(R.string.reset_account))
                .setContent(fragment.getString(R.string.dialog_balance_reset_msg))
                .setStartText(fragment.getString(R.string.cancel))//设置左侧按钮文本
                .setOnStartListener {
                    LogEventUtil.setLogEvent(BuryPointConstant.V341.ORDERS_RESET_CANCEL_BUTTON_CLICK)
                }
                .setEndText(fragment.getString(R.string.confirm))//设置右侧按钮文本
                .setOnEndListener {
                    viewModel.accountBalanceNegativeReset()
                    LogEventUtil.setLogEvent(BuryPointConstant.V341.ORDERS_RESET_CONFIRM_BUTTON_CLICK)
                }
                .build()
                .showDialog()
        }
        LogEventUtil.setLogEvent(BuryPointConstant.V341.ORDERS_RESET_BUTTON_PAGE_VIEW)
    }

}

/**
 * ViewModel
 */
class BalanceResetViewModel : BaseViewModel() {
    private val _showReset = MutableStateFlow<ResetState>(ResetState.Gone)
    val showReset: StateFlow<ResetState> = _showReset.asStateFlow()

    private val _resetMsgOnSuccess = MutableSharedFlow<String>()
    val resetMsgOnSuccess: SharedFlow<String> = _resetMsgOnSuccess.asSharedFlow()

    private val _messageFlow = MutableSharedFlow<String>()
    val messageFlow = _messageFlow.asSharedFlow()

    fun handleMessage(message: String = "handleMessage") {
        viewModelScope.launch {
            _messageFlow.emit(message)
        }
    }

    /**
     * 账户余额检测重置状态
     */
    fun accountBalanceCheckReset() {

        val token = UserDataUtil.loginToken()
        val accountCd = UserDataUtil.accountCd()
        if (TextUtils.isEmpty(token) || TextUtils.isEmpty(accountCd)) return

        val params = HashMap<String, Any>()
        params["token"] = token
        params["accountNo"] = accountCd
        requestNet(
            { baseService.accountBalanceCheckReset(params) },
            onSuccess = {
                val showReset = it?.data?.obj == true
                viewModelScope.launch {
                    _showReset.emit(if (showReset) ResetState.Visible else ResetState.Gone)
                }
            }
        )
    }

    /**
     * 账户余额重置清零
     */
    fun accountBalanceNegativeReset() {

        val params = HashMap<String, Any>()
        params["token"] = UserDataUtil.loginToken()
        params["accountNo"] = UserDataUtil.accountCd()
        requestNet(
            { baseService.accountBalanceNegativeReset(params) },
            onSuccess = {

                viewModelScope.launch {
                    it?.msgInfo?.let {
//                        ToastUtil.showToast(it)
                        _resetMsgOnSuccess.emit(it)
                    }
                }
            },
            onError = {
            },
            isShowDialog = true
        )

    }
}

sealed class ResetState {
    data object Gone : ResetState()
    data object Visible : ResetState()

}

