package cn.com.vau.ui.deal.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.*
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.data.trade.LossOrdersBean
import cn.com.vau.util.*
import com.google.android.material.checkbox.MaterialCheckBox

/**
 * Created by roy on 2018/10/25.
 */
class LossOrderRcyAdapter(
    var mContext: Context,
    var dataList: ArrayList<LossOrdersBean.Obj>,
    var currency: String? = UserDataUtil.currencyType()
) : RecyclerView.Adapter<LossOrderRcyAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val holder = ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_rcy_loss_order, parent, false))
        holder.itemView.setOnClickListener {
            mOnItemClickListener?.onItemClick(holder.adapterPosition)
        }
        return holder
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        bindTo(holder, mContext, dataList.elementAtOrNull(position))
    }

    @SuppressLint("UseCompatLoadingForDrawables", "SetTextI18n")
    fun bindTo(holder: ViewHolder, mContext: Context, dataBean: LossOrdersBean.Obj?) {

        val tvProdName = holder.view.findViewById<TextView>(R.id.tvProdName)
        val tvOrderType = holder.view.findViewById<TextView>(R.id.tvOrderType)
        val tvOrderNum = holder.view.findViewById<TextView>(R.id.tvOrderNum)
        val tvVolume = holder.view.findViewById<TextView>(R.id.tvVolume)
        val tvOpenPrice = holder.view.findViewById<TextView>(R.id.tvOpenPrice)
        val tvClosePrice = holder.view.findViewById<TextView>(R.id.tvClosePrice)
        val tvPnlTitle = holder.view.findViewById<TextView>(R.id.tvPnlTitle)
        val tvFloatingPnL = holder.view.findViewById<TextView>(R.id.tvFloatingPnL)
        val mcbOrder = holder.view.findViewById<MaterialCheckBox>(R.id.mcbOrder)

        dataBean?.run {

            tvOrderNum.text = orderNo.ifNull()
            if (dataBean.cmd == 0) {
                tvOrderType.text = "Buy"
                tvOrderType.setBackgroundResource(R.drawable.shape_c1f00c79c_r100)
                tvOrderType.setTextColor(ContextCompat.getColor(mContext, R.color.c00c79c))

            } else {
                tvOrderType.text = "Sell"
                tvOrderType.setBackgroundResource(R.drawable.shape_c1fe35728_r100)
                tvOrderType.setTextColor(ContextCompat.getColor(mContext, R.color.ce35728))
            }

            tvVolume.text = "${volume}${mContext.getString(R.string.lots)}"
            tvProdName.text = symbol ?: ""

            // 开仓平仓
            tvOpenPrice.text = openPrice ?: ""
            tvClosePrice.text = closePrice ?: ""

            // 盈亏
            tvPnlTitle.text = "${mContext.getString(R.string.pnl)} (${currency})"
            tvFloatingPnL.setTextColor(
                if (profit.mathCompTo("0") == 1) ContextCompat.getColor(mContext, R.color.c00c79c)
                else if (profit.mathCompTo("0") == -1) ContextCompat.getColor(mContext, R.color.ce35728)
                else AttrResourceUtil.getColor(mContext, R.attr.color_c1e1e1e_cebffffff)
            )
            tvFloatingPnL.text = "${profit ?: ""} $currency"

            mcbOrder.isChecked = checkState

        }

    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    class ViewHolder(val view: View) : RecyclerView.ViewHolder(view)

    private var mOnItemClickListener: OnItemClickListener? = null

    interface OnItemClickListener {
        fun onItemClick(position: Int)
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        mOnItemClickListener = onItemClickListener
    }

}