package cn.com.vau.ui.common.activity

import android.annotation.SuppressLint
import android.app.Activity
import android.content.res.ColorStateList
import android.os.Build
import android.os.Bundle
import android.view.*
import androidx.activity.OnBackPressedCallback
import androidx.core.content.ContextCompat
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import cn.com.vau.MainActivity
import cn.com.vau.R
import cn.com.vau.common.application.VauApplication.Companion.context
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.databinding.ActivityChooseYourThemeBinding
import cn.com.vau.util.AppUtil.isNightMode
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import org.greenrobot.eventbus.EventBus

/**
 * 主题切换
 */
class ChooseYourThemeActivity : BaseMvvmBindingActivity<ActivityChooseYourThemeBinding>() {

    private var isOperate = false

    private val tickGreen by lazy {
        ContextCompat.getDrawable(context, R.drawable.bitmap_img_source_tick11x8_c00c79c)?.apply {
            setBounds(0, 0, intrinsicWidth, intrinsicHeight)
        }
    }

    override fun initParam(savedInstanceState: Bundle?) {
        // 在Activity中处理返回手势
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                changeTheme()
            }
        })

        val styleState = SpManager.getStyleState(0)
        val isFollowSystem = SpManager.getStyleFollowSystem(true)

        if (isFollowSystem) {
            mBinding.ivFollowSystem.isVisible = true
            mBinding.tvLight.setCompoundDrawablesRelative(null, null, null, null)
            mBinding.tvDark.setCompoundDrawablesRelative(null, null, null, null)
            val nightMode = isNightMode()
            initThemeView(if (nightMode) 1 else 0)
        } else {
            mBinding.ivFollowSystem.isInvisible = true
            if (styleState == 0) {
                mBinding.tvLight.setCompoundDrawablesRelative(null, null, tickGreen, null)
                mBinding.tvDark.setCompoundDrawablesRelative(null, null, null, null)
            } else {
                mBinding.tvLight.setCompoundDrawablesRelative(null, null, null, null)
                mBinding.tvDark.setCompoundDrawablesRelative(null, null, tickGreen, null)
            }
            initThemeView(styleState)
        }
    }

    override fun initView() {
        mBinding.groupFollowSystem.isVisible = Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q
    }

    override fun initListener() {
        super.initListener()
        mBinding.mHeaderBar.setStartBackIconClickListener {
            changeTheme()
        }

        mBinding.viewFollowSystem.clickNoRepeat {
            isOperate = true
            initFollowSystemView()
        }

        mBinding.tvLight.clickNoRepeat {
            isOperate = true
            changeYourThemeView(0)
            SpManager.putStyleFollowSystem(false)
        }
        mBinding.tvDark.clickNoRepeat {
            isOperate = true
            changeYourThemeView(1)
            SpManager.putStyleFollowSystem(false)
        }
    }

    private fun changeTheme() {
        if (isOperate) {
            EventBus.getDefault().post(NoticeConstants.CHANGE_OF_LANGUAGE)
            openActivity(MainActivity::class.java)
            if (!UserDataUtil.isLogin()) {
                SensorsDataUtil.track(SensorsConstant.V3550.NLIPROFILESETTINGPAGE_THEME_SWITCH)
            }
        }
        finish()
    }

    private fun initFollowSystemView() {
        SpManager.putStyleFollowSystem(true)
        changeYourThemeView(-1)
        val themeTypeInt = if (isNightMode()) 1 else 0
        SpManager.putStyleState(themeTypeInt)
        initThemeView(themeTypeInt)
    }

    private fun changeYourThemeView(themeTypeInt: Int) {
        if (themeTypeInt == -1) {
            mBinding.ivFollowSystem.isVisible = true
            mBinding.tvLight.setCompoundDrawablesRelative(null, null, null, null)
            mBinding.tvDark.setCompoundDrawablesRelative(null, null, null, null)
            window.navigationBarColor = ContextCompat.getColor(this, if (isNightMode()) R.color.c1a1d20 else R.color.cffffff)
            return
        }
        SpManager.putStyleState(themeTypeInt)
        initThemeView(themeTypeInt)
        mBinding.ivFollowSystem.isInvisible = true
        if (themeTypeInt == 0) {
            mBinding.tvLight.setCompoundDrawablesRelative(null, null, tickGreen, null)
            mBinding.tvDark.setCompoundDrawablesRelative(null, null, null, null)
        } else {
            mBinding.tvLight.setCompoundDrawablesRelative(null, null, null, null)
            mBinding.tvDark.setCompoundDrawablesRelative(null, null, tickGreen, null)
        }
        window.navigationBarColor = ContextCompat.getColor(this, if (themeTypeInt == 0) R.color.cffffff else R.color.c1a1d20)
    }

    // 0 浅色
    private fun initThemeView(themeTypeInt: Int) {
        setDarkStatusIcon(themeTypeInt == 0)
        if (themeTypeInt == 0) {
            setWindowStatusBarColor(this, R.color.cffffff)
            mBinding.root.setBackgroundColor(ContextCompat.getColor(context, R.color.cffffff))
            mBinding.mHeaderBar.run {
                setStartBackIconImageTintList(ColorStateList.valueOf(ContextCompat.getColor(context, R.color.c1e1e1e)))
                setTitleTextColor(ContextCompat.getColor(context, R.color.c1e1e1e))
            }

            mBinding.tvFollowSystem.setTextColor(ContextCompat.getColor(context, R.color.c1e1e1e))
            mBinding.tvFollowSystemPrompt.setTextColor(ContextCompat.getColor(context, R.color.ca61e1e1e))
            mBinding.tvLight.setTextColor(ContextCompat.getColor(context, R.color.c1e1e1e))
            mBinding.tvDark.setTextColor(ContextCompat.getColor(context, R.color.c1e1e1e))
            mBinding.viewLine.setBackgroundResource(R.color.c1f1e1e1e)
        } else {
            setWindowStatusBarColor(this, R.color.c1a1d20)
            mBinding.root.setBackgroundColor(ContextCompat.getColor(context, R.color.c1a1d20))
            mBinding.mHeaderBar.run {
                setStartBackIconImageTintList(ColorStateList.valueOf(ContextCompat.getColor(context, R.color.cebffffff)))
                setTitleTextColor(ContextCompat.getColor(context, R.color.cebffffff))
            }

            mBinding.tvFollowSystem.setTextColor(ContextCompat.getColor(context, R.color.cebffffff))
            mBinding.tvFollowSystemPrompt.setTextColor(ContextCompat.getColor(context, R.color.c99ffffff))
            mBinding.tvLight.setTextColor(ContextCompat.getColor(context, R.color.cebffffff))
            mBinding.tvDark.setTextColor(ContextCompat.getColor(context, R.color.cebffffff))
            mBinding.viewLine.setBackgroundResource(R.color.c1fffffff)
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    @SuppressLint("ObsoleteSdkInt")
    fun setWindowStatusBarColor(activity: Activity, colorResId: Int) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                val window = activity.window
                window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                window.statusBarColor = activity.resources.getColor(colorResId)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun setDarkStatusIcon(bDark: Boolean) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val decorView = window.decorView
            var vis = decorView.systemUiVisibility
            vis = if (bDark) {
                vis or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
            } else {
                vis and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv()
            }
            decorView.systemUiVisibility = vis
        }
    }

}