package cn.com.vau.history.data.uiStatus

/**
 * Create data：2025/2/24 16:35
 * @author: Brin
 * Describe:
 */
sealed class HistoryStatus {
    data object Initial : HistoryStatus()
    data object Refreshing : HistoryStatus()
    data object RefreshingEnd : HistoryStatus()
    data object RefreshingFailed : HistoryStatus()
    data object LoadingMore : HistoryStatus()
    data object LoadingMoreEnd : HistoryStatus()
    data object LoadingMoreFailed : HistoryStatus()
    data object Empty : HistoryStatus()
    data object NoMore : HistoryStatus()
    data object CloseSymbolFilter : HistoryStatus()
    data object CloseTimeFilter : HistoryStatus()
}

data class PositionDialogStatus(
    val pnlDialogShow: Boolean = false,
    val netPnlDialogShow: Boolean = false,
    val chargesDialogShow: Boolean = false,
)