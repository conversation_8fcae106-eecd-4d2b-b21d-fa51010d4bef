package cn.com.vau.history.ui.widget

import android.content.Context
import android.util.ArrayMap
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import cn.com.vau.R
import cn.com.vau.databinding.LayoutTimeSelectionYmdBinding
import cn.com.vau.util.LogUtil
import cn.com.vau.util.TimeUtil
import java.util.Calendar
import java.util.Date
import javax.annotation.Nonnull


/**
 * Create data：2025/1/16 18:40
 * @author: Brin
 * Describe: 控件滑动的时间范围为 2000-01-01 : 2060-12-31
 */
class TimeSelectionYmd @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {


    private lateinit var binding: LayoutTimeSelectionYmdBinding
    private var selectedYearNum: Int = -1
    private var selectedMonthNum: Int = -1
    private var selectedDayNum: Int = -1

    private var yearData: MutableList<String> = mutableListOf()
    private var monthData: MutableList<String> = mutableListOf()
    private var dayDataOfMonth: MutableList<String> = mutableListOf()

    val calendar: Calendar = Calendar.getInstance()
    private val monthMap = ArrayMap<Int, String>(12)

    init {

        monthMap.put(1, context.getString(R.string.january))
        monthMap.put(2, context.getString(R.string.february))
        monthMap.put(3, context.getString(R.string.march))
        monthMap.put(4, context.getString(R.string.april))
        monthMap.put(5, context.getString(R.string.may))
        monthMap.put(6, context.getString(R.string.june))
        monthMap.put(7, context.getString(R.string.july))
        monthMap.put(8, context.getString(R.string.august))
        monthMap.put(9, context.getString(R.string.september))
        monthMap.put(10, context.getString(R.string.october))
        monthMap.put(11, context.getString(R.string.november))
        monthMap.put(12, context.getString(R.string.december))

        initView()
    }


    private fun initView() {
        binding = LayoutTimeSelectionYmdBinding.inflate(LayoutInflater.from(context), this, true)

        val currentDate = Date()
        // 获取当前年份
        val currentYearNum: Int = String.format("%tY", currentDate).toInt()
        // 获取当前月份
        val currentMonthNum: Int = String.format("%tm", currentDate).toInt()
        // 获取当前日期
        val currentDayNum: Int = String.format("%td", currentDate).toInt()

        // 设置默认日期
        selectedYearNum = currentYearNum
        selectedMonthNum = currentMonthNum
        selectedDayNum = currentDayNum


        // 初始化设置数据是否循环显示。防止数据顺序移位
        binding.pvYear.apply {
            setIsLoop(false)
        }
        binding.pvMonth.apply {
            setIsLoop(false)
        }
        binding.pvDay.apply {
            setIsLoop(false)
        }

        // 构建「年」选择器数据
        getYears(currentYearNum)
        // 构建「月」选择器数据
        getMonthsOfYear(currentMonthNum)
        // 构建「日」选择器数据
        getDaysOfMonth(currentYearNum, currentMonthNum)

        // 设置初始化选择时间
        binding.pvYear.setSelected("$currentYearNum")
        binding.pvMonth.setSelected(monthMap.get(currentMonthNum))
        binding.pvDay.setSelected(getDayStr(currentDayNum))

        binding.pvYear.setOnSelectListener { text, position ->
            selectedYearNum = text.toInt()
            getDaysOfMonth(selectedYearNum, selectedMonthNum)
            onDateSelectedListener?.onDateSelected(
                selectedYearNum,
                selectedMonthNum,
                selectedDayNum
            )
        }

        binding.pvMonth.setOnSelectListener { text, position ->
            selectedMonthNum = monthData.indexOf(text) + 1
            getDaysOfMonth(selectedYearNum, selectedMonthNum)
            onDateSelectedListener?.onDateSelected(
                selectedYearNum,
                selectedMonthNum,
                selectedDayNum
            )
        }
        binding.pvDay.setOnSelectListener { text, position ->
            selectedDayNum = text.toInt()
            onDateSelectedListener?.onDateSelected(
                selectedYearNum,
                selectedMonthNum,
                selectedDayNum
            )
        }
    }

    /**
     * 年份可选择范围固定，所以只会在初始化时候调用一次
     */
    private fun getYears(currentYearNum: Int) {
        yearData = (2000..2060).map { it.toString() }.toMutableList()
        binding.pvYear.setData(yearData)

    }

    /**
     * 月份可选择范围固定，所以只会在初始化时候调用一次
     */
    private fun getMonthsOfYear(currentMonthNum: Int) {
        monthData = monthMap.values.toMutableList()
        binding.pvMonth.setData(monthData)
    }

    /**
     * 日期可选择范围根据月份来，所以每次年份和月份变化时候都会调用
     */
    private fun getDaysOfMonth(year: Int, month: Int) {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.YEAR, year)
        calendar.set(Calendar.MONTH, month - 1)
        val maxDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
        if (maxDay != dayDataOfMonth.size) {
            val daysOfMonth = (1..maxDay).toMutableList().map { it.toString() }
            dayDataOfMonth.clear()
            dayDataOfMonth.addAll(daysOfMonth)
            // 只有日期数据发生变化时才更新数据
            binding.pvDay.setData(dayDataOfMonth.toList())
        }

        // 当前已选择日期超过最大日期时，自动调整
        if (selectedDayNum > maxDay) {
            selectedDayNum = maxDay
        }
        binding.pvDay.setSelected(getDayStr(selectedDayNum))
    }

    private fun getDayStr(selectedDayNum: Int): String = selectedDayNum.toString()


    /**
     * 设置当前日期, 日期是否超过限制，由调用者来判断
     * @param dateStr 日期字符串，格式：yyyy-MM-dd
     */
    fun setCurrentDateStr(dateStr: String) {
        // 检测输入的 dateStr 是否符合 yyyy-MM-dd 格式
        if (!dateStr.matches(Regex("\\d{4}-\\d{2}-\\d{2}"))) {
            LogUtil.e(
                "TimeSelectionYmd",
                "content: ${dateStr} ------ 日期格式不正确，请使用 yyyy-MM-dd 格式"
            )
            throw IllegalArgumentException("日期格式不正确，请使用 yyyy-MM-dd 格式")
        }
        val currDate = TimeUtil.string2Date(dateStr, "yyyy-MM-dd")
        // 获取当前年份
        val currentYearNum: Int = String.format("%tY", currDate).toInt()
        // 获取当前月份
        val currentMonthNum: Int = String.format("%tm", currDate).toInt()
        // 获取当前日期
        val currentDayNum: Int = String.format("%td", currDate).toInt()

        selectedYearNum = currentYearNum
        selectedMonthNum = currentMonthNum
        selectedDayNum = currentDayNum
        getDaysOfMonth(selectedYearNum, selectedMonthNum)
        binding.pvYear.setSelected("$selectedYearNum")
        binding.pvMonth.setSelected(monthMap.get(selectedMonthNum))
    }

    private var onDateSelectedListener: OnDateSelectedListener? = null

    fun setOnDateSelectedListener(@Nonnull listener: (year: Int, month: Int, day: Int) -> Unit) {
        onDateSelectedListener = object : OnDateSelectedListener {
            override fun onDateSelected(year: Int, month: Int, day: Int) {
                listener(year, month, day)
            }
        }
    }


    interface OnDateSelectedListener {
        fun onDateSelected(year: Int, month: Int, day: Int) {}
    }

}