package cn.com.vau.profile.activity.twoFactorAuth.fragment

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.databinding.FragmentTfaPasswordBinding
import cn.com.vau.profile.activity.twoFactorAuth.viewmodel.TFAViewModel
import cn.com.vau.util.AttrResourceUtil

/**
 * Filename: TFAUnBindPwdFragment.kt
 * Author: GG
 * Date: 2024/2/19
 * Description:
 */
class TFAPwdFragment : BaseMvvmBindingFragment<FragmentTfaPasswordBinding>() {

    private val mViewModel: TFAViewModel by activityViewModels()

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        mViewModel.currentPageLiveData.value = TFAViewModel.PAGE_PWD
    }

    override fun initView() {
        mBinding.tvSubmit.setOnClickListener {
            if (mBinding.etPwd.getText().isNotBlank()) {
                mViewModel.verifyPasswordApi()
            }
        }
        mBinding.etPwd.doAfterTextChanged {
            mViewModel.password = it.toString()
            checkInput()
        }
    }

    private fun checkInput() {
        mBinding.tvSubmit.setBackgroundResource(
            if (mBinding.etPwd.getText().isNotEmpty()) {
                mBinding.tvSubmit.setTextColor(AttrResourceUtil.getColor(requireContext(), R.attr.color_cebffffff_c1e1e1e))
                R.drawable.draw_shape_c1e1e1e_cebffffff_r100
            } else {
                mBinding.tvSubmit.setTextColor(AttrResourceUtil.getColor(requireContext(), R.attr.color_c731e1e1e_c61ffffff))
                R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100
            }
        )
    }

}