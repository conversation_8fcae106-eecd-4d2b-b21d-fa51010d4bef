package cn.com.vau.profile.activity.changeSecurityPWD

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseFrameActivity
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.databinding.ActivityChangeSecurityPwdBinding
import cn.com.vau.page.login.FromPageType
import cn.com.vau.page.login.SendCodeType
import cn.com.vau.page.login.activity.VerifyEmailCodeActivity
import cn.com.vau.page.login.activity.VerifyEmailCodeActivity.VerifyEmailCodeType
import cn.com.vau.page.login.activity.VerifySmsCodeActivity
import cn.com.vau.page.login.activity.VerifySmsCodeActivity.VerifySmsCodeType
import cn.com.vau.page.login.viewmodel.SendCodeViewModel
import cn.com.vau.page.security.SecurityViewModel
import cn.com.vau.profile.activity.addOrForgotSecurityPWD.AddOrForgotSecurityPWDActivity
import cn.com.vau.util.*
import com.netease.nis.captcha.Captcha

/**
 * 修改资金安全密码
 * Created by zhy on 2018/11/29.
 */
class ChangeSecurityPWDActivity : BaseFrameActivity<ChangeSecurityPWDPresenter, ChangeSecurityPWDModel>(), ChangeSecurityPWDContract.View {

    private val mBinding: ActivityChangeSecurityPwdBinding by lazy { ActivityChangeSecurityPwdBinding.inflate(layoutInflater) }

    private var isNext = false
    private val color_c731e1e1e_c61ffffff by lazy {
        AttrResourceUtil.getColor(this, R.attr.color_c731e1e1e_c61ffffff)
    }

    private val color_cffffff_c1e1e1e by lazy {
        AttrResourceUtil.getColor(this, R.attr.color_cebffffff_c1e1e1e)
    }

    private val mViewModel by viewModels<SecurityViewModel>()
    private val sendCodeViewModel by viewModels<SendCodeViewModel>()
    private var mCaptcha: Captcha? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
    }

    override fun initParam() {
        sendCodeViewModel.signUpRequestBean = intent.getParcelableExtra("signUpRequestBean")
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        super.initView()
        mBinding.mHeaderBar.setTitleText(getString(R.string.change_funds_password))
        mBinding.tvForgotSecurityCode.setOnClickListener(this)
        mBinding.tvOk.setOnClickListener(this)
        mBinding.layoutPasswordCheck.tvPasswordSpecial.text = buildString {
            append(getString(R.string.at_least_1_following_characters))
            append(" !@#\$%^&*.()")
        }
        mBinding.etPwdNew.doAfterTextChanged {
            checkNewPassword()
        }
        mBinding.etPwdRepeat.doAfterTextChanged {
            checkNewPassword()
        }
        createObserver()
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.ivLeft -> finish()

            R.id.tvForgotSecurityCode -> {
                if (SpManager.isV1V2()) {
                    showNetDialog()
                    sendCodeViewModel.signUpRequestBean?.fromPage = FromPageType.FROM_PAGE_FORGET_FUNDS_PWD
                    mViewModel.getAuthConfigApi("reset-fund-code")
                } else {
                    AddOrForgotSecurityPWDActivity.openActivity(
                        this,
                        AddOrForgotSecurityPWDActivity.TYPE_CHANGE,
                        AddOrForgotSecurityPWDActivity.STATE_SETUP
                    )
                }
            }

            R.id.tvOk -> noRepeat {
                if (!isNext) {
                    return@noRepeat
                }
                if (mBinding.etOldPwd.getText() == mBinding.etPwdNew.getText()) {
                    ToastUtil.showToast(getString(R.string.the_new_password_original_password))
                    return@noRepeat
                }
                //修改资金安全密码
                mPresenter?.updateFundPWDApi(
                    UserDataUtil.loginToken(), "1",
                    mBinding.etOldPwd.getText(), mBinding.etPwdNew.getText(), mBinding.etPwdRepeat.getText()
                )
            }
        }
    }

    private fun createObserver() {
        // 触发网易易盾验证
        sendCodeViewModel.showCaptchaLiveData.observe(this) {
            hideNetDialog()
            showCaptcha(it)
        }
        // 验证码发送成功
        sendCodeViewModel.sendCodeSuccessLiveData.observe(this) {
            hideNetDialog()
            if (sendCodeViewModel.signUpRequestBean?.sendCodeType == SendCodeType.EMAIL) {
                VerifyEmailCodeActivity.open(this, sendCodeViewModel.signUpRequestBean)
            } else {
                VerifySmsCodeActivity.open(this, sendCodeViewModel.signUpRequestBean)
            }
        }
        // 获取安全中心配置信息成功
        mViewModel.getAuthConfigSuccessLiveData.observe(this) {
            val validateType = it?.second?.validateType
            sendCodeViewModel.signUpRequestBean?.validateType = validateType
            if (it.first == "reset-fund-code") { // 忘记资金密码
                when (validateType) {
                    2 -> { // 手机号已验证：先验证手机OTP，然后跳转到设置资金密码页面
                        sendCodeViewModel.signUpRequestBean?.countryCode = it.second?.phoneCountryCode
                        sendCodeViewModel.signUpRequestBean?.countryNum = it.second?.phoneCode
                        sendCodeViewModel.signUpRequestBean?.mobile = it.second?.phone
                        sendCodeViewModel.signUpRequestBean?.verifySmsCodeType = VerifySmsCodeType.FUNDS_PWD_FORGET
                        sendCodeViewModel.signUpRequestBean?.sendCodeType = SendCodeType.PHONE
                        sendCodeViewModel.sendPhoneCodeApi("6")
                    }

                    else -> { // 等于1或者其他，邮箱已验证：先验证邮箱OTP，然后跳转到设置资金密码页面
                        sendCodeViewModel.signUpRequestBean?.verifyEmailCodeType = VerifyEmailCodeType.FUNDS_PWD_FORGET
                        sendCodeViewModel.signUpRequestBean?.email = it.second?.email
                        sendCodeViewModel.signUpRequestBean?.sendCodeType = SendCodeType.EMAIL
                        sendCodeViewModel.sendEmailCodeApi("6")
                    }
                }
            }
        }
        // 获取安全中心配置信息失败
        mViewModel.getAuthConfigErrorLiveData.observe(this) {
            hideNetDialog()
        }
        // 接口请求失败
        sendCodeViewModel.requestErrorLiveData.observe(this) {
            hideNetDialog()
        }
    }

    /**
     * 判断密码是否符合规范，并且会直接改变对应的状态
     */
    private fun checkNewPassword() {
        val password = mBinding.etPwdNew.getText()
        mBinding.layoutPasswordCheck.tvPasswordLength.isSelected = password.length in 8..16
        //正则表达式的意思是 包含字母数字和特殊字符
        mBinding.layoutPasswordCheck.tvPasswordContent.isSelected = RegexUtil.isContainsLetter(password)
        mBinding.layoutPasswordCheck.tvPasswordNumber.isSelected = RegexUtil.isContainsNumber(password)
        mBinding.layoutPasswordCheck.tvPasswordSpecial.isSelected = RegexUtil.isContainsSpecial(password)
        mBinding.layoutPasswordCheck.tvPasswordMatch.isSelected = password.isNotBlank() && password == mBinding.etPwdRepeat.getText()
        updateButton()
    }

    /**
     * 更新按钮背景
     */
    private fun updateButton() {
        if (mBinding.layoutPasswordCheck.tvPasswordLength.isSelected &&
            mBinding.layoutPasswordCheck.tvPasswordContent.isSelected &&
            mBinding.layoutPasswordCheck.tvPasswordNumber.isSelected &&
            mBinding.layoutPasswordCheck.tvPasswordSpecial.isSelected &&
            mBinding.layoutPasswordCheck.tvPasswordMatch.isSelected
        ) {
            isNext = true
            mBinding.tvOk.setTextColor(color_cffffff_c1e1e1e)
            mBinding.tvOk.setBackgroundResource(R.drawable.draw_shape_c1e1e1e_cebffffff_r100)
        } else {
            isNext = false
            mBinding.tvOk.setTextColor(color_c731e1e1e_c61ffffff)
            mBinding.tvOk.setBackgroundResource(R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100)
        }
    }

    override fun refreshFundPWD() {
        finish()
    }

    /**
     * 触发网易易盾验证
     */
    private fun showCaptcha(type: String) {
        mCaptcha = CaptchaUtil.getCaptcha(this) {
            showNetDialog()
            if (sendCodeViewModel.signUpRequestBean?.sendCodeType == SendCodeType.EMAIL) { // 邮箱
                sendCodeViewModel.sendEmailCodeApi(type, it)
            } else {// 手机号
                sendCodeViewModel.sendPhoneCodeApi(type, it)
            }
        }
        mCaptcha?.validate()
    }

    override fun onDestroy() {
        KeyboardUtil.unregisterSoftInputChangedListener(this.window)
        super.onDestroy()
        mCaptcha?.destroy()
    }
}
