package cn.com.vau.profile.activity.authentication

import cn.com.vau.common.base.mvvm.BaseRepository
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.ServiceManager

/**
 * Filename: AuthRepository.kt
 * Author: GG
 * Date: 2024/1/12
 * Description:
 */
class AuthRepository : BaseRepository() {

    fun getAuditStatus() =
        ServiceManager.getInstance().baseUrlService.getAuditStatus(UserDataUtil.loginToken())
            .compose(threadToMain())

}