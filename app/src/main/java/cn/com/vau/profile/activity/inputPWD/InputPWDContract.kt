package cn.com.vau.profile.activity.inputPWD

import cn.com.vau.common.base.mvp.BaseModel
import cn.com.vau.common.base.mvp.BasePresenter
import cn.com.vau.common.base.mvp.BaseView
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.DataObjStringBean
import cn.com.vau.page.WithdrawalBundleBean

/**
 * Created by zhy on 2018/11/22.
 */
interface InputPWDContract {

    interface Model : BaseModel {
        fun withdrawal(map: HashMap<String, Any>, baseObserver: BaseObserver<DataObjStringBean>)
    }

    interface View : BaseView {
        fun refreshWithdrawal(number: String?)
    }

    abstract class Presenter : BasePresenter<Model?, View?>() {
        abstract fun withdrawal(
            userToken: String?, accountId: String?, currency: String?,
            data: WithdrawalBundleBean, fundSafePwd: String?,
            state: Int
        )
    }
}
