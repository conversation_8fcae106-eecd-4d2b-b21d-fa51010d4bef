package cn.com.vau.profile.adapter

import androidx.annotation.Keep
import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.page.common.BaseListBean
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.ifNull
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * Filename: SelectAccountAdapter
 * Author: GG
 * Date: 2023/9/7 0007 14:47
 * Description:
 */
class SelectAccountAdapter<T : BaseListBean>(var selectTitle: String? = null, private val isChangeSelectTextColor: Boolean = true) : BaseQuickAdapter<T, BaseViewHolder>(R.layout.item_select_account) {

    override fun convert(holder: BaseViewHolder, item: T) {
        holder.setText(R.id.title, item.getShowItemValue())
            .setGone(R.id.tvLabel, item.getLabelStr(context).isNullOrBlank()) // 是否显示标签
            .setText(R.id.tvLabel, item.getLabelStr(context).ifNull()) // 标签的文字
            .setBackgroundResource(R.id.tvLabel, item.getLabelBgRes(context) ?: 0) // 标签的背景
            .apply {
                if (isChangeSelectTextColor) {
                    setTextColor(
                        R.id.title, if (selectTitle == item.getShowItemValue()) {
                            ContextCompat.getColor(context, R.color.ce35728)
                        } else {
                            AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
                        }
                    )
                }
            }
            .setImageResource(
                R.id.icon, if (selectTitle == item.getShowItemValue()) {
                    R.drawable.icon2_cb_tick_circle_c15b374
                } else {
                    R.drawable.draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14
                }
            )
    }
}

@Keep
data class SelectBean(val title: String, val id: String? = null, var isCanSelect: Boolean = true) : BaseListBean {

    override fun getShowItemValue(): String = title
}