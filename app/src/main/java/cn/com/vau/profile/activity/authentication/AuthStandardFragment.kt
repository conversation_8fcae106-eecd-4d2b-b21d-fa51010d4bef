package cn.com.vau.profile.activity.authentication

import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.AuditStatusData
import cn.com.vau.databinding.FragmentAuthStandardBinding
import cn.com.vau.page.user.openAccoGuide.lv1.OpenAccoGuideLv1Activity
import cn.com.vau.page.user.sumsub.SumSubJumpHelper
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.KycVerifyHelper
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil

/**
 * Filename: AuthStandardFragment.kt
 * Author: GG
 * Date: 2024/1/11
 * Description:
 */
class AuthStandardFragment : BaseMvvmBindingFragment<FragmentAuthStandardBinding>() {

    private val mViewModel by activityViewModels<AuthViewModel>()

    override fun initView() {
    }

    override fun initListener() {
        super.initListener()
        mViewModel.accountStatusLiveData.observe(this) {
            updateView(it)
        }
        mBinding.tvLv1VerifyNow.clickNoRepeat(1000) {
            if (SpManager.isV1V2()) {
                activity?.let {
                    KycVerifyHelper.showKycDialog(
                        it,
                        mapOf(Constants.GoldParam.CODE to Constants.GoldParam.CODE_OPEN_ACCOUNT)
                    )
                }
            } else {
                val data = mViewModel.accountStatusLiveData.value ?: return@clickNoRepeat
                // 如果账户审核状态为未提交、待审核、审核不通过2或审核不通过9，则打开 Lv1 开户引导界面
                val bundle = Bundle()
                if (data.basicInfo == 0) {
                    // 如果基本信息未填写，则打开基本信息填写界面
                    bundle.putInt("index", 0)
                } else if (data.basicInfo == 1 && data.tradeAccount == 0) {
                    // 如果基本信息已填写，但交易账号未开通，则打开交易账号开通界面
                    bundle.putInt("index", 1)
                }
                val intent = Intent(requireContext(), OpenAccoGuideLv1Activity::class.java)
                intent.putExtras(bundle)
                requireContext().startActivity(intent)
            }

            LogEventUtil.setLogEvent(
                BuryPointConstant.V334.REGISTER_LIVE_LVL1_BUTTON_CLICK,
                hashMapOf("Position" to "Profile_info")
            )
        }
        mBinding.tvLv2VerifyNow.clickNoRepeat(1000) {
//            // 如果 POI 审核状态为待审核或审核不通过，则打开 Lv2 开户引导界面
//            val intent = Intent(requireContext(), OpenAccoGuideLv2Activity::class.java)
//            requireContext().startActivity(intent)
            SumSubJumpHelper.isJumpSumSub(requireContext(), Constants.SUMSUB_TYPE_POI)

            LogEventUtil.setLogEvent(
                BuryPointConstant.V334.REGISTER_LIVE_LVL2_BUTTON_CLICK,
                hashMapOf("Position" to "Profile_info")
            )
        }
    }

    private fun updateView(data: AuditStatusData.Obj?) {
        data?.let {
            updateLv1(it)
            updateLv2(it)
        }
    }

    private fun updateLv1(data: AuditStatusData.Obj) {
        changeTextViewLeftDrawAndColor(mBinding.tvLv1Per1)
        changeTextViewLeftDrawAndColor(mBinding.tvLv1Per2)
        when (data.accountAuditStatus) {
            AuthenticationActivity.TYPE_LV1_SUBMITTED, AuthenticationActivity.TYPE_LV1_REAUDIT -> {
                AuthenticationActivity.updateTextView(mBinding.tvLv1VerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_UNDER_REVIEW)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv1Re1, AuthenticationActivity.PASS_STATUS_ORANGE)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv1Re2, AuthenticationActivity.PASS_STATUS_ORANGE)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv1Re3, AuthenticationActivity.PASS_STATUS_ORANGE)
                mBinding.tvLv1VerifyNow.isVisible = false
            }

            AuthenticationActivity.TYPE_LV1_NOTSUBMITTED, AuthenticationActivity.TYPE_LV1_PENDING -> {
                AuthenticationActivity.updateTextView(mBinding.tvLv1VerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_UN_VERIFIED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv1Re1, AuthenticationActivity.PASS_STATUS_GREY)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv1Re2, AuthenticationActivity.PASS_STATUS_GREY)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv1Re3, AuthenticationActivity.PASS_STATUS_GREY)
                mBinding.tvLv1VerifyNow.isVisible = true
            }

            AuthenticationActivity.TYPE_LV1_COMPLETED -> {
                AuthenticationActivity.updateTextView(mBinding.tvLv1VerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_VERIFIED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv1Re1, AuthenticationActivity.PASS_STATUS_GREEN)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv1Re2, AuthenticationActivity.PASS_STATUS_GREEN)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv1Re3, AuthenticationActivity.PASS_STATUS_GREEN)
                mBinding.tvLv1VerifyNow.isVisible = false
            }

            AuthenticationActivity.TYPE_LV1_REJECTED2, AuthenticationActivity.TYPE_LV1_REJECTED9 -> {
                AuthenticationActivity.updateTextView(mBinding.tvLv1VerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_REJECTED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv1Re1, AuthenticationActivity.PASS_STATUS_REJECTED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv1Re2, AuthenticationActivity.PASS_STATUS_REJECTED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv1Re3, AuthenticationActivity.PASS_STATUS_REJECTED)
                mBinding.tvLv1VerifyNow.isVisible = true
            }

            else -> {
                AuthenticationActivity.updateTextView(mBinding.tvLv1VerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_UN_VERIFIED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv1Re1, AuthenticationActivity.PASS_STATUS_GREY)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv1Re2, AuthenticationActivity.PASS_STATUS_GREY)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv1Re3, AuthenticationActivity.PASS_STATUS_GREY)
                mBinding.tvLv1VerifyNow.isVisible = false
            }
        }
        mViewModel.isLv1VerifyNowShow = mBinding.tvLv1VerifyNow.isVisible
    }

    private fun updateLv2(data: AuditStatusData.Obj) {
        changeTextViewLeftDrawAndColor(mBinding.tvTitleLv2Trade)
        when (data.poiAuditStatus) {
            AuthenticationActivity.TYPE_SUBMITTED, AuthenticationActivity.TYPE_REAUDIT -> {
                AuthenticationActivity.updateTextView(mBinding.tvTitleLv2VerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_UNDER_REVIEW)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvTitleLv2Re1, AuthenticationActivity.PASS_STATUS_ORANGE)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvTitleLv2Re2, AuthenticationActivity.PASS_STATUS_ORANGE)
                mBinding.tvLv2VerifyNow.isVisible = false
            }

            AuthenticationActivity.TYPE_PENDING -> {
                AuthenticationActivity.updateTextView(mBinding.tvTitleLv2VerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_UN_VERIFIED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvTitleLv2Re1, AuthenticationActivity.PASS_STATUS_GREY)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvTitleLv2Re2, AuthenticationActivity.PASS_STATUS_GREY)
                mBinding.tvLv2VerifyNow.isVisible = !mViewModel.isLv1VerifyNowShow
            }

            AuthenticationActivity.TYPE_COMPLETED -> {
                AuthenticationActivity.updateTextView(mBinding.tvTitleLv2VerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_VERIFIED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvTitleLv2Re1, AuthenticationActivity.PASS_STATUS_GREEN)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvTitleLv2Re2, AuthenticationActivity.PASS_STATUS_GREEN)
                mBinding.tvLv2VerifyNow.isVisible = false
            }

            AuthenticationActivity.TYPE_REJECTED -> {
                AuthenticationActivity.updateTextView(mBinding.tvTitleLv2VerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_REJECTED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvTitleLv2Re1, AuthenticationActivity.PASS_STATUS_REJECTED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvTitleLv2Re2, AuthenticationActivity.PASS_STATUS_REJECTED)
                mBinding.tvLv2VerifyNow.isVisible = !mViewModel.isLv1VerifyNowShow
            }

            else -> {
                AuthenticationActivity.updateTextView(mBinding.tvTitleLv2VerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_UN_VERIFIED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvTitleLv2Re1, AuthenticationActivity.PASS_STATUS_GREY)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvTitleLv2Re2, AuthenticationActivity.PASS_STATUS_GREY)
                mBinding.tvLv2VerifyNow.isVisible = !mViewModel.isLv1VerifyNowShow
            }
        }
    }

    private fun changeTextViewLeftDrawAndColor(tv: TextView) {
        val drawables: Array<Drawable?> = tv.compoundDrawablesRelative
        val drawableStart: Drawable? = drawables[0]
        drawableStart?.setTint(AttrResourceUtil.getColor(requireContext(), R.attr.color_c1e1e1e_cebffffff))
        tv.setTextColor(AttrResourceUtil.getColor(requireContext(), R.attr.color_c1e1e1e_cebffffff))
    }

    companion object {

        fun newInstance(): AuthStandardFragment {
            val fragment = AuthStandardFragment()
            return fragment
        }
    }
}