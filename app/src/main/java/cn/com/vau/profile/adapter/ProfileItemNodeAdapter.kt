package cn.com.vau.profile.adapter

import cn.com.vau.R
import com.chad.library.adapter.base.entity.node.BaseNode
import com.chad.library.adapter.base.provider.BaseNodeProvider
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * @description:
 * @author: GG
 * @createDate: 2024 12月 09 13:55
 * @updateUser:
 * @updateDate: 2024 12月 09 13:55
 */
class ProfileItemNodeAdapter : BaseNodeProvider() {

    override val itemViewType: Int = ProfileItemBean.TYPE_ITEM
    override val layoutId: Int = R.layout.item_recycler_profile_item

    override fun convert(helper: BaseViewHolder, item: BaseNode) {
        if (item !is ProfileItemBean) return
        helper.setText(R.id.tvTitle, item.title)
            .setVisible(R.id.ivIcon, item.icon!= null)
            .setVisible(R.id.tvTop, item.rightTopString!= null)
        item.icon?.let {
            helper.setImageResource(R.id.ivIcon, it)
        }
        item.rightTopIcon?.let {
            helper.setImageResource(R.id.icon, it)
        }
        item.rightTopString?.let {
            helper.setText(R.id.tvTop, it)
        }
        helper.setVisible(R.id.icon, item.rightTopIcon != null)
    }

}