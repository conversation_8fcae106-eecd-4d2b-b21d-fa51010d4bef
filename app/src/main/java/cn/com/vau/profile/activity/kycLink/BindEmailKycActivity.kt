package cn.com.vau.profile.activity.kycLink

import android.content.*
import android.os.Bundle
import android.view.MotionEvent
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.databinding.ActivityKycBindEmailBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.login.*
import cn.com.vau.page.login.activity.VerifyEmailCodeActivity
import cn.com.vau.page.login.activity.VerifyEmailCodeActivity.VerifyEmailCodeType
import cn.com.vau.page.login.viewmodel.SendCodeViewModel
import cn.com.vau.util.*
import com.netease.nis.captcha.Captcha

/**
 * author：lvy
 * date：2025/03/21
 * desc：kyc-绑定邮箱
 */
class BindEmailKycActivity : BaseMvvmBindingActivity<ActivityKycBindEmailBinding>() {

    private val sendCodeViewModel by viewModels<SendCodeViewModel>()
    private var mCaptcha: Captcha? = null

    override fun initParam(savedInstanceState: Bundle?) {
        sendCodeViewModel.signUpRequestBean = intent.getParcelableExtra("signUpRequestBean")
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        sendCodeViewModel.signUpRequestBean = intent?.getParcelableExtra("signUpRequestBean")
        // 清空输入框
        mBinding.emailView.clearText()
    }

    override fun initView() {
        addLoadingObserve(sendCodeViewModel)
        mBinding.pwdView.setHintText("${getString(R.string.password)} ${getString(R.string._8_16_characters)}")
    }

    override fun createObserver() {
        // 触发网易易盾验证
        sendCodeViewModel.showCaptchaLiveData.observe(this) {
            showCaptcha()
        }
        // 验证码发送成功
        sendCodeViewModel.sendCodeSuccessLiveData.observe(this) {
            sendCodeViewModel.signUpRequestBean?.verifyEmailCodeType = VerifyEmailCodeType.BIND_EMAIL_KYC
            VerifyEmailCodeActivity.open(this, sendCodeViewModel.signUpRequestBean)
        }
        // 密码校验成功
        sendCodeViewModel.checkPwdSuccessLiveData.observe(this) {
            sendCode(it)
        }
    }

    override fun initListener() {
        // 客服
        mBinding.mHeaderBar.setEndIconClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
        // 邮箱输入框
        mBinding.emailView.afterTextChangedListener {
            checkNextBtn()
        }
        // 密码输入框
        mBinding.pwdView.afterTextChangedListener {
            checkNextBtn()
        }
        // 下一步
        mBinding.tvNext.clickNoRepeat {
            sendCodeViewModel.signUpRequestBean?.run {
                email = mBinding.emailView.getInputText()
                pwd = mBinding.pwdView.getInputText()
                sendCodeType = SendCodeType.EMAIL
            }
            sendCodeViewModel.checkUserPasswordApi()
        }
    }

    private fun checkNextBtn() {
        mBinding.tvNext.isEnabled = mBinding.emailView.getInputText().isNotBlank() && mBinding.pwdView.getInputText().isNotBlank()
    }

    /**
     * 触发网易易盾验证
     */
    private fun showCaptcha() {
        mCaptcha = CaptchaUtil.getCaptcha(this) {
            sendCodeViewModel.checkUserPasswordApi(it)
        }
        mCaptcha?.validate()
    }

    /**
     * 发送验证码
     */
    private fun sendCode(validateCode: String? = null) {
        // kyc绑定邮箱 bizType=26
        sendCodeViewModel.sendEmailCodeApi(type = "26", validate = validateCode)
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        KeyboardUtil.hideSoftKeyboard(this, currentFocus?.rootView, event, R.id.bgView)
        return super.dispatchTouchEvent(event)
    }

    override fun onDestroy() {
        super.onDestroy()
        mCaptcha?.destroy()
    }

    companion object {
        fun open(context: Context, signUpRequestBean: SignUpRequestBean?) {
            val intent = Intent(context, BindEmailKycActivity::class.java)
            intent.putExtras(bundleOf().apply {
                putParcelable("signUpRequestBean", signUpRequestBean)
            })
            context.startActivity(intent)
        }
    }
}