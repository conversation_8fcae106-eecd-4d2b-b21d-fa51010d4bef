package cn.com.vau.profile.activity.addOrForgotSecurityPWD

import android.content.*
import android.os.Bundle
import androidx.core.os.bundleOf
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.databinding.ActivityKycSetFundsPwdBinding
import cn.com.vau.page.login.*
import cn.com.vau.page.login.activity.*
import cn.com.vau.profile.activity.changeSecurityPWD.ChangeSecurityPWDActivity
import cn.com.vau.profile.viewmodel.SetFundsPwdKycViewModel
import cn.com.vau.util.*
import org.greenrobot.eventbus.EventBus

/**
 * author：lvy
 * date：2025/04/07
 * desc：kyc-设置/修改资金密码
 */
class SetFundsPwdKycActivity : BaseMvvmActivity<ActivityKycSetFundsPwdBinding, SetFundsPwdKycViewModel>() {

    override fun initParam(savedInstanceState: Bundle?) {
        mViewModel.signUpRequestBean = intent.getParcelableExtra("signUpRequestBean")
    }

    override fun initView() {
        if (mViewModel.signUpRequestBean?.fromPage == FromPageType.FROM_PAGE_FORGET_FUNDS_PWD) { // 忘记
            mBinding.mHeaderBar.setTitleText(getString(R.string.forgot_funds_password))
            mBinding.tvPwdTips.text = getString(R.string.reset_new_funds_password)
            mBinding.pwdView.setHintText(getString(R.string.reset_new_funds_password))
            mBinding.tvConfirmPwdTips.text = getString(R.string.confirm_new_funds_password)
            mBinding.confirmPwdView.setHintText(getString(R.string.confirm_new_funds_password))
            mBinding.tvNext.text = getString(R.string.reset_new_funds_password)
        } else { // 新增
            mBinding.mHeaderBar.setTitleText(getString(R.string.set_funds_password))
            mBinding.tvPwdTips.text = getString(R.string.funds_password)
            mBinding.pwdView.setHintText(getString(R.string.set_a_8_to_16_digit_funds_password))
            mBinding.tvConfirmPwdTips.text = getString(R.string.confirm_your_funds_password)
            mBinding.confirmPwdView.setHintText(getString(R.string.confirm_your_funds_password))
            mBinding.tvNext.text = getString(R.string.set_funds_password)
        }
        mBinding.layoutPasswordCheck.tvPasswordSpecial.text = buildString {
            append(getString(R.string.at_least_1_following_characters))
            append("!@#$%^&*.()")
        }
    }

    override fun createObserver() {
        // 设置/修改资金密码成功
        mViewModel.setFundsPwdSuccessLiveData.observe(this) {
            if (it == "0") { // 新增
                EventBus.getDefault().post(NoticeConstants.NOTICE_SECURITY_REFRESH)
                EventBus.getDefault().post(DataEvent(tag = NoticeConstants.SECURITY_PWD_ADD_SUCCESS, data = mViewModel.signUpRequestBean?.pwd))
                ActivityManagerUtil.getInstance().finishActivity(VerifySmsCodeActivity::class.java)
                ActivityManagerUtil.getInstance().finishActivity(VerifyEmailCodeActivity::class.java)
                finish()
            }
        }
        // 忘记资金密码刷新成功
        mViewModel.refreshFundsPwdSuccessLiveData.observe(this) {
            EventBus.getDefault().post(NoticeConstants.NOTICE_SECURITY_REFRESH)
            ActivityManagerUtil.getInstance().finishActivity(VerifySmsCodeActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(VerifyEmailCodeActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(ChangeSecurityPWDActivity::class.java)
            finish()
        }
    }

    override fun initListener() {
        // 新密码
        mBinding.pwdView.afterTextChangedListener {
            checkNextBtn()
        }
        // 确认密码
        mBinding.confirmPwdView.afterTextChangedListener {
            checkNextBtn()
        }
        // 下一步
        mBinding.tvNext.clickNoRepeat {
            if (mViewModel.signUpRequestBean?.fromPage == FromPageType.FROM_PAGE_FORGET_FUNDS_PWD) { // 忘记
                mViewModel.forgetFundsPwdApi(mBinding.pwdView.getInputText())
            } else { // 新增
                mViewModel.setFundsPwdApi(optType = "0", newPwd = mBinding.pwdView.getInputText())
            }
        }
    }

    /**
     * 检测按钮是否可点击
     */
    private fun checkNextBtn() {
        val password = mBinding.pwdView.getInputText()
        mBinding.layoutPasswordCheck.tvPasswordLength.isSelected = password.length in 8..16
        // 正则表达式的意思是 包含字母数字和特殊字符
        mBinding.layoutPasswordCheck.tvPasswordContent.isSelected = RegexUtil.isContainsLetter(password)
        mBinding.layoutPasswordCheck.tvPasswordNumber.isSelected = RegexUtil.isContainsNumber(password)
        mBinding.layoutPasswordCheck.tvPasswordSpecial.isSelected = RegexUtil.isContainsSpecial(password)
        mBinding.layoutPasswordCheck.tvPasswordMatch.isSelected = password.isNotBlank() && mBinding.confirmPwdView.getInputText() == password
        // 对下方的按钮的判断进行赋值
        val isNext = mBinding.layoutPasswordCheck.tvPasswordLength.isSelected &&
                mBinding.layoutPasswordCheck.tvPasswordContent.isSelected &&
                mBinding.layoutPasswordCheck.tvPasswordNumber.isSelected &&
                mBinding.layoutPasswordCheck.tvPasswordSpecial.isSelected &&
                mBinding.layoutPasswordCheck.tvPasswordMatch.isSelected
        mBinding.tvNext.isEnabled = isNext
    }

    companion object {
        fun open(context: Context, signUpRequestBean: SignUpRequestBean?) {
            val intent = Intent(context, SetFundsPwdKycActivity::class.java)
            intent.putExtras(bundleOf().apply {
                putParcelable("signUpRequestBean", signUpRequestBean)
            })
            context.startActivity(intent)
        }
    }
}