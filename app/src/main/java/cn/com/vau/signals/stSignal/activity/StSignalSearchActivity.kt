package cn.com.vau.signals.stsignal.activity

import android.os.Bundle
import android.view.*
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.lifecycle.*
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.databinding.*
import cn.com.vau.signals.stsignal.fragment.StSearchFragment
import cn.com.vau.signals.stsignal.viewmodel.StSignalSearchViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * Filename: StSearchActivity.kt
 * Author: GG
 * Date: 2024/3/28
 * Description:
 */
class StSignalSearchActivity : BaseMvvmActivity<ActivityStSearchBinding, StSignalSearchViewModel>() {

    private val mergeSearchBinding by lazy { MergeSearchBinding.bind(mBinding.root) }

    private val typeface500 by lazy { ResourcesCompat.getFont(this, R.font.gilroy_medium) }
    private val typeface400 by lazy { ResourcesCompat.getFont(this, R.font.gilroy_regular) }

    private val fragmentList by lazy {
        arrayListOf<Fragment>(
            StSearchFragment.newInstance(StSearchFragment.TYPE_TOP),
            StSearchFragment.newInstance(StSearchFragment.TYPE_SEARCH),
        )
    }

    override fun initView() {
        mergeSearchBinding.etSearch.setHint(R.string.search_strategy_or_signal_provider)
        mergeSearchBinding.etSearch.doAfterTextChanged {
            if (it?.toString().isNullOrBlank()) {
                mergeSearchBinding.etSearch.typeface = typeface400
                mViewModel.inputSearchLiveData.value = ""
                updatePage(0)
            } else {
                mergeSearchBinding.etSearch.typeface = typeface500
                mViewModel.inputSearchLiveData.value = it
            }
            lifecycleScope.launch {
                mViewModel.hasInputFlow.emit(!it?.toString().isNullOrBlank())
            }
        }
        mergeSearchBinding.etSearch.setOnFocusChangeListener { view, hasFocus ->
            lifecycleScope.launch {
                mViewModel.isEditFocusFlow.emit(hasFocus)
            }
        }
        KeyboardUtil.registerSoftInputChangedListener(this) {
            if (it == 0) {
                mergeSearchBinding.etSearch.clearFocus()
            }
            lifecycleScope.launch {
                mViewModel.isSoftInputShowFlow.emit(it != 0)
            }
        }
        mergeSearchBinding.ivClear.setOnClickListener {
            mergeSearchBinding.etSearch.setText("")
        }
        mergeSearchBinding.etSearch.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                if (mViewModel.inputSearchLiveData.value.isNullOrBlank()) {
                    return@setOnEditorActionListener true
                }
                // 在这里处理搜索按钮被按下的逻辑
                lifecycleScope.launch {
                    updatePage(1)
                    mViewModel.sendClickEvent()
                    KeyboardUtil.hideSoftInput(this@StSignalSearchActivity)
                    val bundle = Bundle()
                    bundle.putString("Search_value", mViewModel.inputSearchLiveData.value?.toString())
                    LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_COMMUNITY_SEARCH_BTN_CLICK, bundle)
                }
                true // 返回 true 表示事件已处理
            } else {
                false // 返回 false 表示事件未处理
            }
        }
        updatePage(0)
        KeyboardUtil.hideSoftInput(this)

        mBinding.mViewPager.init(fragmentList, arrayListOf(), supportFragmentManager, this)
        mBinding.mViewPager.isUserInputEnabled = false    // 禁止左右滑动
    }

    override fun initData() {
        super.initData()
        lifecycleScope.launch {
            mViewModel.clearShowFlow.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED).collectLatest {
                mergeSearchBinding.ivClear.isVisible = it
            }
        }
    }

    private fun updatePage(position: Int) {
        mBinding.mViewPager.setCurrentItem(position, false)
    }

    override fun onDestroy() {
        KeyboardUtil.unregisterSoftInputChangedListener(this.window)
        super.onDestroy()
    }

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        if (ev.action == MotionEvent.ACTION_DOWN) {
            val v = currentFocus
            if (isShouldHideKeyboard(v, ev)) {
                KeyboardUtil.hideSoftInput(this)
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    // Return whether touch the view.
    private fun isShouldHideKeyboard(v: View?, event: MotionEvent): Boolean {
        if ((v is EditText)) {
            val l = intArrayOf(0, 0)
            v.getLocationOnScreen(l)
            val left = l[0]
            val top = l[1]
            val bottom = top + v.height
            val right = left + v.width
            return !(event.rawX > left && event.rawX < right && event.rawY > top && event.rawY < bottom)
        }
        return false
    }
}