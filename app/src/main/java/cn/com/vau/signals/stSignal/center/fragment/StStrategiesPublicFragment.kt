package cn.com.vau.signals.stsignal.center.fragment

import android.app.Activity
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.activityViewModels
import cn.com.vau.R
import cn.com.vau.common.constants.UrlConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmFragment
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.common.view.share.ShareHelper
import cn.com.vau.data.strategy.StrategyBean
import cn.com.vau.databinding.FragmentRecyclerviewBinding
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.signals.stsignal.activity.*
import cn.com.vau.signals.stsignal.center.adapter.StStrategiesPublicAdapter
import cn.com.vau.signals.stsignal.center.vm.StSignalCenterViewModel
import cn.com.vau.signals.stsignal.center.vm.StStrategiesViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.widget.NoDataView
import cn.com.vau.util.widget.dialog.*

/**
 * author：lvy
 * date：2024/04/02
 * desc：信号源中心->Strategies->Public
 */
class StStrategiesPublicFragment :
    BaseMvvmFragment<FragmentRecyclerviewBinding, StStrategiesViewModel>() {

    private val mActivityVM: StSignalCenterViewModel by activityViewModels()

    private var mTabType = 0 // 0=公开；1=下架；2=草稿
    private val mAdapter by lazy {
        StStrategiesPublicAdapter(mTabType).apply {
            setEmptyView(NoDataView(requireContext()).apply {
                setHintMessage(getString(R.string.no_records_found))
            })
        }
    }

    private val bottomTipsDialog by lazy {
        BottomInfoListDialog.Builder(requireActivity()).build()
    }

    override fun initParam(savedInstanceState: Bundle?) {
        mTabType = arguments?.getInt("tabType", 0) ?: 0
    }

    override fun initView() {
        initRecyclerView()
    }

    override fun initListener() {
        //上架成功
        mViewModel.publishStrategySuccessLiveData.observe(this) {
            CenterActionWithIconDialog.Builder(requireActivity())
                .setLottieIcon(R.raw.lottie_dialog_ok)
                .setTitle(getString(R.string.publish_successful)) //设置则展示标题，否则不展示
                .setContent(getString(R.string.your_strategy_is_by_others)) //设置内容
                .setDismissOnBackPressed(false)
                .setSingleButton(true) //展示一个按钮，默认两个按钮
                .setSingleButtonText(getString(R.string.ok)) //设置单个按钮文本
                .setOnSingleButtonListener {
                    mActivityVM.refreshStrategyListLiveData.value = 1 //上架成功，tab切换到上架列表，并刷新策略列表
                }
                .build()
                .showDialog()
        }
        // 该策略源账号已经有上架的策略了，需要弹框提示
        mViewModel.publishStrategyHasSameLiveData.observe(this) {
            CenterActionDialog.Builder(requireActivity())
                .setContent(getString(R.string.you_have_a_strategies_same_time)) //设置内容
                .setSingleButton(true) //展示一个按钮，默认两个按钮
                .setSingleButtonText(getString(R.string.ok)) //设置单个按钮文本
                .build()
                .showDialog()
        }
        //下架成功
        mViewModel.delistStrategyLiveData.observe(this) {
            mActivityVM.refreshStrategyListLiveData.value = 2 //下架成功，tab切换到下架列表，并刷新策略列表
        }
    }

    private fun initRecyclerView() {
        mBinding.mRecyclerView.adapter = mAdapter

        mAdapter.addChildClickViewIds(
            R.id.ivShare, R.id.tvEdit, R.id.tvDelistOrPublic, R.id.tvMore, R.id.tvKey1, R.id.tvKey5, R.id.tvKey6
        )
        mAdapter.setNbOnItemChildClickListener { _, view, position ->
            val bean = mAdapter.data.elementAtOrNull(position) ?: return@setNbOnItemChildClickListener
            when (view.id) {
                R.id.ivShare -> { // 分享
                    if (!UserDataUtil.isLogin()) {
                        openActivity(LoginActivity::class.java)
                        return@setNbOnItemChildClickListener
                    }
                    ShareHelper.strategyShare(
                        requireActivity() as AppCompatActivity,
                        strategyId = bean.strategyId,
                        avatarUrl = bean.avatar,
                        strategyName = bean.strategyName,
                        strategyNo = bean.strategyNo,
                    )
                    LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_PROFILE_SP_CENTER_SHARE_BTN_CLICK)
                }

                R.id.tvEdit -> { //编辑策略
                    if (mTabType == 0 || mTabType == 1) {
                        editResultLauncher.launch(
                            StCreateAndEditStrategyActivity.createIntent(
                                requireContext(), StCreateAndEditStrategyActivity.TYPE_OPEN_EDIT, bean
                            )
                        )
                    } else {
                        //编辑草稿
                        editResultLauncher.launch(
                            StCreateAndEditStrategyActivity.createIntent(
                                requireContext(), StCreateAndEditStrategyActivity.TYPE_EDIT, bean
                            )
                        )
                    }
                }

                R.id.tvDelistOrPublic -> { //下架/公开策略
                    when (mTabType) {
                        0 -> { //如果为公开策略，请求下架接口
                            CenterActionDialog.Builder(requireActivity())
                                .setTitle(getString(R.string.confirm_delist)) //设置则展示标题，否则不展示
                                .setContent(getString(R.string.all_copiers_pending_be_rejected)) //设置内容
                                .setStartText(getString(R.string.cancel))//设置左侧按钮文本
                                .setEndText(getString(R.string.confirm))//设置右侧按钮文本
                                //如果展示两个按钮，点击监听使用setOnStartListener和setOnEndListener
                                .setOnEndListener { textView ->
                                    mViewModel.stDelistStrategyApi(bean.strategyId)
                                }
                                .build()
                                .showDialog()
                        }

                        1 -> { //如果为下架策略，请求上架接口
                            mViewModel.stPublishStrategyApi(bean.strategyId)
                        }

                        2 -> { //如果为草稿，请求创建接口
                            if (draftDataCheck(bean)) {
                                mViewModel.createStrategyApi(bean)
                            }
                        }
                    }
                }

                R.id.tvMore -> {
                    showMorePopup(bean)
                }

                R.id.tvKey1 -> { // Active Copiers 点击文案
                    showBtoPpw(
                        arrayListOf(
                            HintLocalData(
                                getString(R.string.active_copiers),
                                getString(R.string.the_amount_of_copiers_the_strategy)
                            ),
                            HintLocalData(
                                getString(R.string.settlement),
                                getString(R.string.the_profit_sharing_amount_settlement_cycle)
                            ),
                            HintLocalData(
                                getString(R.string.profit_sharing),
                                getString(R.string.glossary_signal_provider_2)
                            ),
                            HintLocalData(
                                getString(R.string.copy_aum),
                                getString(R.string.the_sum_of_equities_this_strategy)
                            ),
                            HintLocalData(
                                getString(R.string.historical_payout),
                                getString(R.string.glossary_signal_provider_3)
                            ),
                        ), getString(R.string.strategy)
                    )
                }

                R.id.tvKey5, R.id.tvKey6 -> { // tvKey5=累计获得分润 tvKey6=未支付金额
                    if (mTabType == 2) { //草稿列表的不可点击
                        return@setNbOnItemChildClickListener
                    }
                    val map = mutableMapOf<String, String>("strategyId" to bean.strategyId.ifNull())
                    NewHtmlActivity.openActivity(requireContext(), url = UrlConstants.HTML_SIGNALSTMT, dataMap = map)
                    LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_SP_CENTER_HISTORICAL_PAYOUT_BTN_CLICK)
                }
            }
        }
    }

    /**
     * item上面的more按钮弹框
     */
    private fun showMorePopup(bean: StrategyBean) {
        BottomSelectListDialog.Builder(requireActivity())
            .setTitle(requireContext().getString(R.string.manage_strategy))
            .setItemType(1) //0选择框，其他是箭头，默认0
            .setDataList(
                arrayListOf(
                    getString(R.string.profit_sharing_statement),
                    getString(R.string.follower_list),
                    getString(R.string.strategy_homepage),
                )
            )
            .setOnItemClickListener { position: Int ->
                val buryPointStr = when (position) {
                    0 -> { //分润结算页
                        val map = mapOf<String, String>("strategyId" to bean.strategyId.ifNull())
                        NewHtmlActivity.openActivity(
                            requireContext(),
                            url = UrlConstants.HTML_SIGNALSTMT,
                            dataMap = map
                        )
                        "Profit_sharing_statement"
                    }

                    1 -> { //被关注列表页
                        StFansListActivity.open(requireContext(), bean.strategyId)
                        "Follower_list"
                    }

                    2 -> { //策略详情页
                        StStrategyDetailsActivity.open(requireContext(), bean.strategyId)
                        "Strategy_homepage"
                    }

                    else -> {
                        ""
                    }
                }

                val bundle = Bundle()
                bundle.putString("Menu", buryPointStr)
                LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_SP_CENTER_MANAGE_MENU_BTN_CLICK, bundle)
            }
            .build()
            .showDialog()
    }

    /**
     * 更新列表数据
     */
    fun updateList(list: MutableList<StrategyBean>?) {
        if (isDestroyed()) return
        mAdapter.setList(list)
        if (mBinding.mRecyclerView.itemDecorationCount == 0) {
            mBinding.mRecyclerView.addItemDecoration(
                DividerItemDecoration(
                    8.dp2px(),
                    dividerColor = AttrResourceUtil.getColor(context = requireContext(), R.attr.color_c0a1e1e1e_c0affffff)
                )
            )
        }
    }

    /**
     * 草稿功能上架策略时需要校验数据是否完整
     */
    private fun draftDataCheck(data: StrategyBean): Boolean {
        if (data.strategyName.isNullOrBlank() || data.sourceAccount.isNullOrBlank()
            || data.paymentAccount.isNullOrBlank() || data.profitShareRatio.isBlank()
            || data.minInvestmentPerCopy.isNullOrBlank() || !checkNumIsPass(data.minInvestmentPerCopy.ifNull(), min = mViewModel.minInvestedValue(data).toDouble())
            || data.minLotsPerOrder.isBlank() || !checkNumIsPass(data.minLotsPerOrder.ifNull(), 0.01, 100)
            || data.minLotsMultiplePerOrder.isBlank() || !checkNumIsPass(data.minLotsMultiplePerOrder.ifNull(), 0.1, 50)
            || data.minLotsRatioPerOrder.isBlank() || !checkNumIsPass(data.minLotsRatioPerOrder.ifNull(), 1, 50)
        ) {
            ToastUtil.showToast(getString(R.string.please_complete_all_before_publishing))
            return false
        }
        return true
    }

    /**
     * 检测 输入数字是否在 限制范围内
     */
    private fun checkNumIsPass(str: String, min: Number, max: Number? = null): Boolean {
        try {
            val value = str.toDoubleCatching()
            return if (max == null) {
                value >= min.toDouble()
            } else {
                value in min.toDouble()..max.toDouble()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

    /**
     * 说明文案底部弹框
     */
    private fun showBtoPpw(list: MutableList<HintLocalData>?, title: String? = null) {
        bottomTipsDialog.setTitle(title)
            .setDataList(list)
            .showDialog()
    }

    /**
     * 修改策略回调
     */
    private val editResultLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val type = result.data?.getIntExtra("type", 0) ?: 0
            when (type) {
                //更新成功，tab保持当前，并刷新策略列表
                0 -> mActivityVM.refreshStrategyListLiveData.value = 0
                //上架成功，tab切换到上架列表，并刷新策略列表
                1 -> mActivityVM.refreshStrategyListLiveData.value = 1
                //更新草稿成功，刷新草稿列表
                2 -> mActivityVM.refreshStrategyListLiveData.value = 3
            }
        }
    }

    companion object {
        fun newInstance(tabType: Int) = StStrategiesPublicFragment().apply {
            arguments = Bundle().apply {
                putInt("tabType", tabType)
            }
        }
    }
}