package cn.com.vau.signals

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import cn.com.vau.BuildConfig
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.mvvm.base.BaseMvvmFragment
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.vm.MainViewModel
import cn.com.vau.databinding.FragmentSignalsBinding
import cn.com.vau.page.StickyEvent
import cn.com.vau.signals.activity.H5DebugActivity
import cn.com.vau.signals.fragment.*
import cn.com.vau.signals.live.LiveListFragment
import cn.com.vau.signals.stsignal.LiveEventData
import cn.com.vau.signals.stsignal.fragment.*
import cn.com.vau.signals.viewModel.DiscoverViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.*
import org.json.JSONObject

/**
 * Created by roy on 2018/10/16.
 */
class DiscoverFragment : BaseMvvmFragment<FragmentSignalsBinding, DiscoverViewModel>() {

    private val mainViewModel by activityViewModels<MainViewModel>()

    private val fragmentList = mutableListOf<Fragment>()
    private val tabsStrList = mutableListOf<String>()

    private var tableLayoutInitFinish = false
    private var liveId: Long = 0L
    private var isInner: Int = 0
    private var willScrollTabTag: String = ""

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    override fun initView() {
        // h5 Debug
        if (HttpUrl.isH5Debug && BuildConfig.DEBUG) {
            mBinding.tvTitle.setOnClickListener {
                openActivity(H5DebugActivity::class.java)
            }
        }

        initTabLayoutTabs()

        // 神策自定义埋点(v3500)
        // App_Tab 页面浏览 -> app内五个tab页面加载完成时触发
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.TAB_NAME, "Discover") // Tab 名称
        SensorsDataUtil.track(SensorsConstant.V3500.APP_TAB_PAGE_VIEW, properties)
    }

    @SuppressLint("SetTextI18n", "UseCompatLoadingForDrawables")
    private fun initTabLayoutTabs() {

        if (!tabsStrList.contains(getString(R.string.economic_calendar))) {
            fragmentList.add(0, EconomyCalendarFragment())
            tabsStrList.add(0, getString(R.string.economic_calendar))
        }

        if (!tabsStrList.contains("24/7")) {
            fragmentList.add(0, FxStreetFragment())
            tabsStrList.add(0, "24/7")
        }

        // 是否显示跟单模块
        if (SpManager.getShowStEntrance(true)) {
            // 只有跟单账号才显示学院模块
            if (UserDataUtil.isStLogin()) {
                if (!tabsStrList.contains(getString(R.string.academy))) {
                    fragmentList.add(0, StAcademyFragment())
                    tabsStrList.add(0, getString(R.string.academy))
                }
            }
            if (!tabsStrList.contains(getString(R.string.community))) {
                fragmentList.add(0, StCommunityFragment())
                tabsStrList.add(0, getString(R.string.community))
            }
            // 跟单账号不显示跟单交易推荐模块
            if (!UserDataUtil.isStLogin()) {
                if (!tabsStrList.contains(getString(R.string.copy_trading))) {
                    fragmentList.add(0, DiscoverCopyTradingFragment())
                    tabsStrList.add(0, getString(R.string.copy_trading))
                }
            }
        }

        if (!tabsStrList.contains(getString(R.string.analysis))) {
            fragmentList.add(AnalysesFragment())
            tabsStrList.add(getString(R.string.analysis))
        }
        if (!tabsStrList.contains(getString(R.string.newsletter))) {
            fragmentList.add(NewsletterFragment())
            tabsStrList.add(getString(R.string.newsletter))
        }

        if (!tabsStrList.contains(getString(R.string.fx_tv))) {
            fragmentList.add(WebTVFragment())
            tabsStrList.add(getString(R.string.fx_tv))
        }

        mBinding.mViewPager.init(fragmentList, tabsStrList, childFragmentManager, this)
        mBinding.mTabLayout.setVp(mBinding.mViewPager, tabsStrList, TabType.TEXT_SCALE) { position ->
            LogEventUtil.setLogEvent(
                BuryPointConstant.V343.GENERAL_DISCOVER_PAGE_VIEW,
                Bundle().apply {
                    putString(
                        "Page_name", when (tabsStrList.getOrNull(position)) {
                            "24/7" -> BuryPointConstant.DiscoverPageType.TWENTY_FOUR_AND_SEVEN
                            getString(R.string.economic_calendar) -> BuryPointConstant.DiscoverPageType.ECONOMIC_CALENDAR
                            getString(R.string.webinar) -> BuryPointConstant.DiscoverPageType.LIVE
                            getString(R.string.analysis) -> BuryPointConstant.DiscoverPageType.ANALYSIS
                            getString(R.string.newsletter) -> BuryPointConstant.DiscoverPageType.NEWSLETTER
                            getString(R.string.fx_tv) -> BuryPointConstant.DiscoverPageType.FX_TV
                            getString(R.string.copy_trading) -> BuryPointConstant.DiscoverPageType.COPY_TRADING
                            getString(R.string.community) -> BuryPointConstant.DiscoverPageType.COMMUNITY
                            else -> ""
                        }
                    )
                    putString(
                        "Account_type", when {
                            !UserDataUtil.isLogin() -> BuryPointConstant.AccountType.NOLOGIN
                            UserDataUtil.isStLogin() -> BuryPointConstant.AccountType.COPY_TRADING
                            UserDataUtil.isDemoAccount() -> BuryPointConstant.AccountType.DEMO
                            else -> BuryPointConstant.AccountType.LIVE
                        }
                    )
                })

        }

        tableLayoutInitFinish = true

        jumpToSelectTab()

        LogEventUtil.setLogEvent(
            BuryPointConstant.V343.GENERAL_DISCOVER_PAGE_VIEW,
            Bundle().apply {
                putString("Page_name", BuryPointConstant.DiscoverPageType.TWENTY_FOUR_AND_SEVEN)
                putString(
                    "Account_type", when {
                        !UserDataUtil.isLogin() -> BuryPointConstant.AccountType.NOLOGIN
                        UserDataUtil.isStLogin() -> BuryPointConstant.AccountType.COPY_TRADING
                        UserDataUtil.isDemoAccount() -> BuryPointConstant.AccountType.DEMO
                        else -> BuryPointConstant.AccountType.LIVE
                    }
                )

            })

    }

    override fun createObserver() {
        super.createObserver()
        lifecycleScope.launch {
            mViewModel.eventFlow.collect { event ->
                if (event !is DataEvent) return@collect
                when (event.tag) {
                    DiscoverViewModel.EVENT_JUMP_LIVE -> {
                        getLiveCountSucceed()
                    }

                    DiscoverViewModel.EVENT_JUMP -> {
                        if (event.data is Int)
                            jumpType(event.data)
                    }
                }
            }
        }
    }

    private fun jumpToSelectTab() {
        if (willScrollTabTag.isNotEmpty() && fragmentList.isNotEmpty()) {
            if (willScrollTabTag == getString(R.string.webinar)) {
                val fragment = fragmentList.firstOrNull { it is LiveListFragment }
                if (fragment == null) {
                    ToastUtil.showToast(getString(R.string.live_streaming_is_over))
                    SpManager.putLivestream("")
                    willScrollTabTag = tabsStrList.first()
                }
            }
            selectTab(willScrollTabTag)
            willScrollTabTag = ""
        }
    }

    private fun selectTab(tag: String) {
        mBinding.mTabLayout.post {
            val index = tabsStrList.indexOf(tag)
            if (index != -1) {
                mBinding.mViewPager.setCurrentItem(index, false)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onStickyEvent(event: StickyEvent) {
        when (event.tag) {
            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_LIVE_ROOM -> {
                if (event.data !is LiveEventData) return
                val liveData: LiveEventData = event.data
                if (liveData.type == 0) {
                    liveId = liveData.liveId
                    isInner = liveData.isInner
                    mViewModel.jumpTypeApi()
                }
                EventBus.getDefault().removeStickyEvent(event)

            }

            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_ANALYSES -> {
                willScrollTabTag = getString(R.string.analysis)
                jumpToSelectTab()
                EventBus.getDefault().removeStickyEvent(event)
            }

            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_COMMUNITY -> {
                willScrollTabTag = getString(R.string.community)
                mainViewModel.communityLiveData.value = event.data as? String
                jumpToSelectTab()
                EventBus.getDefault().removeStickyEvent(event)
            }

            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_CALENDAR -> {
                willScrollTabTag = getString(R.string.economic_calendar)
                jumpToSelectTab()
                EventBus.getDefault().removeStickyEvent(event)
            }

            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_LIVE -> {
                willScrollTabTag = getString(R.string.webinar)
                jumpToSelectTab()
                EventBus.getDefault().removeStickyEvent(event)
            }

            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_COPY_TRADE -> {
                willScrollTabTag = getString(R.string.copy_trading)
                jumpToSelectTab()
                EventBus.getDefault().removeStickyEvent(event)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onDataEvent(event: DataEvent) {
        when (event.tag) {
            NoticeConstants.STStrategy.MAIN_SHOW_SIGNALS_ITEM_COMMUNITY_FILTER -> {
                val index = fragmentList.indexOfFirst {
                    it is StCommunityFragment
                }
                if (index != -1)
                    mBinding.mViewPager.currentItem = index
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.AFTER_LOGOUT_RESET, NoticeConstants.SWITCH_ACCOUNT -> {
                fragmentList.clear()
                tabsStrList.clear()
                mViewModel.selectVideoCountApi()
                initTabLayoutTabs()
            }

            NoticeConstants.SHOW_ST_ENTRANCE -> {
                mBinding.mViewPager.post {
                    if (context != null) {
                        if (SpManager.getShowStEntrance(true)) {
                            if (UserDataUtil.isStLogin())
                                mBinding.mViewPager.addFragment(StAcademyFragment(), getString(R.string.academy), 0)
                            mBinding.mViewPager.addFragment(StCommunityFragment(), getString(R.string.community), 0)
                            if (!UserDataUtil.isStLogin())
                                mBinding.mViewPager.addFragment(DiscoverCopyTradingFragment(), getString(R.string.copy_trading), 0)
                        } else {
                            mBinding.mViewPager.removeFragment(getString(R.string.academy))
                            mBinding.mViewPager.removeFragment(getString(R.string.community))
                            mBinding.mViewPager.removeFragment(getString(R.string.copy_trading))
                        }
                        mBinding.mTabLayout.post {
                            mBinding.mViewPager.setCurrentItem(0, false)
                        }
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    private fun getLiveCountSucceed() {
        mBinding.mViewPager.post {
            if (context != null) {
                mBinding.mViewPager.addFragment(LiveListFragment(), getString(R.string.webinar), tabsStrList.indexOf(getString(R.string.economic_calendar)) + 1)
            }
        }
    }

    private fun jumpType(count: Int) {
        if (count > 0) {
            val itemIndex = fragmentList.indexOfFirst {
                it is LiveListFragment
            }
            if (itemIndex != -1) {
                mBinding.mViewPager.currentItem = itemIndex
                EventBus.getDefault().post(
                    DataEvent(
                        NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_LIVE_ROOM,
                        LiveEventData(liveId, 2, isInner)
                    )
                )
            } else {
                mBinding.mViewPager.currentItem = 0
                // 直播已结束，敬请期待下次直播
                ToastUtil.showToast(getString(R.string.live_streaming_is_over))
            }
        } else {
            // 直播已结束，敬请期待下次直播
            mBinding.mViewPager.currentItem = 0
            ToastUtil.showToast(getString(R.string.live_streaming_is_over))
        }
    }

}