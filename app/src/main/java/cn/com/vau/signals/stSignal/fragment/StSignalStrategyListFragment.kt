package cn.com.vau.signals.stsignal.fragment

import android.os.Bundle
import androidx.core.os.bundleOf
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.data.enums.EnumStrategyFollowState
import cn.com.vau.data.strategy.StrategyBean
import cn.com.vau.databinding.FragmentStStrategyDetailsPortfolioBinding
import cn.com.vau.signals.stsignal.activity.StStrategyDetailsActivity
import cn.com.vau.signals.stsignal.adapter.StSignalStrategyListAdapter
import cn.com.vau.trade.st.StrategyOrderBaseData
import cn.com.vau.trade.st.activity.StStrategyOrdersActivity
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.NoDataView
import cn.com.vau.util.widget.dialog.BottomContentDialog

/**
 * author：lvy
 * date：2024/03/26
 * desc：信号源详情->策略列表
 *
 */
class StSignalStrategyListFragment : BaseMvvmBindingFragment<FragmentStStrategyDetailsPortfolioBinding>() {

    private val mAdapter by lazy {
        StSignalStrategyListAdapter().apply {
            setEmptyView(NoDataView(requireContext()).apply {
                setHintMessage(getString(R.string.no_records_found))
            })
        }
    }

    private val bottomTipsDialog by lazy {
        BottomContentDialog.Builder(requireActivity())
    }

    override fun initView() {
        initRecyclerView()
    }

    private fun initRecyclerView() {
        mBinding.mRecyclerView.adapter = mAdapter
        mBinding.mRecyclerView.addItemDecoration(
            DividerItemDecoration(
                8.dp2px(),
                dividerColor = AttrResourceUtil.getColor(context = requireContext(), R.attr.color_c0a1e1e1e_c0affffff)
            )
        )

        mAdapter.setNbOnItemClickListener { _, _, position ->
            val bean = mAdapter.data.elementAtOrNull(position) ?: return@setNbOnItemClickListener
            StStrategyDetailsActivity.open(requireContext(), bean.strategyId)
            logEvent(bean.strategyId)
        }

        mAdapter.addChildClickViewIds(R.id.tvAction, R.id.tvRiskKey, R.id.tvProfitSharingKey)
        mAdapter.setNbOnItemChildClickListener { _, view, position ->
            val bean = mAdapter.data.elementAtOrNull(position) ?: return@setNbOnItemChildClickListener
            when (view.id) {
                R.id.tvAction -> {
                    if (bean.pendingApplyApproval) { //待审核 点击跳转策略订单详情页 setting tab
                        openActivity(StStrategyOrdersActivity::class.java, Bundle().apply {
                            putSerializable("data_strategy", StrategyOrderBaseData().apply {
                                this.type = EnumStrategyFollowState.PENDING_REVIEW
                                this.signalStrategyId = bean.strategyId
                                this.portfolioId = bean.portfolioId
                                this.followRequestId = bean.followRequestId
                            })
                        })
                    } else { //审核通过或拒绝等其他状态
                        if (bean.followerStatus == true) { //已跟单 跳转策略订单详情页 默认 tab
                            openActivity(StStrategyOrdersActivity::class.java, Bundle().apply {
                                putSerializable("data_strategy", StrategyOrderBaseData().apply {
                                    this.type = EnumStrategyFollowState.OPEN
                                    this.signalStrategyId = bean.strategyId
                                    this.portfolioId = bean.portfolioId
                                    this.followRequestId = bean.followRequestId
                                })
                            })
                        } else { //按钮View状态 未跟单 跳转到策略详情页
                            StStrategyDetailsActivity.open(requireContext(), bean.strategyId)
                            logEvent(bean.strategyId)
                        }
                    }
                }

                R.id.tvRiskKey -> {
                    showBtoPpw(
                        getString(R.string.risk_band),
                        getString(R.string.the_risk_band_the_the_here_date_status)
                    )
                }

                R.id.tvProfitSharingKey -> {
                    showBtoPpw(
                        getString(R.string.profit_sharing_ratio),
                        getString(R.string.the_percentage_of_signal_provider)
                    )
                }
            }
        }
        //viewMore点击监听
        mAdapter.viewMoreClickListener { _, intro ->
            showBtoPpw("", intro)
        }
    }

    /**
     * activity获取数据成功后赋值到fragment
     */
    fun setList(list: MutableList<StrategyBean>?) {
        if (isDestroyed()) return
        mAdapter.setList(list)
    }

    /**
     * 说明文案底部弹框
     */
    private fun showBtoPpw(title: String, intro: String) {
        bottomTipsDialog.setTitle(title)
            .setContent(intro)
            .build()
            .showDialog()
    }

    private fun logEvent(strategyId: String?) {
        LogEventUtil.setLogEvent(
            BuryPointConstant.V348.CT_STRATEGY_PAGE_VIEW, bundleOf(
                "Type_of_account" to "Copy Trading",
                "Position" to "Signal_provider",
                "Strategy_ID" to strategyId.ifNull()
            )
        )
    }
}