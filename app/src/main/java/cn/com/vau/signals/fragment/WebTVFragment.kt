package cn.com.vau.signals.fragment

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.viewModels
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.mvvm.state.ListUIState
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.data.discover.WebTVObj
import cn.com.vau.databinding.FragmentRefreshBinding
import cn.com.vau.signals.activity.VideoDetailsActivity
import cn.com.vau.signals.adapter.WebTvAdapter
import cn.com.vau.signals.viewModel.WebTvViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.NoDataView
import org.json.JSONObject

/**
 * Created by roy on 2018/10/16.
 * 讲堂  FX TV
 */
class WebTVFragment : BaseMvvmBindingFragment<FragmentRefreshBinding>() {

    private val mViewModel: WebTvViewModel by viewModels(ownerProducer = { requireParentFragment() })
    private val adapter: WebTvAdapter by lazy {
        WebTvAdapter().apply {
            setEmptyView(NoDataView(requireContext()).apply {
                setHintMessage(getString(R.string.no_records_found))
            })
        }
    }

    // 定义一个 ActivityResultLauncher 来处理回调
    private var resultLauncher: ActivityResultLauncher<Intent>? = null

    override fun initView() {
        mBinding.mRecyclerView.adapter = adapter
        mBinding.mRecyclerView.addItemDecoration(DividerItemDecoration(0.5.dp2px(), dividerColor = AttrResourceUtil.getColor(requireContext(), R.attr.color_c1f1e1e1e_c1fffffff)))
    }

    override fun initListener() {
        super.initListener()
        mBinding.mRefreshLayout.setOnRefreshListener {
            mViewModel.refresh()
        }
        mBinding.mRefreshLayout.setOnLoadMoreListener {
            mViewModel.loadMore()
        }
        resultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            // 在这里处理回调结果
            if (result.resultCode == Activity.RESULT_OK) {
                mViewModel.uiListLiveData.value = ListUIState.RefreshSuccess(result.data?.getParcelableArrayListExtra<WebTVObj>(VideoDetailsActivity.KEY_WEB_TV_LIST))
            }
        }
        adapter.setNbOnItemClickListener { _, _, position ->
            val bean = adapter.data.getOrNull(position)
            resultLauncher?.launch(VideoDetailsActivity.createIntent(requireContext(), bean, arrayListOf<WebTVObj>().apply {
                addAll(adapter.data)
            }))

            LogEventUtil.setLogEvent("signals", Bundle().apply {
                putString("categroy_title", "tv+${bean?.videoName ?: ""}")
            })

            // 神策自定义埋点(v3500)
            sensorsTrack(bean?.videoId.ifNull(), position, bean?.url.ifNull())
        }
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.uiListLiveData.observeUIState(viewLifecycleOwner, adapter, mBinding.mRefreshLayout)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constants.REQUEST_CODE_WEB_TV) {
            val tempList = data?.extras?.getSerializable("web_tv_list") as? ArrayList<WebTVObj>
            if ((tempList?.size ?: 0) > 0) {
                adapter.setList(tempList)
                mViewModel.date = tempList?.last()?.createTime.toString()
            }
        }
    }

    /**
     * 神策自定义埋点(v3500)
     * App_发现页面点击 -> 点击app发现页面内容时触发
     */
    private fun sensorsTrack(mktId: String, mktPos: Int, targetUrl: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BELONG_TAB_NAME, "") // 所属Tab 名称
        properties.put(SensorsConstant.Key.MODULE_ID, "") // 模块id
        properties.put(SensorsConstant.Key.MODULE_NAME, "") // 模块名称
        properties.put(SensorsConstant.Key.MODULE_RANK, "") // 模块序号
        properties.put(SensorsConstant.Key.MKT_ID, mktId) // 素材id
        properties.put(SensorsConstant.Key.MKT_NAME, "") // 素材名称
        properties.put(SensorsConstant.Key.MKT_RANK, mktPos + 1) // 素材排序
        properties.put(SensorsConstant.Key.TARGET_URL, targetUrl) // 跳转链接
        SensorsDataUtil.track(SensorsConstant.V3500.APP_DISCOVER_PAGE_CLICK, properties)
    }
}