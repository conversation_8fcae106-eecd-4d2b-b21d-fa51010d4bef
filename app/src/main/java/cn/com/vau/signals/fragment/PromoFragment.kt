package cn.com.vau.signals.fragment

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.GridLayoutManager
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.utils.VAUStartUtil
import cn.com.vau.common.view.CustomViewPagerOnPageChangeListener
import cn.com.vau.data.discover.PromoEventData
import cn.com.vau.databinding.FragmentPromoBinding
import cn.com.vau.databinding.HeaderRecyclerPromoBinding
import cn.com.vau.signals.adapter.PromoAdapter
import cn.com.vau.signals.viewModel.PromoViewModel
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.util.ScreenUtil
import cn.com.vau.util.arabicReverseTextByFlag
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.ifNull
import cn.com.vau.util.noRepeat
import cn.com.vau.util.observeUIState
import cn.com.vau.util.opt.PerfTraceUtil
import cn.com.vau.util.setNbOnItemClickListener
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.NoDataView
import com.bumptech.glide.request.RequestOptions
import com.youth.banner.adapter.BannerImageAdapter
import com.youth.banner.holder.BannerImageHolder
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject

/**
 * Created by roy on 2018/10/16.
 */
class PromoFragment : BaseMvvmBindingFragment<FragmentPromoBinding>() {

    private val mViewModel by activityViewModels<PromoViewModel>()

    private val headerView: HeaderRecyclerPromoBinding by lazy { HeaderRecyclerPromoBinding.inflate(layoutInflater) }

    private val mAdapter: PromoAdapter by lazy {
        PromoAdapter().apply {
            addHeaderView(headerView.root)
            setEmptyView(NoDataView(requireContext()).apply {
                setHintMessage(getString(R.string.no_records_found))
            })
        }
    }
    private var bannerClickPos: Int = 0

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        PerfTraceUtil.startTrace(PerfTraceUtil.StartTrace.Perf_v6_Promo_Create_First)
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        if (!EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().register(this)
    }

    @SuppressLint("ObsoleteSdkInt")
    override fun initView() {
        PerfTraceUtil.firstFrameTrace(mBinding.root, PerfTraceUtil.StartTrace.Perf_v6_Promo_Create_First, PerfTraceUtil.StartTrace.Perf_v6_Promo_First_Finish)
        val gridLayoutManager = GridLayoutManager(context, getSpanCount())
        gridLayoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                return if (position == 0) {
                    getSpanCount()
                } else {
                    1
                }
            }
        }
        mBinding.mRecyclerView.layoutManager = gridLayoutManager

        mBinding.mRecyclerView.adapter = mAdapter
        mBinding.tvTitle.text = getString(if (Constants.PromoTabTxt) R.string.promo else R.string.info)

        headerView.mBanner.setAdapter(object : BannerImageAdapter<PromoEventData>(emptyList()) {
            override fun onBindView(holder: BannerImageHolder?, data: PromoEventData?, position: Int, size: Int) {
                val options = RequestOptions()
                    .placeholder(R.drawable.shape_placeholder)
                    .error(R.drawable.shape_placeholder)
                ImageLoaderUtil.loadImageWithOption(requireContext(), data?.imgUrl, holder?.imageView, options)
            }
        }).setScrollTime(1500)
            .addBannerLifecycleObserver(this)

        // 神策自定义埋点(v3500)
        // App_Tab 页面浏览 -> app内五个tab页面加载完成时触发
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.TAB_NAME, "Promo") // Tab 名称
        SensorsDataUtil.track(SensorsConstant.V3500.APP_TAB_PAGE_VIEW, properties)
    }

    private fun getSpanCount() = if (ScreenUtil.isScreenExpanded(requireContext())) 3 else 2

    override fun initListener() {
        super.initListener()

        headerView.mBanner.setOnBannerListener { _, bannerPosition ->
            noRepeat {
                val bannerBean = mViewModel.bannerLiveData.value?.getOrNull(bannerPosition)
                itemClick(bannerBean, bannerPosition, SensorsConstant.V3500.APP_PROMO_BANNER_CLICK)
            }
        }
        headerView.mBanner.addOnPageChangeListener(object : CustomViewPagerOnPageChangeListener() {
            @SuppressLint("SetTextI18n", "UseCompatLoadingForDrawables")
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                bannerClickPos = position
                val dataBean = mViewModel.bannerLiveData.value?.getOrNull(position)

                headerView.tvBannerEventTitle.text = dataBean?.appJumpDefModel?.title
                headerView.tvBannerEventTime.text = if ("1" == dataBean?.longTerm) {
                    if (Constants.PromoTabTxt)
                        getString(R.string.ongoing_promotions)
                    else
                        ""
                } else {
                    "${dataBean?.startTime.ifNull()} - ${dataBean?.endTime.ifNull()}"
                }

                if (dataBean?.eventsStatus == 0) {
                    headerView.tvBannerEventState.text = getString(R.string.in_progress)
                } else if (dataBean?.appJumpDefModel?.status == "1") {
                    headerView.tvBannerEventState.text = getString(R.string.coming_soon)
                }
                headerView.mIndicator.changeIndicator(position)
            }
        })

        headerView.root.clickNoRepeat {
            val pushBean = mViewModel.bannerLiveData.value?.getOrNull(bannerClickPos)
            itemClick(pushBean, bannerClickPos, SensorsConstant.V3500.APP_PROMO_BANNER_CLICK)
        }

        mBinding.mRefreshLayout.setOnRefreshListener {
            mViewModel.refresh()
        }

        mBinding.mRefreshLayout.setOnLoadMoreListener {
            mBinding.mRefreshLayout.finishLoadMoreWithNoMoreData()
        }

        mAdapter.setNbOnItemClickListener { _, _, position ->
            val dataBean = mAdapter.getItemOrNull(position)
            itemClick(dataBean, position, SensorsConstant.V3500.APP_PROMO_LIST_CLICK)
        }
    }

    private fun itemClick(dataBean: PromoEventData?, bannerPosition: Int, pointEvent: String) {
        val pushBean = dataBean?.appJumpDefModel
        VAUStartUtil.openActivity(requireActivity(), pushBean)

        if ("url" == pushBean?.openType) {
            dataBean.eventId?.let {
                mViewModel.eventsAddClicksCount(it)
            }
        }
        LogEventUtil.setLogEvent(
            BuryPointConstant.V330.PROMO_TRAFFIC_BUTTON_CLICK, hashMapOf(
                "Promoted_Page_Name" to dataBean?.appJumpDefModel?.eventId.ifNull(),
                "Position" to "Promotion_button"
            )
        )
        // 神策自定义埋点(v3500)
        // App_活动页面Banner点击 -> 点击app活动页面Banner时触发
        val dataBean = mViewModel.bannerLiveData.value?.getOrNull(bannerPosition)
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.IS_LONG_TERM, if ("1" == dataBean?.longTerm) 1 else 0) // 是否长期活动
        properties.put(SensorsConstant.Key.ACTIVITY_START_TIME, dataBean?.startTime.ifNull()) // 活动开始时间
        properties.put(SensorsConstant.Key.ACTIVITY_END_TIME, dataBean?.endTime.ifNull()) // 活动结束时间
        properties.put(SensorsConstant.Key.ACTIVITY_ID, dataBean?.eventId.ifNull()) // 活动id
        properties.put(SensorsConstant.Key.ACTIVITY_NAME, pushBean?.title.ifNull()) // 活动名称
        properties.put(SensorsConstant.Key.ACTIVITY_RANK, bannerPosition + 1) // 活动排序
        SensorsDataUtil.track(pointEvent, properties)
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.uiListLiveData.observeUIState(
            this,
            mAdapter,
            mBinding.mRefreshLayout,
            after = { PerfTraceUtil.stopTrace(PerfTraceUtil.StartTrace.Perf_v6_Promo_First_Finish) }
        )
        mViewModel.bannerLiveData.observe(this) { bannerList ->
            if (bannerList.isNullOrEmpty()) {
                headerView.mBanner.visibility = View.GONE
                headerView.mIndicator.visibility = View.GONE
                headerView.root.isVisible = false
                if (mAdapter.data.isEmpty()) {
                    mAdapter.setEmptyView(NoDataView(requireContext()).apply {
                        setHintMessage(getString(R.string.no_records_found))
                    })
                }
                return@observe
            }
            mAdapter.removeEmptyView()
            headerView.root.isVisible = true
            headerView.mIndicator.visibility = View.VISIBLE
            headerView.mIndicator.initIndicatorCount(bannerList.size)
            headerView.mBanner.visibility = View.VISIBLE
            headerView.mBanner.setDatas(bannerList)
            val dataBean = bannerList.getOrNull(0)
            headerView.tvBannerEventTitle.text = dataBean?.appJumpDefModel?.title
            headerView.tvBannerEventTime.text = if ("1" == dataBean?.longTerm) {
                if (Constants.PromoTabTxt)
                    getString(R.string.ongoing_promotions)
                else
                    ""
            } else {
                "${dataBean?.startTime.ifNull()} - ${dataBean?.endTime.ifNull()}".arabicReverseTextByFlag(" - ")
            }
            if (dataBean?.eventsStatus == 0) {
                headerView.tvBannerEventState.text = getString(R.string.in_progress)
            } else if (dataBean?.appJumpDefModel?.status == "1") {
                headerView.tvBannerEventState.text = getString(R.string.coming_soon)
            }
            headerView.mBanner.start()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.ASIC_CHANGE_PROMO_TO_INFO -> {
                mBinding.tvTitle.text = getString(if (Constants.PromoTabTxt) R.string.promo else R.string.info)
                mViewModel.refresh()
            }
            // 切换账号或者退出登录 就清除数据并重新请求
            NoticeConstants.SWITCH_ACCOUNT, NoticeConstants.AFTER_LOGOUT_RESET -> {
                mAdapter.setList(null)
                mViewModel.refresh()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        headerView.mBanner.start()
    }

    override fun onStop() {
        super.onStop()
        headerView.mBanner.stop()
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
        headerView.mBanner.stop()
    }
}