package cn.com.vau.signals.stsignal.activity

import android.annotation.SuppressLint
import android.content.*
import android.os.Bundle
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.databinding.ActivityStFansListBinding
import cn.com.vau.signals.stsignal.adapter.StFansListAdapter
import cn.com.vau.signals.stsignal.viewmodel.StFansListViewModel
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.widget.NoDataView

/**
 * author：lvy
 * date：2024/04/18
 * desc：粉丝列表
 */
@SuppressLint("InflateParams")
class StFansListActivity : BaseMvvmActivity<ActivityStFansListBinding, StFansListViewModel>() {
    private var strategyId: String? = null

    private val mAdapter by lazy {
        StFansListAdapter().apply {
            setEmptyView(NoDataView(this@StFansListActivity).apply {
                setHintMessage(getString(R.string.no_records_found))
            })
        }
    }
    private var pageNum = 1 //当前页码
    private val pageSize = 20 //每页请求数量

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        strategyId = intent.getStringExtra("strategyId") ?: ""
    }

    override fun initView() {
        initRecyclerView()
    }

    override fun initData() {
        refreshData()
    }

    override fun createObserver() {
        mViewModel.fansListLiveData.observe(this) {
            if (pageNum == 1) {
                if ((it?.fansList?.size ?: 0) > 0) {
                    mAdapter.setList(it?.fansList)
                } else {
                    mAdapter.setList(null)
                }
                mBinding.mRefreshLayout.finishRefresh()
            } else {
                it?.fansList?.let { list ->
                    mAdapter.addData(list)
                }
            }
            if ((it?.fansList?.size ?: 0) < pageSize) { //加载结束
                mBinding.mRefreshLayout.finishLoadMoreWithNoMoreData()
            } else { //加载完成还有下一页
                mBinding.mRefreshLayout.finishLoadMore(true)
            }
        }

        //请求失败
        mViewModel.reqErrLiveData.observe(this) {
            ToastUtil.showToast(it)
            if (pageNum == 1) {
                mBinding.mRefreshLayout.finishRefresh()
            } else {
                mBinding.mRefreshLayout.finishLoadMore(Constants.finishRefreshOrMoreTime, false, true)
            }
        }
    }

    override fun initListener() {
        mBinding.mRefreshLayout.setOnRefreshListener {
            refreshData()
        }
        mBinding.mRefreshLayout.setOnLoadMoreListener {
            loadMoreData()
        }
    }

    private fun initRecyclerView() {
        mBinding.mRecyclerView.adapter = mAdapter
        mBinding.mRecyclerView.setHasFixedSize(true)
    }

    /**
     * 下拉刷新
     */
    private fun refreshData() {
        pageNum = 1 //下拉刷新，需要重置页数
        mViewModel.stFansListApi(strategyId, pageNum, pageSize)
    }

    /**
     * 上拉加载更多
     */
    private fun loadMoreData() {
        pageNum++
        mViewModel.stFansListApi(strategyId, pageNum, pageSize)
    }

    companion object {
        fun open(context: Context, strategyId: String?) {
            val intent = Intent(context, StFansListActivity::class.java)
            intent.putExtra("strategyId", strategyId)
            context.startActivity(intent)
        }
    }
}