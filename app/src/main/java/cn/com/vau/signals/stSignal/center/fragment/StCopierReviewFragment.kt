package cn.com.vau.signals.stsignal.center.fragment

import android.annotation.SuppressLint
import android.widget.TextView
import androidx.fragment.app.*
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.databinding.FragmentStSignalCenterCopierReviewBinding
import cn.com.vau.signals.stsignal.center.vm.StSignalCenterViewModel
import cn.com.vau.util.*

/**
 * author：lvy
 * date：2024/04/03
 * desc：信号源中心->Copier Review
 */
@SuppressLint("SetTextI18n")
class StCopierReviewFragment : BaseMvvmBindingFragment<FragmentStSignalCenterCopierReviewBinding>() {

    private val mActivityVM: StSignalCenterViewModel by activityViewModels()

    private var pendingFrag: StCopierReviewPendingFragment? = null //待审核
    private var approvedFrag: StCopierReviewApprovedFragment? = null //通过
    private var rejectedFrag: StCopierReviewApprovedFragment? = null //拒绝

    override fun initView() {
        initTabLayout()
    }

    override fun initListener() {
        //StSignalCenterActivity页面请求的接口
        mActivityVM.copyPageTotalsLiveData.observe(this) {
            refreshTabTotals() //刷新tab上面的数量
        }
        //审核通过或拒绝，切换到对应tab
        mActivityVM.refreshCopierReviewListLiveData.observe(this) {
            when (it) {
                1 -> { //通过
                    mBinding.viewPager2.currentItem = 1
                    approvedFrag?.refreshData()
                }

                2 -> { //拒绝
                    mBinding.viewPager2.currentItem = 2
                    rejectedFrag?.refreshData()
                }
            }
        }
    }

    /**
     * 初始化tab
     */
    private fun initTabLayout() {
        val fragments = arrayListOf<Fragment>()
        pendingFrag = StCopierReviewPendingFragment()
        fragments.add(pendingFrag!!)
        approvedFrag = StCopierReviewApprovedFragment.newInstance(1)
        fragments.add(approvedFrag!!)
        rejectedFrag = StCopierReviewApprovedFragment.newInstance(2)
        fragments.add(rejectedFrag!!)

        val titleList = arrayListOf<String>()
        titleList.add(getString(R.string.pending))
        titleList.add(getString(R.string.approved))
        titleList.add(getString(R.string.rejected))

        mBinding.viewPager2.init(fragments, titleList, childFragmentManager, this)
        mBinding.viewPager2.offscreenPageLimit = fragments.size
        mBinding.tabLayout.setVp(mBinding.viewPager2, titleList, TabType.WRAP_INDICATOR)
    }

    /**
     * 刷新tab上面的数量
     */
    private fun refreshTabTotals() {
        mActivityVM.copyPageTotalsLiveData.value?.let {
            //待审核
            val tvTab0 = mBinding.tabLayout.getChildAt(0)?.findViewById<TextView>(R.id.tvTab)
            tvTab0?.text = "${getString(R.string.pending)}(${it.pending ?: "0"})"
            //通过
            val tvTab1 = mBinding.tabLayout.getChildAt(1)?.findViewById<TextView>(R.id.tvTab)
            tvTab1?.text = "${getString(R.string.approved)}(${it.approved ?: "0"})"
            //拒绝
            val tvTab2 = mBinding.tabLayout.getChildAt(2)?.findViewById<TextView>(R.id.tvTab)
            tvTab2?.text = "${getString(R.string.rejected)}(${it.rejected ?: "0"})"
        }
    }

    /**
     * 下拉刷新
     */
    fun refreshData() {
        when (mBinding.viewPager2.currentItem) {
            0 -> pendingFrag?.refreshData()
            1 -> approvedFrag?.refreshData()
            2 -> rejectedFrag?.refreshData()
        }
    }

    companion object {
        fun newInstance(): StCopierReviewFragment {
            return StCopierReviewFragment()
        }
    }
}