package cn.com.vau.signals.stsignal.activity

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.VAUStartUtil
import cn.com.vau.common.view.expandabletextview.app.StatusType
import cn.com.vau.common.view.share.ShareHelper
import cn.com.vau.data.enums.EnumStrategyFollowState
import cn.com.vau.data.strategy.StrategyBean
import cn.com.vau.databinding.ActivityStStrategyDetailsBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.page.user.openSameNameAccount.OpenSameNameAccountActivity
import cn.com.vau.signals.stsignal.center.activity.StSignalCenterActivity
import cn.com.vau.signals.stsignal.fragment.StStrategyDetailsOverviewFragment
import cn.com.vau.signals.stsignal.fragment.StStrategyDetailsPortfolioFragment
import cn.com.vau.signals.stsignal.viewmodel.StStrategyDetailsViewModel
import cn.com.vau.trade.st.StrategyOrderBaseData
import cn.com.vau.trade.st.activity.StStrategyOrdersActivity
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.dialog.*
import kotlin.math.abs

/**
 * author：lvy
 * date：2024/03/27
 * desc：策略详情
 */
@SuppressLint("SetTextI18n")
class StStrategyDetailsActivity : BaseMvvmActivity<ActivityStStrategyDetailsBinding, StStrategyDetailsViewModel>() {

    private val img_gs_shield by lazy { ContextCompat.getDrawable(this, R.drawable.img_gs_shield) }

    private var strategyId: String? = null

    private var mBean: StrategyBean? = null

    private val bottomTipsDialog by lazy {
        BottomContentDialog.Builder(this)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        strategyId = intent.getStringExtra(Constants.STRATEGY_ID)
    }

    override fun initView() {
        initTabLayout()
    }

    override fun initData() {
        mViewModel.stStrategyDetailTopApi(strategyId)
        mViewModel.stStrategyFansCountApi(strategyId)
    }

    override fun createObserver() {
        // 策略详情顶部卡片
        mViewModel.strategyDetailTopLiveData.observe(this) {
            mBean = it
            setData()
        }
        // 获取申请开通mt4账户号类型
        mViewModel.accountTypeLiveData.observe(this) { obj ->
            if (obj?.applyTpe == 2) {
                openActivity(OpenSameNameAccountActivity::class.java, bundleOf().apply {
                    putBoolean("isSelectedCopyTrading", true)
                })
            } else {
                obj?.let {
                    VAUStartUtil.openAccountGuide(this, it, 0, isSelectedCopyTrading = true)
                }
            }
        }
        // 策略粉丝数量
        mViewModel.strategyFansCountLiveData.observe(this) {
            mBinding.tvCollectCount.text = if (it.totalCount.mathCompTo("999") == 1) "1K+" else it.totalCount
            mBinding.ivCollect.setImageResource(
                if (it.watched == true) R.drawable.bitmap2_favorite_cf44040 else R.drawable.draw_bitmap2_favorite12x12_c1e1e1e_cebffffff
            )

            // 刷新上个页面数据
            intent.putExtra("strategyId", strategyId)
            intent.putExtra("watchingStatus", it.watched)
            setResult(RESULT_OK, intent)
        }
        // 点击收藏策略的状态
        mViewModel.followStrategyStatusLiveData.observe(this) {
            mViewModel.sensorsTrack(mBean, true, if (it) "Like" else "Unlike") // 神策埋点，按钮点击事件
        }
    }

    override fun initListener() {
        // 标题是否显示
        mBinding.appbarLayout.addOnOffsetChangedListener { appBarLayout, verticalOffset ->
            val needShow = (abs(verticalOffset) >= appBarLayout.totalScrollRange)
            mBinding.mHeaderBar.getTitleView()?.isInvisible = !needShow
        }
        // 标题栏
        mBinding.mHeaderBar.apply {
            setEndIconClickListener {
                if (!UserDataUtil.isLogin()) {
                    openActivity(LoginActivity::class.java)
                    return@setEndIconClickListener
                }
                if (mBean?.offLine == true) { // 策略已下架，弹框提示
                    CenterActionDialog.Builder(this@StStrategyDetailsActivity)
                        .setContent(getString(R.string.this_strategy_has_signal_provider)) //设置内容
                        .setSingleButton(true) //展示一个按钮，默认两个按钮
                        .setSingleButtonText(getString(R.string.ok)) //设置单个按钮文本
                        .build()
                        .showDialog()
                    return@setEndIconClickListener
                }
                ShareHelper.strategyShare(
                    this@StStrategyDetailsActivity,
                    strategyId = strategyId,
                    avatarUrl = mViewModel.strategyDetailTopLiveData.value?.avatar,
                    strategyName = mViewModel.strategyDetailTopLiveData.value?.strategyName,
                    strategyNo = mViewModel.strategyDetailTopLiveData.value?.strategyNo,
                )
                LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_STRATEGY_DETAILS_SHARE_BTN_CLICK)
            }
            setEndIcon1ClickListener {
                openActivity(HelpCenterActivity::class.java)
            }
        }
        // 该策略所属信号源，跳转到信号源详情页
        mBinding.tvProvider.clickNoRepeat {
            val stUserId = mBean?.summaryData?.stUserId ?: return@clickNoRepeat
            StSignalDetailsActivity.open(this, stUserId)
            LogEventUtil.setLogEvent(
                BuryPointConstant.V348.CT_SP_PAGE_VIEW, bundleOf(
                    "Type_of_account" to when {
                        !UserDataUtil.isLogin() -> BuryPointConstant.AccountType.NOLOGIN
                        UserDataUtil.isStLogin() -> "Copy Trading"
                        UserDataUtil.isDemoAccount() -> BuryPointConstant.AccountType.DEMO
                        else -> BuryPointConstant.AccountType.LIVE
                    },
                    "Position" to "Strategy",
                    "Signal_provider_ID" to stUserId.ifNull()
                )
            )
        }
        // 简介。第二个参数false 只触发点击 不真正触发展开和收回操作
        mBinding.tvIntro.setExpandOrContractClickListener({
            if (it == StatusType.STATUS_EXPAND) {
                val intro = mBean?.comments ?: return@setExpandOrContractClickListener
                showBtoPpw("", intro)
            }
        }, false)
        // 风险度说明
        mBinding.tvRiskKey.clickNoRepeat {
            showBtoPpw(getString(R.string.risk_band), getString(R.string.the_risk_band_the_the_here_date_status))
        }
        // 分润比例，跳转到分润示例页
        mBinding.tvProfitSharingKey.clickNoRepeat {
            val url = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/socialTrading/profitSharingCalculation"
            NewHtmlActivity.openActivity(this@StStrategyDetailsActivity, url = url, title = getString(R.string.more_illustrations))
        }
        // 关注按钮
        mBinding.ivCollect.setOnClickListener {
            favouriteClickWithPoint()
        }
        mBinding.tvCollectCount.setOnClickListener {
            favouriteClickWithPoint()
        }
        // 底部按钮
        mBinding.tvNext.clickNoRepeat {
            btoActionClick()
            val bundle = Bundle()
            bundle.putString("Strategy_ID", strategyId)
            bundle.putString(
                "Type_of_account", when {
                    !UserDataUtil.isLogin() -> BuryPointConstant.AccountType.NOLOGIN
                    UserDataUtil.isStLogin() -> "Copy trading"
                    UserDataUtil.isDemoAccount() -> BuryPointConstant.AccountType.DEMO
                    else -> BuryPointConstant.AccountType.LIVE
                }
            )
            LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_STRATEGY_COPY_BTN_CLICK, bundle)
        }
    }

    private fun favouriteClickWithPoint() {
        favouriteClick()
        val bundle = Bundle()
        bundle.putString("Strategy_ID", strategyId)
        LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_STRATEGY_FAVOURITE_BTN_CLICK, bundle)
    }

    /**
     * 关注按钮
     */
    private fun favouriteClick() {
        /*
        1、点击空心按钮关注策略，变成红心并toast提示：Added to Favourite，已关注的策略会显示在Profile的关注页
        2、点击红心按钮取消关注策略，变成空心并toast提示：Removed，从关注列表里移除策略
        3、关注按钮下显示关注者数，超过999个后显示固定值1k+
        4、策略下架后也可以关注和取消关注
        */
        if (UserDataUtil.isLogin() || UserDataUtil.isStLogin()) {
            if (!UserDataUtil.isOpenStAccount()) {
                CenterActionDialog.Builder(this@StStrategyDetailsActivity)
                    // 开通跟单账户后才可进行跟单
                    .setContent(getString(R.string.please_open_or_switch_to_copy_trading_account_to_proceed)) //设置内容
                    .setStartText(getString(R.string.cancel))
                    .setEndText(getString(R.string.ok))
                    .setOnEndListener {
                        if (SpManager.isV1V2()) {
                            KycVerifyHelper.showKycDialog(
                                this,
                                mapOf(
                                    Constants.GoldParam.CODE to Constants.GoldParam.CODE_OPEN_ACCOUNT,
                                    Constants.GoldParam.CREATE_COPY_TRADING_ACCOUNT to "1"
                                )
                            )
                        } else {
                            mViewModel.queryStAccountTypeApi()
                        }
                    }
                    .build()
                    .showDialog()
            } else if (UserDataUtil.isOpenStAccount() && UserDataUtil.isStLogin()) {
                mViewModel.initStrategyFollow(strategyId)
            } else if ((!UserDataUtil.isStLogin())) {
                CenterActionDialog.Builder(this@StStrategyDetailsActivity)
                    // 请切换到跟单账户进行跟单管理
                    .setContent(getString(R.string.please_open_or_switch_to_copy_trading_account_to_proceed)) //设置内容
                    .setEndText(getString(R.string.confirm))
                    .setOnEndListener {
                        openActivity(AccountManagerActivity::class.java, Bundle().apply {
                            putInt(Constants.IS_FROM, 2)
                        })
                    }
                    .build()
                    .showDialog()
            }
        } else {
            openActivity(LoginActivity::class.java)
        }
    }

    /**
     * 底部按钮
     */
    private fun btoActionClick() {
        /*
        底部按钮点击事件
        1、未登录点击Copy和关注按钮，跳登录注册页
        2、Live和Demo点击Copy和关注按钮，出现弹窗提示，点击跳转到账户列表页
        3、未跟单，点击跳转跟单策略下单页
        4、正在跟单（包括暂停跟单），点击跳转策略订单详情页
        5、待审核，点击跳转策略订单详情页
        6、是自己的策略，公开策略：点击跳转信号源中心的公开列表，下架策略：点击跳转信号源中心的下架列表
         */
        if (UserDataUtil.isLogin() || UserDataUtil.isStLogin()) {
            if (!UserDataUtil.isOpenStAccount()) {
                CenterActionDialog.Builder(this)
                    // 请开通或切换至跟单账户后进行操作
                    .setContent(getString(R.string.please_open_or_switch_to_copy_trading_account_to_proceed)) //设置内容
                    .setStartText(getString(R.string.cancel))
                    .setEndText(getString(R.string.ok))
                    .setOnEndListener {
                        if (SpManager.isV1V2()) {
                            KycVerifyHelper.showKycDialog(
                                this,
                                mapOf(
                                    Constants.GoldParam.CODE to Constants.GoldParam.CODE_OPEN_ACCOUNT,
                                    Constants.GoldParam.CREATE_COPY_TRADING_ACCOUNT to "1"
                                )
                            )
                        } else {
                            mViewModel.queryStAccountTypeApi()
                        }
                    }
                    .build()
                    .showDialog()
            } else if (UserDataUtil.isOpenStAccount() && UserDataUtil.isStLogin()) {
                if (mBean?.owner == true) { // 自己看自己
                    if (mBean?.offLine == true) { // 下架的策略，点击跳转信号源中心的下架列表
                        startActivity(StSignalCenterActivity.createIntent(this, strategiesFragTabIndex = 1))
                    } else { // 公开的策略，点击跳转信号源中心的公开列表
                        startActivity(StSignalCenterActivity.createIntent(this, strategiesFragTabIndex = 0))
                    }
                } else {
                    if (mBean?.pendingApplyApproval == true) { // 待审核，点击跳转策略订单详情页 setting tab
                        openActivity(StStrategyOrdersActivity::class.java, Bundle().apply {
                            putSerializable("data_strategy", StrategyOrderBaseData().apply {
                                this.type = EnumStrategyFollowState.PENDING_REVIEW
                                this.signalStrategyId = mBean?.summaryData?.strategyId
                                this.portfolioId = mBean?.summaryData?.portfolioId
                                this.followRequestId = mBean?.summaryData?.followRequestId
                            })
                        })
                        mViewModel.sensorsTrack(mBean, true, "Manage") // 神策埋点，按钮点击事件
                    } else { // 审核通过或拒绝等其他状态
                        if (mBean?.followerStatus == true) { // 跟单状态，跳转策略订单详情页 默认 tab
                            openActivity(StStrategyOrdersActivity::class.java, Bundle().apply {
                                putSerializable("data_strategy", StrategyOrderBaseData().apply {
                                    this.type = EnumStrategyFollowState.OPEN
                                    this.signalStrategyId = mBean?.summaryData?.strategyId
                                    this.portfolioId = mBean?.summaryData?.portfolioId
                                    this.followRequestId = mBean?.summaryData?.followRequestId
                                })
                            })
                            mViewModel.sensorsTrack(mBean, true, "Manage") // 神策埋点，按钮点击事件
                        } else { // 未跟单，点击跳转跟单策略下单页
                            if (mBean?.offLine == true) { // 策略已下架，弹框提示
                                CenterActionDialog.Builder(this@StStrategyDetailsActivity)
                                    .setContent(getString(R.string.this_strategy_has_signal_provider)) //设置内容
                                    .setSingleButton(true) //展示一个按钮，默认两个按钮
                                    .setSingleButtonText(getString(R.string.ok)) //设置单个按钮文本
                                    .build()
                                    .showDialog()
                                return
                            }
                            // 只读跟单账户不允许进入跟单下单页 && 不是V1V2的监管 才会进入
                            if (UserDataUtil.isReadOnly() && !SpManager.isV1V2()) {
                                CenterActionDialog.Builder(this)
                                    .setContent(getString(R.string.your_account_is_trade_now)) //设置内容
                                    .setSingleButton(true) //展示一个按钮，默认两个按钮
                                    .setSingleButtonText(getString(R.string.confirm)) //设置单个按钮文本
                                    .build()
                                    .showDialog()
                                return
                            }
                            StStrategyCopyActivity.open(this, strategyId)
                            mViewModel.sensorsTrack(mBean, true, "Copy") // 神策埋点，按钮点击事件
                        }
                    }
                }
            } else if ((!UserDataUtil.isStLogin())) {
                CenterActionDialog.Builder(this)
                    // 请开通或切换至跟单账户后进行操作
                    .setContent(getString(R.string.please_open_or_switch_to_copy_trading_account_to_proceed)) //设置内容
                    .setEndText(getString(R.string.confirm))
                    .setOnEndListener {
                        openActivity(AccountManagerActivity::class.java, Bundle().apply {
                            putInt(Constants.IS_FROM, 2)
                        })
                    }
                    .build()
                    .showDialog()
            }
        } else {
            openActivity(LoginActivity::class.java)
        }
    }

    /**
     * 初始化tab
     */
    private fun initTabLayout() {
        val fragments = arrayListOf<Fragment>()
        fragments.add(StStrategyDetailsOverviewFragment.newInstance(strategyId))
        fragments.add(StStrategyDetailsPortfolioFragment.newInstance(strategyId))

        val titleList = arrayListOf<String>()
        titleList.add(getString(R.string.overview))
        titleList.add(getString(R.string.portfolio))

        mBinding.viewPager2.isUserInputEnabled = false // 禁止滑动
        mBinding.viewPager2.init(fragments, titleList, supportFragmentManager, this)
        mBinding.mTabLayout.setVp(mBinding.viewPager2, titleList, TabType.LINE_INDICATOR) {
            mViewModel.sensorsTrackTabClick(mBean, if (it == 0) "Overview" else "Portfolio")
        }
    }

    /**
     * 设置页面数据
     */
    private fun setData() {
        mBean?.let {
            /*
            点击右上角分享按钮，进入分享流程
            信号源自己浏览自己的策略时，如果是公开策略，则展示分享按钮
            信号源自己浏览自己的策略时，如果是已经下架的策略，则不展示分享按钮
             */
            mBinding.mHeaderBar.apply {
                if (it.owner) {
                    setEndIconVisible(!it.offLine)
                } else {
                    setEndIconVisible(true)
                }
            }
            // 头像
            ImageLoaderUtil.loadImage(this, it.avatar, mBinding.ivAvatar, R.mipmap.ic_launcher)
            // 策略昵称
            mBinding.tvNick.text = it.strategyName
            mBinding.tvNick.setCompoundDrawablesWithIntrinsicBounds(if (it.isProfitShieldStrategy == true) img_gs_shield else null, null, null, null)
            mBinding.tvNick.compoundDrawablePadding = if (it.isProfitShieldStrategy == true) 4.dp2px() else 0
            mBinding.mHeaderBar.setTitleText(it.strategyName)
            // 所属信号源的昵称
            mBinding.tvProviderDesc.text = "${getString(R.string.provider)}："
            mBinding.tvProvider.text = it.nickname
            // 策略id
            mBinding.tvStrategyId.text = "${getString(R.string.strategy_id)}：${it.strategyNo}".arabicReverseTextByFlag("：")
            // 策略简介
            if (it.comments.isNullOrBlank()) {
                mBinding.tvIntro.isVisible = false
            } else {
                mBinding.tvIntro.isVisible = true
                mBinding.tvIntro.setContent(it.comments)
            }
            // 汇总数据
            it.summaryData?.let { summary ->
                // 三个月回报率
                val returnRate = (summary.returnRate ?: "0").percent()
                mBinding.tvRoi.text = "${returnRate}%"
                mBinding.tvRoi.setTextColor(getReturnRateColor(returnRate.toDoubleCatching()))
                // 该策略的跟单者人数
                mBinding.tvCopiers.text = "${summary.copiers}"
                // 最近一天风险档位
                mBinding.tvRisk.text = "${summary.riskLevel}"
                mBinding.tvRisk.setTextColor(
                    ContextCompat.getColor(
                        this, when (summary.riskLevel) {
                            "4", "5", "6" -> R.color.cff8e5c
                            "7", "8", "9", "10" -> R.color.ce35728
                            else -> R.color.c00c79c
                        }
                    )
                )
                // 分润比例，对于已经跟单的跟单者，展示跟单者适用的分润比例，对于正在待审核的策略，显示提交跟单申请时的分润比例，
                // 对于未跟单用户，展示该策略最新的分润比例
                val profitRatio = summary.profitShareRatio.ifNull().percent(0)
                mBinding.tvProfitSharing.text = "${profitRatio}%"
            }
            /*
            底部按钮：
            1、未登录、Live和Demo只显示Copy按钮
            2、对于没跟单的用户，展示‘Copy’，对于已经跟单的人，展示‘Manage’，待审核状态，展示‘Manage’
            3、信号源自己浏览自己的策略时，底部的操作按钮显示为‘Edit’
            4、如果是信号源自己的策略，不显示关注按钮
            */
            if (UserDataUtil.isStLogin()) {
                if (it.owner) { // 自己看自己
                    mBinding.tvNext.text = getString(R.string.edit_strategy)
                    mBinding.ivCollect.isVisible = false
                    mBinding.tvCollectCount.isVisible = false
                } else {
                    if (it.followerStatus == true || it.pendingApplyApproval) {
                        mBinding.tvNext.text = getString(R.string.manage)
                    } else { // 没跟单
                        mBinding.tvNext.text = getString(R.string.copy)
                    }
                    mBinding.ivCollect.isVisible = true
                    mBinding.tvCollectCount.isVisible = true
                }
            } else {
                mBinding.tvNext.text = getString(R.string.copy)
                mBinding.ivCollect.isVisible = true
                mBinding.tvCollectCount.isVisible = true
            }

            mViewModel.sensorsTrack(mBean) // 神策埋点，页面浏览事件
        }
    }

    private fun getReturnRateColor(value: Double) = when {
        value > 0 -> ContextCompat.getColor(this, R.color.c00c79c)
        value == 0.0 -> AttrResourceUtil.getColor(this, R.attr.color_c1e1e1e_cebffffff)
        else -> ContextCompat.getColor(this, R.color.ce35728)
    }

    /**
     * 说明文案底部弹框
     */
    private fun showBtoPpw(title: String, intro: String) {
        bottomTipsDialog.setTitle(title)
            .setContent(intro)
            .build()
            .showDialog()
    }

    companion object {
        fun open(context: Context?, strategyId: String? = null) {
            val intent = Intent(context, StStrategyDetailsActivity::class.java)
            intent.putExtra(Constants.STRATEGY_ID, strategyId)
            context?.startActivity(intent)
        }

        fun createIntent(context: Context?, strategyId: String? = null) =
            Intent(context, StStrategyDetailsActivity::class.java).apply {
                putExtra(Constants.STRATEGY_ID, strategyId)
            }
    }
}