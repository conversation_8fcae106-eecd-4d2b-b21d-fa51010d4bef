package cn.com.vau.signals.stsignal.fragment

import android.os.Bundle
import android.text.InputType
import android.view.*
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.*
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.application.VauApplication
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.utils.VAUStartUtil
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.popup.BottomCommunityFilterDialog
import cn.com.vau.common.view.popup.adapter.StCommunityFilterBean
import cn.com.vau.common.vm.MainViewModel
import cn.com.vau.data.account.MT4AccountTypeObj
import cn.com.vau.databinding.*
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.page.user.openSameNameAccount.OpenSameNameAccountActivity
import cn.com.vau.signals.stsignal.activity.StSignalSearchActivity
import cn.com.vau.signals.stsignal.activity.StStrategyDetailsActivity
import cn.com.vau.signals.stsignal.adapter.StCommunityAdapter
import cn.com.vau.signals.stsignal.viewmodel.StCommunityViewModel
import cn.com.vau.util.*
import cn.com.vau.util.opt.PerfTraceUtil
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.widget.NoDataView
import cn.com.vau.util.widget.dialog.BottomSelectListDialog
import cn.com.vau.util.widget.dialog.CenterActionDialog
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.*

/**
 * 策略社区
 * Author: GG
 * Date: 2024/3/22
 * Description:
 */
class StCommunityFragment : BaseMvvmBindingFragment<FragmentStCommunityBinding>() {

    private val activityViewModel by activityViewModels<MainViewModel>()

    private val mViewModel: StCommunityViewModel by viewModels(ownerProducer = { requireParentFragment() })

    private val headerSearch by lazy { HeaderRecyclerStCommunitySearchBinding.inflate(layoutInflater) }
    private val mergeSearchBinding by lazy { MergeSearchBinding.bind(headerSearch.root) }
    private val headerFilter by lazy { HeaderRecyclerStCommunityFilterBinding.inflate(layoutInflater) }

    private val communityAdapter by lazy {
        StCommunityAdapter().apply {
            setEmptyView(NoDataView(requireContext()).apply {
                setHintMessage(getString(R.string.no_records_found))
            })
            headerWithEmptyEnable = true
        }
    }

    private val arrowUpDrawable by lazy {
        ContextCompat.getDrawable(VauApplication.context, R.drawable.icon_source2_community_return_top)?.apply {
            setBounds(0, 0, intrinsicWidth, intrinsicHeight)
        }
    }
    private val arrowDownDrawable by lazy {
        ContextCompat.getDrawable(VauApplication.context, R.drawable.icon_source2_community_return_bottom)?.apply {
            setBounds(0, 0, intrinsicWidth, intrinsicHeight)
        }
    }

    private val sortList by lazy { listOf(getString(R.string.rating), getString(R.string.signal_filter_detail_return), getString(R.string.copiers), getString(R.string.win_rate), getString(R.string.risk_band)) }

    private val filterPopup by lazy { BottomCommunityFilterDialog.Builder(requireActivity()).build() }

    private val tradeTypePopup by lazy {
        BottomSelectListDialog.Builder(requireActivity())
            .setTitle(getString(R.string.sort_by))
            .setSelectIndex(mViewModel.selectQuickIndex)
            .setDataList(
                arrayListOf(
                    getString(R.string.rating_high_to_low),
                    getString(R.string.return_high_to_low),
                    getString(R.string.copiers_high_to_low),
                    getString(R.string.win_rate_high_to_low),
                    getString(R.string.risk_band_low_to_high),
                )
            )
            .setOnItemClickListener { position ->
                updateQuickFilter(position)
                mViewModel.sensorsTrack(position)
            }
            .build()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        PerfTraceUtil.startTrace(PerfTraceUtil.StartTrace.Perf_v6_Signals_strategy_Create_First)
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        EventBus.getDefault().register(this)
        mViewModel.dataTimeBean = StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = getString(R.string.last_x_months, "3"), requestData = "3")
        mViewModel.dataRatingBean = StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = getString(R.string.all), requestData = "0", tag = "2")
    }

    override fun initView() {
        PerfTraceUtil.firstFrameTrace(mBinding.root, PerfTraceUtil.StartTrace.Perf_v6_Signals_strategy_Create_First, PerfTraceUtil.StartTrace.Perf_v6_Signals_strategy_First_Finish)
        mBinding.rvView.layoutManager = WrapContentLinearLayoutManager(activity)
        mBinding.rvView.isNestedScrollingEnabled = false
        mBinding.rvView.adapter = communityAdapter
        communityAdapter.removeAllHeaderView()
        communityAdapter.addHeaderView(headerSearch.root)
        communityAdapter.addHeaderView(headerFilter.root)
        mBinding.sflView.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {
                mViewModel.refresh()
            }

            override fun onLoadMore(refreshLayout: RefreshLayout) {
                mViewModel.loadMore()
            }
        })
        mergeSearchBinding.etSearch.setHint(R.string.search_strategy_or_signal_provider)
        mergeSearchBinding.etSearch.isFocusable = false
        mergeSearchBinding.etSearch.isFocusableInTouchMode = false
        mergeSearchBinding.etSearch.inputType = InputType.TYPE_NULL

        filterPopup.confirmCallback { dataTimeBean, dataRatingBean, dataReturnBean, dataRiskBandBean, dataWinRateBean, dataTradingBean ->
            mViewModel.dataTimeBean = dataTimeBean
            mViewModel.dataRatingBean = dataRatingBean
            mViewModel.dataReturnBean = dataReturnBean
            mViewModel.dataRiskBandBean = dataRiskBandBean
            mViewModel.dataWinRateBean = dataWinRateBean
            mViewModel.dataTradingBean = dataTradingBean
            resetRecyclerViewToTop()
            showLoadDialog()
            mViewModel.refresh()
        }

        communityAdapter.setOnItemClickListener { _, _, position ->
            //跳转策略详情
            communityAdapter.data.getOrNull(position)?.let {
                StStrategyDetailsActivity.open(requireContext(), it.strategyId)
                LogEventUtil.setLogEvent(
                    BuryPointConstant.V348.CT_STRATEGY_PAGE_VIEW, bundleOf(
                        "Type_of_account" to when {
                            !UserDataUtil.isLogin() -> BuryPointConstant.AccountType.NOLOGIN
                            UserDataUtil.isStLogin() -> "Copy Trading"
                            UserDataUtil.isDemoAccount() -> BuryPointConstant.AccountType.DEMO
                            else -> BuryPointConstant.AccountType.LIVE
                        },
                        "Position" to "Community",
                        "Strategy_ID" to it.strategyId.ifNull()
                    )
                )

                // 神策自定义埋点(v3500)
                mViewModel.sensorsTrack(it.strategyId.ifNull(), position, "")
            }
        }
        mBinding.layoutFilter.root.isVisible = mViewModel.totalScrollDistance >= headerSearch.root.measuredHeight && mViewModel.totalScrollDistance != 0
        quickFilterView(mViewModel.selectQuickIndex)
    }

    override fun lazyLoadData() {
        super.lazyLoadData()
        // 创建一个变量来保存滑动总距离
        mBinding.rvView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                // 累加垂直滑动的距离
                mViewModel.totalScrollDistance += dy
                if (mViewModel.totalScrollDistance >= headerSearch.root.measuredHeight && mViewModel.totalScrollDistance != 0) {
                    if (!mBinding.layoutFilter.root.isVisible)
                        mBinding.layoutFilter.root.isVisible = true
                } else {
                    if (mBinding.layoutFilter.root.isVisible)
                        mBinding.layoutFilter.root.isVisible = false
                }
            }
        })

    }

    override fun initListener() {
        super.initListener()
        mergeSearchBinding.etSearch.clickNoRepeat {
            if (checkStPermission())
                return@clickNoRepeat
            openActivity(StSignalSearchActivity::class.java)
        }
        headerFilter.tvQuickFilter.clickNoRepeat {
            quickFilterClick()
        }
        headerFilter.tvFilter.clickNoRepeat {
            filterClick()
        }
        mBinding.layoutFilter.root.setOnClickListener(null)
        mBinding.layoutFilter.tvQuickFilter.clickNoRepeat {
            quickFilterClick()
        }
        mBinding.layoutFilter.tvFilter.clickNoRepeat {
            filterClick()
        }
    }

    private fun quickFilterClick() {
        if (checkStPermission())
            return
        tradeTypePopup.setSelectIndex(mViewModel.selectQuickIndex)
        tradeTypePopup.showDialog()
        LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_COMMUNITY_SORT_BTN_CLICK)
    }

    private fun filterClick() {
        if (checkStPermission())
            return
        filterPopup.updateTradingInfo(VAUSdkUtil.shareGoodList().map { it.groupname })
        filterPopup.setData(
            dataTimeBean = mViewModel.dataTimeBean,
            dataRatingBean = mViewModel.dataRatingBean,
            dataReturnBean = mViewModel.dataReturnBean,
            dataRiskBandBean = mViewModel.dataRiskBandBean,
            dataWinRateBean = mViewModel.dataWinRateBean,
            dataTradingBean = mViewModel.dataTradingBean
        )
        filterPopup.show()

        LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_COMMUNITY_FILTER_BTN_CLICK)
    }

    override fun createObserver() {
        super.createObserver()
        //策略列表的接口请求
        mViewModel.uiListLiveData.observeUIState(viewLifecycleOwner, communityAdapter, mBinding.sflView, before = {
            hideLoadDialog()
            communityAdapter.filterType = mViewModel.overallSort
        }, after = {
            PerfTraceUtil.stopTrace(PerfTraceUtil.StartTrace.Perf_v6_Signals_strategy_First_Finish)
        })
        //跟单首页的策略推荐跳转过来，进行对应的请求
        activityViewModel.communityLiveData.observe(this) {
            it?.let { event ->
                mViewModel.clearData()
                when (event) {
                    Constants.STRATEGY_MOST_COPIED -> {
                        mViewModel.dataTimeBean = StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = getString(R.string.last_x_months, "3"), requestData = "3", tag = "1")
                        updateQuickFilter(1)
                    }

                    Constants.STRATEGY_HIGHEST_RETURN -> {
                        mViewModel.dataTimeBean = StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = getString(R.string.last_x_months, "12"), requestData = "12", tag = "1")
                        updateQuickFilter(0)
                    }

                    Constants.STRATEGY_LOW_RISK_RETURN -> {
                        mViewModel.dataTimeBean = StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = getString(R.string.last_x_months, "3"), requestData = "3", tag = "1")
                        mViewModel.dataReturnBean = StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = ">50%", requestData = "0.5", tag = "2")
                        mViewModel.dataRiskBandBean = StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = "<=6", requestData = "6", tag = "3")
                        updateQuickFilter(0)
                    }

                    Constants.STRATEGY_HIGH_WIN_RATE -> {
                        mViewModel.dataTimeBean = StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = getString(R.string.last_x_months, "3"), requestData = "3", tag = "1")
                        mViewModel.dataReturnBean = StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = ">50%", requestData = "0.5", tag = "2")
                        mViewModel.dataWinRateBean = StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = ">70%", requestData = "0.7", tag = "4")
                        updateQuickFilter(2)
                    }
                }
                activityViewModel.communityLiveData.value = null
            }
        }
        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED).collectLatest {
                val data = it as? MT4AccountTypeObj ?: return@collectLatest
                if (data.applyTpe == 2) {
                    openActivity(OpenSameNameAccountActivity::class.java, bundleOf().apply {
                        putBoolean("isSelectedCopyTrading", true)
                    })
                } else {
                    data.let {
                        VAUStartUtil.openAccountGuide(requireActivity(), it, 0, isSelectedCopyTrading = true)
                    }
                }
            }
        }
    }

    /**
     * 更新快速筛选按钮样式和文案
     */
    private fun updateQuickFilter(position: Int) {
        quickFilterView(position)
        if (communityAdapter.data.isNotEmpty()) {
            showLoadDialog()
        }
        mViewModel.refresh()
        resetRecyclerViewToTop()
    }

    private fun quickFilterView(position: Int) {
        headerFilter.tvQuickFilter.text = sortList.getOrNull(position)
        mBinding.layoutFilter.tvQuickFilter.text = sortList.getOrNull(position)
        mViewModel.selectQuickIndex = position
        when (position) {
            0 -> {
                headerFilter.tvQuickFilter.setCompoundDrawablesRelativeWithIntrinsicBounds(arrowDownDrawable, null, null, null)
                mBinding.layoutFilter.tvQuickFilter.setCompoundDrawablesRelativeWithIntrinsicBounds(arrowDownDrawable, null, null, null)
                mViewModel.overallSort = OVERALLSORT_RATING
            }

            1 -> {
                headerFilter.tvQuickFilter.setCompoundDrawablesRelativeWithIntrinsicBounds(arrowDownDrawable, null, null, null)
                mBinding.layoutFilter.tvQuickFilter.setCompoundDrawablesRelativeWithIntrinsicBounds(arrowDownDrawable, null, null, null)
                mViewModel.overallSort = OVERALLSORT_RETURN
            }

            2 -> {
                headerFilter.tvQuickFilter.setCompoundDrawablesRelativeWithIntrinsicBounds(arrowDownDrawable, null, null, null)
                mBinding.layoutFilter.tvQuickFilter.setCompoundDrawablesRelativeWithIntrinsicBounds(arrowDownDrawable, null, null, null)
                mViewModel.overallSort = OVERALLSORT_COPIERS
            }

            3 -> {
                headerFilter.tvQuickFilter.setCompoundDrawablesRelativeWithIntrinsicBounds(arrowDownDrawable, null, null, null)
                mBinding.layoutFilter.tvQuickFilter.setCompoundDrawablesRelativeWithIntrinsicBounds(arrowDownDrawable, null, null, null)
                mViewModel.overallSort = OVERALLSORT_WIN_RATE
            }

            4 -> {
                headerFilter.tvQuickFilter.setCompoundDrawablesRelativeWithIntrinsicBounds(arrowUpDrawable, null, null, null)
                mBinding.layoutFilter.tvQuickFilter.setCompoundDrawablesRelativeWithIntrinsicBounds(arrowUpDrawable, null, null, null)
                mViewModel.overallSort = OVERALLSORT_RISK_BAND
            }
        }
    }

    /**
     * 重置recyclerview到顶部
     */
    private fun resetRecyclerViewToTop() {
        mBinding.rvView.scrollToPosition(0)
        mViewModel.totalScrollDistance = 0
    }

    /**
     * 检测是否登录跟单账号跳转
     */
    private fun checkStPermission(): Boolean {
        if (UserDataUtil.isLogin() || UserDataUtil.isStLogin()) {
            if (!UserDataUtil.isOpenStAccount()) {
                CenterActionDialog.Builder(requireActivity())
                    .setContent(getString(R.string.please_open_or_switch_to_copy_trading_account_to_proceed)) //设置内容
                    .setStartText(getString(R.string.cancel))//设置左侧按钮文本
                    .setEndText(getString(R.string.ok))//设置右侧按钮文本
                    //如果展示两个按钮，点击监听使用setOnStartListener和setOnEndListener
                    .setOnEndListener { textView ->
                        if (SpManager.isV1V2()) {
                            activity?.let {
                                KycVerifyHelper.showKycDialog(
                                    it,
                                    mapOf(
                                        Constants.GoldParam.CODE to Constants.GoldParam.CODE_OPEN_ACCOUNT,
                                        Constants.GoldParam.CREATE_COPY_TRADING_ACCOUNT to "1"
                                    )
                                )
                            }
                        } else {
                            //默认关闭
                            mViewModel.queryMT4AccountTypeApi()
                        }
                    }
                    .build()
                    .showDialog()
            } else if (UserDataUtil.isOpenStAccount() && UserDataUtil.isStLogin()) {
                return false
            } else if ((!UserDataUtil.isStLogin())) {
                CenterActionDialog.Builder(requireActivity())
                    .setContent(getString(R.string.please_open_or_switch_to_copy_trading_account_to_proceed)) //设置内容
                    .setEndText(getString(R.string.confirm))//设置右侧按钮文本
                    //如果展示两个按钮，点击监听使用setOnStartListener和setOnEndListener
                    .setOnEndListener { textView ->
                        //默认关闭
                        openActivity(AccountManagerActivity::class.java, Bundle().apply {
                            putInt(Constants.IS_FROM, 2)
                        })
                    }
                    .build()
                    .showDialog()
            }
        } else {
            openActivity(LoginActivity::class.java)
        }
        return true
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            // 退出登陆后 页面重置
            NoticeConstants.AFTER_LOGOUT_RESET,
                // 删除账户/解绑手机号后需要跳转到登录页面
            NoticeConstants.UNBIND_ACCOUNT,
                // 切换账号 (断开WebSocket,获取产品列表...)  并切换到首页
            NoticeConstants.SWITCH_ACCOUNT -> {
                stateReset()

                mViewModel.refresh()
                resetRecyclerViewToTop()
            }
        }
    }

    private fun stateReset() {
        mViewModel.pageNum = 1
        mViewModel.clearData()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        EventBus.getDefault().unregister(this)
    }

    companion object {
        const val OVERALLSORT_RATING = "7"
        const val OVERALLSORT_RETURN = "1"
        const val OVERALLSORT_COPIERS = "2"
        const val OVERALLSORT_WIN_RATE = "3"
        const val OVERALLSORT_RISK_BAND = "4"
    }
}
