package cn.com.vau.signals.fragment

import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.*
import cn.com.vau.R
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.databinding.FragmentFxStreetBinding
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.signals.adapter.FxStreetAdapter
import cn.com.vau.signals.viewModel.FxStreetViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.NoDataScrollView
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.json.JSONObject

/**
 * Filename: FxStreetFragment
 * Author: GG
 * Date: 2024/9/20
 * Description:
 */
class FxStreetFragment : BaseMvvmBindingFragment<FragmentFxStreetBinding>() {

    private val mViewModel: FxStreetViewModel by viewModels(ownerProducer = { requireParentFragment() })

    private val adapter by lazy {
        FxStreetAdapter().apply {
            setEmptyView(NoDataScrollView(requireContext()).apply {
                setHintMessage(getString(R.string.no_records_found))
            })
        }
    }

    override fun initView() {
        mBinding.layoutRefresh.mRecyclerView.adapter = adapter
        mBinding.layoutRefresh.mRecyclerView.addItemDecoration(DividerItemDecoration(0.5.dp2px(), dividerColor = AttrResourceUtil.getColor(context = requireContext(), R.attr.color_c1f1e1e1e_c1fffffff)))
    }

    override fun initData() {
        super.initData()
        mViewModel.startPeriodicTask()
    }

    override fun initListener() {
        super.initListener()

        mBinding.tvNewMessage.clickNoRepeat {
            mBinding.tvNewMessage.isVisible = false
            mViewModel.refresh()
            mBinding.layoutRefresh.mRecyclerView.smoothScrollToPosition(0)
        }

        mBinding.layoutRefresh.mRefreshLayout.setOnRefreshListener {
            mViewModel.refresh()
            mBinding.tvNewMessage.isVisible = false
        }

        mBinding.layoutRefresh.mRefreshLayout.setOnLoadMoreListener {
            mViewModel.loadMore()
        }

        adapter.setNbOnItemClickListener { _, _, position ->
            val selectData = adapter.data.getOrNull(position) ?: return@setNbOnItemClickListener

            val htmlUrl = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/nativeTitle/fxStreet/${selectData.id.ifNull()}"
            NewHtmlActivity.openActivity(requireContext(), url = htmlUrl)

            // 神策自定义埋点(v3500)
            sensorsTrack(selectData.id.ifNull(), position, htmlUrl)
        }
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.uiListLiveData.observeUIState(viewLifecycleOwner, adapter, mBinding.layoutRefresh.mRefreshLayout)
        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED).collectLatest {
                if (!mBinding.tvNewMessage.isVisible)
                    mBinding.tvNewMessage.isVisible = true
            }
        }
    }

    /**
     * 神策自定义埋点(v3500)
     * App_发现页面点击 -> 点击app发现页面内容时触发
     */
    private fun sensorsTrack(mktId: String, mktPos: Int, targetUrl: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BELONG_TAB_NAME, "") // 所属Tab 名称
        properties.put(SensorsConstant.Key.MODULE_ID, "") // 模块id
        properties.put(SensorsConstant.Key.MODULE_NAME, "") // 模块名称
        properties.put(SensorsConstant.Key.MODULE_RANK, "") // 模块序号
        properties.put(SensorsConstant.Key.MKT_ID, mktId) // 素材id
        properties.put(SensorsConstant.Key.MKT_NAME, "") // 素材名称
        properties.put(SensorsConstant.Key.MKT_RANK, mktPos + 1) // 素材排序
        properties.put(SensorsConstant.Key.TARGET_URL, targetUrl) // 跳转链接
        SensorsDataUtil.track(SensorsConstant.V3500.APP_DISCOVER_PAGE_CLICK, properties)
    }
}