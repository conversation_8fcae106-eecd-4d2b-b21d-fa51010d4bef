package cn.com.vau.signals.stsignal.activity

import android.content.*
import android.os.Bundle
import android.view.MotionEvent
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.widget.AppCompatEditText
import androidx.core.view.isGone
import androidx.core.widget.doAfterTextChanged
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.databinding.ActivityEditStrategyMinBinding
import cn.com.vau.util.*

/**
 * Filename: StEditStrategyMinActivity
 * Author: GG
 * Date: 2024/4/10
 * Description:
 */
class StEditStrategyMinActivity : BaseMvvmBindingActivity<ActivityEditStrategyMinBinding>() {

    private val minInvestment by lazy { intent.getStringExtra(KEY_MIN_INVESTMENT) }
    private val lots by lazy { intent.getStringExtra(KEY_LOTS) }
    private val multiples by lazy { intent.getStringExtra(KEY_MULTIPLES) }
    private val equivalentMargin by lazy { intent.getStringExtra(KEY_EQUIVALENT_MARGIN) }
    private val currency by lazy {
        val currencyVal = intent.getStringExtra(KEY_CURRENCY)
        if (currencyVal.isNullOrBlank()) {
            UserDataUtil.currencyType()
        } else {
            currencyVal
        }
    }

    /**
     * 最小跟单金额  根据不同币种判断
     */
    private val minInvestedValue: Int by lazy {
        when (currency) {
            "HKD" -> 400
            "JPY" -> 7000
            "USC", "INR" -> 4000
            else -> 50
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
    }

    override fun initView() {
        mBinding.layoutEditThreshold.tvThresholdTitle.isGone = true
        mBinding.layoutEditThreshold.etMinInvestment.setHint("${getString(R.string.min_dot)} $minInvestedValue")
        mBinding.layoutEditThreshold.tvCurrencyType.text = currency
        mBinding.layoutEditThreshold.etMinInvestment.setText(minInvestment.toIntCatching().toString())
        mBinding.layoutEditThreshold.etLots.setText(lots.toDoubleCatching().toString())
        mBinding.layoutEditThreshold.etMultiples.setText(multiples.toDoubleCatching().toString())
        mBinding.layoutEditThreshold.etEquivalentMargin.setText(equivalentMargin.toDoubleCatching().toString())

        KeyboardUtil.registerSoftInputChangedListener(this) {
            if (it == 0) {
                mBinding.root.requestFocus()
                removeFocusBg(mBinding.layoutEditThreshold.etMinInvestment, true)
                removeFocusBg(mBinding.layoutEditThreshold.etLots, true)
                removeFocusBg(mBinding.layoutEditThreshold.etMultiples, true)
                removeFocusBg(mBinding.layoutEditThreshold.etEquivalentMargin, true)
            }
        }

        mBinding.root.viewTreeObserver.addOnGlobalFocusChangeListener { oldFocus, newFocus ->
            removeFocusBg(mBinding.layoutEditThreshold.etMinInvestment)
            removeFocusBg(mBinding.layoutEditThreshold.etLots)
            removeFocusBg(mBinding.layoutEditThreshold.etMultiples)
            removeFocusBg(mBinding.layoutEditThreshold.etEquivalentMargin)
            when (newFocus) {
                mBinding.layoutEditThreshold.etMinInvestment -> addFocusBg(mBinding.layoutEditThreshold.etMinInvestment)
                mBinding.layoutEditThreshold.etLots -> addFocusBg(mBinding.layoutEditThreshold.etLots)
                mBinding.layoutEditThreshold.etMultiples -> addFocusBg(mBinding.layoutEditThreshold.etMultiples)
                mBinding.layoutEditThreshold.etEquivalentMargin -> addFocusBg(mBinding.layoutEditThreshold.etEquivalentMargin)
            }
        }

        mBinding.layoutEditThreshold.etLots.setRangeAndDecimalPlaces(0.01, 100)
        mBinding.layoutEditThreshold.etMultiples.setRangeAndDecimalPlaces(0.1, 50, 1)
        mBinding.layoutEditThreshold.etEquivalentMargin.setRangeAndDecimalPlaces(1, 50, 1)
        mBinding.layoutEditThreshold.etMinInvestment.doAfterTextChanged {
            updateSubmitButton()
        }

        mBinding.layoutEditThreshold.etLots.doAfterTextChanged {
            updateSubmitButton()
        }

        mBinding.layoutEditThreshold.etMultiples.doAfterTextChanged {
            updateSubmitButton()
        }

        mBinding.layoutEditThreshold.etEquivalentMargin.doAfterTextChanged {
            updateSubmitButton()
        }

        mBinding.tvNext.setOnClickListener {
            if (dataCheck(true)) {
                val intent = Intent()
                intent.putExtra(KEY_MIN_INVESTMENT, mBinding.layoutEditThreshold.etMinInvestment.text?.trim().toString())
                intent.putExtra(KEY_LOTS, mBinding.layoutEditThreshold.etLots.text?.trim().toString())
                intent.putExtra(KEY_MULTIPLES, mBinding.layoutEditThreshold.etMultiples.text?.trim().toString().toDoubleOrNull().toString())
                intent.putExtra(KEY_EQUIVALENT_MARGIN, mBinding.layoutEditThreshold.etEquivalentMargin.text?.trim().toString().toDoubleOrNull().toString())
                setResult(Constants.REQUEST_CODE_STRATEGY_EDIT, intent)
                finish()
            }
        }

    }

    private fun addFocusBg(et: AppCompatEditText) {
        // 键盘可见
        et.background = AppCompatResources.getDrawable(this, R.drawable.draw_shape_stroke_c1e1e1e_cebffffff_solid_c0a1e1e1e_c262930_r10)
    }

    private fun removeFocusBg(et: AppCompatEditText, isClear: Boolean = false) {
        et.background = AppCompatResources.getDrawable(this, R.drawable.draw_shape_c0a1e1e1e_c262930_r10)
        if (isClear)
            et.clearFocus()
    }

    /**
     * 通过是否有变化来控制按钮颜色显示
     */
    private fun updateSubmitButton() {
        if (dataCheck()) {
            mBinding.tvNext.setBackgroundResource(R.drawable.draw_shape_c1e1e1e_cebffffff_r100)
            mBinding.tvNext.setTextColor(AttrResourceUtil.getColor(this, R.attr.color_cebffffff_c1e1e1e))
        } else {
            mBinding.tvNext.setBackgroundResource(R.drawable.draw_shape_c1f1e1e1e_c1fffffff_r100)
            mBinding.tvNext.setTextColor(AttrResourceUtil.getColor(this, R.attr.color_c731e1e1e_c61ffffff))
        }
    }

    /**
     * 检测数据是否填写
     */
    private fun dataCheck(showToast: Boolean = false): Boolean {

        if (mBinding.layoutEditThreshold.etMinInvestment.text.isNullOrBlank() || !checkNumIsPass(mBinding.layoutEditThreshold.etMinInvestment.text.toString().ifNull(), minInvestedValue.toDouble())) {
            if (showToast) {
                ToastUtil.showToast(getString(R.string.the_minimum_investment_x, "$minInvestedValue $currency"))
            }
            return false
        }

        if (mBinding.layoutEditThreshold.etLots.text.isNullOrBlank() || !checkNumIsPass(mBinding.layoutEditThreshold.etLots.text.toString().ifNull(), 0.01, 100)) {
            if (showToast) {
                ToastUtil.showToast(getString(R.string.min_lots_per_1_100))
            }
            return false
        }

        if (mBinding.layoutEditThreshold.etMultiples.text.isNullOrBlank() || !checkNumIsPass(mBinding.layoutEditThreshold.etMultiples.text.toString().ifNull(), 0.1, 50)) {
            if (showToast)
                ToastUtil.showToast(getString(R.string.min_lots_multiple))
            return false
        }
        if (mBinding.layoutEditThreshold.etEquivalentMargin.text.isNullOrBlank() || !checkNumIsPass(mBinding.layoutEditThreshold.etEquivalentMargin.text.toString().ifNull(), 1, 50)) {
            if (showToast)
                ToastUtil.showToast(getString(R.string.min_multiples_for_1_50))
            return false
        }
        return true
    }

    /**
     * 检测 输入数字是否在 限制范围内
     */
    private fun checkNumIsPass(str: String, min: Number, max: Number? = null): Boolean {
        try {
            val value = str.toDoubleCatching()
            return if (max == null) {
                value >= min.toDouble()
            } else {
                value in min.toDouble()..max.toDouble()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return false
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        KeyboardUtil.hideSoftKeyboard(this, mBinding.root, event)
        return super.dispatchTouchEvent(event)
    }

    override fun onDestroy() {
        super.onDestroy()
        KeyboardUtil.unregisterSoftInputChangedListener(this.window)
    }

    companion object {

        const val KEY_MIN_INVESTMENT = "min_investment"
        const val KEY_LOTS = "lots"
        const val KEY_MULTIPLES = "multiples"
        const val KEY_EQUIVALENT_MARGIN = "equivalent_margin"
        const val KEY_CURRENCY = "currency"

        fun createIntent(
            context: Context, minInvestment: String?, lots: String?, multiples: String?,
            equivalentMargin: String?, currency: String?
        ) = Intent(context, StEditStrategyMinActivity::class.java).apply {
            putExtra(KEY_MIN_INVESTMENT, minInvestment)
            putExtra(KEY_LOTS, lots)
            putExtra(KEY_MULTIPLES, multiples)
            putExtra(KEY_EQUIVALENT_MARGIN, equivalentMargin)
            putExtra(KEY_CURRENCY, currency)
        }
    }

}