package cn.com.vau.signals.stsignal.adapter

import android.graphics.drawable.Drawable
import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.data.strategy.StrategyPositionListSymbolBean
import cn.com.vau.util.*
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * author：lvy
 * date：2024/03/28
 * desc：策略详情->Portfolio
 */
class StStrategyDetailsPortfolioAdapter :
    BaseQuickAdapter<StrategyPositionListSymbolBean, BaseViewHolder>(R.layout.item_st_strategy_details_portfolio) {

    private val shape_c00c79c_r100: Drawable? by lazy {
        ContextCompat.getDrawable(context, R.drawable.shape_c1f00c79c_r100)
    }
    private val shape_ce35728_r100: Drawable? by lazy {
        ContextCompat.getDrawable(context, R.drawable.shape_c1fe35728_r100)
    }

    private val c00c79c by lazy { ContextCompat.getColor(context, R.color.c00c79c) }
    private val ce35728 by lazy { ContextCompat.getColor(context, R.color.ce35728) }
    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff) }

    private var showType = 1 // 默认显示 daily change
    private var currency: String? = "" // 币种

    fun setShowTypeAndCurrency(showType: Int, currency: String?) {
        this.showType = showType
        this.currency = currency
    }

    override fun convert(holder: BaseViewHolder, item: StrategyPositionListSymbolBean) {
        // 产品
        holder.setText(R.id.tvSymbol, item.symbol)

        // 方向
        holder.setText(R.id.tvDirection, item.tradeAction)
            .setBackground(
                R.id.tvDirection,
                if (item.tradeAction?.contains("buy", true) == true) shape_c00c79c_r100 else shape_ce35728_r100
            ).setTextColor(R.id.tvDirection, if (item.tradeAction?.contains("buy", true) == true) c00c79c else ce35728)

        // dailyChange / pnl
        if (showType == 0) { // pnl
            val pnl = item.pnl.ifNull()
            holder.setText(R.id.tvDailyChange, "${formatPnl(pnl)} $currency")
                .setTextColor(R.id.tvDailyChange, color_c1e1e1e_cebffffff)
        } else { // dailyChange
            val dailyChange = item.dailyChange.ifNull().percent()
            holder.setText(R.id.tvDailyChange, "$dailyChange%")
                .setTextColor(
                    R.id.tvDailyChange, if (dailyChange.contains("-")) ce35728 else c00c79c
                )
        }
    }

    private fun formatPnl(pnl: String?): String {
        val decimalCount = when (currency) {
            "JPY", "USC" -> 0
            else -> 2
        }
        return pnl?.numFormat(decimalCount, true).ifNull()
    }
}