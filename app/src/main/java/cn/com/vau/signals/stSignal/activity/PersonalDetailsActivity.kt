package cn.com.vau.signals.stsignal.activity

import android.os.Bundle
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.data.strategy.StSignalInfoData
import cn.com.vau.databinding.ActivityPersonalDetailsBinding
import cn.com.vau.signals.stsignal.fragment.SignalInfoFragment
import cn.com.vau.signals.stsignal.fragment.StStrategyDetailsOverviewFragment
import cn.com.vau.signals.stsignal.fragment.StStrategyDetailsPortfolioFragment
import cn.com.vau.signals.stsignal.viewmodel.PersonalDetailsViewModel
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.util.ImageUtil
import cn.com.vau.util.PermissionUtil
import cn.com.vau.util.TabType
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.createCameraRequestForUri
import cn.com.vau.util.createPhotoRequestForUri
import cn.com.vau.util.createTempImageUri
import cn.com.vau.util.ifNull
import cn.com.vau.util.init
import cn.com.vau.util.launchPhoto
import cn.com.vau.util.setVp
import cn.com.vau.util.widget.dialog.BottomSelectListDialog
import com.bumptech.glide.request.RequestOptions
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/**
 * 个人详情
 */
@Suppress("DEPRECATION", "SetTextI18n")
class PersonalDetailsActivity : BaseMvvmActivity<ActivityPersonalDetailsBinding, PersonalDetailsViewModel>() {
    private val verifiedStatus: String? by lazy { intent?.getStringExtra("IS_VERIFIED") }

    private val pickImage =
        createPhotoRequestForUri { uri ->
            uri.let {
                lifecycleScope.launch {
                    ImageUtil.compressImageToStream(uri = it)?.let { inputStream ->
                        mViewModel.updateAvatarApi(inputStream)
                    }
                }
            }
        }

    private val cameraUri by lazy { createTempImageUri() }

    private val pickCamera =
        createCameraRequestForUri { isSuccess ->
            if (isSuccess) {
                cameraUri?.let { uri ->
                    lifecycleScope.launch {
                        ImageUtil.compressImageToStream(uri = uri)?.let { inputStream ->
                            mViewModel.updateAvatarApi(inputStream)
                        }
                    }
                }
            }
        }

    private val startForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Constants.REQUEST_CODE_NICKNAME) {
                val intent = result.data
                var nickName = mBinding.tvNickname.text.toString()
                if (intent != null) {
                    if (intent.getStringExtra(EditNicknameActivity.KEY_NICKNAME) != null) {
                        nickName = intent.getStringExtra(EditNicknameActivity.KEY_NICKNAME) ?: ""
                    }
                }
                mViewModel.userformUpdateApi(nickName, mViewModel.signContent)
            }
        }

    private val pictureDialog by lazy {
        BottomSelectListDialog
            .Builder(this)
            .setTitle(getString(R.string.add_picture_from))
            .setDataList(arrayListOf(getString(R.string.camera), getString(R.string.photo_library)))
            .setItemType(1)
            .setOnItemClickListener { position ->
                if (position == 0) {
                    pickCamera.launch(cameraUri)
                } else {
                    pickImage.launchPhoto()
                }
            }.build()
    }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        mViewModel.verifiedStatus = verifiedStatus
    }

    override fun initView() {
        ImageLoaderUtil.loadImageWithOption(
            this,
            UserDataUtil.userPic(),
            mBinding.ivAvatar,
            RequestOptions()
                .placeholder(R.mipmap.ic_launcher)
                .error(R.mipmap.ic_launcher)
        )

        mBinding.tvNickname.text = UserDataUtil.nickname()

        initTabLayout()
    }

    override fun initData() {
        if (UserDataUtil.isCopyTradingAccount()) {
            mViewModel.getSignalInfoApi()
        }
        if (verifiedStatus.isNullOrBlank()) {
            mViewModel.getAuditStatus()
        }
        if (verifiedStatus.isNullOrBlank()) {
            mViewModel.getAuditStatus()
        }
    }

    override fun createObserver() {
        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle, Lifecycle.State.STARTED).collectLatest {
                if (it !is DataEvent) return@collectLatest
                when (it.tag) {
                    PersonalDetailsViewModel.EVENT_UPDATE_NICKNAME -> {
                        UserDataUtil.setUserNickName(it.data.toString())
                        mBinding.tvNickname.text = it.data.toString()
                        EventBus.getDefault().post(NoticeConstants.CHANGE_NAME)
                    }

                    PersonalDetailsViewModel.EVENT_UPDATE_AVATAR -> {
                        finishPage(it.data.toString())
                    }

                    PersonalDetailsViewModel.EVENT_ST_USER_INFO -> {
                        updateView(it.data as? StSignalInfoData)
                    }
                }
            }
        }
    }

    private fun initTabLayout() {
        val fragments = arrayListOf<Fragment>()
        val titleList = arrayListOf<String>()

        if (!UserDataUtil.isCopyTradingAccount()) { // 非跟单profile
            fragments.add(SignalInfoFragment.newInstance())
            titleList.add(getString(R.string.info))
        } else { // 跟单profile
            fragments.add(SignalInfoFragment.newInstance())
            fragments.add(StStrategyDetailsOverviewFragment.newInstance(UserDataUtil.stAccountId()))
            fragments.add(StStrategyDetailsPortfolioFragment.newInstance(UserDataUtil.stAccountId().ifNull(), true))

            titleList.add(getString(R.string.info))
            titleList.add(getString(R.string.overview))
            titleList.add(getString(R.string.invested))
        }
        // 总览页面的webview 因为要横向滑动，导致和viewpager2的滑动冲突，所以需要禁止viewpager2的滑动
        mBinding.viewPager2.isUserInputEnabled = false // 禁止滑动
        mBinding.viewPager2.init(fragments, titleList, supportFragmentManager, this)
        mBinding.mTabLayout.setVp(mBinding.viewPager2, titleList, TabType.LINE_INDICATOR)
    }

    override fun initListener() {
        mBinding.tvNickname.clickNoRepeat {
            startForResult.launch(EditNicknameActivity.createIntent(this, nickname = mBinding.tvNickname.text.toString().trim()))
        }
        mBinding.ivAvatar.clickNoRepeat {
            PermissionUtil.checkPermissionWithCallback(this, *Constants.PERMISSION_STORAGE, Constants.PERMISSION_CAMERA) { isGranted ->
                if (isGranted) {
                    showSelectPhotoPopupWindow()
                }
            }
        }
    }

    private fun updateView(signalInfo: StSignalInfoData?) {
        ImageLoaderUtil.loadImage(this, signalInfo?.profilePictureUrl, mBinding.ivAvatar, R.mipmap.ic_launcher)
        mBinding.tvNickname.text = signalInfo?.nickname
    }

    private fun showSelectPhotoPopupWindow() {
        pictureDialog.showDialog()
    }

    private fun finishPage(pic: String) {
        UserDataUtil.setUserPic(pic)

        ImageLoaderUtil.loadImage(this, pic, mBinding.ivAvatar)
        EventBus.getDefault().post(NoticeConstants.CHANGE_PHOTO)
    }
}