package cn.com.vau.data.strategy

import android.os.Parcelable
import androidx.annotation.Keep
import cn.com.vau.data.BaseBean
import kotlinx.parcelize.Parcelize
import java.io.Serializable

// 策略相关 （分润/策略收藏点赞相关 除外）

/**
 * 信号源中心->策略列表
 */
@Keep
data class StProfileStrategiesBean(
    val publicStrategies: MutableList<StrategyBean>?, //公开的策略
    val delistedStrategies: MutableList<StrategyBean>?, //下架的策略
)

/**
 * 策略详情
 */
@Keep
data class SignalDetailsBean(
    val userDto: StrategyBean?, //信号源信息
    val strategies: MutableList<StrategyBean>?, //策略列表
    val status: Boolean, //登录用户状态（自己看自己 true，反之 false）
)

@Parcelize
@Keep
data class SignalDetailsTenantBean(
    val id: String, //user_id
    val name: String?, //姓名
    val description: String?, //简介
    val active: Int,
) : Parcelable

/**
 * 待审核 / 已拒绝 策略
 */
@Keep
data class StrategyOtherData(
    var `data`: List<StrategyData>?
) : BaseBean() {
    @Keep
    data class Data(
        var code: Int?,
        var `data`: List<StrategyData>?,
        var msg: String?
    )

    @Keep
    data class StrategyData(

        var pnlUI: String?,
        var equityUI: String?,
        var investedUI: String?,
        var totalSharedProfitUI: String?,

        // 跟随申请时间  2
        var applyDate: String?,
        // 审批时间  2
        var reviewDate: String?,
        // 审批截止时间  2
        var reviewDeadline: String?,
        // 是否跟随已存在订单  2
        var copyExistingPositions: Boolean?,
        // 跟随模式  2
        var copyMode: String?,
        // 跟随模式的值  2
        var copyModeValue: Double?,
        // 币种  2
        var currency: String?,
        // 跟单者净值   2
        var equity: String?,
        // Follow Request 的id （后端备注）   2
        var followRequestId: String?,
        // 跟单者投资金额  2
        var investmentAmount: String?,
        // 是否开启最小手数  2
        var minVolRoundup: Boolean?,
        // 头像        2
        var profilePictureUrl: String?,
        // 信号源策略id  2
        var strategyId: String?,
        // 信号源策略名  2
        var strategyName: String?,
        // 信号源编号   2
        var strategyNo: String?,
        //            2
        var status: String?,
        // 止损百分比  2
        var stopLossPercentage: String?,
        // 止盈百分比  2
        var takeProfitPercentage: String?,
        // 跟单者全部已平仓收益  2
        var totalHistoryProfit: String?,
        // 跟单者总分润金额  2
        var totalSharedProfit: String?,
        //信用金
        var investmentCredit: String?
    )
}

/**
 * 历史跟随策略
 */
@Keep
data class StrategyHistoryBean(
    var `data`: List<Data>?
) : BaseBean() {
    @Keep
    data class Data(

        var pnlUI: String? = null,
        var returnUI: String? = null,
        var equityUI: String? = null,
        var investedUI: String? = null,
        var totalSharedProfitUI: String? = null,

        // 申请日期
        var applyDate: String? = null,
        // 余额
        var balance: String? = null,
        // 跟随日期
        var copyDate: String? = null,
        // 净值
        var equity: String? = null,
        // 跟随状态
        var followingStatus: Int? = null,
        // 是否有 pending order
        var hasPendingOrder: Boolean? = null,
        // 投资金额
        var investmentAmount: String? = null,
        //信用金
        var investmentCredit: String? = null,
        // 已使用预付款比
        var marginUsed: String? = null,
        // 停止跟随时的浮动
        var pnL: String? = null,
        // 跟单虚拟账户ID
        var portfolioId: String? = null,
        // 头像
        var profilePictureUrl: String? = null,
        // 盈亏
        var profit: String? = null,
        // 分润比例
        var profitShareRatio: String? = null,
        // 停止跟单前的回报率
        var roi: String? = null,
        // 信号源策略ID
        var strategyId: String? = null,
        // 跟单虚拟账户ID
        var strategyName: String? = null,
        // 信号源策略编号
        var strategyNo: String? = null,
        // 停止跟随时间
        var stopDate: String? = null,
        // 总跟单金额
        var totalDeposit: String? = null,
        // 历史总分润金额
        var totalSharedProfit: String? = null,
        // 跟随模式
        var copyMode: String? = null
    )
}

/**
 * st 上架策略数量
 */
@Keep
data class StrategyCountBean(
    val `data`: Data?
) : BaseBean() {
    // 除了添加注释的两个返参，其他参数用不到
    @Keep
    data class Data(
        val avatar: Any?,
        val copiers: Any?,
        val copyAum: Any?,
        // 可上架最多数量
        val maxStrategyCount: String?,
        val nickname: Any?,
        // 上架的数量
        val publicStrategyCount: String?,
        val stUserId: Any?,
        val totalReceivedProfit: Any?,
        val unpaidAmount: Any?
    )
}

/**
 * 策略已设置信息
 */
@Keep
data class StStrategyCopySettingsData(
    val accountId: Int?,
    // 策略头像
    val avatar: String?,
    val balance: Double?,
    /**
     * 跟随模式：
     * FORMULA：固定保证金比例公式
     * FIXED_VOLUME：固定手数
     * FIXED_MAGNIFICATION：固定倍数
     */
    val copyMode: String?,
    val initialInvestment: String?,
    val totalInvestment: String?,
    // 跟随值
    val copyModeValue: String?,
    val copyExistingPositions: Boolean?,
    val currency: Any?,
    val minFollowAmount: Double?,
    val minFollowMultiplier: Double?,
    // 最小跟随金额
    val minFollowVolume: Double?,
    val minVolRoundup: Boolean?,
    // 策略昵称
    val nickname: String?,
    val profitSharePercentage: Any?,
    val returnRate: Double?,
    val settlementFrequency: Int?,
    // 止损
    val stopLossPercentage: String?,
    // 策略ID
    val strategyId: String?,
    val takeProfitPercentage: String?,

    // 跟随时间
    val copyTime: String?,
    // 提交时间
    val applyTime: String?,
    // 跟随结束时间
    val stopTime: String,
    // 被拒绝时间
    val rejectedTime: String,
    // 结束审核时间
    val reviewDeadline: String,
    val strategyNo: String?,
    var isProfitShieldStrategy: Boolean? = null, // 是否为GS活动策略
    var eligibleFundLevel: String? = null,  // 有效资金     200
)

/**
 * st profile 页面 多策略相关数据
 */
@Keep
data class StStrategySignalProviderCenterBean(
    val data: Data?
) : BaseBean() {
    @Keep
    data class Data(
        val allowToCreateStrategy: Any?,
        val avatar: Any?,
        // 被收藏者数量
        val followers: String,
        // 跟单者人数
        val copiers: String?,
        // 管理资金总额 copy aum
        val copyAum: String?,
        // 允许的总策略数量
        val maxStrategyCount: String?,
        val nickname: Any?,
        // 上架的数量
        val publicStrategyCount: String?,
        val stUserId: Any?,
        val totalReceivedProfit: Any?,
        val unpaidAmount: Any?
    )
}

/**
 * 按昵称搜索策略
 */
@Keep
data class SearchStrategyListData(
    val data: MutableList<SearchStrategyBean>
) : BaseBean()

@Keep
data class SearchStrategyBean(
    val accountLevel: Int? = null,
    val copiedFunds: String? = null,
    val copiers: String? = null,
    val followPortFolioId: String? = null,
    val followerTotalTradeCount: Int? = null,
    val isFollowed: Boolean? = null,
    val isWatched: Boolean? = null,
    val masterPortFolioId: String? = null,
    val profilePictureUrl: String? = null,
    val returnRate: String? = null,
    val signalCreateTime: Long? = null,
    val signalFaceStatus: String? = null,
    val signalFansCount: Int? = null,
    val signalId: String? = null,
    val signalName: String? = null,
    val signalWatchCount: Int? = null,
    val totalTradeCount: Int? = null,
    val tradeAccountNo: String? = null,
    val stUserId: String? = null,
    val nickname: String? = null,
    val avatar: String? = null,
    val pendingApplyApproval: Boolean? = null,
    val followRequestId: String? = null,
)

/**
 * 获取头像列表 -- 创建新策略 || 编辑策略
 * Author: GG
 * Date: 2024/4/8
 * Description:
 */
@Keep
data class SelectAllPicBean(
    val data: Data? = null
) : BaseBean() {
    @Keep
    data class Data(
        val obj: MutableList<Obj>? = null,
    )

    @Keep
    data class Obj(
        val id: Int? = null,
        val url: String? = null
    )
}

/**
 * 创建策略
 */
@Keep
data class StrategyDetailBean(
    var data: StrategyBean
) : BaseBean()

/**
 * 策略订单修改页面，获取策略订单数据 加载页面的接口
 */
@Keep
data class StStrategyCopyLoadData(var data: StStrategyCopyLoadBean) : BaseBean()

@Keep
data class StStrategyCopyLoadBean(

    var strategyId: String? = null,   // 策略id

    var strategyNo: String? = null,   // 策略id显示

    var avatar: String? = null,       // 策略头像

    var offLine: Boolean? = null,     // 策略是否已下架

    var strategyName: String? = null,     // 策略昵称

    var returnRate: Double? = null,   // 策略三个月回报率

    var profitSharePercentage: Double? = null, // 策略分润比例

    var settlementCycle: Int? = null, // 策略结算周期

    var accountId: String? = null,    // 登录的account id

    var currency: String? = null,     // 币种

    var minFollowAmount: Double? = null, // 最小跟单金额

    var minLotsRatioPerOrder: Double? = null, // 最小保证金倍数

    var minFollowVolume: Double? = null, // 最小跟单手数

    var minFollowMultiplier: Double? = null, // 最小跟单倍数

    var balance: Double? = null,     // 余额

    var nickname: String? = null,     // 策略昵称

    var requireReview: Boolean? = null, // false代表不需要审核  true代表需要审核

//    var initialInvestment: Double? = null, // 跟随资金
    var totalInvestment: Double? = null, // 跟随资金

    var minVolRoundup: Boolean? = null,    // 是否开启最小手数

    var settlementFrequency: Int? = null, // 策略结算周期

    var copyMode: String? = null, // 跟随模式： FORMULA：固定保证金比例公式    FIXED_VOLUME：固定手数       FIXED_MAGNIFICATION：固定倍数

    var copyModeValue: String? = null, // 跟随值

    var stopLossPercentage: Double? = null, // SL Rate

    var takeProfitPercentage: Double? = null, // TP Rate

    var stopLossDefaultPercentage: String? = null, // SL设置默认比例

    var accountLeverage: String? = null, // 当前登录账号杠杆比例

    var strategyLeverage: String? = null, // 策略杠杆比例

    var slippageProtection: Boolean? = null, // 是否展示0滑点开关

    var slippageProtectionStatus: Boolean? = null, // 0滑点开关状态

    var followLimit: String? = null, // 跟随次数限制

    var isProfitShieldStrategy: Boolean? = null, // 是否为GS活动策略

    var lossCoverPercentage: String? = null, // 兜底抹亏的百分比    0.8

    var eligibleFundLevel: String? = null,  // 有效资金     200

    // KYC流程跟单交易限制字段
    var userId: String? = null,
    var nextLevel: String? = null,
    var titleString: String? = null,
    var descriptionString: String? = null
)

/**
 * 提交跟随策略审核申请
 */
@Keep
data class StStrategyCopySubmitData(
    var data: StStrategyCopySubmitBean
) : BaseBean()

@Keep
data class StStrategyCopySubmitBean(
    var reviewDeadline: String? = null,  // 自动审核时间
    // KYC流程跟单交易限制字段
    var userId: String? = null,
    var nextLevel: String? = null,
    var titleString: String? = null,
    var descriptionString: String? = null
)

/**
 * 策略持仓列表
 */
@Keep
data class StrategyPositionListBean(
    val strategyId: String?, //被点击信号源策略ID
    val accountId: String?, //当前登录的account ID
    val stUserId: String?, //当前登录user id
    val symbolInfo: MutableList<StrategyPositionListSymbolBean>?, // 持仓产品统计
    val showType: Int? = 1, // 0=pnl、1=Daily Change
    val currency: String? = null, // 币种
)

@Keep
data class StrategyPositionListSymbolBean(
    val symbol: String?, // 产品
    val tradeAction: String?, // 交易行为
    val dailyChange: String?, // 当日涨跌
    val pnl: String?, // 盈亏
)

/**
 * 热门搜索 信号源
 */
@Keep
data class StTopListData(
    var data: MutableList<StTopBean>
) : BaseBean()

@Keep
@Parcelize
data class StTopBean(
    var avatar: String? = null,
    var strategyName: String? = null,
    var nickname: String? = null,
    var strategyId: String? = null,
    var clickedStrategyId: String? = null,
    var clickedUserId: String? = null,
    var threeMonthRR: String? = null,
    var copiers: String? = null,
    var stUserId: String? = null,
) : Parcelable

/**
 * st 获取某个策略信息(Get signal info)
 */
@Keep
data class StSignalInfoBean(
    val `data`: StSignalInfoData?
) : BaseBean()

@Keep
data class StSignalInfoData(
    val accountLevel: Int?,
    val fans: String?,
    val fansList: List<Fans>?,
    val introduction: String?,
    val location: String?,
    //信号源name
    val nickname: String?,
    //策略name
    val name: String?,
    //策略编号
    val strategyNo: String?,
    val profilePictureUrl: String?,
    val risk: String?,
    val signalId: String?,
    val masterPortFolioId: String?,
    val followPortFolioId: String?,
    val signalCreateTime: String?,
    val signalStatus: Boolean,
    val watch: Int?,
    val copiers: Int?,
    val watchList: List<Watch>?,
    val followed: Boolean?,
    // 跟随者数量
    val followerCount: String?,
    // 是否关注
    var watched: Boolean,
    val assestAllocatedForSignal: Double,
    val currentValueOfTheInvestment: Double,
    val differencesOfCurrentValueOfTheInvestment: Double,
    val returnPercentFromSignal: Double,
    val tradesFollowed: Long,
    val topThreeFrequentTradeProducts: String = "",
    // 分润比例
    val profitSharePercentage: Double?,
    val status: String?,
    // 是否享有免分润特权
    var profitShareExempted: Boolean?,
    // 免分润特权结束日期
    var profitShareExemptionEnd: String?
) : Serializable

@Keep
data class Fans(
    var accountId: String?,
    var name: String?,
    var profilePictureUrl: String?,
    var returnRate: Double?,
    var risk: Int?,
    var type: String?,
    var watchFansAccountId: String?,
    var watchFansMasterPortfolioId: String?,
    var isFollowed: Boolean,
    var accountLevel: Int,
    var isWatched: Boolean
) : Serializable

@Keep
data class Watch(
    var accountId: String?,
    var name: String?,
    var profilePictureUrl: String?,
    var returnRate: Double?,
    var risk: Int?,
    var type: String?,
    var watchFansAccountId: String?,
    var watchFansMasterPortfolioId: String?,
    var isFollowed: Boolean,
    var accountLevel: Int,
    var isWatched: Boolean
) : Serializable

/**
 * tab上展示的数量（因为列表有分页，tab需要展示数量，所以让后台单独提供个展示数量的接口）
 */
@Keep
data class StProfileCopyPageTotalsBean(
    val pending: String?, //待审核数量
    val approved: String?, //审核通过数量
    val rejected: String?, //审核拒绝数量
)

/**
 * 跟单审核列表
 */
@Keep
data class StProfileCopyResBean(
    val content: MutableList<StProfileCopyContentBean>?,
    val last: Boolean,
    val totalElements: Int?, //总数据个数
    val totalPages: Int?, //总页数
    val first: Boolean,
    val size: Int?,
    val number: Int?,
    val numberOfElements: Int?,
    val empty: Boolean,
)

@Keep
data class StProfileCopyContentBean(
    val followerUserId: String?, //跟单者stUserId
    val avatar: String?, //跟单者头像
    val name: String?, //跟单者真实姓名
    val strategyCopyListItem: MutableList<StrategyCopyListItemBean>?, //跟单者跟随的策略数据
    val followerAccountId: String?, // 跟单者的交易账号ID
    val followerAccountNo: String?, // 跟单者的交易账号login
    val masterTradeAccountNo: String?, // 跟单者自主交易账号
)

@Keep
data class StrategyCopyListItemBean(
    val followRequestId: String?, //
    val strategyId: String?, //跟单者跟随的策略id
    val strategyName: String?, //跟单者跟随的策略名称
    val followAmount: String?, //跟单金额
    val currency: String?, //币种
    val applyTime: String?, //跟单者提交时间(applyStatus为PENDING时前端取此时间)
    val reviewTime: String?, //通过或拒绝的时间((applyStatus为APPROVED, AUTO_APPROVED, REJECTED, AUTO_REJECTED时前端取此时间))
    val applyStatus: String?, //PENDING,APPROVED,AUTO_APPROVED,REJECTED,AUTO_REJECTED
)

/**
 * 策略详情页h5调原生
 *
 * {"code":6,"data":{"yearList":[2023,2024],"selectedYear":2024}}
 */
@Keep
data class StrategyDetailsChartResBean(
    val code: Int,
    val data: StrategyDetailsChartData?,
    val message: String? = null, // 吐司文案
)

/**
 * 策略详情页原生调h5
 *
 * {"code":6,"data":{"selectedYear":2024}}
 */
@Keep
data class StrategyDetailsChartData(
    var yearList: MutableList<String>? = null,
    val selectedYear: String? = null,
)