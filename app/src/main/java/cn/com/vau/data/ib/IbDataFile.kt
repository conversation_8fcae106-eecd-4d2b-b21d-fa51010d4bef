package cn.com.vau.data.ib

import androidx.annotation.Keep

// Type: IB && 邀请

/**
 * 邀请查询二维码
 */
@Keep
class InvitationsData(
    var obj: InvitationsObj?
)

@Keep
class InvitationsObj(
    var isInvite: String?,
    var headPic: String?,
    var nickName: String?,
    var refereeUserId: String?,
    var refereeMt4AccountId: String?,
    var refereeUrl: String?,
    var qrcodeUrl: String?,
    var inviteCode: String?,
    var listActivityPic: MutableList<String>?
)

@Keep
data class IBUrlData(val obj: IBUrlBean?) {
    @Keep
    data class IBUrlBean(val url: String? = "", val crmUserId: String? = null)
}
