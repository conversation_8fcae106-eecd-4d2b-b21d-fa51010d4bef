package cn.com.vau.data.trade

import androidx.annotation.Keep
import cn.com.vau.data.init.ShareOrderData
import java.io.Serializable

@Keep
data class PositionBean(
    /**
     * 0:位置设止盈止损，1 收起状态，2 展开状态
     */
    var tpSlStatus: Int = 0,
    var estimatedTP: String = "",
    var estimatedSl: String = "",
    var volumeAmount: String = "",
    var currentPriceUI: String = "",
    var openPriceUI: String = "",
    var profitUI: String = "",
    var tpUI: String = "",
    var slUI: String = "",
    var swapUI:String = "",
    var commissionUI:String = "",
    /**
     * 回报率
     */
    var roi: String = "--",
) : Serializable
