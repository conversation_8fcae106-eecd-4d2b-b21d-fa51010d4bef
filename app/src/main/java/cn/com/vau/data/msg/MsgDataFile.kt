package cn.com.vau.data.msg

import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

// Type: 消息 / 客服 / 推送 / InApp / 通知

/**
 * 获取客服咨询分类
 * Created by zhy on 2018/11/6.
 */
@Keep
class CSConsultData {
    var obj: CSConsultObj? = null
}

@Keep
data class CSConsultObj(
    var items: List<CSConsultItem>?,
    var extInfo: CSExtInfo?,
    var zendeskInfo: ZendeskInfo?,
    var supervision: String? = ""
)

@Keep
data class CSConsultItem(
    var code: String? = null,
    var name: String? = null
)

@Keep
data class CSExtInfo(
    var customerTime: String?,
    var contactUsTime: String?,
    var customWelcom: String?
)

@Keep
data class ZendeskInfo(
    var countryName: String? = "",
    var langCode: String? = "",
    var userJWT: String? = "",
)

@Keep
class ChannelKeyData {
    var obj: ChannelKeyInfo? = null
}

@Keep
data class ChannelKeyInfo(
    var id: String? = "",
    var countryCode: String? = "",
    var keyDetail: String? = "",
    var systemType: String? = ""
)

/**
 * 获取客服联系我们
 * Created by zhy on 2018/11/6.
 */
@Keep
class CSContactusData {
    var obj: List<CSContactusObj>? = null
}

@Keep
class CSContactusObj {
    /**
     * area : 上海
     * phones : ************
     * phonelist : ["************"]
     * address : 上海市静安区
     * email : <EMAIL>
     */
    var area: String? = null
    var email: String? = null
    var phonelist: List<String>? = null
}

/**
 * 获取客服问题答案
 * Created by zhy on 2018/11/6.
 */
@Keep
class CSAnswerData {
    var obj: CSAnswerObj? = null
}

@Keep
class CSAnswerObj {
    var quest: String? = null
    var answer: String? = null
}

/**
 * 获取客服问题列表
 * Created by zhy on 2018/11/6.
 */
@Keep
class CSQuestsData {
    var obj: List<CSQuestsObj>? = null
}

@Keep
class CSQuestsObj {
    var id: String? = null
    var quest: String? = null
    var answer: String? = null
    var expanded: Boolean = false
}

/**
 * 公告查询
 */
@Keep
@Parcelize
data class MsgInAppTypeData(
    val obj: List<Obj?>? = null
) : Parcelable {
    @Keep
    @Parcelize
    data class Obj(
        val code: String? = null,
        var count: String? = null,
        val name: String? = null
    ) : Parcelable
}

@Keep
data class NewNoticeData(val obj: List<NoticeBean>)

@Keep
data class NoticeBean(
    val content: String? = null,
    val firstTitle: String? = null,
    val secondTitle: List<String?>? = null,
    val id: String? = null,
    val publishTime: String? = null,
    var read: Boolean? = null,
    val redirectInfo: PushBean? = null
)

@Keep
data class NoticeSettingData(val obj: List<NoticeSettingBean>?)

@Keep
data class NoticeSettingBean(
    val code: String? = null,
    var value: String? = null
)