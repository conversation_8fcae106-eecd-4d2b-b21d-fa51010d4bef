package cn.com.vau.data.init

import androidx.annotation.Keep
import cn.com.vau.data.BaseBean
import cn.com.vau.data.trade.PositionBean
import java.io.Serializable

/***
 * 挂单列表 ---- 多品牌（以前和持仓共用）
 */
@Keep
data class PendingOrdersBean(
    val obj: List<ShareOrderData>?
) : BaseBean()

/**
 * 持仓列表v2 ---- 多品牌 & 跟单自主
 */
@Keep
data class PositionOrdersBean(
    val `data`: PositionOrdersData?
) : BaseBean()

@Keep
data class PositionOrdersData(
    val positionList:List<ShareOrderData>?
)

/**
 * 持仓订单 多品牌
 */
@Keep
class ShareOrderData : Serializable {

    var bid = 0.0f
    var ask = 0.0f

    // 订单相关类使用
    var bidType = 0
    var askType = 0
    var roseType = -1   // -1: --  0: 涨 1: 跌

    var isRefresh = true

    // 刷新时间
    var lasttime = "0"

    // 盈亏
    var profit = 0.0

    // 产品小数点位数
    var digits = 2

    var currentPriceUI: String? = null
    // 可移除 -- 需多品牌后端对齐跟单自主进行格式化
    var volumeUI: String? = null
    var profitUI: String? = null

    // ---------------------------

    // 最小手数 (产品属性 )
    var minvolume: String = ""

    // 总盈亏 , 手续费, 利息 总和
    var totalProfit: String? = null

    // 平仓价（ 时时价格 ）
    var closePrice: String? = null

    // ---------------------------

    // 订单号
    var order: String? = null

    var symbol: String? = null

    /**
     * 0-buy
     * 1-sell
     * 2-buy limit  买入限价
     * 3-sell limit 卖出限价
     * 4-buy stop   买入提损
     * 5-sell stop  卖出停损
     * 6-buy stop limit   买入停损限价
     * 7-sell stop limit  卖出停损限价
     */
    var cmd = "0"

    // 开仓价(挂单价格)
    var openPrice = "0"

    // 开仓时间 (dd/mm/yyyy hh:mm:ss)
    var openTimeStr: String? = null

    // 止损价
    var stopLoss: String? = null

    // 止盈价
    var takeProfit: String? = null

    var volume: String? = null

    // 交易手续费 佣金
    var commission: String? = null

    // 库存费 / 隔夜仓息
    var swap: String? = null

    /**
     * 手续费
     *
     * 计算单笔持仓订单盈亏使用，
     * 计算 totalProfile 时使用
     * mt4加commission,mt5和跟单不加
     * mt4 加 佣金，因为 mt4 平台下单时余额不扣除佣金，mt5平台订单成交时就会扣除佣金
     */
    var profitCommission: String? = null

    /**
     * 手续费总和
     * 计算互抵平仓和其他场景使用
     * 和 profitCommission 的区别是此费用是 所有手续费相加之和
     */
    var totalCommission: String? = null

    // 【跟单自主】订单号 ---- 多品牌用不到
    var stOrder: String? = ""

    // 【多品牌】挂单使用
    var stopLimitPrice = "0"

    // 【跟单 - 跟随策略】订单状态 跟随策略下持仓订单需要，自主交易不会出现 (待平仓：PENDING_CLOSE，待开仓：PENDING_OPEN，已开仓：OPEN)
    var status: String? = ""

    // 计算挂单价和市价差距
    var gapPriceUI:String?=""

    //保证金
    var margin: String? = null

    // 交易货币，这个产品的交易货币币种
    var priceCurrency: String? = null

    // 挂单使用
    var volumeAmount: String = ""
    // 持仓APP本地的状态
    var positionBean:PositionBean?= null

}