package cn.com.vau.data.trade

import androidx.annotation.Keep
import cn.com.vau.data.BaseBean
import java.io.Serializable

/**
 * st 公开交易
 */
@Keep
data class OpenConditionBean(
    var data: OpenConditionData
): BaseBean()

@Keep
data class OpenConditionData(
    var authorityAccepted: String,
    var depositAccepted: String
): Serializable

/**
 * 查询用户杠杆数据
 */
@Keep
data class LeverageBean(
    var obj: Obj? = null
) {
    @Keep
    data class Obj(
        var mt4AccountId: String? = null,
        var leverage: String? = null,
        var list: MutableList<Int>?
    )
}

