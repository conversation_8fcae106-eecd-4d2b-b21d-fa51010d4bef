package cn.com.vau.data.trade

import androidx.annotation.Keep
import cn.com.vau.data.BaseBean

@Keep
data class ClosedHistoryBean(
    var remainedVolume: String? = null,
    var closedVolume: String? = null,
    var closePnl: String? = null,
    var closeNetPnl: String? = null,
    var symbol: String? = null,
    var dealAction: String? = null,
    var partCloseList:ArrayList<PartyCloseItemBean>?= null
) : BaseBean()

@Keep
data class PartyCloseItemBean(
    var closedVolume: String? = null,
    var closePnl: String? = null,
    var closeNetPnl: String? = null,
    var closeTime: String? = null,
    var closePrice: String? = null,
    var commission: String? = null,
    var swap: String? = null,
    var tradeDealId: String? = null,
    var partClose: Boolean? = true,
    var isExpand:Boolean = false,
)

@Keep
data class TradeClosedHistoryBean(
    var obj:ClosedHistoryBean
):BaseBean()