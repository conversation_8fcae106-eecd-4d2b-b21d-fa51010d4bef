package cn.com.vau.trade.adapter;

import android.content.Context;
import android.util.TypedValue;
import android.view.*;
import android.widget.*;

import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import cn.com.vau.R;
import cn.com.vau.common.storage.SpManager;
import cn.com.vau.trade.bean.kchart.ChartTypeBean;
import cn.com.vau.util.AttrResourceUtil;

/**
 * Created by roy on 2018/7/26.
 * 产品详情 图表类型
 */

public class ChartTypeRecyclerWeightAdapter extends RecyclerView.Adapter<ChartTypeRecyclerWeightAdapter.ChartTypeViewHolder> {

    private Context context;
    private List<ChartTypeBean> dataList;

    public ChartTypeRecyclerWeightAdapter(Context context, List<ChartTypeBean> dataList) {
        this.context = context;
        this.dataList = dataList;
    }

    @Override
    public ChartTypeViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_recycler_kchart_type_weight, null, false);
        ChartTypeViewHolder myViewHolder = new ChartTypeViewHolder(view);
        return myViewHolder;
    }

    @Override
    public void onBindViewHolder(final ChartTypeViewHolder holder, int position) {

//        holder.rightView.setVisibility(position == dataList.size() - 1 ? View.GONE : View.VISIBLE);
        if (position == 0) {
            holder.llContainer.setGravity(Gravity.START);
        }  else {
            holder.llContainer.setGravity(Gravity.CENTER_HORIZONTAL);
        }
        ChartTypeBean dataBean = dataList.get(position);
        holder.tvName.setText(dataBean.getName());
        if(dataBean.isSelected()) {
            holder.bottomView.setVisibility(View.INVISIBLE);
            holder.tvName.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff));
            holder.tvName.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
            holder.tvName.setTextAppearance(context, R.style.bold_semi_font);
            if(SpManager.getLanguageSelect() == 4 && position == 0) {
                holder.tvName.setTextSize(TypedValue.COMPLEX_UNIT_SP, 11);
            }
        }else {
            holder.bottomView.setVisibility(View.INVISIBLE);
            holder.tvName.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff));
            holder.tvName.setTextSize(TypedValue.COMPLEX_UNIT_SP, 11);
            holder.tvName.setTextAppearance(context, R.style.medium_font);
        }
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    int index = holder.getLayoutPosition();
                    onItemClickListener.onItemClick(index);
                }
            });
        }

    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class ChartTypeViewHolder extends RecyclerView.ViewHolder {
        TextView tvName;
        View bottomView, rightView;
        LinearLayout llContainer;

        public ChartTypeViewHolder(View itemView) {
            super(itemView);
            tvName = itemView.findViewById(R.id.tv_name);
            bottomView = itemView.findViewById(R.id.view_bottom);
            rightView = itemView.findViewById(R.id.rightView);
            llContainer = itemView.findViewById(R.id.llContainer);
        }
    }

    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(int position);
    }

}
