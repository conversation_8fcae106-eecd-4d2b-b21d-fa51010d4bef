package cn.com.vau.trade.activity

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import androidx.activity.OnBackPressedCallback
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import cn.com.vau.MainActivity
import cn.com.vau.R
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.http.ws.StWsManager
import cn.com.vau.common.http.ws.WsManager
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.performance.PerformManager
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.*
import cn.com.vau.common.view.CustomTextWatcher
import cn.com.vau.common.view.popup.adapter.PlatAdapter
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.ActivityOrderBinding
import cn.com.vau.page.StickyEvent
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.page.html.HtmlActivity
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.profile.adapter.SelectAccountAdapter
import cn.com.vau.profile.adapter.SelectBean
import cn.com.vau.profile.performance.TradePermissionPerformance
import cn.com.vau.trade.dialog.BottomSymbolSearchDialog
import cn.com.vau.trade.dialog.OrderConfirmDialog
import cn.com.vau.trade.ext.SymbolSearchConstants.FROM_ORDER
import cn.com.vau.trade.viewmodel.OrderViewModel
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.BottomInfoListDialog
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.base.BottomListDialog
import com.neovisionaries.ws.client.WebSocketState
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import kotlin.math.abs
import kotlin.math.pow

@Deprecated("废弃")
@SuppressLint("NotifyDataSetChanged")
class OrderActivity : BaseMvvmActivity<ActivityOrderBinding, OrderViewModel>(), SDKIntervalCallback {

    private val shape_c00c79c_r10 by lazy { R.drawable.shape_c00c79c_r10 }
    private val shape_cf44040_r10 by lazy { R.drawable.shape_cf44040_r10 }
    private val draw_shape_c0a1e1e1e_c262930_r10 by lazy { R.drawable.draw_shape_c0a1e1e1e_c262930_r10 }

    private val draw_shape_c0a1e1e1e_c0affffff_r100 by lazy { R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100 }
    private val draw_shape_cf44040_r100 by lazy { R.drawable.draw_shape_cf44040_r100 }
    private val draw_shape_c00c79c_r100 by lazy { R.drawable.draw_shape_c00c79c_r100 }

    private val cebffffff by lazy { R.color.cebffffff }
    private val cf44040 by lazy { R.color.cf44040 }
    private val color_ca61e1e1e_c99ffffff by lazy { AttrResourceUtil.getColor(this, R.attr.color_ca61e1e1e_c99ffffff) }
    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(this, R.attr.color_c1e1e1e_cebffffff) }
    private val color_c731e1e1e_c61ffffff by lazy { AttrResourceUtil.getColor(this, R.attr.color_c731e1e1e_c61ffffff) }

    /**
     * 订单类型说明弹框
     */
    private val orderTypePopup: BottomListDialog? by lazy {

        BottomListDialog.Builder(this)
            .setTitle(getString(R.string.order_types))
            .setAdapter(PlatAdapter().apply {
                setList(arrayListOf<HintLocalData>().apply {
                    add(HintLocalData(getString(R.string.market_order), getString(R.string.an_order_to_market_price)))
                    add(HintLocalData(getString(R.string.limit_order), getString(R.string.an_order_placed_to_either_certain_price)))
                    add(HintLocalData(getString(R.string.stop_order), getString(R.string.an_order_placed_to_buy_certain_price)))
                    if (UserDataUtil.isMT5()) {
                        add(HintLocalData(getString(R.string.stop_limit_order), getString(R.string.an_order_that_and_for_execution)))
                    }
                })
            })
            .build()
    }

    /**
     * 订单类型选择
     */
    private val typeAdapter: SelectAccountAdapter<SelectBean> by lazy {
        SelectAccountAdapter<SelectBean>(isChangeSelectTextColor = false).apply {
            setNewInstance(mViewModel.tradeTypeList)
            selectTitle = mViewModel.tradeTypeList.getOrNull(0)?.getShowItemValue()

            setOnItemClickListener { _, _, position ->
                if (mViewModel.tradeTypeIndex == position) return@setOnItemClickListener

                mViewModel.tradeTypeIndex = position
                selectTitle = data.getOrNull(position)?.getShowItemValue()
                notifyDataSetChanged()
                initTradeTypeView()
                initAtPriceShow()
                reSetMaxOpen()
                tradeTypePopup?.dismiss()
            }
        }
    }
    private val tradeTypePopup: BottomListDialog? by lazy {
        BottomListDialog.Builder(this)
            .setTitle(getString(R.string.type))
            .setAdapter(typeAdapter)
            .build()
    }

    /**
     * 手数类型：金额、手
     */
    private val unitAdapter: SelectAccountAdapter<SelectBean> by lazy {
        SelectAccountAdapter<SelectBean>(isChangeSelectTextColor = false).apply {
            setNewInstance(mViewModel.unitTypeList)
            selectTitle = mViewModel.unitTypeList.getOrNull(0)?.getShowItemValue()

            setOnItemClickListener { _, _, position ->
                if (mViewModel.unit == "${position + 1}") return@setOnItemClickListener

                mViewModel.unit = "${position + 1}"
                selectTitle = data.getOrNull(position)?.getShowItemValue()
                notifyDataSetChanged()
                switchVolumeUint(selectTitle ?: getString(R.string.lots))
                SpManager.putOpenPositionUnit(mViewModel.unit)
                unitTypePopup?.dismiss()
            }
        }
    }

    private val unitTypePopup: BottomListDialog? by lazy {
        mViewModel.initUintTypeList(this)
        BottomListDialog.Builder(this)
            .setTitle(getString(R.string.volume_unit))
            .setAdapter(unitAdapter)
            .build()
    }

    private var mSearchDialog: BottomSymbolSearchDialog? = null

    /**
     * 手数小数位检查
     */
    private val volumeWatcher by lazy {
        object : CustomTextWatcher() {
            @SuppressLint("SetTextI18n")
            override fun afterTextChanged(edt: Editable) {
                if (mViewModel.unit == OrderViewModel.UNIT_AMOUNT) {
                    checkAmountDigits(edt)
                    return
                }
                checkVolumeDigits(edt)
            }
        }
    }

    private val performManager by lazy {
        PerformManager(this)
    }
    private val tradePermissionPerformance by lazy {
        TradePermissionPerformance(this)
    }

    /**
     * 手数是金额时小数位检查
     */
    private fun checkAmountDigits(edt: Editable) {
        val temp = edt.toString()
        if (temp.contains(".")) {
            val posDot = temp.indexOf(".")
            //删除前进行数值校验，看是否在length内
            if (temp.length - posDot - 1 > mViewModel.getCurrencyDigits()) {
                val endIndex = posDot + 2 + mViewModel.getCurrencyDigits()
                if (endIndex <= edt.length) {
                    edt.delete(posDot + mViewModel.getCurrencyDigits() + 1, endIndex)
                }
            }
            if (posDot > 9 && posDot - 1 in 0..edt.length && posDot in 0..edt.length) {
                edt.delete(posDot - 1, posDot)
            }
        } else {
            if (temp.length > 9)
                edt.delete(temp.length - 1, temp.length)
        }
        showTransfer()
        checkVolume(edt.toString())
        showMargin()
        showOrNotTransferTv()
//        if (mViewModel.isInputVolumeFromKeyBoard) {
//            mBinding.volSeekBar.setProgress(0)
//        }
    }

    /**
     * 手数是手时小数位检查
     */
    private fun checkVolumeDigits(edt: Editable) {
        val temp = edt.toString()
        if (temp.contains(".")) {
            val posDot = temp.indexOf(".")
            if (posDot <= 0) return
            if (temp.length - posDot - 1 > 2) {
                edt.delete(posDot + 3, posDot + 4)
            }
            if (posDot > 5 && posDot - 1 in 0..edt.length && posDot in 0..edt.length)
                edt.delete(posDot - 1, posDot)
        } else {
            if (temp.length > 5)
                edt.delete(temp.length - 1, temp.length)
        }
        showTransfer()
        checkVolume(edt.toString())
        showMargin()
        showOrNotTransferTv()
//        if (mViewModel.isInputVolumeFromKeyBoard) {
//            mBinding.volSeekBar.setProgress(0)
//        }
    }

    /**
     * 挂单价格小数位检查
     */
    private val atPriceWatcher by lazy {
        object : CustomTextWatcher() {
            override fun afterTextChanged(edt: Editable) {
                checkAtPriceDigits(edt)
            }
        }
    }

    /**
     * 挂单价格小数位检查
     */
    private fun checkAtPriceDigits(edt: Editable) {
        val temp = edt.toString()
        if (temp.contains(".")) {
            val posDot = temp.indexOf(".")
            //删除前进行数值校验，看是否在length内
            if (temp.length - posDot - 1 > mViewModel.digits) {
                val endIndex = posDot + 2 + mViewModel.digits
                if (endIndex <= edt.length) {
                    edt.delete(posDot + mViewModel.digits + 1, endIndex)
                }
            }
            if (posDot > 9 && posDot - 1 in 0..edt.length && posDot in 0..edt.length) {
                edt.delete(posDot - 1, posDot)
            }
        } else {
            if (temp.length > 9)
                edt.delete(temp.length - 1, temp.length)
        }
        checkAtPrice()
        checkStopLimitPrice()
        reSetMaxOpen()
//        checkTvNext()
    }

    /**
     * 止盈、止损价格小数位检查
     */
    private val takeProfitWatcher by lazy {
        object : CustomTextWatcher() {
            override fun afterTextChanged(edt: Editable) {
                checkTakeProfitDigits(edt)
            }
        }
    }

    /**
     * 止盈、止损价格小数位检查
     */
    private fun checkTakeProfitDigits(edt: Editable) {
        val temp = edt.toString()
        if (temp.contains(".")) {
            val posDot = temp.indexOf(".")
            if (temp.length - posDot - 1 > mViewModel.digits) {
                edt.delete(posDot + mViewModel.digits + 1, posDot + 2 + mViewModel.digits)
            }
            if (posDot > 9 && posDot - 1 in 0..edt.length && posDot in 0..edt.length)
                edt.delete(posDot - 1, posDot)
        } else {
            if (temp.length > 9)
                edt.delete(temp.length - 1, temp.length)
        }
        checkTakeProfitAndStopLoss()
//        checkTvNext()
    }

    override fun initParam(savedInstanceState: Bundle?) {
        if (SpManager.getAppLockOrder(false))
            VAUStartUtil.checkLockPass(this, NoticeConstants.Unlock.UNLOCK_TO_NEW_ORDER)
        if (VAUSdkUtil.symbolList().size == 0) return

        super.initParam(savedInstanceState)
        mViewModel.tradeType = intent?.extras?.getString(Constants.PARAM_ORDER_TYPE) ?: "0"  // 0：买  1：卖
        mViewModel.defaultLot = intent?.extras?.getString(Constants.PARAM_ORDER_VOLUME) ?: ""  // 默认手数
        mViewModel.productName = intent?.extras?.getString(Constants.PARAM_PRODUCT_NAME)
            ?: (VAUSdkUtil.symbolList().find { "2" == it.enable }?.symbol ?: "")

        mViewModel.unit = SpManager.getOpenPositionUnit(OrderViewModel.UNIT_LOTS)
        mViewModel.initUintTypeList(this)
    }

    override fun initView() {
        addPerformance()
        mBinding.mSmartRefreshLayout.setEnableLoadMore(false)
        // 初始订单信息
        showProductData()
        mBinding.mTimeChartView.setData(mViewModel.productData)
        // 进来时判断网络连接状态
        if (UserDataUtil.isStLogin()) {
            if (StWsManager.getInstance().getWsState() != WebSocketState.OPEN) {
                mViewModel.isConnected = false
                mBinding.tvNetworkStatus.text = getString(R.string.network_disconnected_please_again_later)
                mBinding.tvNetworkStatus.isVisible = true
            }
        } else {
            if (WsManager.getInstance().getWsState() != WebSocketState.OPEN) {
                mViewModel.isConnected = false
                mBinding.tvNetworkStatus.text = getString(R.string.network_disconnected_please_again_later)
                mBinding.tvNetworkStatus.isVisible = true
            }
        }

        // 检查闭市状态 && 断开连接
        mViewModel.productData?.let {
            checkNetWorkStatus(it)
        }
        KeyboardUtil.registerSoftInputChangedListener(this) {
            if (it == 0) {
                clearFocus()
            }
        }
        initOrderUnit()
        mViewModel.tradePageView()
    }

    private fun addPerformance() {
        performManager.addPerformance(tradePermissionPerformance)
    }

    private fun initOrderUnit() {
        if (mViewModel.unit != OrderViewModel.UNIT_LOTS) {
            unitAdapter.selectTitle = mViewModel.unitTypeList.getOrNull(if (mViewModel.unit == OrderViewModel.UNIT_LOTS) 0 else 1)?.getShowItemValue()
            switchVolumeUint(unitAdapter.selectTitle ?: getString(R.string.lots))
        }
    }

    override fun createObserver() {
        mViewModel.submitOrderSuccessLiveData.observe(this) {
            handleSuccess()
        }

        mViewModel.fundLackLiveData.observe(this) {
            showFundLackDialog()
        }

        mViewModel.tokenErrorLiveData.observe(this) {
            showTokenErrorDialog(it)
        }

        mViewModel.hintDataDialogLiveData.observe(this) {
            showHintDataDialog(it)
        }

        mViewModel.checkDelayLiveData.observe(this) {
            showCheckDelayDialog()
        }
    }

    /**
     * 服务端400ms，推送一次行情信息，根据行情信息更新买卖价，挂单价格范围，限价价格范围，止盈止损范围
     */
    override fun onCallback() {
        syncShowProductData()
        mBinding.mTimeChartView.updateQuotation()
    }

    @SuppressLint("SetTextI18n")
    override fun initListener() {
        mBinding.mSmartRefreshLayout.setOnRefreshListener {
            mBinding.mSmartRefreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
        }

        mBinding.tvGoProduct.clickNoRepeat {
            showSearchDialog()
            mViewModel.tradePageProductClick()
        }
//        mBinding.ivGoProduct.clickNoRepeat {
//            val bundle = Bundle()
//            bundle.putString("selectProductName", mViewModel.productName)
//            openActivity(SelectSymbolActivity::class.java, bundle, Constants.RESULT_CODE)
//            mViewModel.tradePageProductClick()
//        }

        mBinding.ivKline.clickNoRepeat {
            openActivity(KLineActivity::class.java, Bundle().apply {
                putString(Constants.PARAM_PRODUCT_NAME, mViewModel.productName)
                putString(Constants.IS_FROM, "order")
            })
            mViewModel.tradePageKChartBtnClick()
        }

        mBinding.llSell.clickNoRepeat {
            selectedOrderType(OrderViewModel.TRADE_SELL)
        }

        mBinding.llBuy.clickNoRepeat {
            selectedOrderType(OrderViewModel.TRADE_BUY)
        }

        mBinding.ivMarketIntroduce.clickNoRepeat {
            orderTypePopup?.show()
            mViewModel.tradePageOrderTypesAnnotationClick()
        }

        mBinding.viewMarketExecution.clickNoRepeat {
            tradeTypePopup?.show()
        }

        mBinding.etVolume.addTextChangedListener(volumeWatcher)

        mBinding.etVolume.setOnFocusChangeListener { _, hasFocus ->
            mBinding.clVolume.isSelected = hasFocus
            formatVolume()
            if (hasFocus) {
                mViewModel.isInputVolumeFromKeyBoard = true
                showTransfer()
                mBinding.volSeekBar.setProgress(0)
            } else {
                mViewModel.isInputVolumeFromKeyBoard = false
            }
            showOrNotTransferTv()
        }

        mBinding.tvVolumeTitle.clickNoRepeat {
            KeyboardUtil.showSoftInput(mBinding.etVolume)
        }

        mBinding.ivVolumeAdd.setOnClickListener {
            addVolume()
        }

        mBinding.ivVolumeSub.setOnClickListener {
            subVolume()
        }

        mBinding.tvVolumeUnit.clickNoRepeat {
            mBinding.etVolume.clearFocus()
            unitTypePopup?.show()
        }

        mBinding.volSeekBar.setOnSeekBarChangeListener { seekBar, progress, isStart ->
            if (isStart) {
                KeyboardUtil.hideSoftInput(mBinding.etVolume)
                mBinding.etVolume.clearFocus()
                mViewModel.isInputVolumeFromKeyBoard = false
                showMaxOpen()
                mViewModel.tradePageVolumeControlClick()
            }
            if (mViewModel.isInputVolumeFromKeyBoard.not()) {
                val max = if (mViewModel.unit == OrderViewModel.UNIT_LOTS) {
                    mViewModel.maxOpenVolume
                } else {
                    mViewModel.maxOpenAmount
                }
                val value = max.mathMul("${progress / 100f}")
                if (mViewModel.unit == OrderViewModel.UNIT_LOTS) {
                    if (value.mathCompTo(mViewModel.minVolume) != -1) {
                        val valueDiv = value.mathDiv(mViewModel.stepVolume, 2)
                        val valueDivInt = valueDiv.substringCatching(0, valueDiv.indexOf("."), valueDiv)
                        val finalValue = valueDivInt.mathMul(mViewModel.stepVolume).numFormat(2)
                        mBinding.etVolume.setText(finalValue)
                    } else {
                        mBinding.etVolume.setText("0.00")
                    }
                } else {
                    if (value.mathCompTo(mViewModel.getMinAmount(getInputPrice())) != -1) {
                        mBinding.etVolume.setText(value)
                    } else {
                        mBinding.etVolume.setText("0".numFormat(mViewModel.digits))
                    }
                }
            }
        }

        mBinding.tvMarginTitle.setOnClickListener {
            BottomInfoListDialog.Builder(this)
                .setTitle(getString(R.string.margin))
                .setDataList(arrayListOf(HintLocalData(getString(R.string.a_portion_of_open_position))))
                .setLinkText(getString(R.string.formulas_and_examples))
                .setLinkListener {
                    openActivity(HtmlActivity::class.java, Bundle().apply {
                        putString("title", getString(R.string.margin_formulas))
                        putString("url", "${HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix}/socialTrading/marginCalculation")
                        putInt("tradeType", 3)
                    })
                }
                .build()
                .showDialog()
            mViewModel.tradePageMarginAnnotationClick()
        }

        mBinding.etAtPrice.addTextChangedListener(atPriceWatcher)

        mBinding.etAtPrice.setOnFocusChangeListener { _, hasFocus ->
            mBinding.clAtPrice.isSelected = hasFocus
            formatAtPrice()
            if (hasFocus.not()) {
                mViewModel.isInputVolumeFromKeyBoard = false
            }
        }

        mBinding.ivAtPriceAdd.setOnClickListener {
            addAtPrice()
        }
        mBinding.ivAtPriceSub.setOnClickListener {
            subAtPrice()
        }

        mBinding.etStopLimitPrice.addTextChangedListener(atPriceWatcher)

        mBinding.ivStopLimitPriceAdd.setOnClickListener {
            addStopLimitPrice()
        }

        mBinding.ivStopLimitPriceSub.setOnClickListener {
            subStopLimitPrice()
        }

        mBinding.etStopLimitPrice.setOnFocusChangeListener { _, hasFocus ->
            mBinding.clStopLimitPrice.isSelected = hasFocus
            formatStopLimitPrice()
            if (hasFocus.not()) {
                mViewModel.isInputVolumeFromKeyBoard = false
            }
        }

        mBinding.ivTakeProfitChecked.clickNoRepeat {
            selectedTakeProfile(mViewModel.takeProfitCb.not())
        }
        mBinding.tvTakeProfitTitle.clickNoRepeat {
            selectedTakeProfile(mViewModel.takeProfitCb.not())
        }

        mBinding.etTakeProfit.addTextChangedListener(takeProfitWatcher)

        mBinding.ivTakeProfitAdd.setOnClickListener {
            addTakeProfit()
        }

        mBinding.ivTakeProfitSub.setOnClickListener {
            subTakeProfit()
        }

        mBinding.etTakeProfit.setOnFocusChangeListener { _, hasFocus ->
            mBinding.clTakeProfit.isSelected = hasFocus
            formatTakeProfit()
        }

        mBinding.ivStopLessChecked.clickNoRepeat {
            selectedStopLoss(mViewModel.stopLossCb.not())
        }
        mBinding.tvStopLessTitle.clickNoRepeat {
            selectedStopLoss(mViewModel.stopLossCb.not())
        }

        mBinding.etStopLoss.addTextChangedListener(takeProfitWatcher)

        mBinding.ivStopLossAdd.setOnClickListener {
            addStopLoss()
        }

        mBinding.ivStopLossSub.setOnClickListener {
            subStopLoss()
        }

        mBinding.etStopLoss.setOnFocusChangeListener { _, hasFocus ->
            mBinding.clStopLoss.isSelected = hasFocus
            formatStopLoss()
        }

        mBinding.mTimeChartView.setExpandStateListener { state ->
            mBinding.clContent.setPadding(0, 0, 0, if (state) 370.dp2px() else 193.dp2px())
        }

        mBinding.tvNext.clickNoRepeat {
            if (UserDataUtil.orderConfirmState() == "1") {
                tradePermissionPerformance.run {
                    if (handleTradeBlockType(true) { submitOrder() }) return@clickNoRepeat
                }
                submitOrder()
            } else {
                mViewModel.volumeParam = mViewModel.inputVolume
                mViewModel.tpParam = mBinding.etTakeProfit.text.toString()
                mViewModel.slParam = mBinding.etStopLoss.text.toString()
                mViewModel.atPriceParam = mBinding.etAtPrice.text.toString()
                mViewModel.stopLimitPriceParam = mBinding.etStopLimitPrice.text.toString()
                tradePermissionPerformance.run {
                    if (handleTradeBlockType(true) { showSubmitOrderConfirmDialog() }) return@clickNoRepeat
                }
                showSubmitOrderConfirmDialog()
            }
            mViewModel.sensorsTrack()
        }

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (mSearchDialog?.isShowDialog() == true) {
                    dismissSearchDialog()
                    return
                }
                finish()
            }
        })
    }

    /**
     * 手数和金额互换标签显示
     */
    @SuppressLint("SetTextI18n")
    private fun showTransfer() {
        mViewModel.productData?.let {
            var unitStr = ""
            if (mViewModel.unit == OrderViewModel.UNIT_LOTS) {
                mViewModel.inputVolume = mBinding.etVolume.text.toString()
                unitStr = "≈ ${OrderUtil.getAmountFromVolume(it, mViewModel.inputVolume, mViewModel.tradeType, getInputPrice())} ${mViewModel.currencyType}"
            } else {
                mViewModel.inputVolume = OrderUtil.getVolumeFromAmount(mBinding.etVolume.text.toString(), it, mViewModel.tradeType, getInputPrice())
                unitStr = "≈ ${mViewModel.inputVolume} ${getString(R.string.lots)}"
            }
            mBinding.tvTransfer.text = unitStr
        }
    }

    /**
     * 获取非市价时，输入的价格
     * 1、默认挂单价格
     * 2、如果有限价价格的话，使用限价价格
     */
    private fun getInputPrice(): String {
        var inputPrice = ""
        if (mViewModel.tradeTypeIndex != OrderViewModel.INDEX_MARKET) {
            inputPrice = mBinding.etAtPrice.text.toString().ifBlank { "0" }
            if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_STOP_LIMIT_BUY_STOP_LIMIT) {
                inputPrice = mBinding.etStopLimitPrice.text.toString().ifBlank { "0" }
            }
        }
        return inputPrice
    }

    /**
     * 切换手数和金额
     */
    private fun switchVolumeUint(volumeUint: String) {
        mBinding.tvVolumeUnit.text = volumeUint
        var unitStr = ""
        if (mViewModel.unit == OrderViewModel.UNIT_AMOUNT) {
            mViewModel.inputVolume = mBinding.etVolume.text.toString()
            mViewModel.inputAmount = OrderUtil.getAmountFromVolume(mViewModel.productData, mViewModel.inputVolume, mViewModel.tradeType, getInputPrice())
            unitStr = "≈ ${mViewModel.inputVolume} ${getString(R.string.lots)}"
        } else {
            mViewModel.inputAmount = mBinding.etVolume.text.toString()
            mViewModel.inputVolume = OrderUtil.getVolumeFromAmount(mBinding.etVolume.text.toString(), mViewModel.productData, mViewModel.tradeType, getInputPrice())
            unitStr = "≈ ${mViewModel.inputAmount} ${mViewModel.currencyType}"
        }
        mBinding.tvTransfer.text = unitStr
        showMaxOpen()
        if (mViewModel.unit == OrderViewModel.UNIT_AMOUNT) {
            var showAmount = mViewModel.inputAmount
            if (showAmount.mathCompTo(mViewModel.getMinAmount(getInputPrice())) == -1) {
                showAmount = mViewModel.getMinAmount(getInputPrice())
                mViewModel.inputAmount = showAmount
            }
            if (showAmount.mathCompTo(mViewModel.maxOpenAmount) == 1) {
                showAmount = mViewModel.maxOpenAmount
                mViewModel.inputAmount = showAmount
            }
            mBinding.etVolume.setText(showAmount.numCurrencyFormat())
        } else {
            var showVolume = mViewModel.inputVolume
            if (showVolume.mathCompTo(mViewModel.minVolume) == -1) {
                showVolume = mViewModel.minVolume
                mViewModel.inputVolume = showVolume
            }
            if (showVolume.mathCompTo(mViewModel.maxOpenVolume) == 1) {
                showVolume = mViewModel.maxOpenVolume
                mViewModel.inputVolume = showVolume
            }
            mBinding.etVolume.setText(showVolume.numFormat(2))
        }
        mViewModel.isSwitchUnit = true
    }

    /**
     * 是否显示手数和金额互换标签
     */
    private fun showOrNotTransferTv() {
        mBinding.tvTransfer.isVisible = mBinding.etVolume.hasFocus() && mBinding.etVolume.text.toString().isNotBlank()
    }

    /**
     * 订单确认弹框
     */
    private fun showSubmitOrderConfirmDialog() {
        OrderConfirmDialog.Builder(this)
            .setTitle(getString(R.string.order_confirmation_uppercase))
            .setOrderViewModel(mViewModel)
            .setOnConfirm { isShowNotAgain ->
                submitOrder()
                if (isShowNotAgain) {
                    mViewModel.setOrderConfirmation(true)
                }
                mViewModel.tradeOpenConfirm()
            }
            .build()
            .showDialog()
    }

    /**
     * 提交订单
     */
    private fun submitOrder() {
//        val inputVolume = mBinding.etVolume.text.toString()
        if (mViewModel.unit == OrderViewModel.UNIT_LOTS) {
            val inputVolume = mViewModel.inputVolume
            if (inputVolume.mathCompTo(mViewModel.minVolume) == -1) {
                val tipStr = "${getString(R.string.the_minimum_value_volume_is, mViewModel.minVolume)} ${getString(R.string.lots)} "
                ToastUtil.showToast(tipStr)
                return
            }
        } else {
            val inputAmount = mBinding.etVolume.text.toString()
            if (inputAmount.mathCompTo(mViewModel.getMinAmount(getInputPrice())) == -1) {
                val tipStr = "${getString(R.string.the_minimum_value_volume_is, mViewModel.getMinAmount(getInputPrice()))} ${mViewModel.currencyType} "
                ToastUtil.showToast(tipStr)
                return
            }
        }

        if (!OrderVolumeUtil.isDivideExactly(mViewModel.inputVolume.toDoubleCatching(), mViewModel.productData?.stepvolume.toDoubleCatching())) {
            // 输入手数有误，请重新输入
            ToastUtil.showToast(getString(R.string.number_of_lots_re_enter))
            return
        }

        if (mBinding.tvTakeProfitTip.isVisible) {
            ToastUtil.showToast(getString(R.string.the_set_take_is_invalid))
            return
        }

        if (mBinding.tvStopLossTip.isVisible) {
            ToastUtil.showToast(getString(R.string.the_set_stop_is_invalid))
            return
        }

        if (mBinding.tvAtPriceTip.isVisible || (mBinding.clAtPrice.isVisible && mBinding.etAtPrice.text.toString().isBlank())) {
            ToastUtil.showToast(getString(R.string.the_set_open_is_invalid))
            return
        }

        if (mBinding.tvStopLimitTip.isVisible || (mBinding.clStopLimitPrice.isVisible && mBinding.etStopLimitPrice.text.toString().isBlank())) {
            ToastUtil.showToast(getString(R.string.the_set_stop_limit_is_invalid))
            return
        }

        mViewModel.volumeParam = mViewModel.inputVolume
        mViewModel.tpParam = mBinding.etTakeProfit.text.toString()
        mViewModel.slParam = mBinding.etStopLoss.text.toString()
        mViewModel.atPriceParam = mBinding.etAtPrice.text.toString()
        mViewModel.stopLimitPriceParam = mBinding.etStopLimitPrice.text.toString()

        if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_MARKET) {
            if (UserDataUtil.isStLogin()) {
                mViewModel.stSubmitOrder()
            } else {
                mViewModel.submitOrder()
            }
        } else {
            val pendingPriceNum = mViewModel.atPriceParam.toDoubleCatching()
            if (pendingPriceNum <= 0.0) {
                ToastUtil.showToast(getString(R.string.the_set_open_is_invalid))
                return
            }
            if (UserDataUtil.isStLogin()) {
                mViewModel.stSubmitPendingOrder()
            } else {
                mViewModel.submitPendingOrder()
            }
        }
    }

    private fun showFundLackDialog() {
        if (UserDataUtil.isDemoAccount()) {
            ToastUtil.showToast(getString(R.string.insufficient_funds))
        } else {
            CenterActionDialog.Builder(this)
                .setTitle(getString(R.string.order_rejected))
                .setEndText(getString(R.string.deposit))
                .setContent(getString(R.string.insufficient_funds_to_deposit))
                .setOnEndListener {
                    NewHtmlActivity.openActivity(this, url = UrlConstants.HTML_FUND_DEPOSIT)
                }
                .build()
                .showDialog()
        }
    }

    private fun showTokenErrorDialog(msg: String?) {
        CenterActionDialog.Builder(this)
            .setContent(msg.ifNull())
            .setSingleButton(true)
            .setOnDismissListener {
                // 退出登录
                EventBus.getDefault().post(NoticeConstants.LOGOUT_ACCOUNT)
            }.build()
            .showDialog()
    }

    private fun showCheckDelayDialog() {
        CenterActionDialog.Builder(this)
            .setTitle(
                getString(
                    R.string.do_you_wish_order_at_x,
                    if (mViewModel.tradeType == "0") "${mViewModel.productData?.ask}" else "${mViewModel.productData?.bid}"
                )
            )
            .setContent(getString(R.string.price_misquote_by_incurred))
            .setOnStartListener {
                WsManager.getInstance().resetConnect()
            }
            .setOnEndListener {
                mViewModel.volumeParam = mBinding.etVolume.text.toString()
                mViewModel.tpParam = mBinding.etTakeProfit.text.toString()
                mViewModel.slParam = mBinding.etStopLoss.text.toString()
                mViewModel.submitOrder(0)
            }.build()
            .showDialog()
    }

    private fun showHintDataDialog(hintMsg: String) {
        CenterActionDialog.Builder(this)
            .setContent(hintMsg)
            .setSingleButton(true)
            .build()
            .showDialog()
    }

    private fun showSearchDialog() {
        mSearchDialog = BottomSymbolSearchDialog.Builder(this)
            .moveUpToKeyboard(false)
            .setViewMode(true)
            .setDestroyOnDismiss(true)
            .setFrom(FROM_ORDER)
            .setOnSelectItem {
                switchProduct(it)
            }
            .build()
        mSearchDialog?.showDialog()
    }

    private fun dismissSearchDialog() {
        mSearchDialog?.dismissDialog()
    }

    /**
     * 检查并设置tvNext状态
     */
    private fun checkTvNext() {
        val volumeIsValid = mBinding.tvVolumeTip.isVisible.not()
        val atPriceIsValid = mBinding.tvAtPriceTip.isVisible.not()
        val stopLimitPriceIsValid = mBinding.tvStopLimitTip.isVisible.not()
        val takeProfileIsValid = mBinding.tvTakeProfitTip.isVisible.not()
        val stopLossIsValid = mBinding.tvStopLossTip.isVisible.not()

        var valid = volumeIsValid
        if (mViewModel.tradeTypeIndex != OrderViewModel.INDEX_MARKET) {
            valid = valid && atPriceIsValid
            if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_STOP_LIMIT_BUY_STOP_LIMIT) {
                valid = valid && stopLimitPriceIsValid
            }
        }

        if (mViewModel.takeProfitCb) {
            valid = valid && takeProfileIsValid
        }
        if (mViewModel.stopLossCb) {
            valid = valid && stopLossIsValid
        }
        if (valid) {
            mBinding.tvNext.isEnabled = true
            mBinding.tvNext.setTextColor(getColor(cebffffff))
            mBinding.tvNext.setBackgroundResource(if (mViewModel.tradeType == OrderViewModel.TRADE_SELL) draw_shape_cf44040_r100 else draw_shape_c00c79c_r100)

        } else {
            mBinding.tvNext.isEnabled = false
            mBinding.tvNext.setBackgroundResource(draw_shape_c0a1e1e1e_c0affffff_r100)
            mBinding.tvNext.setTextColor(color_c731e1e1e_c61ffffff)
        }

    }

    /**
     * 设置订单类型buy 或 sell
     * tradeType: OrderViewModel.TRADE_SELL 是 sell； OrderViewModel.TRADE_BUY是buy
     */
    private fun selectedOrderType(tradeType: String) {
        mViewModel.tradeType = tradeType
        if (OrderViewModel.TRADE_SELL == tradeType) {
            mBinding.llSell.setBackgroundResource(shape_cf44040_r10)
            mBinding.llBuy.setBackgroundResource(draw_shape_c0a1e1e1e_c262930_r10)
            mBinding.tvSell.setTextColor(getColor(cebffffff))
            mBinding.tvSellPrice.setTextColor(getColor(cebffffff))
            mBinding.tvBuy.setTextColor(color_ca61e1e1e_c99ffffff)
            mBinding.tvBuyPrice.setTextColor(color_ca61e1e1e_c99ffffff)
        } else {
            mBinding.llSell.setBackgroundResource(draw_shape_c0a1e1e1e_c262930_r10)
            mBinding.llBuy.setBackgroundResource(shape_c00c79c_r10)
            mBinding.tvSell.setTextColor(color_ca61e1e1e_c99ffffff)
            mBinding.tvSellPrice.setTextColor(color_ca61e1e1e_c99ffffff)
            mBinding.tvBuy.setTextColor(getColor(cebffffff))
            mBinding.tvBuyPrice.setTextColor(getColor(cebffffff))
        }
        mViewModel.initTradeTypeList(this)
        typeAdapter.selectTitle =
            mViewModel.tradeTypeList.getOrNull(mViewModel.tradeTypeIndex)?.getShowItemValue()
        typeAdapter.notifyDataSetChanged()
        initTradeTypeView()
        showMaxOpen()
        showTransfer()
        setTvNext()
    }

    private fun setTvNext() {
        mBinding.tvNext.setTextColor(getColor(cebffffff))
        mBinding.tvNext.setBackgroundResource(if (mViewModel.tradeType == OrderViewModel.TRADE_SELL) draw_shape_cf44040_r100 else draw_shape_c00c79c_r100)
    }

    /**
     * 初始订单信息
     */
    private fun showProductData() {
        mBinding.tvGoProduct.text = mViewModel.productName
        val data = VAUSdkUtil.symbolList().firstOrNull {
            it.symbol == mViewModel.productName
        } ?: return

        mViewModel.productData = data
        mViewModel.digits = data.digits
        mViewModel.minVolume = data.minvolume ?: "0.01"
        mViewModel.maxVolume = data.maxvolume
        mViewModel.stepVolume = data.stepvolume
        mViewModel.minProfit = "${1 / 10.toDouble().pow(mViewModel.digits.toDouble())}"
        OrderUtil.editextTop(
            mBinding.tvSellPrice,
            data.bid.formatProductPrice(data.digits, false)
        )
        OrderUtil.editextTop(
            mBinding.tvBuyPrice,
            data.ask.formatProductPrice(data.digits, false)
        )
        mBinding.tvSpread.text = data.spreadUI
        selectedOrderType(mViewModel.tradeType)
        showTakeProfitOrStopLossAndRange(data)
        showMaxOpen()
        initVolume()
    }

    /**
     * 展示保证金和可用保证金
     */
    @SuppressLint("SetTextI18n")
    private fun showMargin() {

        val margin = mViewModel.getMargin(mViewModel.inputVolume, getInputPrice())
        val freeMargin = mViewModel.getFreeMargin()
        mBinding.tvMargin.text = "$margin ${mViewModel.currencyType}".arabicReverseTextByFlag(" ")
        mBinding.tvFreeMargin.text = "$freeMargin ${mViewModel.currencyType}".arabicReverseTextByFlag(" ")
        if (margin.mathCompTo(freeMargin) == 1) {
            mBinding.tvFreeMargin.setTextColor(getColor(cf44040))
        } else {
            mBinding.tvFreeMargin.setTextColor(color_c1e1e1e_cebffffff)
        }
        mBinding.tvMarginLevel.setTextDiff("${mViewModel.getMarginLevelAfterTrading(mViewModel.productData, mViewModel.inputVolume, getInputPrice())}%")
    }

    /**
     * 计算挂单价格和市价点差
     */
    @SuppressLint("SetTextI18n")
    private fun showDistance() {
        if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_MARKET) {
            return
        }
        mViewModel.productData?.let {
            // distance = 挂单价- 买价/卖价
            var atPriceStr = mBinding.etAtPrice.text.toString()
            if (atPriceStr.isEmpty()) {
                atPriceStr = "0"
            }
            val atPriceFloat = atPriceStr.toFloatCatching()
            val abs = if (mViewModel.tradeType == OrderViewModel.TRADE_SELL) {
                abs(atPriceFloat - it.bid)
            } else {
                abs(atPriceFloat - it.ask)
            }
            val distance: Float = abs * 10.0.pow((it.digits).toDouble()).toFloat()
            mBinding.tvDistance.text = "${distance.numFormat(0, true)} ${getString(R.string.points)}"
        }
    }

    /**
     * 检查挂单价格是否合法
     */
    private fun checkAtPrice() {
        val atRange = mBinding.tvAtPriceRange.text.toString().replace(">=", "").replace("<=", "")
        val atCompareResults = if (mViewModel.tradeType == OrderViewModel.TRADE_BUY) {
            if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT) 1 else -1
        } else {
            if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT) -1 else 1
        }

        val atPrice = mBinding.etAtPrice.text.toString()
        if (atPrice.mathCompTo(atRange) == atCompareResults) {
            val tipStr = if (atCompareResults == 1) {
                "${getString(R.string.max_value)}:$atRange"
            } else {
                "${getString(R.string.min_value)}:$atRange"
            }
            mBinding.tvAtPriceTip.text = tipStr
            mBinding.tvAtPriceTip.isVisible = true
            mBinding.tvDistanceTitle.isVisible = false
            mBinding.tvDistance.isVisible = false
        } else {
            mBinding.tvAtPriceTip.isVisible = false
            mBinding.tvDistanceTitle.isVisible = true
            mBinding.tvDistance.isVisible = true
        }
    }

    /**
     * 检查限价价格是否合法
     */
    private fun checkStopLimitPrice() {
        if (mViewModel.tradeTypeIndex != OrderViewModel.INDEX_SELL_STOP_LIMIT_BUY_STOP_LIMIT) {
            return
        }

        val stopLimitPriceRange = mBinding.tvStopLimitPriceRange.text.toString().replace(">=", "").replace("<=", "")
        val atCompareResults = if (mViewModel.tradeType == OrderViewModel.TRADE_BUY) {
            1
        } else {
            -1
        }
        val stopLimitPrice = mBinding.etStopLimitPrice.text.toString()
        if (stopLimitPrice.mathCompTo(stopLimitPriceRange) == atCompareResults) {
            val tipStr = if (atCompareResults == 1) {
                "${getString(R.string.max_value)}:$stopLimitPriceRange"
            } else {
                "${getString(R.string.min_value)}:$stopLimitPriceRange"
            }
            mBinding.tvStopLimitTip.text = tipStr
            mBinding.tvStopLimitTip.isVisible = true
        } else {
            mBinding.tvStopLimitTip.isVisible = false
        }
    }

    /**
     * 检查止盈价格是否合法
     */
    private fun checkTakeProfit(tpCompareResults: Int) {
        val takeProfit = mBinding.etTakeProfit.text.toString()
        if (takeProfit.mathCompTo(mViewModel.takeProfitRange) == tpCompareResults) {
            val tipStr = if (tpCompareResults == 1) {
                "${getString(R.string.max_value)}:${mViewModel.takeProfitRange}"
            } else {
                "${getString(R.string.min_value)}:${mViewModel.takeProfitRange}"
            }
            mBinding.tvTakeProfitTip.text = tipStr
            mBinding.tvTakeProfitTip.isVisible = mViewModel.takeProfitCb
            mBinding.tvEstimatedProfitTitle.isVisible = false
            mBinding.tvEstimatedProfit.isVisible = false
        } else {
            mBinding.tvTakeProfitTip.isVisible = false
            mBinding.tvEstimatedProfitTitle.isVisible = mViewModel.takeProfitCb
            mBinding.tvEstimatedProfit.isVisible = mViewModel.takeProfitCb
        }
    }

    /**
     * 检查止损价格是否合法
     */
    private fun checkStopLoss(slCompareResults: Int) {
        val stopLoss = mBinding.etStopLoss.text.toString()
        if (stopLoss.mathCompTo(mViewModel.stopLossRange) == slCompareResults) {
            val tipStr = if (slCompareResults == 1) {
                "${getString(R.string.max_value)}:${mViewModel.stopLossRange}"
            } else {
                "${getString(R.string.min_value)}:${mViewModel.stopLossRange}"
            }
            mBinding.tvStopLossTip.text = tipStr
            mBinding.tvStopLossTip.isVisible = mViewModel.stopLossCb
            mBinding.tvEstimatedLossTitle.isVisible = false
            mBinding.tvEstimatedLoss.isVisible = false
        } else {
            mBinding.tvStopLossTip.isVisible = false
            mBinding.tvEstimatedLossTitle.isVisible = mViewModel.stopLossCb
            mBinding.tvEstimatedLoss.isVisible = mViewModel.stopLossCb
        }
    }

    /**
     * 检查止盈止损是否合法
     */
    private fun checkTakeProfitAndStopLoss() {
        if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_MARKET) {
            val tpCompareResults = if (mViewModel.tradeType == OrderViewModel.TRADE_SELL) 1 else -1
            checkTakeProfit(tpCompareResults)
            checkStopLoss(-tpCompareResults)
        } else {
            val atCompareResults = if (mViewModel.tradeType == OrderViewModel.TRADE_BUY) {
                if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT) 1 else -1
            } else {
                if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT) -1 else 1
            }
            val tpCompareResults =
                if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT) -atCompareResults else atCompareResults

            checkTakeProfit(tpCompareResults)
            checkStopLoss(-tpCompareResults)
        }
    }

    /**
     * 检查网络状态和闭市状态
     */
    private fun checkNetWorkStatus(data: ShareProductData) {
        if (mViewModel.isConnected.not()) {
            mBinding.tvNetworkStatus.text = getString(R.string.network_disconnected_please_again_later)
            mBinding.tvNetworkStatus.isVisible = true
            mBinding.tvGoProduct.setPadding(0, 16.dp2px(), 0, 8.dp2px())
        } else if (data.marketClose) {
            mBinding.tvNetworkStatus.text = getString(R.string.market_is_closed)
            mBinding.tvNetworkStatus.isVisible = true
            mBinding.tvGoProduct.setPadding(0, 16.dp2px(), 0, 8.dp2px())
        } else {
            mBinding.tvNetworkStatus.isVisible = false
            mBinding.tvGoProduct.setPadding(0, 8.dp2px(), 0, 8.dp2px())
        }
    }

    /**
     * 同步产品行情，并刷新
     */
    private fun syncShowProductData() {
        val data = VAUSdkUtil.symbolList().firstOrNull {
            it.symbol == mViewModel.productName
        } ?: return

        mViewModel.productData = data
        // 检查闭市状态 && 断开连接
//        checkNetWorkStatus(data)
        //刷新买卖价
        showProduceInfo(data)
        var volume = mViewModel.inputVolume
        if (TextUtils.isEmpty(volume)) volume = "0.01"
        // 止损水平= 止损位 / 10的小数位次方
        //TODO Felix 不用重复计算
        val stopLossLevel = data.stopslevel.mathDiv("${10.0.pow(data.digits)}", data.digits + 1)
        // 如果是挂单，计算挂单价格和市价点差
        showDistance()
        // 市价
        if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_MARKET) {
            //计算市价的止盈止损
            computeMarketTakeProfitAndStopLoss(data, stopLossLevel)
            //计算预估盈利
            showEstimated(data, volume)
        } else { // 挂单
            //挂单刷新挂单价格范围和止盈止损价格范围
            refreshAtPriceRange(data, stopLossLevel)
            //检查挂单价格是否合法
            checkAtPrice()
            //检查限价价格是否合法
            checkStopLimitPrice()
        }
        //检查止盈止损是否合法
        checkTakeProfitAndStopLoss()

        //手数和金额互换标签显示
        showTransfer()

        //展示占用保证金和可用保证金
        showMargin()
        checkVolume(mBinding.etVolume.text.toString())
        //检查并设置tvNext状态
//        checkTvNext()
        val maxOpenStr = mBinding.tvMaxOpen.text.toString()
        val maxOpenAmount = maxOpenStr.substringCatching(maxOpenStr.lastIndexOf(" ") + 1, maxOpenStr.length)
        //最大可开手数不是实时更新的，当maxOpenAmount没有计算过或者当前值是0时计算
        if (mViewModel.isFirstSync || maxOpenAmount.mathCompTo("0") != 1) {
            mViewModel.isFirstSync = false
            //显示最大可开手数
            showMaxOpen()
        }
    }

    /**
     * 市价计算预估盈利
     */
    private fun showEstimated(data: ShareProductData, volume: String) {
        mBinding.tvEstimatedProfit.text =
            if (mViewModel.takeProfitCb) {
                buildString {
                    append(
                        (VAUSdkUtil.getProfitLoss(
                            data,
                            "${if (OrderViewModel.TRADE_SELL == mViewModel.tradeType) data.bid else data.ask}",
                            volume,
                            mViewModel.tradeType,
                            mBinding.etTakeProfit.text.toString()
                        ).toString().numCurrencyFormat() + " " + mViewModel.currencyType).arabicReverseTextByFlag(" ")
                    )
                }.arabicReverseTextByFlag(": ")
            } else {
                "-- ${mViewModel.currencyType}"
            }

        mBinding.tvEstimatedLoss.text =
            if (mViewModel.stopLossCb) {
                buildString {
                    append(
                        (VAUSdkUtil.getProfitLoss(
                            data,
                            "${if (OrderViewModel.TRADE_SELL == mViewModel.tradeType) data.bid else data.ask}",
                            volume,
                            mViewModel.tradeType,
                            mBinding.etStopLoss.text.toString()
                        ).toString().numCurrencyFormat() + " " + mViewModel.currencyType).arabicReverseTextByFlag(" ")
                    )
                }.arabicReverseTextByFlag(": ")
            } else {
                "-- ${mViewModel.currencyType}"
            }
    }

    /**
     * 展示产品买卖价
     */
    private fun showProduceInfo(data: ShareProductData) {
        OrderUtil.editextTop(
            mBinding.tvSellPrice,
            data.bid.formatProductPrice(data.digits, false)
        )
        OrderUtil.editextTop(
            mBinding.tvBuyPrice,
            data.ask.formatProductPrice(data.digits, false)
        )
        mBinding.tvSpread.text = data.spreadUI
    }

    /**
     * 计算市价的止盈止损
     * 买入：止损 低于等于（卖出价-止损水平）；止盈：大于等于（卖出价+止损水平）
     * 卖出：止损 大于等于（买入价+止损水平）；止盈：低于等于（买入价-止损水平）
     */
    private fun computeMarketTakeProfitAndStopLoss(data: ShareProductData, stopLossLevel: String) {
        // 止盈止损范围
        if (mViewModel.tradeType == OrderViewModel.TRADE_SELL) {
            // sell
            // 止盈范围（低于等于（买入价-止损水平））
            val stopLossLevelYES = "${data.ask}".mathSub(stopLossLevel)
            mViewModel.takeProfitRange = stopLossLevelYES.numFormat(mViewModel.digits)
            // 止损范围（大于等于（买入价+止损水平））
            val stopLossLevelNO = "${data.ask}".mathAdd(stopLossLevel)
            mViewModel.stopLossRange = stopLossLevelNO.numFormat(mViewModel.digits)
        } else {
            // buy
            // 止盈范围（大于等于（卖出价+止损水平））
            val stopLossLevelYES = "${data.bid}".mathAdd(stopLossLevel)
            mViewModel.takeProfitRange = stopLossLevelYES.numFormat(mViewModel.digits)
            // 止损范围（低于等于（卖出价-止损水平））
            val stopLossLevelNO = "${data.bid}".mathSub(stopLossLevel)
            mViewModel.stopLossRange = stopLossLevelNO.numFormat(mViewModel.digits)
        }
    }

    /**
     * 重新计算最大可开手数并将进度条重置为0
     */
    private fun reSetMaxOpen() {
        showMaxOpen()
        mViewModel.isInputVolumeFromKeyBoard = true
        mBinding.volSeekBar.setProgress(0)
    }

    /**
     * 显示最大可开手数
     * 当手数单位是手时，显示手数；是金额时显示金额
     */
    @SuppressLint("SetTextI18n")
    private fun showMaxOpen() {
        mViewModel.maxOpenVolume = getMaxOpenVolume()
        mViewModel.maxOpenAmount = getMaxOpenAmount()
        if (mViewModel.unit == OrderViewModel.UNIT_LOTS) {
            mBinding.tvMaxOpen.text = "${getString(R.string.max_open)} ${mViewModel.maxOpenVolume}"
        } else {
            mBinding.tvMaxOpen.text = "${getString(R.string.max_open)} ${mViewModel.maxOpenAmount}"
        }
        checkVolume(mBinding.etVolume.text.toString())
    }

    /**
     * 获取最大可开手数
     */
    private fun getMaxOpenVolume(): String {
        var maxOpenVolume = OrderUtil.getMaxOpenVolume(mViewModel.tradeType, mViewModel.productData, getInputPrice())
        val valueDiv = maxOpenVolume.mathDiv(mViewModel.stepVolume, 1)
        val valueDivInt = valueDiv.substringCatching(0, valueDiv.indexOf("."), valueDiv)
        maxOpenVolume = valueDivInt.mathMul(mViewModel.stepVolume).numFormat(2)
        if (maxOpenVolume.mathCompTo(mViewModel.maxVolume) == 1) {
            maxOpenVolume = mViewModel.maxVolume
        }
        return maxOpenVolume
    }

    /**
     * 获取最大可开手数对应的金额
     */
    private fun getMaxOpenAmount(): String {
        var maxOpenVolume = getMaxOpenVolume()
        if (maxOpenVolume.mathCompTo(mViewModel.maxVolume) == 1) {
            maxOpenVolume = mViewModel.maxVolume
        }
        val productMaxOpen = OrderUtil.getAmountFromVolume(mViewModel.productData, maxOpenVolume, mViewModel.tradeType, getInputPrice())
        return productMaxOpen
    }

    /**
     * 初始化手数
     * 手数规则：没有传入默认手数时使用产品存在本地的手数，需要检查本地的手是否能被手数步长整除，能整除则用本地的手，否则使用：手数步长+（(本地的手/手数步长）取整）* 手数步长
     *
     * 如果有默认手数，则将最小手数替换为默认手数计算。
     */
    private fun initVolume() {
        val realShowVolume = if (TextUtils.isEmpty(mViewModel.defaultLot)) {
            val localVolume = mViewModel.readStorageProductLots()
            if (localVolume == "0") {
                0
            } else if (OrderVolumeUtil.isDivideExactly(
                    localVolume.toDoubleCatching(),
                    mViewModel.stepVolume.toDoubleCatching()
                )
            ) // 如果可以整出
                localVolume.toDoubleCatching()
            else
                mViewModel.stepVolume.toDoubleCatching() + (localVolume.toDoubleCatching() / mViewModel.stepVolume.toDoubleCatching()).toInt() * mViewModel.stepVolume.toDoubleCatching()
        } else {
            // 如果可以整出
            if (OrderVolumeUtil.isDivideExactly(
                    mViewModel.defaultLot.toDoubleCatching(),
                    mViewModel.stepVolume.toDoubleCatching()
                )
            )
                mViewModel.defaultLot.toDoubleCatching()
            else
                mViewModel.stepVolume.toDoubleCatching() + (mViewModel.defaultLot.toDoubleCatching() / mViewModel.stepVolume.toDoubleCatching()).toInt() * mViewModel.stepVolume.toDoubleCatching()
        }

        mBinding.etVolume.setText(realShowVolume.numFormat(2, false))
        mViewModel.inputVolume = mBinding.etVolume.text.toString()
    }

    /**
     * 点击加号，手数计算
     */
    private fun addVolume() {
        showMaxOpen()
        if (mViewModel.unit == OrderViewModel.UNIT_LOTS) {
            val currentCount = mBinding.etVolume.text.toString()
            if (currentCount.mathCompTo(mViewModel.maxOpenVolume) != -1) {
                checkVolume(currentCount)
                return
            }
            val editCount = currentCount.mathAdd(mViewModel.stepVolume)
            //手数
            mBinding.etVolume.setText(editCount.numFormat(2, false))
        } else {
            // 金额的增加
            val currentCount = mBinding.etVolume.text.toString()
            if (currentCount.mathCompTo(mViewModel.getMinAmount(getInputPrice())) == -1) {
                mBinding.etVolume.setText(mViewModel.getMinAmount(getInputPrice()))
                mBinding.etVolume.setSelection(mBinding.etVolume.text.toString().length)
                return
            }
            if (currentCount.mathCompTo(mViewModel.maxOpenAmount) == 1) {
                mBinding.etVolume.setText(mViewModel.maxOpenAmount)
                mBinding.etVolume.setSelection(mBinding.etVolume.text.toString().length)
                return
            }
            if (currentCount.mathCompTo(mViewModel.maxOpenAmount) != -1) return
            val editCount = currentCount.mathAdd(mViewModel.getMinStepAmount(getInputPrice()))
            val div = editCount.mathDiv(mViewModel.getMinStepAmount(getInputPrice()), 0)
            var finalValue = div.mathMul(mViewModel.getMinStepAmount(getInputPrice()))
            if (finalValue.mathCompTo(mViewModel.maxOpenAmount) == 1) {
                finalValue = mViewModel.maxOpenAmount
            }
            mBinding.etVolume.setText(finalValue.numCurrencyFormat())
        }
        mBinding.etVolume.setSelection(mBinding.etVolume.text.toString().length)
        mViewModel.isInputVolumeFromKeyBoard = true
        mBinding.volSeekBar.setProgress(0)
        mViewModel.tradePagePlusMinusBtnClick("Volume", "Plus")
    }

    /**
     * 点击减号，手数计算
     */
    private fun subVolume() {
        showMaxOpen()
        if (mViewModel.unit == OrderViewModel.UNIT_LOTS) {
            val currentCount = mBinding.etVolume.text.toString()
            if (currentCount.isEmpty()) {
                initVolume()
                mBinding.etVolume.setSelection(mBinding.etVolume.text.toString().length)
                return
            }
            val editCount = currentCount.mathSub(mViewModel.stepVolume)
            if (currentCount.mathCompTo(mViewModel.minVolume) != 1) return
            mBinding.etVolume.setText(editCount.numFormat(2, false))
        } else {
            //金额的减少
            val currentCount = mBinding.etVolume.text.toString()
            if (currentCount.mathCompTo(mViewModel.getMinAmount(getInputPrice())) == -1) {
                mBinding.etVolume.setText(mViewModel.getMinAmount(getInputPrice()))
                mBinding.etVolume.setSelection(mBinding.etVolume.text.toString().length)
                return
            }
            if (currentCount.mathCompTo(mViewModel.maxOpenAmount) == 1) {
                mBinding.etVolume.setText(mViewModel.maxOpenAmount)
                mBinding.etVolume.setSelection(mBinding.etVolume.text.toString().length)
                return
            }
            if (currentCount.mathCompTo(mViewModel.getMinAmount(getInputPrice())) != 1) return
            val editCount = currentCount.mathSub(mViewModel.getMinStepAmount(getInputPrice()))
            val div = editCount.mathDiv(mViewModel.getMinStepAmount(getInputPrice()), 0)
            var finalValue = div.mathMul(mViewModel.getMinStepAmount(getInputPrice()))
            if (finalValue.mathCompTo(mViewModel.getMinStepAmount(getInputPrice())) == -1) {
                finalValue = mViewModel.getMinAmount(getInputPrice())
            }
            mBinding.etVolume.setText(finalValue.numCurrencyFormat())
        }
        mBinding.etVolume.setSelection(mBinding.etVolume.text.toString().length)
        mViewModel.isInputVolumeFromKeyBoard = true
        mBinding.volSeekBar.setProgress(0)
        mViewModel.tradePagePlusMinusBtnClick("Volume", "Minus")
    }

    /**
     * 手数格式化，固定两位小数
     */
    private fun formatVolume() {
        if (mBinding.etVolume.text.toString().isEmpty()) {
            return
        }
        if (mViewModel.unit == OrderViewModel.UNIT_LOTS) {
            val currentCount = mBinding.etVolume.text.toString()
            mBinding.etVolume.setText(currentCount.numFormat(2, false))
        } else {
            val currentCount = mBinding.etVolume.text.toString()
            mBinding.etVolume.setText(currentCount.numCurrencyFormat())
        }
    }

    /**
     * 点击减号，挂单价格计算
     */
    private fun subAtPrice() {
        val currentCount = mBinding.etAtPrice.text.toString()
        if (currentCount.isEmpty()) {
            initAtPrice()
            mBinding.etAtPrice.setSelection(mBinding.etStopLimitPrice.text.toString().length)
            return
        }
        if (currentCount.mathCompTo("0") != 1) return
        val editCount = currentCount.mathSub(mViewModel.minProfit)
        mBinding.etAtPrice.setText(editCount.numFormat(mViewModel.digits, false))
        mBinding.etAtPrice.setSelection(mBinding.etAtPrice.text.toString().length)
        mViewModel.tradePagePlusMinusBtnClick("Pending Price", "Minus")
    }

    /**
     * 点击加号，挂单价格计算
     */
    private fun addAtPrice() {
        val currentCount = mBinding.etAtPrice.text.toString()
        if (currentCount.isEmpty()) {
            initAtPrice()
            mBinding.etAtPrice.setSelection(mBinding.etStopLimitPrice.text.toString().length)
            return
        }
        val editCount = currentCount.mathAdd(mViewModel.minProfit)
        mBinding.etAtPrice.setText(editCount.numFormat(mViewModel.digits, false))
        mBinding.etAtPrice.setSelection(mBinding.etAtPrice.text.toString().length)
        mViewModel.tradePagePlusMinusBtnClick("Pending Price", "Plus")
    }

    /**
     * 挂单价格格式化，小数位和产品支持的小数位相同
     */
    private fun formatAtPrice() {
        val atPrice = mBinding.etAtPrice.text.toString()
        if (atPrice.isEmpty()) {
            return
        }
        mBinding.etAtPrice.setText(atPrice.numFormat(mViewModel.digits, false))
    }

    /**
     * 点击加号，限价价格计算
     */
    private fun addStopLimitPrice() {
        val currentCount = mBinding.etStopLimitPrice.text.toString()
        if (currentCount.isEmpty()) {
            initStopLimitPrice()
            mBinding.etStopLimitPrice.setSelection(mBinding.etStopLimitPrice.text.toString().length)
            return
        }
        val editCount = currentCount.mathAdd(mViewModel.minProfit)
        mBinding.etStopLimitPrice.setText(editCount.numFormat(mViewModel.digits, false))
        mBinding.etStopLimitPrice.setSelection(mBinding.etStopLimitPrice.text.toString().length)
        mViewModel.tradePagePlusMinusBtnClick("Stop Limit Price", "Plus")
    }

    /**
     * 点击减号，限价价格计算
     */
    private fun subStopLimitPrice() {
        val currentCount = mBinding.etStopLimitPrice.text.toString()
        if (currentCount.isEmpty()) {
            initStopLimitPrice()
            mBinding.etStopLimitPrice.setSelection(mBinding.etStopLimitPrice.text.toString().length)
            return
        }
        if (currentCount.mathCompTo("0") != 1) return
        val editCount = currentCount.mathSub(mViewModel.minProfit)
        mBinding.etStopLimitPrice.setText(editCount.numFormat(mViewModel.digits, false))
        mBinding.etStopLimitPrice.setSelection(mBinding.etStopLimitPrice.text.toString().length)
        mViewModel.tradePagePlusMinusBtnClick("Stop Limit Price", "Minus")
    }

    /**
     * 限价价格格式化，小数位和产品支持的小数位相同
     */
    private fun formatStopLimitPrice() {
        val stopLimitPrice = mBinding.etStopLimitPrice.text.toString()
        if (stopLimitPrice.isEmpty()) {
            return
        }
        mBinding.etStopLimitPrice.setText(stopLimitPrice.numFormat(mViewModel.digits, false))
    }

    /**
     * 点击减号，止盈价格计算
     */
    private fun subTakeProfit() {
        val currentCount = mBinding.etTakeProfit.text.toString()
        if (currentCount.isEmpty()) {
            mBinding.etTakeProfit.setText(mViewModel.takeProfitRange)
            mBinding.etTakeProfit.setSelection(mBinding.etTakeProfit.text.toString().length)
            return
        }
        if (currentCount.mathCompTo("0") != 1) return
        val editCount = currentCount.mathSub(mViewModel.minProfit)
        mBinding.etTakeProfit.setText(
            editCount.numFormat(mViewModel.digits)
        )
        mBinding.etTakeProfit.setSelection(mBinding.etTakeProfit.text.toString().length)
        mViewModel.tradePagePlusMinusBtnClick("Take Profit", "Minus")
    }

    /**
     * 点击加号，止盈价格计算
     */
    private fun addTakeProfit() {
        val currentCount = mBinding.etTakeProfit.text.toString()
        if (currentCount.isEmpty()) {
            mBinding.etTakeProfit.setText(mViewModel.takeProfitRange)
            mBinding.etTakeProfit.setSelection(mBinding.etTakeProfit.text.toString().length)
            return
        }
        val editCount = currentCount.mathAdd(mViewModel.minProfit)
        mBinding.etTakeProfit.setText(editCount.numFormat(mViewModel.digits, false))
        mBinding.etTakeProfit.setSelection(mBinding.etTakeProfit.text.toString().length)
        mViewModel.tradePagePlusMinusBtnClick("Take Profit", "Plus")
    }

    /**
     * 止盈价格格式化，小数位和产品支持的小数位相同
     */
    private fun formatTakeProfit() {
        val takeProfit = mBinding.etTakeProfit.text.toString()
        if (takeProfit.isEmpty()) {
            return
        }
        mBinding.etTakeProfit.setText(takeProfit.numFormat(mViewModel.digits, false))
    }

    /**
     * 点击减号，止损价格计算
     */
    private fun subStopLoss() {
        val currentCount = mBinding.etStopLoss.text.toString()
        if (currentCount.isEmpty()) {
            mBinding.etStopLoss.setText(mViewModel.stopLossRange)
            mBinding.etStopLoss.setSelection(mBinding.etStopLoss.text.toString().length)
            return
        }
        if (currentCount.mathCompTo("0") != 1) return
        val editCount = currentCount.mathSub(mViewModel.minProfit)
        mBinding.etStopLoss.setText(editCount.numFormat(mViewModel.digits, false))
        mBinding.etStopLoss.setSelection(mBinding.etStopLoss.text.toString().length)
        mViewModel.tradePagePlusMinusBtnClick("Stop Loss", "Minus")
    }

    /**
     * 点击加号，止损价格计算
     */
    private fun addStopLoss() {
        val currentCount = mBinding.etStopLoss.text.toString()
        if (currentCount.isEmpty()) {
            mBinding.etStopLoss.setText(mViewModel.stopLossRange)
            mBinding.etStopLoss.setSelection(mBinding.etStopLoss.text.toString().length)
            return
        }
        val editCount = currentCount.mathAdd(mViewModel.minProfit)
        mBinding.etStopLoss.setText(editCount.numFormat(mViewModel.digits, false))
        mBinding.etStopLoss.setSelection(mBinding.etStopLoss.text.toString().length)
        mViewModel.tradePagePlusMinusBtnClick("Stop Loss", "Plus")
    }

    /**
     * 止损价格格式化，小数位和产品支持的小数位相同
     */
    private fun formatStopLoss() {
        val currentCount = mBinding.etStopLoss.text.toString()
        if (currentCount.isEmpty()) {
            return
        }
        mBinding.etStopLoss.setText(currentCount.numFormat(mViewModel.digits, false))
    }

    /**
     * 检查手数是否合法
     */
    @SuppressLint("SetTextI18n")
    private fun checkVolume(inputVolume: String) {
        if (mViewModel.unit == OrderViewModel.UNIT_LOTS) {
//            if (mViewModel.maxOpenVolume.mathCompTo("0") != 1) {
//                mBinding.tvVolumeTip.isVisible = true
//                mBinding.tvVolumeTip.text = getString(R.string.insufficient_funds)
//                return
//            }
            if (inputVolume.mathCompTo(mViewModel.minVolume) == -1) {
                mBinding.tvVolumeTip.isVisible = true
                mBinding.tvVolumeTip.text = "${getString(R.string.min_value)}:${mViewModel.minVolume} ${getString(R.string.lots)}"
            } else if (inputVolume.mathCompTo(mViewModel.maxOpenVolume) == 1) {
                mBinding.tvVolumeTip.isVisible = true
                mBinding.tvVolumeTip.text = "${getString(R.string.max_value)}:${mViewModel.maxOpenVolume} ${getString(R.string.lots)}"
            } else {
                mBinding.tvVolumeTip.isVisible = false
            }
        } else {
//            if (mViewModel.maxOpenAmount.mathCompTo("0") != 1) {
//                mBinding.tvVolumeTip.isVisible = true
//                mBinding.tvVolumeTip.text = getString(R.string.insufficient_funds)
//                return
//            }
            if (inputVolume.mathCompTo(mViewModel.getMinAmount(getInputPrice())) == -1) {
                mBinding.tvVolumeTip.isVisible = true
                mBinding.tvVolumeTip.text = "${getString(R.string.min_value)}:${mViewModel.getMinAmount(getInputPrice())} ${mViewModel.currencyType}"
            } else if (inputVolume.mathCompTo(mViewModel.maxOpenAmount) == 1) {
                mBinding.tvVolumeTip.isVisible = true
                mBinding.tvVolumeTip.text = "${getString(R.string.max_value)}:${mViewModel.maxOpenAmount} ${mViewModel.currencyType}"
            } else {
                mBinding.tvVolumeTip.isVisible = false
            }
        }
//        checkTvNext()
    }

    /**
     * 是否开启止盈
     * isSelected: true 开启
     */
    private fun selectedTakeProfile(isSelected: Boolean) {
        mViewModel.takeProfitCb = isSelected
        mBinding.gpTakeProfit.isVisible = isSelected
        mBinding.tvTakeProfitTip.isVisible = false
        mBinding.ivTakeProfitChecked.setImageResource(if (isSelected) R.drawable.icon2_cb_tick_circle_c15b374 else R.drawable.draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14)
        if (isSelected) {
            mBinding.etTakeProfit.setText(mViewModel.takeProfitRange)
            checkTakeProfitAndStopLoss()
//            checkTvNext()
        }
    }

    /**
     * 是否开启止损
     * isSelected: true 开启
     */
    private fun selectedStopLoss(isSelected: Boolean) {
        mViewModel.stopLossCb = isSelected
        mBinding.gpStopLoss.isVisible = isSelected
        mBinding.ivStopLessChecked.setImageResource(if (isSelected) R.drawable.icon2_cb_tick_circle_c15b374 else R.drawable.draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14)
        if (isSelected) {
            mBinding.etStopLoss.setText(mViewModel.stopLossRange)
            checkTakeProfitAndStopLoss()
//            checkTvNext()
        }
    }

    /**
     * 根据订单类型是否是挂单显示对应View
     * mViewModel.tradeTypeIndex ！= 0 是挂单
     */
    private fun initTradeTypeView() {
        mBinding.tvMarketExecution.text =
            mViewModel.tradeTypeList.getOrNull(mViewModel.tradeTypeIndex)?.getShowItemValue()
        if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_MARKET) {
            mBinding.tvNext.text = getString(if (mViewModel.tradeType == OrderViewModel.TRADE_BUY) R.string.buy else R.string.sell)
        } else {
            mBinding.tvNext.text = mViewModel.tradeTypeList.getOrNull(mViewModel.tradeTypeIndex)?.getShowItemValue() ?: ""
        }
        mBinding.gpAtPrice.isGone = mViewModel.tradeTypeIndex == OrderViewModel.INDEX_MARKET
        mBinding.gpStopLimitPrice.isVisible = mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_STOP_LIMIT_BUY_STOP_LIMIT
        mViewModel.pendingTypeStr = if (mViewModel.tradeType == "0") {
            if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT) "<" else ">"
        } else {
            if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT) ">" else "<"
        }
        if (mViewModel.tradeTypeIndex != OrderViewModel.INDEX_MARKET) {
            checkAtPrice()
            if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_STOP_LIMIT_BUY_STOP_LIMIT) {
                checkStopLimitPrice()
            }
        }
    }

    /**
     * 初始展示挂单价格和限价价格
     *
     */
    private fun initAtPriceShow() {
        // 没初始化时 用变量标记是否初始化
        if (mViewModel.tradeTypeIndex != OrderViewModel.INDEX_MARKET && mViewModel.isInitAtPrice.not()) {
            mViewModel.productData?.let {
                val stopLossLevel =
                    mViewModel.productData?.stopslevel.toDoubleCatching() / 10.0.pow(
                        it.digits.toDouble()
                    ).toFloat()
                var atPrice = ""
                var stopLimitValue = 0.0
                if (mViewModel.tradeType == OrderViewModel.TRADE_SELL) {
                    // sell limit 1：卖出价+止损水平，Sell Stop 2：卖出价-止损水平；Sell Stop Limit 3 卖出价-止损水平
                    atPrice = (if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT) it.bid + stopLossLevel else it.bid + stopLossLevel).numFormat(
                        mViewModel.digits,
                        false
                    )
                    stopLimitValue = atPrice.toDoubleCatching() + stopLossLevel
                } else {
                    atPrice = (if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT) it.ask - stopLossLevel else it.ask + stopLossLevel).numFormat(
                        mViewModel.digits,
                        false
                    )
                    stopLimitValue = atPrice.toDoubleCatching() - stopLossLevel
                }
                mBinding.etAtPrice.setText(atPrice)
                mBinding.etStopLimitPrice.setText(stopLimitValue.numFormat(mViewModel.digits, false))
                mViewModel.isInitAtPrice = true
            }
        }
    }

    /**
     * 初始化挂单价格
     */
    private fun initAtPrice() {
        mViewModel.productData?.let {
            val stopLossLevel =
                mViewModel.productData?.stopslevel.toDoubleCatching() / 10.0.pow(
                    it.digits.toDouble()
                ).toFloat()
            var atPrice = ""
            var stopLimitValue = 0.0
            if (mViewModel.tradeType == OrderViewModel.TRADE_SELL) {
                // sell limit 1：卖出价+止损水平，Sell Stop 2：卖出价-止损水平；Sell Stop Limit 3 卖出价-止损水平
                atPrice = (if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT) it.bid + stopLossLevel else it.bid + stopLossLevel).numFormat(
                    mViewModel.digits,
                    false
                )
            } else {
                atPrice = (if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT) it.ask - stopLossLevel else it.ask + stopLossLevel).numFormat(
                    mViewModel.digits,
                    false
                )
            }
            mBinding.etAtPrice.setText(atPrice)
        }
    }

    /**
     * 限价价格
     */
    private fun initStopLimitPrice() {
        mViewModel.productData?.let {
            val stopLossLevel =
                mViewModel.productData?.stopslevel.toDoubleCatching() / 10.0.pow(
                    it.digits.toDouble()
                ).toFloat()
            var atPrice = ""
            var stopLimitValue = 0.0
            if (mViewModel.tradeType == OrderViewModel.TRADE_SELL) {
                // sell limit 1：卖出价+止损水平，Sell Stop 2：卖出价-止损水平；Sell Stop Limit 3 卖出价-止损水平
                atPrice = (if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT) it.bid + stopLossLevel else it.bid + stopLossLevel).numFormat(
                    mViewModel.digits,
                    false
                )
                stopLimitValue = atPrice.toDoubleCatching() + stopLossLevel
            } else {
                atPrice = (if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT) it.ask - stopLossLevel else it.ask + stopLossLevel).numFormat(
                    mViewModel.digits,
                    false
                )
                stopLimitValue = atPrice.toDoubleCatching() - stopLossLevel
            }
            mBinding.etStopLimitPrice.setText(stopLimitValue.numFormat(mViewModel.digits, false))
        }
    }

    /**
     * 挂单刷新挂单价格范围
     * Buy Limit ：挂单价 低于等于（买入价-止损水平）
     * Buy Stop: 挂单价 大于等于（买入价+止损水平）
     * Buy Stop Limit:  挂单价 大于等于（买入价+止损水平）
     *
     * Sell Limit: 挂单价 大于等于（卖出价+止损水平）
     * Sell Stop:    挂单价   低于等于（卖出价-止损水平）
     * Sell Stop Limit:   挂单价  低于等于（卖出价-止损水平）
     *
     * 挂单止盈范围：
     * Buy Limit：止盈 大于等于（挂单价+止损水平）
     * Buy Stop：  止盈    大于等于（挂单价+止损水平）
     * Buy Stop Limit 止盈  大于等于（Stop Limit 价+止损水平
     *
     * Sell Limit：   止盈 低于等于（挂单价-止损水平）
     * Sell Stop：     止盈 低于等于（挂单价-止损水平）
     * Sell Stop Limit 止盈  低于等于（Stop Limit 价-止损水平）
     *
     *  挂单止损范围：
     * Buy Limit：止损 低于等于（挂单价-止损水平）
     * Buy Stop：  止损 低于等于（挂单价-止损水平）
     * Buy Stop Limit 止损 低于等于（Stop Limit 价-止损水平）
     *
     * Sell Limit：   止损 大于等于（挂单价+止损水平）
     * Sell Stop：     止损 大于等于（挂单价+止损水平）
     * Sell Stop Limit 止损 大于等于（Stop Limit 价+止损水平）
     *
     */
    private fun refreshAtPriceRange(data: ShareProductData, stopLossLevel: String) {
        val atPriceValue = mBinding.etAtPrice.text.toString().trim()
        val stopLimitPriceValue = mBinding.etStopLimitPrice.text.toString().trim()
        var volume = mViewModel.inputVolume
        if (TextUtils.isEmpty(volume)) volume = "0.01"
        if (mViewModel.tradeType == OrderViewModel.TRADE_SELL) {
            //sell 挂单价初始值 Sell Limit 是 卖出价+止损水平 其他是 卖出价-止损水平
            val atPriceRange = computeSellAtPriceRange(data, stopLossLevel)
            //计算Sell 止盈止损范围
            computerSellTakeProfitStopLossRange(atPriceValue, stopLimitPriceValue, stopLossLevel)
            mBinding.tvAtPriceRange.setTextDiff("${mViewModel.pendingTypeStr}=$atPriceRange".arabicText().ifNull())
            mBinding.tvStopLimitPriceRange.setTextDiff(
                ">=${
                    mBinding.etAtPrice.text.toString().mathAdd(stopLossLevel)
                        .numFormat(mViewModel.digits)
                }".arabicText().ifNull()
            )
        } else {
            //buy
            // 挂单范围 Buy Limit 低于等于（买入价-止损水平）其他都是 大于等于（买入价+止损水平）
            val atPriceRange = computerBuyAtPriceRange(data, stopLossLevel)
            // 计算Buy 止盈止损范围
            computerBuyTakeProfitStopLossRange(atPriceValue, stopLimitPriceValue, stopLossLevel)
            mBinding.tvAtPriceRange.setTextDiff("${mViewModel.pendingTypeStr}=$atPriceRange".arabicText().ifNull())
            mBinding.tvStopLimitPriceRange.setTextDiff(
                "<=${
                    mBinding.etAtPrice.text.toString().mathSub(stopLossLevel)
                        .numFormat(mViewModel.digits)
                }".arabicText().ifNull()
            )
        }
        mBinding.tvEstimatedProfit.text =
            if (mViewModel.takeProfitCb) {
                buildString {
                    append(
                        (VAUSdkUtil.getProfitLoss(
                            data,
                            mBinding.etAtPrice.text.toString(),
                            volume,
                            mViewModel.tradeType,
                            mBinding.etTakeProfit.text.toString()
                        ).toString().numCurrencyFormat() + " " + mViewModel.currencyType).arabicReverseTextByFlag(" ")
                    )
                }.arabicReverseTextByFlag(": ")
            } else {
                "-- ${mViewModel.currencyType}"
            }

        mBinding.tvEstimatedLoss.text =
            if (mViewModel.stopLossCb) {
                buildString {
                    append(
                        (VAUSdkUtil.getProfitLoss(
                            data,
                            mBinding.etAtPrice.text.toString(),
                            volume,
                            mViewModel.tradeType,
                            mBinding.etStopLoss.text.toString()
                        ).toString().numCurrencyFormat() + " " + mViewModel.currencyType).arabicReverseTextByFlag(" ")
                    )
                }.arabicReverseTextByFlag(": ")
            } else {
                "-- ${mViewModel.currencyType}"
            }
    }

    /**
     * 计算sell 挂单价格范围
     * Sell Limit: 挂单价 大于等于（卖出价+止损水平）
     * Sell Stop:    挂单价   低于等于（卖出价-止损水平）
     * Sell Stop Limit:   挂单价  低于等于（卖出价-止损水平）
     */
    private fun computeSellAtPriceRange(data: ShareProductData, stopLossLevel: String): String {
        return (if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT)
            "${data.bid}".mathAdd(stopLossLevel)
        else
            "${data.bid}".mathSub(stopLossLevel)).numFormat(mViewModel.digits, false)
    }

    /**
     * 计算buy 挂单价格范围
     * Buy Limit ：挂单价 低于等于（买入价-止损水平）
     * Buy Stop: 挂单价 大于等于（买入价+止损水平）
     * Buy Stop Limit:  挂单价 大于等于（买入价+止损水平）
     */
    private fun computerBuyAtPriceRange(data: ShareProductData, stopLossLevel: String): String {
        return (if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT)
            "${data.ask}".mathSub(stopLossLevel)
        else
            "${data.ask}".mathAdd(stopLossLevel)).numFormat(
            mViewModel.digits,
            false
        )
    }

    /**
     * 计算挂单 Sell 止盈止损范围
     * Sell Limit：   止损  大于等于（挂单价+止损水平）
     * Sell Stop：     止损  大于等于（挂单价+止损水平）
     * Sell Stop Limit 止损 大于等于（Stop Limit 价+止损水平）
     *
     * Sell Limit：   止盈 低于等于（挂单价-止损水平）
     * Sell Stop：     止盈 低于等于（挂单价-止损水平）
     * Sell Stop Limit 止盈  低于等于（Stop Limit 价-止损水平
     *
     */
    private fun computerSellTakeProfitStopLossRange(atPriceValue: String, stopLimitPriceValue: String, stopLossLevel: String) {
        var stopLossRange = "0"
        var takeProfitRange = "0"
        // sell 止盈范围
        if (mViewModel.tradeTypeIndex != OrderViewModel.INDEX_SELL_STOP_LIMIT_BUY_STOP_LIMIT) {
            stopLossRange = atPriceValue.mathAdd(stopLossLevel).numFormat(mViewModel.digits)
            takeProfitRange =
                atPriceValue.mathSub(stopLossLevel).numFormat(mViewModel.digits)
        } else {
            stopLossRange =
                stopLimitPriceValue.mathAdd(stopLossLevel).numFormat(mViewModel.digits)
            takeProfitRange =
                stopLimitPriceValue.mathSub(stopLossLevel).numFormat(mViewModel.digits)
        }
        mViewModel.takeProfitRange = takeProfitRange
        mViewModel.stopLossRange = stopLossRange
    }

    /**
     * 计算Buy 止盈止损范围
     * Buy 止损
     * Buy Limit：止损 低于等于（挂单价-止损水平）
     * Buy Stop：  止损 低于等于（挂单价-止损水平）
     * Buy Stop Limit 止损 低于等于（Stop Limit 价-止损水平）
     *
     * Buy 止盈计算
     * Buy Limit：止盈 大于等于（挂单价+止损水平）
     * Buy Stop：  止盈    大于等于（挂单价+止损水平）
     * Buy Stop Limit 止盈  大于等于（Stop Limit 价+止损水平）
     *
     */
    private fun computerBuyTakeProfitStopLossRange(atPriceValue: String, stopLimitPriceValue: String, stopLossLevel: String) {
        var stopLossRange = "0"
        var takeProfitRange = "0"
        if (mViewModel.tradeTypeIndex != OrderViewModel.INDEX_SELL_STOP_LIMIT_BUY_STOP_LIMIT) {
            stopLossRange = atPriceValue.mathSub(stopLossLevel).numFormat(mViewModel.digits)
            takeProfitRange =
                atPriceValue.mathAdd(stopLossLevel).numFormat(mViewModel.digits)
        } else {
            stopLossRange =
                stopLimitPriceValue.mathSub(stopLossLevel).numFormat(mViewModel.digits)
            takeProfitRange =
                stopLimitPriceValue.mathAdd(stopLossLevel).numFormat(mViewModel.digits)
        }
        mViewModel.takeProfitRange = takeProfitRange
        mViewModel.stopLossRange = stopLossRange
    }

    /**
     * 初始产品信息时调用，不会在onCallBack 中调用，计算并展示止盈止损
     */
    @SuppressLint("SetTextI18n")
    private fun showTakeProfitOrStopLossAndRange(productData: ShareProductData) {
        val stopLossLevel =
            productData.stopslevel.mathDiv("${10.0.pow(mViewModel.digits)}", 8)

        if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_MARKET) {
            //计算逻辑同:computeMarketTakeProfitAndStopLoss(),不同地方是多了mBinding.etStopLoss.setText 和 mBinding.etTakeProfit.setText
            // 止盈止损范围
            if (mViewModel.tradeType == OrderViewModel.TRADE_SELL) {
                // 止盈范围（低于等于（买入价-止损水平））
                val stopLossLevelYES = "${productData.ask}".mathSub(stopLossLevel)
                val takeProfitRange = stopLossLevelYES.numFormat(mViewModel.digits)
                mViewModel.takeProfitRange = takeProfitRange
                mBinding.etTakeProfit.setText(takeProfitRange)
                // 止损范围（大于等于（买入价+止损水平））
                val stopLossLevelNO = "${productData.ask}".mathAdd(stopLossLevel)
                val stopLossRange = stopLossLevelNO.numFormat(mViewModel.digits)
                mViewModel.stopLossRange = stopLossRange
                mBinding.etStopLoss.setText(stopLossRange)
            } else {
                // 止盈范围（大于等于（卖出价+止损水平））
                val stopLossLevelYES = "${productData.bid}".mathAdd(stopLossLevel)
                val takeProfitRange = stopLossLevelYES.numFormat(mViewModel.digits)
                mBinding.etTakeProfit.setText(takeProfitRange)
                // 止损范围（低于等于（卖出价-止损水平））
                val stopLossLevelNO = "${productData.bid}".mathSub(stopLossLevel)
                val stopLossRange = stopLossLevelNO.numFormat(mViewModel.digits)
                mBinding.etStopLoss.setText(stopLossRange)
            }

        } else {
            //TODO Felix initView 时只会有市价不回有挂单 所以这块逻辑应该没有卵用，暂时同步过来
            if (mViewModel.tradeType == OrderViewModel.TRADE_SELL) {
                //没有卵用的逻辑
                //sell
                val atPrice = (if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_STOP_BUY_STOP)
                    "${productData.bid}".mathSub(stopLossLevel).numFormat(mViewModel.digits, false)
                else
                    "${productData.bid}".mathAdd(stopLossLevel)).numFormat(mViewModel.digits, false)

                mBinding.etAtPrice.setText(atPrice)
                val atPriceValue = mBinding.etAtPrice.text.toString().trim()
                val stopLimitValue = atPrice.mathAdd(stopLossLevel)
                mBinding.etStopLimitPrice.setText(stopLimitValue.numFormat(mViewModel.digits, false))

                var stopLossRange = "0"
                var takeProfitRange = "0"
                if (mViewModel.tradeTypeIndex != OrderViewModel.INDEX_SELL_STOP_LIMIT_BUY_STOP_LIMIT) {
                    takeProfitRange =
                        atPriceValue.mathSub(stopLossLevel).numFormat(mViewModel.digits)
                    stopLossRange = atPriceValue.mathAdd(stopLossLevel).numFormat(mViewModel.digits)
                } else {
                    takeProfitRange =
                        stopLimitValue.mathSub(stopLossLevel).numFormat(mViewModel.digits)
                    stopLossRange =
                        stopLimitValue.mathAdd(stopLossLevel).numFormat(mViewModel.digits)
                }
                mViewModel.takeProfitRange = takeProfitRange
                mViewModel.stopLossRange = stopLossRange
//            mBinding.tvTakeProfitTitle.text = "<=$takeProfitRange".ifNull()
//            mBinding.tvStopLossRange.text = ">=$stopLossRange".ifNull()

                mBinding.etTakeProfit.setText(if (mViewModel.takeProfitCb) takeProfitRange else null)
                mBinding.etStopLoss.setText(if (mViewModel.stopLossCb) stopLossRange else null)
                mBinding.tvAtPriceRange.text = "${mViewModel.pendingTypeStr}=$atPrice".arabicText()
                mBinding.tvStopLimitPriceRange.setTextDiff(
                    ">=${
                        mBinding.etAtPrice.text.toString().mathAdd(stopLossLevel)
                            .numFormat(mViewModel.digits)
                    }".arabicText().ifNull()
                )
            } else {
                //没有卵用的逻辑
                //buy
                val atPrice = (if (mViewModel.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT)
                    "${productData.ask}".mathSub(stopLossLevel).numFormat(mViewModel.digits, false)
                else
                    "${productData.ask}".mathAdd(stopLossLevel)).numFormat(mViewModel.digits, false)
                mBinding.etAtPrice.setText(atPrice)
                val stopLimitValue = atPrice.mathSub(stopLossLevel)
                mBinding.etStopLimitPrice.setText(stopLimitValue.numFormat(mViewModel.digits))

                val atPriceValue = mBinding.etAtPrice.text.toString().trim()
                var stopLossRange = "0"
                var takeProfitRange = "0"
                if (mViewModel.tradeTypeIndex != OrderViewModel.INDEX_SELL_STOP_LIMIT_BUY_STOP_LIMIT) {
                    stopLossRange = atPriceValue.mathSub(stopLossLevel).numFormat(mViewModel.digits)
                    takeProfitRange =
                        atPriceValue.mathAdd(stopLossLevel).numFormat(mViewModel.digits)
                } else {
                    stopLossRange =
                        stopLimitValue.mathSub(stopLossLevel).numFormat(mViewModel.digits)
                    takeProfitRange =
                        stopLimitValue.mathAdd(stopLossLevel).numFormat(mViewModel.digits)
                }
                mViewModel.takeProfitRange = takeProfitRange
                mViewModel.stopLossRange = stopLossRange
//                mBinding.layoutOrderStopLoss.tvTakeProfitRange.text = ">=$takeProfitRange".ifNull()
//                mBinding.layoutOrderStopLoss.tvStopLossRange.text = "<=$stopLossRange".ifNull()
                mBinding.etTakeProfit.setText(if (mViewModel.takeProfitCb) takeProfitRange else null)
                mBinding.etStopLoss.setText(if (mViewModel.stopLossCb) stopLossRange else null)
                mBinding.tvAtPriceRange.text =
                    "${mViewModel.pendingTypeStr}=$atPrice"

                mBinding.tvStopLimitPriceRange.setTextDiff(
                    "<=${
                        mBinding.etAtPrice.text.toString().mathAdd(stopLossLevel)
                            .numFormat(mViewModel.digits)
                    }".arabicText().ifNull()
                )
            }
        }

    }

    /**
     * 处理下单成功
     */
    private fun handleSuccess() {
        lifecycleScope.launch {
            delay(200)
            // 刷接口
            EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_OPEN_ORDER)
        }
        mViewModel.storageProduceLots(mViewModel.inputVolume)
        when (mViewModel.tradeTypeIndex) {
            0 -> EventBus.getDefault().postSticky(
                StickyEvent(NoticeConstants.MAIN_SHOW_ORDERS_ITEM_OPEN)
            )

            else -> EventBus.getDefault().postSticky(
                StickyEvent(NoticeConstants.MAIN_SHOW_ORDERS_ITEM_PENDING)
            )
        }
        ActivityManagerUtil.getInstance().finishOtherActivities(MainActivity::class.java)
        // 无需Finish了上方有了finishOtherActivities(MainActivity::class.java)
//        finish()
    }

    override fun useEventBus(): Boolean = true

    override fun onMsgEvent(eventTag: String) {
        super.onMsgEvent(eventTag)
        when (eventTag) {
            // 断开连接
            NoticeConstants.WS.SOCKET_DISCONNECTED -> {
                mViewModel.isConnected = false
                // 检查闭市状态 && 断开连接
                mViewModel.productData?.let {
                    checkNetWorkStatus(it)
                }

            }
            // 已连接
            NoticeConstants.Init.WS_SUCCESS_CONNECT -> {
                mViewModel.isConnected = true
                // 检查闭市状态 && 断开连接
                mViewModel.productData?.let {
                    checkNetWorkStatus(it)
                }
            }

            NoticeConstants.Init.MARKET_CLOSE_CHECK -> {
                // 检查闭市状态 && 断开连接
                mViewModel.productData?.let {
                    checkNetWorkStatus(it)
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        SDKIntervalUtil.instance.removeCallBack(this)
        SDKIntervalUtil.instance.addCallBack(this)
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, dataIntent: Intent?) {
        super.onActivityResult(requestCode, resultCode, dataIntent)
//        when (resultCode) {
//            Constants.RESULT_CODE -> {
//                mViewModel.productName = dataIntent?.extras?.getString(Constants.PARAM_PRODUCT_NAME) ?: ""
//                showProductData()
//                mBinding.mTimeChartView.setData(mViewModel.productData, true)
//                // 检查闭市状态 && 断开连接
//                mViewModel.productData?.let {
//                    checkNetWorkStatus(it)
//                }
//            }
//        }
    }

    private fun switchProduct(productData: ShareProductData) {
        mViewModel.productName = productData.symbol
        showProductData()
        initOrderUnit()
        mBinding.mTimeChartView.setData(mViewModel.productData, true)
        // 检查闭市状态 && 断开连接
        mViewModel.productData?.let {
            checkNetWorkStatus(it)
        }
    }

    override fun onPause() {
        super.onPause()
        SDKIntervalUtil.instance.removeCallBack(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        mBinding.etVolume.removeTextChangedListener(volumeWatcher)
        mBinding.etAtPrice.removeTextChangedListener(atPriceWatcher)
        mBinding.etStopLimitPrice.removeTextChangedListener(atPriceWatcher)
        mBinding.etTakeProfit.removeTextChangedListener(takeProfitWatcher)
        mBinding.etStopLoss.removeTextChangedListener(takeProfitWatcher)
        KeyboardUtil.unregisterSoftInputChangedListener(this.window)
    }

    private fun clearFocus() {
        mBinding.etVolume.clearFocus()
        mBinding.etAtPrice.clearFocus()
        mBinding.etStopLimitPrice.clearFocus()
        mBinding.etTakeProfit.clearFocus()
        mBinding.etStopLoss.clearFocus()
    }

    companion object {
        fun open(context: Context?, bundle: Bundle? = null, checkReadOnly: Boolean = true) {
            // 只读跟单账户不允许进入自主交易下单页
            if (checkReadOnly && UserDataUtil.isStLogin() && UserDataUtil.isReadOnly()) {
                CenterActionDialog.Builder(context as Activity)
                    .setContent(context.getString(R.string.your_account_is_trade_now))
                    .setSingleButton(true)
                    .setSingleButtonText(context?.getString(R.string.confirm).ifNull())
                    .build()
                    .showDialog()
                return
            }
            noRepeat {
                val intent = Intent(context, OrderActivity::class.java)
                bundle?.let { intent.putExtras(bundle) }
                context?.startActivity(intent)
            }
        }
    }
}