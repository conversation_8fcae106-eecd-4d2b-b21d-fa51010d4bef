package cn.com.vau.trade.fragment.search

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.databinding.FragmentSymbolSearchCategoryBinding
import cn.com.vau.trade.bean.SymbolItemBean
import cn.com.vau.trade.ext.SSDialogUiState
import cn.com.vau.trade.ext.SymbolSearchConstants.SORT_DOWN
import cn.com.vau.trade.ext.SymbolSearchConstants.SORT_NONE
import cn.com.vau.trade.ext.SymbolSearchConstants.SORT_UP
import cn.com.vau.trade.ext.SymbolSearchUtil
import cn.com.vau.trade.ext.addScrollStateListener
import cn.com.vau.trade.viewmodel.SSCategoryViewModel
import cn.com.vau.trade.viewmodel.SymbolSearchDialogViewModel
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.TabType
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.dp2px
import cn.com.vau.util.getStatusHeight
import cn.com.vau.util.init
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.screenHeight
import cn.com.vau.util.setVp
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * Created by array on 2025/4/24 15:24
 * Desc: 产品分类页
 */
class SSCategoryFragment : BaseMvvmBindingFragment<FragmentSymbolSearchCategoryBinding>() {

    companion object {
        fun newInstance(): SSCategoryFragment {
            return SSCategoryFragment()
        }
    }

    private val titleList = mutableListOf<String>()
    private val fragmentList = mutableListOf<Fragment>()

    //整个Dialog的ViewModel
    private var dialogViewModel: SymbolSearchDialogViewModel? = null

    fun setViewModel(viewModel: SymbolSearchDialogViewModel?) {
        dialogViewModel = viewModel
    }

    //Fragment的ViewModel
    private lateinit var categoryViewModel: SSCategoryViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        categoryViewModel = ViewModelProvider(this)[SSCategoryViewModel::class.java]
        categoryViewModel.setContext(requireActivity())
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        childFragmentManager.fragments.forEach {
            if (hidden) {
                it.onPause()
            } else {
                it.onResume()
            }
        }
    }

    override fun initView() {

        /** 设置产品分类页ViewPager高度 */
        constraintH(mBinding.clCategoryRoot, R.id.mViewPager, screenHeight - requireActivity().getStatusHeight() - 203.dp2px())

        /** 点击搜索占位按钮，切换为搜索页面 */
        mBinding.tvSearchPlaceHolder.setOnClickListener {
            dialogViewModel?.setDialogUiState(SSDialogUiState.SearchLayout)
        }

        initSortIcon()
    }

    /**
     * 约束布局高度
     */
    private fun constraintH(root: ConstraintLayout, id: Int, height: Int) {
        val set = ConstraintSet()
        set.clone(root)
        set.constrainHeight(id, height)
        set.applyTo(root)
    }

    override fun createObserver() {
        /** 分类数据处理完毕 */
        lifecycleScope.launch {
            categoryViewModel.categoryMap.flowWithLifecycle(lifecycle, Lifecycle.State.STARTED).collectLatest {
                setTabLayout(it)
            }
        }
    }

    private fun setTabLayout(map: LinkedHashMap<String, List<SymbolItemBean>>) {
        titleList.clear()
        fragmentList.clear()
        for (entry in map) {
            titleList.add(entry.key)
            val fragment = SSCategorySymbolListFragment.newInstance(entry.key)
            fragment.setViewModel(dialogViewModel)
            fragment.setDataList(entry.value)
            fragmentList.add(fragment)
        }
        mBinding.mViewPager.init(fragmentList, titleList, childFragmentManager, requireActivity())
        mBinding.mViewPager.addScrollStateListener {
            dialogViewModel?.isCategoryPagerScrolling = it
        }
        mBinding.mTabLayout.setVp(mBinding.mViewPager, titleList, TabType.LINE_INDICATOR)
    }

    @SuppressLint("SetTextI18n")
    private fun initSortIcon() {

        mBinding.tvPriceRose.text = requireActivity().getString(R.string.last_price_change)
        mBinding.tvPriceRose.clickNoRepeat(500) {
            SymbolSearchUtil.nextSort(dialogViewModel?.sortModeCategorySymbolList ?: SORT_NONE) { sortMode ->
                dialogViewModel?.sortModeCategorySymbolList = sortMode
                setPriceRoseIcon(sortMode)
                dialogViewModel?.sortCategorySymbolList(sortMode)
            }
        }

        setPriceRoseIcon(dialogViewModel?.sortModeCategorySymbolList ?: SORT_NONE)

    }

    /**
     * 设置价格涨跌幅排序icon
     */
    private fun setPriceRoseIcon(sortMode: Int) {
        val drawable = ContextCompat.getDrawable(
            requireActivity(), AttrResourceUtil.getDrawable(
                requireActivity(), when (sortMode) {
                    SORT_DOWN -> R.attr.imgDownSort
                    SORT_UP -> R.attr.imgUpSort
                    else -> R.attr.imgNotSort
                }
            )
        )
        if (LanguageHelper.isRtlLanguage()) {
            mBinding.tvPriceRose.setCompoundDrawablesWithIntrinsicBounds(drawable, null, null, null)
        } else {
            mBinding.tvPriceRose.setCompoundDrawablesWithIntrinsicBounds(null, null, drawable, null)
        }
    }

    override fun onResume() {
        super.onResume()
        categoryViewModel.getCategoryData()
    }


    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.OPTIONAL_PRODUCT_LIST_UPDATED -> {
                categoryViewModel.getCategoryData()
            }
        }
    }

}


