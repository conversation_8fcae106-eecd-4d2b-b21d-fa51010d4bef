package cn.com.vau.trade.fragment.kchart

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.fragment.app.activityViewModels
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.mvvm.base.BaseMvvmFragment
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.databinding.FragmentKlineInfoBinding
import cn.com.vau.trade.bean.kchart.ProductInfoBean
import cn.com.vau.trade.model.KLineInfoViewModel
import cn.com.vau.trade.model.KLineViewModel
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathDiv
import cn.com.vau.util.mathMul
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import kotlin.getValue

/**
 * author：lvy
 * date：2024/10/19
 * desc：k线页->info（产品属性详情）
 */
class KLineInfoFragment : BaseMvvmFragment<FragmentKlineInfoBinding, KLineInfoViewModel>() {

    private val activityViewModel: KLineViewModel by activityViewModels()

    private var prodName = ""

    override fun initParam(savedInstanceState: Bundle?) {
        prodName = activityViewModel.symbol
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    override fun initView() {}

    override fun initData() {
        showCommonData()
        mViewModel.tradeProductDetailApi(prodName) // 获取产品属性详情
    }

    override fun createObserver() {
        mViewModel.productInfoLiveData.observe(this) {
            refreshData(it)
            mBinding.refreshLayout.finishRefresh()
        }
    }

    override fun initListener() {
        mBinding.refreshLayout.setEnableLoadMore(false)
        mBinding.refreshLayout.setOnRefreshListener {
            mViewModel.tradeProductDetailApi(prodName) // 获取产品属性详情
        }
    }

    /**
     * 显示公共数据
     */
    @SuppressLint("SetTextI18n")
    private fun showCommonData() {
        val data = VAUSdkUtil.symbolList().firstOrNull { it.symbol == prodName } ?: return

        // dealing
        mBinding.tvDigits.text = "${data.digits}"
        mBinding.tvStopsLevel.text = data.stopslevel.ifNull("-")
        mBinding.tvContractSize.text = data.contractsize.ifNull("-")
        mBinding.tvMinimumVolume.text = data.minvolume.ifNull("-")
        mBinding.tvMaximumVolume.text = data.maxvolume.ifNull("-")
        mBinding.tvProfitCalculation.text = data.stoplossmodel.ifNull("-")

        // margin
        mBinding.tvMarginCalculation.text = data.marginmodel.ifNull("-")
        mBinding.tvMarginInitial.text = data.margininit.ifNull("-")
        // (1 / 预付款百分比 * 100) 拼接百分号
        mBinding.tvMarginPercentage.text = "${"1".mathMul("100").mathDiv(data.marginpercent, 2)}%"
        mBinding.tvMarginCurrency.text = data.margin_currency.ifNull("-")

        // Trading time
        mBinding.tvTradingTimeGMT.text = "${getString(R.string.trading_time)}(GMT+${Constants.season})"

        val weekViewList = arrayOf(
            mBinding.tvMonDate,
            mBinding.tvTuesDate,
            mBinding.tvWedDate,
            mBinding.tvThurDate,
            mBinding.tvFriDate,
            mBinding.tvSatDate,
            mBinding.tvSunDate
        )

        val gmtDataStrList = arrayListOf<String>()
        for (timeBean in data.tradetime) {
            val tList = timeBean.timeList
            var gmtDateStr = ""
            for ((index, value) in tList.withIndex()) {
                gmtDateStr += value
                if (tList.size == 1) continue
                if (index == tList.size - 1) continue
                gmtDateStr += "\n"
            }
            gmtDataStrList.add(gmtDateStr)
        }

        for ((index, value) in gmtDataStrList.withIndex()) {
            if (index < weekViewList.size) {
                if (value.isEmpty()) {
                    weekViewList[index].text = getString(R.string.no_trading)
                } else {
                    weekViewList[index].text = value
                }
            }
        }
    }

    /**
     * 显示接口数据
     */
    private fun refreshData(infoBean: ProductInfoBean?) {
        // dealing
        mBinding.tvPendingsGTC.text = infoBean?.gtc.ifNull("-")

        // margin
        mBinding.tvMarginHedge.text = infoBean?.marginlock

        // swap
        mBinding.tvSwapType.text = infoBean?.swapmodel.ifNull("-")
        mBinding.tvSwapLong.text = infoBean?.swapbuy.ifNull("-")
        mBinding.tvSwapShort.text = infoBean?.swapsell.ifNull("-")
        mBinding.tvDaySwap.text = infoBean?.swap.ifNull("-")
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEventBus(tag: String) {
        if (tag == NoticeConstants.EVENT_KLINE_SWITCH_PRODUCT) {
            prodName = activityViewModel.symbol
            mViewModel.tradeProductDetailApi(prodName)
            showCommonData()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    companion object {
        fun newInstance() = KLineInfoFragment()
    }
}