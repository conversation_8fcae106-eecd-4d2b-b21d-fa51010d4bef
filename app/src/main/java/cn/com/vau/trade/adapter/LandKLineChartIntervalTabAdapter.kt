package cn.com.vau.trade.adapter

import android.annotation.SuppressLint
import android.graphics.Color
import android.widget.TextView
import cn.com.vau.R
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.setFontG500
import cn.com.vau.util.setFontG600
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class LandKLineChartIntervalTabAdapter : BaseQuickAdapter<String, BaseViewHolder>(R.layout.item_land_kline_tab_interval) {

    private var selectedText = ""
    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff) }
    private val color_ca61e1e1e_c99ffffff by lazy { AttrResourceUtil.getColor(context, R.attr.color_ca61e1e1e_c99ffffff) }
    override fun convert(holder: BaseViewHolder, item: String) {
        val tabView = holder.getView<TextView>(R.id.tvTab)
        tabView.text = item
        if (selectedText == item) {
            tabView.setTextColor(color_c1e1e1e_cebffffff)
            tabView.setFontG600()
        } else {
            tabView.setTextColor(color_ca61e1e1e_c99ffffff)
            tabView.setFontG500()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun changeSelected(text: String) {
        selectedText = text
        notifyDataSetChanged()
    }
}