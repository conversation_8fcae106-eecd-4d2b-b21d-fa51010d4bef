package cn.com.vau.trade.presenter

import HomepageContract
import android.os.Handler
import android.os.Looper
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.data.DataObjStringBean
import cn.com.vau.data.account.AccountAuditBean
import cn.com.vau.data.account.AccountOpeningGuideBean
import cn.com.vau.data.account.AccountOpeningGuideObj
import cn.com.vau.data.account.CheckVirtualAccountBean
import cn.com.vau.data.account.KycVerifyLevelDataBean
import cn.com.vau.data.account.MT4AccountTypeBean
import cn.com.vau.data.enums.EnumLinkSkipState
import cn.com.vau.data.init.ImgAdvertInfoObj
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import io.reactivex.disposables.Disposable
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

/**
 * Created by roy on 2018/12/1.
 * 订单持仓
 */
class HomepagePresenter : HomepageContract.Presenter() {

    // 开户信息
    var accountGuideData: AccountOpeningGuideObj? = null

    // KYC流程检查主交易账户开通状态
    var accountAuditStatus: String = ""

    // 用户是否入过金  默认：入过金
    var isDeposited: Boolean = true

    // Banner数据
    var bannerData: ImgAdvertInfoObj? = null

    private var clickAble = true

    override fun queryMT4AccountState(state: EnumLinkSkipState) {
        mView?.showNetDialog()
        val map = hashMapOf<String, String>()
        map["token"] = UserDataUtil.loginToken()
        mModel?.queryMT4AccountState(map, object : BaseObserver<MT4AccountTypeBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(typeBean: MT4AccountTypeBean) {

                mView?.hideNetDialog()
                EventBus.getDefault().post("html_dialog_net_finish")

                if (typeBean.resultCode != "V00000") {
                    ToastUtil.showToast(typeBean.msgInfo)
                    return
                }
                val obj = typeBean.data?.obj
                mView?.skipOpenAccountActivity(state, obj)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
                EventBus.getDefault().post("html_dialog_net_finish")
            }

        })
    }

    override fun accountOpeningGuide() {
        val map = hashMapOf<String, Any>()
        map["token"] = UserDataUtil.loginToken()
        mModel?.accountOpeningGuide(map, object : BaseObserver<AccountOpeningGuideBean>() {

            override fun onNext(dataBean: AccountOpeningGuideBean) {
                if (dataBean.resultCode != "V00000") {
//                    ToastUtils.showToast(dataBean.msgInfo)
                    return
                }

                accountGuideData = dataBean.data?.obj
                mView?.assetsCardUpdate()
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    override fun userQueryUserLevel() {
        val map = hashMapOf<String, Any>()
        mModel?.userQueryUserLevel(map, object : BaseObserver<KycVerifyLevelDataBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: KycVerifyLevelDataBean) {
                if (dataBean.resultCode != "V00000") {
                    return
                }
                mView?.kycGuideInfo(dataBean.data?.obj)
            }
        })
    }

    override fun queryVirtualAccount(isHandleError: Boolean) {
        if (!isHandleError) {
            mView?.showNetDialog()
        }
        mModel?.queryVirtualAccount(object : BaseObserver<CheckVirtualAccountBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: CheckVirtualAccountBean) {
                mView?.hideNetDialog()
                if (dataBean.resultCode != "V00000") {
                    return
                }
                val virtualEnable = dataBean.data?.obj?.isNotEmpty() == true
                // 有虚拟MT5账户
                if (virtualEnable) {
                    if (!isHandleError) {
                        // 已跟H5开发@Ian You确认不用传virtualId了，他们会自己查
//                        val virtualAccount = dataBean.data?.obj?.getOrNull(0)
//                        virtualAccount?.let {
//                            if (it.virtualId?.isNotEmpty() == true) {
//                                UserDataUtil.setVirtualId(it.virtualId)
//                            }
//                        }
                        mView?.goLiveAccount(true)
                    }
                }
                // 无虚拟MT5账户
                else {
                    if (isHandleError) {
                        mView?.virtualAccountStateError()   // 虚拟MT5账户状态异常，返回账户列表
                    } else {
                        mView?.goLiveAccount(false)
                    }
                }
            }
        })
    }

    override fun queryFirstAccountAuditStatus() {
        mModel?.queryFirstAccountAuditStatus(object : BaseObserver<AccountAuditBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: AccountAuditBean) {
                if (dataBean.resultCode != "V00000") {
                    return
                }
                accountAuditStatus = dataBean.data?.obj?.accountAuditStatus.ifNull()
                mView?.assetsCardUpdate()
            }
        })
    }

    override fun imgCloseApi(idList: String) {
        if (clickAble) {
            clickAble = false
            Looper.myLooper()?.let {
                Handler(it).postDelayed({ clickAble = true }, 1000)
            }
            val paramMap = hashMapOf<String, Any>(
                "imgType" to 21,     // 21:首页广告位
                "eventIdList" to idList,
                "userId" to UserDataUtil.userId(),
                "accountId" to UserDataUtil.accountCd(),
                "token" to UserDataUtil.loginToken()
            )
            mModel?.imgCloseApi(paramMap, object : BaseObserver<DataObjStringBean>() {
                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onNext(dataBean: DataObjStringBean?) {
                    if (dataBean?.resultCode != "V00000" && dataBean?.resultCode != "********") {
//                        ToastUtil.showToast(dataBean?.msgInfo)
                        return
                    }
                    bannerData = null
                }
            })
        }
    }

    fun sensorUpgradeClick(buttonName: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.CURRENT_PAGE_NAME, "Home Page")
        properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName)
        SensorsDataUtil.track(SensorsConstant.V3700.UPGRADE_CLICK, properties)
    }
}