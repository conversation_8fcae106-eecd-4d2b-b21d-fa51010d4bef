package cn.com.vau.trade.bean;

/**
 * Created by zhy on 2018/12/11.
 */

public class SearchObjProducts {

    public SearchObjProducts(String name) {
        this.prodName = name;
        this.prodCode = name;
    }

    /**
     * id : fd524f8b93e5413490c93e9137034901
     * prodType : 1
     * prodCode : XPDAUD
     * prodName : XPDAUD
     * prodDesc : 0
     * prodStatus : 1
     * digits : 5
     * serialNo : 7
     * createrId : 0
     * createTime : 1544070803000
     */

    private String id;
    private String prodType;
    private String prodCode;
    private String prodName;
    private String prodDesc;
    private String prodStatus;
    private int digits;
    private int serialNo;
    private String createrId;
    private long createTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProdType() {
        return prodType;
    }

    public void setProdType(String prodType) {
        this.prodType = prodType;
    }

    public String getProdCode() {
        return prodCode;
    }

    public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
    }

    public String getProdName() {
        return prodName;
    }

    public void setProdName(String prodName) {
        this.prodName = prodName;
    }

    public String getProdDesc() {
        return prodDesc;
    }

    public void setProdDesc(String prodDesc) {
        this.prodDesc = prodDesc;
    }

    public String getProdStatus() {
        return prodStatus;
    }

    public void setProdStatus(String prodStatus) {
        this.prodStatus = prodStatus;
    }

    public int getDigits() {
        return digits;
    }

    public void setDigits(int digits) {
        this.digits = digits;
    }

    public int getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(int serialNo) {
        this.serialNo = serialNo;
    }

    public String getCreaterId() {
        return createrId;
    }

    public void setCreaterId(String createrId) {
        this.createrId = createrId;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }
}
