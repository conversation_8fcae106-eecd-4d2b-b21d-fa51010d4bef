package cn.com.vau.trade.data

import cn.com.vau.data.init.ShareProductData

/**
 * 交易订单类型
 */
sealed class TradeOrderType {
    //Sell单
    data object Sell : TradeOrderType()

    //Buy单
    data object Buy : TradeOrderType()
}

/**
 * 产品状态
 */
sealed class ProductState {

    //禁止交易
    data object NoTrading : ProductState()

    //只允许平仓
    data object OnlyClose : ProductState()

    //完全访问
    data object FullAccess : ProductState()

    //闭市状态
    data object MarketClose : ProductState()

    //无产品
    data object NoProduct : ProductState()
}

fun ShareProductData?.toProductState(): ProductState {
    return when {
        this == null -> ProductState.NoProduct
        marketClose -> ProductState.MarketClose
        enable == "2" -> ProductState.FullAccess
        enable == "1" -> ProductState.OnlyClose
        enable == "0" -> ProductState.NoTrading
        else -> ProductState.FullAccess
    }
}