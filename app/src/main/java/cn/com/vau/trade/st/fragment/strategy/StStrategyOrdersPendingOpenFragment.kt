package cn.com.vau.trade.st.fragment.strategy

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import android.view.ViewStub
import androidx.fragment.app.activityViewModels
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.databinding.FragmentOpenTradesOrderBinding
import cn.com.vau.databinding.VsLayoutNoDataScrollBinding
import cn.com.vau.trade.activity.KLineActivity
import cn.com.vau.trade.adapter.StStrategyPendingOpenRecyclerAdapter
import cn.com.vau.trade.st.model.StStrategyOrdersViewModel
import cn.com.vau.util.widget.dialog.CenterActionDialog

/**
 * 策略订单详情页 -- 待开仓 pending open
 */
class StStrategyOrdersPendingOpenFragment : BaseMvvmBindingFragment<FragmentOpenTradesOrderBinding>() {

    private var mAdapter: StStrategyPendingOpenRecyclerAdapter? = null

    private val mViewModel by activityViewModels<StStrategyOrdersViewModel>()

    @SuppressLint("WrongConstant")
    override fun initView() {
        mBinding.mSmartRefreshLayout.setEnableLoadMore(false)

        mBinding.mVsNoDataScroll.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutNoDataScrollBinding.bind(inflated)
                vs.mNoDataScrollView.setHintMessage(getString(R.string.no_positions))
            }
        })

        mAdapter = StStrategyPendingOpenRecyclerAdapter(
            requireContext(),
            mViewModel.shareStrategyData?.pendingOpen ?: arrayListOf()
        )
        mBinding.mRecyclerView.adapter = mAdapter
        mBinding.mRecyclerView.setEmptyView(mBinding.mVsNoDataScroll)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mAdapter?.notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun createObserver() {
        super.createObserver()
        mViewModel.stTradePositionCancelLiveData.observe(this) {
            mAdapter?.notifyDataSetChanged()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initListener() {
        super.initListener()
        mBinding.mSmartRefreshLayout.setOnRefreshListener {
            mAdapter?.notifyDataSetChanged()
            mBinding.mSmartRefreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
        }

        mAdapter?.setOnItemClickListener(object : StStrategyPendingOpenRecyclerAdapter.OnItemClickListener {

            override fun onDeleteClick(orderData: ShareOrderData) {
                CenterActionDialog.Builder(requireActivity())
                    .setContent("${getString(R.string.delete_order)}?")
                    .setEndText(getString(R.string.yes_confirm))
                    .setOnEndListener {
                        mViewModel.stTradePositionCancel(orderData)
                    }
                    .build()
                    .showDialog()
            }

            override fun onStartKLine(position: Int) {
                openActivity(KLineActivity::class.java, Bundle().apply {
                    putString(
                        Constants.PARAM_PRODUCT_NAME,
                        mViewModel.shareStrategyData?.pendingOpen?.getOrNull(position)?.symbol ?: ""
                    )
                })
            }
        })
    }
}