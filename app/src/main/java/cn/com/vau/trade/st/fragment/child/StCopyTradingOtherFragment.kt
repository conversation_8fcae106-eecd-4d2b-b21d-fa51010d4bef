package cn.com.vau.trade.st.fragment.child

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.*
import androidx.fragment.app.Fragment
import cn.com.vau.R
import cn.com.vau.common.base.fragment.BaseFragment
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.databinding.FragmentStCopyTradingPositionsBinding
import cn.com.vau.page.StickyEvent
import cn.com.vau.util.init
import org.greenrobot.eventbus.*

/**
 * other (待审核/已拒绝)
 */
class StCopyTradingOtherFragment : BaseFragment() {

    private val mBinding by lazy { FragmentStCopyTradingPositionsBinding.inflate(layoutInflater) }

    private val fragmentList by lazy {
        ArrayList<Fragment>().apply {
            add(StCopyTradingPositionsPendingReviewFragment())
            add(StCopyTradingPositionsRejectedFragment())
        }
    }

    private val titleList = ArrayList<String>()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = mBinding.root

    override fun initParam() {
        super.initParam()
        EventBus.getDefault().register(this)

        titleList.add(getString(R.string.pending_review))
        titleList.add(getString(R.string.rejected))
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    override fun initView() {
        super.initView()

        mBinding.mViewPager2.isUserInputEnabled = false // 禁止滑动
        mBinding.mViewPager2.init(fragmentList,titleList,childFragmentManager,this)
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onStickyEvent(event: StickyEvent) {
        when (event.tag) {
            // 待审核
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_STRATEGY_PENDING_REVIEW -> {
                mBinding.mViewPager2.post {
                    mBinding.mViewPager2.setCurrentItem(titleList.indexOfFirst {
                        it == getString(R.string.pending_review)
                    }, false)
                }
            }
            // 已拒绝
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_STRATEGY_REJECTED -> {
                mBinding.mViewPager2.post {
                    mBinding.mViewPager2.currentItem = titleList.indexOfFirst {
                        it == getString(R.string.rejected)
                    }
                }
            }
        }
    }

    // 切换到对应的fragment
    fun switchFragment(position: Int) {
        if (isAdded) {
            mBinding.mViewPager2.setCurrentItem(position, false)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

}