import cn.com.vau.common.base.mvp.*
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.DataObjStringBean
import cn.com.vau.data.account.*
import cn.com.vau.data.enums.EnumLinkSkipState
import cn.com.vau.data.init.MaintenanceBean
import io.reactivex.disposables.Disposable

/**
 * 產品列表
 */
interface HomepageContract {

    interface Model : BaseModel {
        fun queryMT4AccountState(map: HashMap<String, String>, baseObserver: BaseObserver<MT4AccountTypeBean>): Disposable
        fun accountOpeningGuide(map: HashMap<String, Any>, baseObserver: BaseObserver<AccountOpeningGuideBean>): Disposable
        fun userQueryUserLevel(map: HashMap<String, Any>, baseObserver: BaseObserver<KycVerifyLevelDataBean>): Disposable
        fun queryVirtualAccount(baseObserver: BaseObserver<CheckVirtualAccountBean>): Disposable
        fun queryFirstAccountAuditStatus(baseObserver: BaseObserver<AccountAuditBean>): Disposable
        fun checkMaintenanceV2(imgType: Int, baseObserver: BaseObserver<MaintenanceBean>): Disposable
        fun imgCloseApi(map: HashMap<String, Any>, baseObserver: BaseObserver<DataObjStringBean>): Disposable
    }

    interface View : BaseView {
        fun skipOpenAccountActivity(state: EnumLinkSkipState, objData: MT4AccountTypeObj?)
        fun assetsCardUpdate()
        fun tableUpdate()
        fun hideOperationBanner()
        fun goLiveAccount(isCompleteVirtual: Boolean)
        fun virtualAccountStateError()
        fun kycGuideInfo(kycInfo: KycVerifyLevelObj?)
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun queryMT4AccountState(state: EnumLinkSkipState)
        abstract fun accountOpeningGuide()
        abstract fun userQueryUserLevel()
        abstract fun queryVirtualAccount(isHandleError: Boolean)
        abstract fun queryFirstAccountAuditStatus()
        abstract fun imgCloseApi(idList: String)
    }

}
