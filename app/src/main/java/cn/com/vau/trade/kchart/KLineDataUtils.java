package cn.com.vau.trade.kchart;

import android.text.TextUtils;

import androidx.annotation.Nullable;

import java.util.ArrayList;
import java.util.List;

import cn.com.vau.common.view.popup.bean.KLineViewSettingData;
import cn.com.vau.common.view.popup.bean.TradingViewSettingData;
import cn.com.vau.data.trade.KChartBean;
import cn.com.vau.trade.bean.kchart.KLineSettingData;
import cn.com.vau.util.ExpandKt;
import cn.com.vau.util.widget.FirebaseManager;

/**
 * Created by roy on 2018/8/3.
 * 产品详情数据类
 */

public class KLineDataUtils {
    // -----------------配置参数-----------------
    public static boolean isFrontPortrait = true;   // 竖屏前台可见
    public static boolean startWithSubCross = false;    // 十字线是否从副图开始
    public static boolean isStartActivity = false;
    public static boolean isJumpToHKLine = false;
    public static String selectedOrderNo = "0";
    // -----------------存储数据-----------------
    public static KLineSettingData userData;
    @Nullable
    public static TradingViewSettingData userDataTV;
    public static KLineViewSettingData userDataKV;
    public static List<KChartBean.DataBean.ChartsBean> mainList = new ArrayList<>();
    public static List<String> timeShareList = new ArrayList<>();
    // -----------------指标线数据-----------------
    public static List<String> mA5List = new ArrayList<>();
    public static List<String> mA10List = new ArrayList<>();
    public static List<String> mA20List = new ArrayList<>();
    public static List<String> mA30List = new ArrayList<>();
    public static List<String> mA60List = new ArrayList<>();
    public static List<String> midList = new ArrayList<>();
    public static List<String> upperList = new ArrayList<>();
    public static List<String> lowerList = new ArrayList<>();
    public static List<String> wrList = new ArrayList<>();
    public static List<String> mrList = new ArrayList<>();
    public static List<String> srList = new ArrayList<>();
    public static List<String> wsList = new ArrayList<>();
    public static List<String> msList = new ArrayList<>();
    public static List<String> ssList = new ArrayList<>();
    public static List<String> bbiList = new ArrayList<>();
    public static List<String> kList = new ArrayList<>();
    public static List<String> dList = new ArrayList<>();
    public static List<String> jList = new ArrayList<>();
    public static List<String> diffList = new ArrayList<>();
    public static List<String> deaList = new ArrayList<>();
    public static List<Float> macdList = new ArrayList<>();
    public static List<String> rsi1List = new ArrayList<>();
    public static List<String> rsi2List = new ArrayList<>();
    public static List<String> rsi3List = new ArrayList<>();
    public static List<String> cciList = new ArrayList<>();


    /**
     * 求出数据极值
     *
     * @param
     * @return
     *
     * 注意 from是临时加的参数，为了排查NaN问题的原因，用来标记是哪个方法调过来的。
     */
    public static float[] getExtremeNumber(List<String> data, boolean isHaveYPadding, float paddingPercent, int from) {
        if (data == null || data.isEmpty()) {
            return new float[]{0, 0};
        }
        float[] extreme = new float[]{0, 0};
        float max = Integer.MIN_VALUE;
        float min = Integer.MIN_VALUE;

        for (String str : data) {
            if (TextUtils.isEmpty(str) || "null".equals(str) || "null".equalsIgnoreCase(str)) {
                continue;
            }
            float value = ExpandKt.toFloatCatching(str, 0f);
            //-------临时代码，为了查找问题-----
            if ((from == 2 || from == 4 || from == 6 || from == 8 || from == 102 || from == 104 || from == 106 || from == 108 || from == 1111) && Float.isNaN(value)) {
                //上报为不严重类型
                FirebaseManager.INSTANCE.recordException(new Exception("KLineDataUtils#getExtremeNumber(), value = "+value + ", str = "+str + ", from = " + from));
            }
            //-------临时代码，结束-----
            if (max == Integer.MIN_VALUE) max = value;
            if (min == Integer.MIN_VALUE) min = value;
            if (max < value) {
                max = value;
            }
            if (min > value) {
                min = value;
            }
        }
        if (isHaveYPadding && paddingPercent > 0) {
            max = max + (max - min) * paddingPercent;
            min = min - (max - min) * paddingPercent;
        }
        extreme[0] = min;
        extreme[1] = max;
        return extreme;
    }
}
