package cn.com.vau.trade.fragment.search

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.view.ViewStub
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import android.widget.TextView.OnEditorActionListener
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.databinding.FragmentSymbolSearchSearchBinding
import cn.com.vau.databinding.VsLayoutNoDataScrollBinding
import cn.com.vau.history.ui.afterTextChangedFlow
import cn.com.vau.trade.bean.SymbolItemBean
import cn.com.vau.trade.ext.SSDialogUiState
import cn.com.vau.trade.ext.SearchUiState
import cn.com.vau.trade.ext.SymbolSearchConstants.SORT_DOWN
import cn.com.vau.trade.ext.SymbolSearchConstants.SORT_NONE
import cn.com.vau.trade.ext.SymbolSearchConstants.SORT_UP
import cn.com.vau.trade.ext.SymbolSearchUtil
import cn.com.vau.trade.ext.addScrollStateListener
import cn.com.vau.trade.viewmodel.SSSearchViewModel
import cn.com.vau.trade.viewmodel.SymbolSearchDialogViewModel
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.KeyboardUtil
import cn.com.vau.util.TabType
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.dp2px
import cn.com.vau.util.getStatusHeight
import cn.com.vau.util.init
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.screenHeight
import cn.com.vau.util.setVp
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * Created by array on 2025/4/24 15:24
 * Desc: 搜索页
 */
class SSSearchFragment : BaseMvvmBindingFragment<FragmentSymbolSearchSearchBinding>() {

    companion object {
        fun newInstance(): SSSearchFragment {
            return SSSearchFragment()
        }
    }

    /**
     * 整个Dialog的ViewModel
     */
    private var dialogViewModel: SymbolSearchDialogViewModel? = null

    fun setViewModel(viewModel: SymbolSearchDialogViewModel?) {
        dialogViewModel = viewModel
    }

    /**
     * 当前Fragment的ViewModel
     */
    private lateinit var searchViewModel: SSSearchViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        searchViewModel = ViewModelProvider(this)[SSSearchViewModel::class.java]
        searchViewModel.setContext(requireActivity())
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        childFragmentManager.fragments.forEach {
            if (hidden) {
                it.onPause()
            } else {
                it.onResume()
            }
        }
        if (!hidden) {
            searchViewModel.setSearchUiState(SearchUiState.FirstEnter)
            KeyboardUtil.showSoftInput(mBinding.etSearch)
        }
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        initInput()
        initSortIcon()
        /** 设置搜索页ViewPager高度 */
        constraintH(mBinding.clSearchRoot, R.id.mViewPager, screenHeight - requireActivity().getStatusHeight() - 203.dp2px())
    }

    /**
     * 约束布局高度
     */
    private fun constraintH(root: ConstraintLayout, id: Int, height: Int) {
        val set = ConstraintSet()
        set.clone(root)
        set.constrainHeight(id, height)
        set.applyTo(root)
    }

    @SuppressLint("SetTextI18n")
    override fun initListener() {
        super.initListener()
        /** 点击Cancel按钮，切换为tab页面 */
        mBinding.tvCancel.setOnClickListener {
            mBinding.etSearch.setText("")
            KeyboardUtil.hideSoftInput(mBinding.etSearch)
            dialogViewModel?.setDialogUiState(SSDialogUiState.CategoryLayout)
        }

        /**搜索结果事件流 */
        lifecycleScope.launch {
            searchViewModel.searchResult.flowWithLifecycle(lifecycle, Lifecycle.State.STARTED).collectLatest { it ->
                setTabLayout(it.input, it.searchMap)
            }
        }

        /** 搜索页面状态 */
        lifecycleScope.launch {
            searchViewModel.searchUiState.flowWithLifecycle(lifecycle, Lifecycle.State.STARTED).collectLatest { it ->
                when (it) {
                    SearchUiState.FirstEnter -> {
                        mBinding.mVsNoDataScroll.isVisible = false
                        mBinding.groupSearchResult.isVisible = false
                    }

                    SearchUiState.ResultEmpty -> {
                        mBinding.mVsNoDataScroll.isVisible = true
                        mBinding.groupSearchResult.isVisible = false
                    }

                    SearchUiState.ResultNotEmpty -> {
                        mBinding.mVsNoDataScroll.isVisible = false
                        mBinding.groupSearchResult.isVisible = true
                    }
                }
            }
        }
    }

    @OptIn(FlowPreview::class)
    @SuppressLint("SetTextI18n")
    private fun initInput() {

        /**
         * 输入框输入监听
         */
        mBinding.etSearch.afterTextChangedFlow().debounce(300).onEach {
            //显示隐藏清空按钮
            mBinding.ivClear.isVisible = it.isNotEmpty()
            //执行搜索
            searchViewModel.searchSymbol(searchViewModel.searchUiState.value, it)
        }.launchIn(lifecycleScope)

        /** 无数据提示 */
        mBinding.mVsNoDataScroll.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutNoDataScrollBinding.bind(inflated)
                vs.mNoDataScrollView.setHintMessage(requireActivity().getString(R.string.no_symbols))
            }
        })

        /** 键盘搜索 */
        mBinding.etSearch.setOnEditorActionListener(object : OnEditorActionListener {
            override fun onEditorAction(textView: TextView?, i: Int, keyEvent: KeyEvent?): Boolean {
                when (i) {
                    EditorInfo.IME_ACTION_SEARCH -> {
                        //隐藏键盘
                        dialogViewModel?.hideSoftInput()

                        //执行搜索
                        searchViewModel.searchSymbol(searchViewModel.searchUiState.value, mBinding.etSearch.text.toString())
                        return true
                    }

                    else -> return false
                }
            }
        })

        /** hint */
        mBinding.etSearch.setHint(R.string.search_instruments)

        /** 点击btn，清空输入框 */
        mBinding.ivClear.clickNoRepeat {
            mBinding.etSearch.setText("")
            //执行搜索
            searchViewModel.searchSymbol(searchViewModel.searchUiState.value, "")
        }

        KeyboardUtil.showSoftInput(mBinding.etSearch)

    }

    /**
     * 设置TabLayout
     */
    private fun setTabLayout(input: String, map: LinkedHashMap<String, List<SymbolItemBean>>) {
        val titleList = mutableListOf<String>()
        val fragmentList = mutableListOf<Fragment>()
        for (entry in map) {
            titleList.add(entry.key)
            val fragment = SSSearchSymbolListFragment.newInstance(entry.key)
            fragment.setViewModel(dialogViewModel)
            fragment.setDataList(entry.value)
            fragmentList.add(fragment)
        }
        mBinding.mViewPager.init(fragmentList, titleList, childFragmentManager, requireActivity())
        mBinding.mViewPager.addScrollStateListener {
            dialogViewModel?.isSearchPagerScrolling = it
        }
        mBinding.mTabLayout.setVp(mBinding.mViewPager, titleList, TabType.LINE_INDICATOR)
//        mBinding.mViewPager.setCurrentItem(0, false)
        mBinding.mTabLayout.scrollTo(0,0)
    }

    @SuppressLint("SetTextI18n")
    private fun initSortIcon() {

        mBinding.tvPriceRose.text = requireActivity().getString(R.string.last_price_change)
        mBinding.tvPriceRose.clickNoRepeat(500) {
            SymbolSearchUtil.nextSort(dialogViewModel?.sortModeSearchSymbolList ?: SORT_NONE) { sortMode ->
                dialogViewModel?.sortModeSearchSymbolList = sortMode
                setPriceRoseIcon(sortMode)
                dialogViewModel?.sortSearchSymbolList(sortMode)
            }
        }

        setPriceRoseIcon(dialogViewModel?.sortModeSearchSymbolList ?: SORT_NONE)

    }

    /**
     * 设置价格涨跌幅排序icon
     */
    private fun setPriceRoseIcon(sortMode: Int) {
        val drawable = ContextCompat.getDrawable(
            requireActivity(), AttrResourceUtil.getDrawable(
                requireActivity(), when (sortMode) {
                    SORT_DOWN -> R.attr.imgDownSort
                    SORT_UP -> R.attr.imgUpSort
                    else -> R.attr.imgNotSort
                }
            )
        )
        if (LanguageHelper.isRtlLanguage()) {
            mBinding.tvPriceRose.setCompoundDrawablesWithIntrinsicBounds(drawable, null, null, null)
        } else {
            mBinding.tvPriceRose.setCompoundDrawablesWithIntrinsicBounds(null, null, drawable, null)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.OPTIONAL_PRODUCT_LIST_UPDATED -> {
                searchViewModel.searchSymbol(searchViewModel.searchUiState.value, mBinding.etSearch.text.toString())
            }
        }
    }

}
