package cn.com.vau.trade.fragment.managesymbols

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.InputType
import android.view.*
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import cn.com.vau.R
import cn.com.vau.common.base.fragment.BaseFrameFragment
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.databinding.*
import cn.com.vau.trade.activity.SearchProductActivity
import cn.com.vau.trade.model.StAddSymbolsModel
import cn.com.vau.trade.presenter.*
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import org.greenrobot.eventbus.*
import org.json.JSONObject

class AddSymbolsFragment : BaseFrameFragment<StAddSymbolsPresenter, StAddSymbolsModel>(), StAddSymbolsContract.View {

    private val mBinding by lazy { FragmentAddSymbolsBinding.inflate(layoutInflater) }
    private val mergeSearchBinding by lazy { MergeSearchBinding.bind(mBinding.root) }

    private val fragmentList = mutableListOf<Fragment>()
    private val titleList = arrayListOf<String>()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = mBinding.root

    override fun initParam() {
        super.initParam()
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    @SuppressLint("WrongConstant")
    override fun initView() {
        super.initView()

        val shareGoodList = VAUSdkUtil.shareGoodList()

        shareGoodList.forEachIndexed { index, (groupName) ->
            fragmentList.add(AddSymbolsItemFragment.newInstance(index))
            titleList.add(VAUSdkUtil.getGroupNameLanguage(requireContext(), groupName.ifNull()))
        }

        mBinding.mViewPager.init(fragmentList, titleList, childFragmentManager, this)
        mBinding.mTabLayout.setVp(mBinding.mViewPager, titleList, TabType.WRAP_INDICATOR) {
            mPresenter.netBean.setrBtnIndex(it)
            mPresenter.initAddOptionalData()
        }

    }

    override fun initData() {
        super.initData()
        mPresenter.netBean.setrBtnIndex(0) //初始選中第一個tabIndex
        mPresenter.initAddOptionalData()
    }

    override fun initListener() {
        super.initListener()
        mergeSearchBinding.etSearch.setHint(R.string.search_instruments)
        mergeSearchBinding.etSearch.isFocusable = false
        mergeSearchBinding.etSearch.isFocusableInTouchMode = false
        mergeSearchBinding.etSearch.inputType = InputType.TYPE_NULL
        mergeSearchBinding.etSearch.setOnClickListener(this)
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.etSearch -> {
                // 跳转至产品搜索页
                openActivity(SearchProductActivity::class.java)

                // 埋点
                LogEventUtil.setLogEvent(
                    BuryPointConstant.V342.GENERAL_SEARCH_BUTTON_CLICK, bundleOf(
                        "Position" to "watchlist",
                        "Type" to "-",
                        "Account_type" to when {
                            UserDataUtil.isDemoAccount() -> "Demo"
                            UserDataUtil.isCopyTradingAccount()-> "Copy_trading"
                            else -> "Live"
                        }
                    )
                )
                // 神策自定义埋点(v3500)
                // 点击搜索按钮 -> 点击搜索按钮时触发
                val properties = JSONObject()
                properties.put(SensorsConstant.Key.CURRENT_PAGE_NAME, javaClass.simpleName) // 当前页面名称
                SensorsDataUtil.track(SensorsConstant.V3500.SEARCH_BTN_CLICK, properties)

            }
        }
    }

    @SuppressLint("SetTextI18n")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.REFRESH_ADD_SYMBOLS_LIST -> {
                mPresenter.initAddOptionalData()
            }
        }
    }

    fun activityNotifyChange() {
        EventBus.getDefault().post(NoticeConstants.REFRESH_ADD_SYMBOLS_LIST)
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

}
