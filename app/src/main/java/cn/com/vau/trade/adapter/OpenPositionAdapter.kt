package cn.com.vau.trade.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Group
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.data.enums.EnumAdapterPosition
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.trade.PositionBean
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.PositionDataUtil
import cn.com.vau.util.arabicReverseTextByFlag
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.setTextColorDiff
import cn.com.vau.util.setTextDiff
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 新的持仓列表Adapter 包含：live，跟单自主交易，K线页 持仓列表，
 */
class OpenPositionAdapter(
    var mContext: Context,
    var dataList: CopyOnWriteArrayList<ShareOrderData>,
    var adapterEnum: EnumAdapterPosition
) : RecyclerView.Adapter<OpenPositionAdapter.ViewHolder>() {

    private val currencyType by lazy { UserDataUtil.currencyType() }
    private val c00c79c by lazy { ContextCompat.getColor(mContext, R.color.c00c79c) }
    private val cf44040 by lazy { ContextCompat.getColor(mContext, R.color.cf44040) }
    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(mContext, R.attr.color_c1e1e1e_cebffffff) }
    private val color_c1f1e1e1e_c1fffffff by lazy { AttrResourceUtil.getColor(mContext, R.attr.color_c1f1e1e1e_c1fffffff) }
    private val pnl by lazy { mContext.getString(R.string.pnl) }
    private val volume by lazy { mContext.getString(R.string.volume) }
    private val lot by lazy { mContext.getString(R.string.lots) }
    private val tpSl by lazy { mContext.getString(R.string.tp_sl) }
    private val tp by lazy { mContext.getString(R.string.majuscule_tp) }
    private val sl by lazy { mContext.getString(R.string.majuscule_sl) }

    @SuppressLint("SetTextI18n")
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val holder = ViewHolder(
            LayoutInflater.from(mContext).inflate(R.layout.item_recycler_open_position, parent, false)
        )
        // K线
        holder.ivKLine.clickNoRepeat {
            mOnItemClickListener?.onStartKLine(holder.bindingAdapterPosition)
        }
        holder.tvProdName.clickNoRepeat {
            mOnItemClickListener?.onStartKLine(holder.bindingAdapterPosition)
        }

        // 分享（持仓列表 - 跟单自主&多品牌)
        holder.ivShare.setOnClickListener {
            mOnItemClickListener?.onShareClick(holder.bindingAdapterPosition)
        }
        // 止盈止损展开收起
        holder.ivTpSlStatue.clickNoRepeat {
            mOnItemClickListener?.onTpSlTitleClick(holder.bindingAdapterPosition)
        }
        holder.tvTpSlTitle.clickNoRepeat {
            mOnItemClickListener?.onTpSlTitleClick(holder.bindingAdapterPosition)
        }

        holder.ivTpSlEdit.clickNoRepeat {
            mOnItemClickListener?.onTpSlClick(holder.bindingAdapterPosition)
        }

        holder.ivTpSlDelete.clickNoRepeat {
            mOnItemClickListener?.onTpSlDeleteClick(holder.bindingAdapterPosition)
        }

        // 反向开仓
        holder.ivReverse.clickNoRepeat {
            mOnItemClickListener?.onReverseClick(holder.bindingAdapterPosition)
        }

        // 设置止盈止损
        holder.tvTpSl.clickNoRepeat {
            mOnItemClickListener?.onTpSlClick(holder.bindingAdapterPosition)
        }

        // 互抵平仓
        holder.tvCloseBy.clickNoRepeat {
            mOnItemClickListener?.onCloseByClick(holder.bindingAdapterPosition)
        }

        // 平仓
        holder.tvClose.clickNoRepeat {
            mOnItemClickListener?.onCloseClick(holder.bindingAdapterPosition)
        }

        //闪电平仓
        holder.tvQuickClose.clickNoRepeat {
            mOnItemClickListener?.onQuickCloseClick(holder.bindingAdapterPosition)
        }

        holder.itemView.clickNoRepeat {
            mOnItemClickListener?.onItemClick(holder.bindingAdapterPosition)
        }

        return holder
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        val data = dataList.getOrNull(position) ?: return
        val positionBean = data.positionBean ?: PositionBean()

        holder.ivKLine.isVisible = (adapterEnum != EnumAdapterPosition.K_LINE)
        holder.ivShare.isVisible = (adapterEnum == EnumAdapterPosition.MAIN_ORDER)

        // 产品名称
        holder.tvProdName.setTextDiff(data.symbol.ifNull())

        // 买卖类型
        if (OrderUtil.isBuyOfOrder(data.cmd)) {
            holder.tvOrderType.setTextDiff("B")
            holder.tvOrderType.background = ContextCompat.getDrawable(mContext, R.drawable.shape_c00c79c_r2_8)
        } else {
            holder.tvOrderType.setTextDiff("S")
            holder.tvOrderType.background = ContextCompat.getDrawable(mContext, R.drawable.shape_cf44040_r2_8)
        }

        // 手数
        if (PositionDataUtil.isAmountOpen()) {
            holder.tvVolTitle.text = "$volume (${UserDataUtil.currencyType()})"
            holder.tvVolume.setTextDiff(data.positionBean?.volumeAmount.ifNull())
        } else {
            holder.tvVolTitle.text = "$volume ($lot)"
            holder.tvVolume.setTextDiff(data.volumeUI.ifNull())
        }

        // 开仓价
        holder.tvOpenPrice.setTextDiff(data.positionBean?.openPriceUI.ifNull())
        // 设置货币
        holder.tvPnlTitle.setTextDiff("${pnl.arabicReverseTextByFlag(" ").ifNull()} (${currencyType})".arabicReverseTextByFlag(" ").ifNull())

        // 订单盈亏
        holder.tvPnl.setTextDiff(if ("-" == data.closePrice) "-" else data.positionBean?.profitUI.ifNull())
        if ("-" == data.closePrice) {
            holder.tvPnl.setTextColorDiff(color_c1e1e1e_cebffffff)
        } else {
            holder.tvPnl.setTextColorDiff(if (data.profit >= 0) c00c79c else cf44040)
        }
        if (positionBean.tpSlStatus != 0) {
            holder.tvTpSlTitle.setTextDiff("$tpSl (1)")
            holder.ivTpSlStatue.setImageResource(if (positionBean.tpSlStatus == 2) R.drawable.draw_bitmap2_arrow_top10x10_c1e1e1e_cebffffff else R.drawable.draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff)
            if (positionBean.tpSlStatus == 2) {
                holder.tvTpSlVolumeTitle.setTextDiff("${mContext.getString(R.string.volume)} (${mContext.getString(R.string.lots)})")
                val tpRange = if (OrderUtil.isBuyOfOrder(data.cmd)) "≥" else "≤"
                val slRange = if (OrderUtil.isBuyOfOrder(data.cmd)) "≤" else "≥"
                val tpStr = if (data.takeProfit.mathCompTo("0") ==1) "$tpRange${data.positionBean?.tpUI?:"0"}" else "--"
                val slStr = if (data.stopLoss.mathCompTo("0") ==1) "$slRange${data.positionBean?.slUI?:"0"}" else "--"
                holder.tvTp.setTextDiff(tpStr)
                holder.tvSl.setTextDiff(slStr)

                holder.tvSlTitle.setTextDiff("$sl (${data.priceCurrency})")
                holder.tvTpTitle.setTextDiff("$tp (${data.priceCurrency})")
                if (PositionDataUtil.isAmountOpen()) {
                    holder.tvTpSlVolumeTitle.setTextDiff("$volume (${UserDataUtil.currencyType()})")
                } else {
                    holder.tvTpSlVolumeTitle.setTextDiff("$volume ($lot)")
                }
            }
        }
        holder.clSetTpSl.isVisible = positionBean.tpSlStatus == 2
        holder.tvTpSlGroup.isVisible = positionBean.tpSlStatus != 0

        holder.offView.setBackgroundColor(color_c1f1e1e1e_c1fffffff)

    }

    @Deprecated("暂时废弃，后面可能加回来")
    private fun setEstimateColor(holder: ViewHolder, positionBean: PositionBean) {
        val estimatedSlColor = if (positionBean.estimatedSl.mathCompTo("0") == 1) {
            c00c79c
        } else if (positionBean.estimatedSl.mathCompTo("0") == -1) {
            cf44040
        } else {
            color_c1e1e1e_cebffffff
        }
//        holder.tvEstimatedSl.setTextColorDiff(estimatedSlColor)

        val estimatedPnlColor = if (positionBean.estimatedTP.mathCompTo("0") == 1) {
            c00c79c
        } else if (positionBean.estimatedTP.mathCompTo("0") == -1) {
            cf44040
        } else {
            color_c1e1e1e_cebffffff
        }
//        holder.tvEstimatedPnl.setTextColorDiff(estimatedPnlColor)
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setData(orderList: CopyOnWriteArrayList<ShareOrderData>?) {
        dataList.clear()
        dataList.addAll(orderList ?: ArrayList<ShareOrderData>())
        notifyDataSetChanged()
    }


    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val tvProdName: TextView = view.findViewById(R.id.tvProdName)
        val ivKLine: ImageView = view.findViewById(R.id.ivKLine)
        val ivShare: AppCompatImageView = view.findViewById(R.id.ivShare)
        val tvOrderType: TextView = view.findViewById(R.id.tvOrderType)

        val tvPnlTitle: TextView = view.findViewById(R.id.tvPnlTitle)
        val tvPnl: TextView = view.findViewById(R.id.tvPnl)

        val tvVolTitle: TextView = view.findViewById(R.id.tvVolTitle)
        val tvVolume: TextView = view.findViewById(R.id.tvVolume)

        val tvOpenPriceTitle: TextView = view.findViewById(R.id.tvOpenPriceTitle)
        val tvOpenPrice: TextView = view.findViewById(R.id.tvOpenPrice)

        val tvTpSlGroup: Group = view.findViewById(R.id.ptSlGP)
        val clSetTpSl: ConstraintLayout = view.findViewById(R.id.clSetTpSl)
        val tvTpSlTitle: TextView = view.findViewById(R.id.tvTpSlTitle)
        val ivTpSlStatue: ImageView = view.findViewById(R.id.ivPtSlStatue)
        val ivTpSlDelete: ImageView = view.findViewById(R.id.ivTpSlDelete)
        val ivTpSlEdit: ImageView = view.findViewById(R.id.ivTpSlEdit)
        val tvTpSlVolumeTitle: TextView = view.findViewById(R.id.tvTpSlVolumeTitle)
        val tvTpTitle: TextView = view.findViewById(R.id.tvTpTitle)
        val tvTp: TextView = view.findViewById(R.id.tvTp)
        val tvSlTitle: TextView = view.findViewById(R.id.tvSlTitle)
        val tvSl: TextView = view.findViewById(R.id.tvSl)

        val ivReverse: ImageView = view.findViewById(R.id.ivReverse)
        val tvTpSl: TextView = view.findViewById(R.id.tvTpSl)
        val tvCloseBy: TextView = view.findViewById(R.id.tvCloseBy)
        val tvClose: TextView = view.findViewById(R.id.tvClose)
        val tvQuickClose: TextView = view.findViewById(R.id.tvQuickClose)

        val offView: View = view.findViewById(R.id.offView)
    }

    private var mOnItemClickListener: OnItemClickListener? = null

    interface OnItemClickListener {
        fun onStartKLine(position: Int)
        fun onShareClick(position: Int)
        fun onReverseClick(position: Int)
        fun onTpSlClick(position: Int)
        fun onTpSlTitleClick(position: Int)
        fun onTpSlDeleteClick(position: Int)
        fun onCloseByClick(position: Int)
        fun onCloseClick(position: Int)
        fun onQuickCloseClick(position:Int)
        fun onItemClick(position: Int)
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        mOnItemClickListener = onItemClickListener
    }
}