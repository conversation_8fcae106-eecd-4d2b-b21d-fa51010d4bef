package cn.com.vau.trade.presenter

import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.data.init.ShareProductGroupsData
import cn.com.vau.trade.bean.ManageOptionalNetBean

/**
 * Created by roy on 2018/11/6.
 * 自选页面
 */
class StAddSymbolsPresenter : StAddSymbolsContract.Presenter() {

    var unSelectDataList: ArrayList<ShareProductData> = arrayListOf()

    var netBean: ManageOptionalNetBean = ManageOptionalNetBean()

    // 初始化未选中
    override fun initAddOptionalData() {

        unSelectDataList.clear()

        if (VAUSdkUtil.shareGoodList().size == 0 || VAUSdkUtil.shareGoodList().size <= netBean.getrBtnIndex()) return

        val symbolList =
            VAUSdkUtil.shareGoodList().getOrElse(netBean.getrBtnIndex()) { ShareProductGroupsData() }.symbolList

        for1@ for (dataBean in symbolList ?: arrayListOf()) {
            // 不可交易直接省略
            if (dataBean.enable != "2") continue

            for (symbolData in VAUSdkUtil.collectSymbolList) {
                // 已经被选，直接跳过两个循环，不走unselectedDataList.add(dataBean)
                if (dataBean.symbol == symbolData.symbol) {
                    continue@for1
                }
            }
            unSelectDataList.add(dataBean)
        }

    }

}