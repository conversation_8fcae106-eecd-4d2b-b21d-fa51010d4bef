package cn.com.vau.trade.ext

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import androidx.core.content.ContextCompat
import androidx.core.view.isEmpty
import androidx.core.widget.NestedScrollView
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import cn.com.vau.R
import cn.com.vau.data.init.ShareAccountInfoData
import cn.com.vau.data.init.StShareAccountInfoData
import cn.com.vau.trade.data.AccountInfoCardBean
import cn.com.vau.trade.data.AccountInfoType
import cn.com.vau.trade.data.MarginRiskLevel
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.screenHeight

/**
 * 保证金水平字体颜色
 */
fun AccountInfoCardBean.toMarginLevelColor(context: Context): Int {
    return if (marginLevel == 0.0) {
        AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
    } else {
        if ("$marginLevel".mathCompTo(marginCall) == 1) {
            ContextCompat.getColor(context, R.color.c00c79c)
        } else {
            if ("$marginLevel".mathCompTo(marginStopOut) == 1) ContextCompat.getColor(context, R.color.ce35728) else ContextCompat.getColor(context, R.color.cf44040)
        }
    }
}

/**
 * 保证金水平风险等级
 */
fun AccountInfoCardBean.toMarginRiskLevel(): MarginRiskLevel {
    return if (marginLevel == 0.0) {
        MarginRiskLevel.Low
    } else {
        if ("$marginLevel".mathCompTo(marginCall) == 1) {
            MarginRiskLevel.Low
        } else {
            if ("$marginLevel".mathCompTo(marginStopOut) == 1) MarginRiskLevel.Medium else MarginRiskLevel.High
        }
    }

}

/**
 * 浮动盈亏字体颜色
 */
fun AccountInfoCardBean.toFloatingPnlColor(context: Context): Int {
    return if (profit == 0.0) {
        AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
    } else {
        if (profit > 0) ContextCompat.getColor(context, R.color.c00c79c) else ContextCompat.getColor(context, R.color.cf44040)
    }
}

/**
 * 余额字体颜色
 */
fun AccountInfoCardBean.toBalanceColor(context: Context): Int {
    return if (balance >= 0) AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff) else ContextCompat.getColor(context, R.color.cf44040)
}

/**
 * 更新账户信息item值的字体颜色
 */
fun AccountInfoType.toAccountInfoColor(context: Context, cardBean: AccountInfoCardBean): Int {
    return when (this) {
        AccountInfoType.Equity -> {
            AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
        }

        AccountInfoType.FloatingPnL -> {
            cardBean.toFloatingPnlColor(context)
        }

        AccountInfoType.MarginLevel -> {
            // 预付款比率
            cardBean.toMarginLevelColor(context)
        }

        AccountInfoType.Credit -> {
            AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
        }

        AccountInfoType.MarginAndFreeMargin -> {
            AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
        }

        AccountInfoType.Balance -> {
            cardBean.toBalanceColor(context)
        }
    }
}

/**
 * 调整ViewPager2的滑动灵敏度
 */
fun ViewPager2.changeTouchSlop(multiple: Int = 2) {
    try {
        val recyclerViewField = ViewPager2::class.java.getDeclaredField("mRecyclerView")
        recyclerViewField.isAccessible = true
        val recyclerView = recyclerViewField.get(this) as RecyclerView

        val touchSlopField = RecyclerView::class.java.getDeclaredField("mTouchSlop")
        touchSlopField.isAccessible = true
        val touchSlop = touchSlopField.get(recyclerView) as Int
        touchSlopField.set(recyclerView, touchSlop * multiple) // 倍数根据需求调整
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

/**
 * 复制一个账户信息卡片对象
 */
fun ShareAccountInfoData.copyData(): AccountInfoCardBean {
    return AccountInfoCardBean().apply {
        this.equity = <EMAIL>
        this.balance = <EMAIL>
        this.credit = <EMAIL>
        this.margin = <EMAIL>
        this.marginCall = <EMAIL>
        this.marginStopOut = <EMAIL>
        this.profit = <EMAIL>
        this.freeMargin = <EMAIL>
        this.marginLevel = <EMAIL>
    }
}

/**
 * 复制一个账户信息卡片对象
 */
fun StShareAccountInfoData.copyData(): AccountInfoCardBean {
    return AccountInfoCardBean().apply {
        this.profit = <EMAIL>
        this.marginLevel = <EMAIL>
        this.equity = <EMAIL>
        this.balance = <EMAIL>
        this.credit = <EMAIL>
        this.freeMargin = <EMAIL>
        this.margin = <EMAIL>
        this.marginCall = <EMAIL>
        this.marginStopOut = <EMAIL>
    }
}

fun NestedScrollView.findHasFocusEditText(): View? {
    if (isEmpty()) return null
    val container = getChildAt(0) as? ViewGroup ?: return null
    return findFocusedEditText(container)
}

private fun findFocusedEditText(viewGroup: ViewGroup): EditText? {
    for (i in 0 until viewGroup.childCount) {
        val child = viewGroup.getChildAt(i)
        when {
            child is EditText && child.hasFocus() -> return child
            child is ViewGroup -> findFocusedEditText(child)?.let { return it }
        }
    }
    return null
}

fun View.getTopToScreen(): Int {
    val location = IntArray(2)
    this.getLocationOnScreen(location)
    return location[1]
}

fun View.getBottomToScreen(): Int {
    val topToScreen = getTopToScreen()
    return screenHeight - topToScreen - this.height
}