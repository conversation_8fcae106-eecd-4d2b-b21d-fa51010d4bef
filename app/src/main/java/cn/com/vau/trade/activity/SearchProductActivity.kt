package cn.com.vau.trade.activity

import android.annotation.SuppressLint
import android.os.Bundle
import android.os.Handler
import android.view.*
import android.view.ViewStub.OnInflateListener
import android.view.inputmethod.InputMethodManager
import androidx.core.os.bundleOf
import androidx.core.widget.doAfterTextChanged
import androidx.recyclerview.widget.OrientationHelper
import cn.com.vau.R
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.performance.PerformManager
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.SDKIntervalUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.ScrollGridLayoutManager
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.*
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.profile.performance.TradePermissionPerformance
import cn.com.vau.signals.stsignal.adapter.SearchSymbolAdapter
import cn.com.vau.trade.adapter.HotProductAdapter
import cn.com.vau.trade.viewmodel.SearchProductViewModel
import cn.com.vau.util.KeyboardUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.widget.dialog.CenterActionDialog
import org.greenrobot.eventbus.*
import java.util.*

/**
 * 产品搜索
 * Created by wj
 */
open class SearchProductActivity : BaseMvvmActivity<ActivitySearchProductBinding, SearchProductViewModel>(), SDKIntervalCallback {

    private val mergeSearchBinding by lazy { MergeSearchBinding.bind(mBinding.root) }

    private var hotProductAdapter: HotProductAdapter? = null
    private var searchHistorySymbolList = arrayListOf<ShareProductData>()
    private var searchSymoblList = arrayListOf<ShareProductData>()

    private val searchHistoryAdapter: SearchSymbolAdapter by lazy {
        SearchSymbolAdapter(
            searchHistorySymbolList,
            true
        )
    }
    private val searchResultAdapter: SearchSymbolAdapter by lazy {
        SearchSymbolAdapter(
            searchSymoblList,
            true
        )
    }

    private val performManager by lazy {
        PerformManager(this)
    }
    private val tradePermissionPerformance by lazy {
        TradePermissionPerformance(this)
    }

    override fun onCallback() {
        if (InitHelper.isNotSuccess()) return
        updateHotProductsData()
    }

    override fun useEventBus(): Boolean = true

    @SuppressLint("SetTextI18n")
    override fun initView() {
        mBinding.mVsNoData.setOnInflateListener(object : OnInflateListener {
            override fun onInflate(stub: ViewStub, inflated: View) {
                val vs = VsLayoutNoDataBinding.bind(inflated)
                vs.mNoDataView.setHintMessage("${getString(R.string.not_found_desc1)}\n${getString(R.string.not_found_desc2)}")
            }
        })
        // 请输入搜索内容
        mergeSearchBinding.etSearch.setHint(R.string.search_instruments)
    }

    @SuppressLint("WrongConstant")
    override fun initData() {
        super.initData()
        val scrollGridLayoutManager = ScrollGridLayoutManager(this, 3, OrientationHelper.VERTICAL, false)
        mBinding.mRecyclerViewHot.layoutManager = scrollGridLayoutManager
        hotProductAdapter = HotProductAdapter(this, mViewModel.hotList, mViewModel.weekTrendMap)
        mBinding.mRecyclerViewHot.adapter = hotProductAdapter
        val history = SpManager.getSearchHistoryKey("")
        searchHistorySymbolList = getHistoryData(history)
        mBinding.mRecyclerViewHistory.adapter = searchHistoryAdapter
        if (searchHistoryAdapter.itemCount != 0) {
            mBinding.rlRecent.visibility = View.VISIBLE
        } else {
            mBinding.rlRecent.visibility = View.INVISIBLE
        }
        mBinding.recyclerResult.adapter = searchResultAdapter
        if (UserDataUtil.isStLogin()) {
            mViewModel.querySTProductHot()
        } else {
            mViewModel.querySearchHot()
        }
        addPerformance()
    }

    private fun addPerformance() {
        performManager.addPerformance(tradePermissionPerformance)
        tradePermissionPerformance.requestTradePermission()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun createObserver() {

        mViewModel.initHotProductsLiveData.observe(this) {
            initHotProducts()
        }

        mViewModel.refreshHotLiveData.observe(this) {
            hotProductAdapter?.notifyDataSetChanged()
        }

        mViewModel.refreshAdapterLiveData.observe(this) {
            refreshAdapter()
        }
    }

    fun initHotProducts() {
        val nameList = mViewModel.hotProductMap["Def"]
        mViewModel.hotList.clear()
        val list = ArrayList<ShareProductData>()
        if (!nameList.isNullOrEmpty()) {
            mBinding.mRecyclerViewHot.visibility = View.VISIBLE
            for (name in nameList) {
                val allMatch = VAUSdkUtil.symbolList().find { it.symbol == name && "0" !== it.enable }
                if (allMatch != null) {
                    list.add(allMatch)
                    continue
                } else {
                    val prefixMatch = VAUSdkUtil.symbolList().find { it.symbol.startsWith(name) && "0" !== it.enable }
                    if (prefixMatch != null) {
                        list.add(prefixMatch)
                        continue
                    }
                }
            }
            if (list.size > 3) {
                mViewModel.hotList.addAll(list.subList(0, 3))
            } else {
                mViewModel.hotList.addAll(list)
            }
            refreshHot()
            //加載趨勢圖
            if (UserDataUtil.isStLogin()) {
                mViewModel.querySTHistoryGetRunChart()
            } else {
                mViewModel.queryWeekTrend()
            }
        } else {
            mBinding.mRecyclerViewHot.visibility = View.INVISIBLE
        }
    }

    fun updateHotProductsData() {
        if (mViewModel.hotList.isNotEmpty()) {
            val data = VAUSdkUtil.symbolList()
            for (i in mViewModel.hotList.indices) {
                for (j in data.indices) {
                    if (data[i].symbol == mViewModel.hotList[i].symbol && "0" != data[i].enable) {
                        mViewModel.hotList[i] = data[j]
                        break
                    }
                }
            }
        }
        refreshHot()
    }

    @SuppressLint("ClickableViewAccessibility", "NotifyDataSetChanged")
    override fun initListener() {
        super.initListener()
        mergeSearchBinding.etSearch.doAfterTextChanged {
            val str = it?.toString().ifNull()
            if (str.isEmpty()) {
                mergeSearchBinding.ivClear.visibility = View.GONE
                mBinding.linearResult.visibility = View.GONE
                mBinding.linearInfo.visibility = View.VISIBLE
            } else {
                mergeSearchBinding.ivClear.visibility = View.VISIBLE
                mBinding.linearResult.visibility = View.VISIBLE
                mBinding.linearInfo.visibility = View.GONE
                searchSymoblList.clear()
                searchSymoblList.addAll(filterData(str))
                if (searchSymoblList.isEmpty()) {
                    mBinding.mVsNoData.visibility = View.VISIBLE
                    mBinding.recyclerResult.visibility = View.GONE
                } else {
                    mBinding.mVsNoData.visibility = View.GONE
                    mBinding.recyclerResult.visibility = View.VISIBLE
                    searchResultAdapter.setKey(str)
                }
            }
        }
        mBinding.mHeaderBar.setStartBackIconClickListener {
            finish()
        }
        mBinding.tvDelete.setOnClickListener(this)
        searchResultAdapter.setOnItemClickListener { _, _, position ->
            val history = SpManager.getSearchHistoryKey("")
            val sop = searchSymoblList.getOrNull(position) ?: ShareProductData()
            if (history.contains(",")) {
                if (!history.contains(sop.symbol)) {
                    SpManager.putSearchHistoryKey(history + sop.symbol + ",")
                    searchHistorySymbolList.add(sop)
                    searchHistoryAdapter.notifyDataSetChanged()
                }
            } else {
                SpManager.putSearchHistoryKey(history + sop.symbol + ",")
                searchHistorySymbolList.add(sop)
                searchHistoryAdapter.notifyDataSetChanged()
            }
            val input = mergeSearchBinding.etSearch.text.toString()
            KeyboardUtil.hideSoftInput(this)
            skipClick(position, searchSymoblList)
            // 埋点
            LogEventUtil.setLogEvent(
                BuryPointConstant.V342.GENERAL_SEARCH_ACTION_BUTTON_CLICK, bundleOf(
                    "Position" to "trade",
                    "Value" to input
                )
            )
            Handler().postDelayed({
                mergeSearchBinding.etSearch.setText("")
            }, 1000)
        }
        searchResultAdapter.setOnItemChildClickListener { _, _, position ->
            val sop = searchSymoblList.getOrNull(position)
            val isExists = VAUSdkUtil.collectSymbolList.any { it.symbol == sop?.symbol }
            clickHeart(isExists, sop?.symbol ?: "")
        }
        searchHistoryAdapter.setOnItemClickListener { _, _, position ->
            skipClick(position, searchHistorySymbolList)
        }
        searchHistoryAdapter.setOnItemChildClickListener { _, _, position ->
            val sop = searchHistorySymbolList.getOrNull(position)
            val isExists = VAUSdkUtil.collectSymbolList.any { it.symbol == sop?.symbol }
            clickHeart(isExists, sop?.symbol ?: "")
        }
        hotProductAdapter?.setOnItemClickListener(itemClickListenerHot)
        mergeSearchBinding.ivClear.setOnClickListener(this)
        mBinding.recyclerResult.setOnTouchListener { v, event ->
            if (event.action == MotionEvent.ACTION_MOVE) {
                if (KeyboardUtil.isSoftInputVisible(this)) {
                    KeyboardUtil.hideSoftInput(v)
                }
            }
            false
        }
    }

    private fun clickHeart(status: Boolean, symbol: String) {
        fun heart() {
            if (status) {
                mViewModel.updOptionalProd(symbol, true)
            } else {
                mViewModel.updOptionalProd(symbol, false)
                val input = mergeSearchBinding.etSearch.text.toString()
                // 埋点
                LogEventUtil.setLogEvent(
                    BuryPointConstant.V342.GENERAL_SEARCH_WATCHLIST_BUTTON_CLICK, bundleOf(
                        "Value" to input
                    )
                )
            }
        }
        tradePermissionPerformance.run {
            if (handleTradeBlockType { heart() }) return
        }
        heart()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onClick(view: View?) {
        when (view?.id) {
            R.id.tvDelete -> if (searchHistorySymbolList.size != 0) {
                CenterActionDialog.Builder(this)
                    // 是否确认删除历史记录？
                    .setContent(getString(R.string.are_you_sure_history))
                    .setOnEndListener {
                        SpManager.putSearchHistoryKey("")
                        searchHistorySymbolList.clear()
                        searchHistoryAdapter.notifyDataSetChanged()
                        mBinding.rlRecent.visibility = View.INVISIBLE
                        mBinding.recyclerResult.visibility = View.INVISIBLE
                    }.build().showDialog()
            }

            R.id.ivClear -> mergeSearchBinding.etSearch.text?.clear()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun refreshHot() {
        hotProductAdapter?.notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun refreshAdapter() {
        searchHistoryAdapter.notifyDataSetChanged()
        searchResultAdapter.notifyDataSetChanged()
    }

    private fun getHistoryData(history: String): ArrayList<ShareProductData> {
        val list = arrayListOf<ShareProductData>()
        if (history.contains(",")) {
            val arr = history.split(",".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            for (name in arr) {
                val findBean = VAUSdkUtil.symbolList().find { it.symbol == name }
                findBean?.let {
                    list.add(it)
                }
            }
        }
        return list
    }

    private fun filterData(input: String): ArrayList<ShareProductData> {
        val list = arrayListOf<ShareProductData>()
        VAUSdkUtil.symbolList().forEach {
            if (it.isMatch(input)) {
                list.add(it)
            }
        }
        return list
    }

    private val itemClickListenerHot = HotProductAdapter.OnItemClickListener { _, position ->
        val bundle = Bundle()
        bundle.putString(Constants.PARAM_PRODUCT_NAME, mViewModel.hotList.elementAtOrNull(position)?.symbol.ifNull())
        openActivity(KLineActivity::class.java, bundle)
        val history = SpManager.getSearchHistoryKey("")
        val sop = mViewModel.hotList.getOrNull(position) ?: ShareProductData()
        if (history.contains(",")) {
            if (!history.contains(sop.symbol)) {
                SpManager.putSearchHistoryKey(history + sop.symbol + ",")
                searchHistorySymbolList.add(sop)
                searchHistoryAdapter.notifyDataSetChanged()
            }
        } else {
            SpManager.putSearchHistoryKey(history + sop.symbol + ",")
            searchHistorySymbolList.add(sop)
            searchHistoryAdapter.notifyDataSetChanged()
        }
    }

    private fun skipClick(position: Int, list: ArrayList<ShareProductData>) {
        if (VAUSdkUtil.symbolList().size == 0) return
        val paramData = list.getOrNull(position) ?: ShareProductData()
        val dataList: List<ShareProductData> = VAUSdkUtil.symbolList()
        for (data in dataList) {
            if ("2" == data.enable || "1" == data.enable) {
                if (position >= 0 && position < list.size && data.symbol.equals(
                        paramData.symbol,
                        ignoreCase = true
                    )
                ) {
                    mBinding.rlRecent.visibility = View.VISIBLE
                    mViewModel.addSearchRecord(
                        UserDataUtil.loginToken(),
                        paramData.symbol, paramData.symbol
                    )
                    val bundle = Bundle()
                    bundle.putString(Constants.PARAM_PRODUCT_NAME, data.symbol)
                    openActivity(KLineActivity::class.java, bundle)
                    return
                }
            }
        }
        val tempDataList: MutableList<ShareProductData> = ArrayList()
        for (data in dataList) {
            if ("2" == data.enable) {
                if (position >= 0 && position < list.size &&
                    data.symbol.lowercase().contains(paramData.symbol.lowercase())
                ) {
                    tempDataList.add(data)
                }
            }
        }
        if (tempDataList.size > 1) {
            val comparator =
                Comparator<ShareProductData> { o1, o2 -> o1.symbol.length - o2.symbol.length }
            Collections.sort(tempDataList, comparator)
            val bundle = Bundle()
            bundle.putString(Constants.PARAM_PRODUCT_NAME, tempDataList.firstOrNull()?.symbol ?: "")
            openActivity(KLineActivity::class.java, bundle)
        } else {
            if (tempDataList.size != 0) {
                val bundle = Bundle()
                bundle.putString(Constants.PARAM_PRODUCT_NAME, tempDataList.firstOrNull()?.symbol ?: "")
                openActivity(KLineActivity::class.java, bundle)
            }
        }
        if (position >= 0 && position < list.size && tempDataList.size != 0) {
            mBinding.rlRecent.visibility = View.VISIBLE
            mViewModel.addSearchRecord(
                UserDataUtil.loginToken(),
                paramData.symbol, paramData.symbol
            )
        }
    }

    /**
     * 隐藏键盘
     */
    private fun hideInput() {
        val imm = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        val v = window.peekDecorView()
        if (null != v) {
            imm.hideSoftInputFromWindow(v.windowToken, 0)
        }
    }

    override fun finish() {
        hideInput()
        super.finish()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    override fun onMsgEvent(eventTag: String) {
        if (NoticeConstants.OPTIONAL_PRODUCT_LIST_UPDATED == eventTag) {
            refreshAdapter()
        }
    }

    override fun onResume() {
        super.onResume()
        SDKIntervalUtil.instance.removeCallBack(this)
        SDKIntervalUtil.instance.addCallBack(this)
    }

    override fun onPause() {
        super.onPause()
        SDKIntervalUtil.instance.removeCallBack(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
        SDKIntervalUtil.instance.removeCallBack(this)
    }
}