package cn.com.vau.trade.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import cn.com.vau.R;
import cn.com.vau.trade.bean.SearchObjProducts;

/**
 * 搜索-历史记录Adapter
 * Created by zhy on 2018/10/30.
 */
public class SearchHistoryAdapter extends RecyclerView.Adapter<SearchHistoryAdapter.SearchHistoryViewHolder> {

    private Context mContext;
    private OnItemClickListener onItemClickListener;
    private List<SearchObjProducts> mList;
    private String mInputText = "";
    private Boolean mIsSearchResult = false;

    public SearchHistoryAdapter(Context mContext, List<SearchObjProducts> list, Boolean isSearchResult) {
        this.mContext = mContext;
        this.mList = list;
        mIsSearchResult = isSearchResult;
    }

    @Override
    public SearchHistoryViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_search_history, parent, false);
        return new SearchHistoryViewHolder(view);
    }

    @Override
    public void onBindViewHolder(final SearchHistoryViewHolder holder, @SuppressLint ("RecyclerView") final int position) {
        SearchObjProducts bean = mList.get(position);

        if(mIsSearchResult) {  //搜尋結果才有字體變色
            if(mList != null && mList.size() != 0 && !TextUtils.isEmpty(mInputText)) {
                int start = bean.getProdName().toUpperCase().indexOf(mInputText.toUpperCase());
                int end = start + mInputText.length();
                if(start != -1) {
                    SpannableStringBuilder span = new SpannableStringBuilder(bean.getProdName());
                    span.setSpan(new ForegroundColorSpan(mContext.getResources().getColor(R.color.cf44040)), start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    holder.tvProductName.setText(span);
                } else {
                    holder.tvProductName.setText(bean.getProdName());
                }
            }
        } else { //歷史紀錄
            holder.tvProductName.setText(bean.getProdName());
        }

        if(onItemClickListener != null) {
            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onItemClickListener.onItemClick(holder.itemView, position);
                }
            });

        }
    }

    @Override
    public int getItemCount() {
        return mList != null ? mList.size() : 0;
    }
    public void refreshList(List<SearchObjProducts> list, String inputText) {
        this.mList = list;
        mInputText = inputText;
        notifyDataSetChanged();
    }
    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(View view, int position);
    }

    public class SearchHistoryViewHolder extends RecyclerView.ViewHolder {
        TextView tvProductName;

        public SearchHistoryViewHolder(View itemView) {
            super(itemView);
            tvProductName = itemView.findViewById(R.id.tv_ProductName);
        }
    }
}
