package cn.com.vau.trade.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.*
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.util.AttrResourceUtil

/**
 * 订单 -- 待开仓 【跟随策略订单详情页】
 */
class StStrategyPendingOpenRecyclerAdapter(
    var mContext: Context,
    var dataList: ArrayList<ShareOrderData>
) : RecyclerView.Adapter<StStrategyPendingOpenRecyclerAdapter.ViewHolder>() {

    private val color_c0a1e1e1e_c0affffff by lazy { AttrResourceUtil.getColor(mContext, R.attr.color_c0a1e1e1e_c0affffff) }
    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(mContext, R.attr.color_c1e1e1e_cebffffff) }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {

        val holder = ViewHolder(
            LayoutInflater.from(mContext).inflate(R.layout.item_recycler_open_trades, parent, false)
        )

        holder.tvClose.setOnClickListener {
            val data = dataList.getOrNull(holder.bindingAdapterPosition) ?: return@setOnClickListener
            mOnItemClickListener?.onDeleteClick(data)
        }

        holder.ivKLine.setOnClickListener {
            mOnItemClickListener?.onStartKLine(holder.bindingAdapterPosition)
        }

        return holder
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        val orderBean = dataList.getOrNull(position) ?: return

        holder.tvEdit.visibility = View.GONE

        holder.tvVolTitle.text = "${mContext.getString(R.string.volume)} (${mContext.getString(R.string.lot)})"
        holder.tvVolume.text = "--"
        // 产品名称
        holder.tvProdName.text = orderBean.symbol
        holder.tvClose.text = mContext.getString(R.string.cancel)

        holder.tvOrderType.text = OrderUtil.getOrderTypeName(orderBean.cmd)

        if (OrderUtil.isBuyOfOrder(orderBean.cmd)) {
            holder.tvOrderType.setTextColor(ContextCompat.getColor(mContext, R.color.c00c79c))
            holder.tvOrderType.setBackgroundResource(R.drawable.shape_c1f00c79c_r100)
        } else {
            holder.tvOrderType.setTextColor(ContextCompat.getColor(mContext, R.color.ce35728))
            holder.tvOrderType.setBackgroundResource(R.drawable.shape_c1fe35728_r100)
        }

        holder.tvOrderId.text = "#${orderBean.order}"

        holder.tvOpenPrice.text = "--"
        holder.tvCurrentPrice.text = "--"
        holder.tvPnlTitle.text = "${mContext.getString(R.string.pnl)} (${UserDataUtil.currencyType()})"
        holder.tvPnl.setTextColor(color_c1e1e1e_cebffffff)
        holder.tvPnl.text = "--"

        holder.offView.setBackgroundColor(color_c0a1e1e1e_c0affffff)

    }

    override fun getItemCount(): Int = dataList.size

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val tvProdName: TextView = view.findViewById(R.id.tvProdName)
        val ivKLine: ImageView = view.findViewById(R.id.ivKLine)
        val tvPnlTitle: TextView = view.findViewById(R.id.tvPnlTitle)
        val tvOrderType: TextView = view.findViewById(R.id.tvOrderType)
        val tvOrderId: TextView = view.findViewById(R.id.tvOrderId)
        val tvPnl: TextView = view.findViewById(R.id.tvPnl)
        val tvVolTitle: TextView = view.findViewById(R.id.tvVolTitle)
        val tvVolume: TextView = view.findViewById(R.id.tvVolume)
        val tvOpenPriceTitle: TextView = view.findViewById(R.id.tvOpenPriceTitle)
        val tvOpenPrice: TextView = view.findViewById(R.id.tvOpenPrice)
        val tvCurrentPriceTitle: TextView = view.findViewById(R.id.tvCurrentPriceTitle)
        val tvCurrentPrice: TextView = view.findViewById(R.id.tvCurrentPrice)

        val tvEdit: TextView = view.findViewById(R.id.tvEdit)
        val tvClose: TextView = view.findViewById(R.id.tvClose)

        val offView: View = view.findViewById(R.id.offView)
    }

    private var mOnItemClickListener: OnItemClickListener? = null

    interface OnItemClickListener {
        fun onDeleteClick(orderData: ShareOrderData)
        fun onStartKLine(position: Int)
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        mOnItemClickListener = onItemClickListener
    }

}
