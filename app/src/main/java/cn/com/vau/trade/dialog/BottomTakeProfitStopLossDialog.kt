package cn.com.vau.trade.dialog

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.text.TextUtils
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import cn.com.vau.R
import cn.com.vau.common.performance.PerformManager
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.databinding.DialogTakeProfitStopLossBinding
import cn.com.vau.trade.perform.TpSlNextPerformance
import cn.com.vau.trade.perform.TpSlOrderInfoPerformance
import cn.com.vau.trade.perform.TpSlStopLossPerformance
import cn.com.vau.trade.perform.TpSlTakeProfitPerformance
import cn.com.vau.trade.perform.TpSlTracePerformance
import cn.com.vau.trade.viewmodel.TakeProfitStopLossViewModel
import cn.com.vau.util.KeyboardUtil
import cn.com.vau.util.LogUtil
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.widget.dialog.base.BaseMvvmBottomDialog
import cn.com.vau.util.widget.dialog.base.IBuilder
import cn.com.vau.util.widget.dialog.base.IDialog
import kotlin.math.pow

/**
 * Created by array on 2025/2/14 18:13
 * Desc: 设置止盈止损
 */
@SuppressLint("ViewConstructor")
class BottomTakeProfitStopLossDialog private constructor(
    activity: FragmentActivity,
    private val orderBean: ShareOrderData
) : BaseMvvmBottomDialog<DialogTakeProfitStopLossBinding, TakeProfitStopLossViewModel>(
    activity = activity,
    title = "${activity.getString(R.string.take_profit)}/${activity.getString(R.string.stop_loss)}",
    viewBinding = DialogTakeProfitStopLossBinding::inflate,
) {

    private val performManager: PerformManager by lazy {
        PerformManager(this)
    }

    /**订单信息*/
    private val orderInfoPerformance: TpSlOrderInfoPerformance by lazy {
        TpSlOrderInfoPerformance(activity, this, mContentBinding, mViewModel)
    }

    /**止盈item*/
    val takeProfitPerformance: TpSlTakeProfitPerformance by lazy {
        TpSlTakeProfitPerformance(activity, this, mContentBinding, mViewModel) {
            nextPerformance.tpSetEnable = it
            nextPerformance.updateNextStyle()
        }
    }

    /**止损item*/
    val stopLossPerformance: TpSlStopLossPerformance by lazy {
        TpSlStopLossPerformance(activity, this, mContentBinding, mViewModel) {
            nextPerformance.slSetEnable = it
            nextPerformance.updateNextStyle()
        }
    }

    /**next*/
    private val nextPerformance: TpSlNextPerformance by lazy {
        TpSlNextPerformance(activity, this, mContentBinding, mViewModel)
    }

    /**埋点*/
    val tracePerformance: TpSlTracePerformance by lazy {
        TpSlTracePerformance(mContentBinding)
    }

    override fun useEventBus(): Boolean {
        return true
    }

    override fun useSDKIntervalUtil(): Boolean {
        return true
    }

    override fun initViewModel(): TakeProfitStopLossViewModel {
        return ViewModelProvider(getMyViewModelStoreOwner())[TakeProfitStopLossViewModel::class.java]
    }

    override fun onCallback() {
        updateMainOrderViewData()

    }

    override fun setContentView() {
        super.setContentView()
        initParam()
        initData()
        initView()
        initListener()
    }

    fun initParam() {
        mViewModel.orderBean = orderBean
    }

    @SuppressLint("SetTextI18n")
    fun initView() {
        addPerformances()
    }

    private fun addPerformances() {
        performManager.addPerformance(orderInfoPerformance)
        performManager.addPerformance(takeProfitPerformance)
        performManager.addPerformance(stopLossPerformance)
        performManager.addPerformance(nextPerformance)
        performManager.addPerformance(tracePerformance)
    }

    fun initData() {
        val data = VAUSdkUtil.symbolList().firstOrNull {
            it.symbol == orderBean.symbol
        } ?: return
        mViewModel.productData = data
        mViewModel.digits = data.digits
        mViewModel.minProfit = "${1 / 10.toDouble().pow(mViewModel.digits.toDouble())}"
    }

    fun initListener() {
        mContentBinding.clContent.clickNoRepeat {
            KeyboardUtil.hideSoftInput(getHostWindow())
        }
        KeyboardUtil.registerSoftInputChangedListener(hostWindow) {
            if (it == 0) {
                mContentBinding.viewTakeProfit.clearEditFocus()
                mContentBinding.viewStopLoss.clearEditFocus()
            }
        }

    }

    /**
     * 更新主订单数据
     */
    @SuppressLint("SetTextI18n")
    fun updateMainOrderViewData() {
        val productData = VAUSdkUtil.symbolList().find {
            TextUtils.equals(it.symbol, orderBean.symbol)
        } ?: return
        mViewModel.productData = productData
        performManager.onCallback()
    }

    @Suppress("unused")
    class Builder(activity: Activity, private val orderBean: ShareOrderData) :
        IBuilder<DialogTakeProfitStopLossBinding, Builder>(activity) {

        override fun build(): BottomTakeProfitStopLossDialog {
            return super.build() as BottomTakeProfitStopLossDialog
        }

        override fun createDialog(context: Context): IDialog<DialogTakeProfitStopLossBinding> {
            return BottomTakeProfitStopLossDialog(context as FragmentActivity, orderBean)
        }

    }

}