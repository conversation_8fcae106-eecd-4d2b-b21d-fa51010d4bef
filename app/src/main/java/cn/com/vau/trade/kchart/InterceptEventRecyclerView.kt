package cn.com.vau.trade.kchart

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.recyclerview.widget.RecyclerView

class InterceptEventRecyclerView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : RecyclerView(context, attrs, defStyleAttr) {

    private var canScroll = true

    override fun dispatchTouchEvent(ev: MotionEvent?): <PERSON><PERSON>an {
//        if (ev?.action == MotionEvent.ACTION_DOWN) {
//            if (canScroll) {
////                LogUtil.i("wj", "dispatchTouchEvent: 拦截事件")
//                parent.requestDisallowInterceptTouchEvent(true)
//            } else {
////                LogUtil.i("wj", "dispatchTouchEvent: 不拦截事件")
//            }
//        } else if (ev?.action == MotionEvent.ACTION_UP) {
//            canScroll = this.canScrollHorizontally(-1)
////            LogUtil.i("wj", "dispatchTouchEvent: canScrollHorizontally(-1) = $canScroll")
//        }
        parent.requestDisallowInterceptTouchEvent(true)
        return super.dispatchTouchEvent(ev)
    }





}