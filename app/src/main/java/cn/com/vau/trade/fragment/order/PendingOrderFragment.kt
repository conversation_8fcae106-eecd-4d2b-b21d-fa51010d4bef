package cn.com.vau.trade.fragment.order

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.TextUtils
import android.view.*
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.SimpleItemAnimator
import cn.com.vau.R
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.constants.*
import cn.com.vau.common.constants.Constants.PARAM_PRODUCT_NAME
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmFragment
import cn.com.vau.common.performance.PerformManager
import cn.com.vau.common.utils.*
import cn.com.vau.common.view.*
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.databinding.FragmentPendingOrderBinding
import cn.com.vau.databinding.VsLayoutNoDataScrollBinding
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.profile.performance.TradePermissionPerformance
import cn.com.vau.trade.activity.*
import cn.com.vau.trade.adapter.PendingOrderAdapter
import cn.com.vau.trade.interfac.RefreshInterface
import cn.com.vau.trade.dialog.BottomModifyOrderDialog
import cn.com.vau.trade.model.PendingOrderViewModel
import cn.com.vau.trade.viewmodel.OrderViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.CenterActionWithIconDialog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.*

/**
 * 挂单
 */
class PendingOrderFragment : BaseMvvmFragment<FragmentPendingOrderBinding, PendingOrderViewModel>(),
    SDKIntervalCallback,RefreshInterface {

    private val icon2CbSquareUncheck by lazy {
        AttrResourceUtil.getDrawable(requireActivity(),R.attr.icon2CbSquareUncheck)
    }
    private var mAdapter: PendingOrderAdapter? = null

    var refreshFinished: (() -> Any?)? = null

    // 懒加载标志
    private var isDataLoad: Boolean = false

    private var refreshController : AdapterRefreshNotifyItemController?= null
    private var abKeyRefreshOpt : Boolean = false
    private val refreshPositionList = mutableListOf<Int>()
    private var mModifyDialog : BottomModifyOrderDialog? = null

    private var vs: VsLayoutNoDataScrollBinding? = null

    private var orderViewModel: OrderViewModel?=null

    private val performManager: PerformManager by lazy {
        PerformManager(this)
    }

    private val tradePermissionPerformance by lazy {
        TradePermissionPerformance(this)
    }

    companion object {
        fun newInstance(filterSymbol:String): PendingOrderFragment {
            val pendingOrderFragment = PendingOrderFragment()
            val bundle = Bundle()
            bundle.putString(PARAM_PRODUCT_NAME,filterSymbol)
            pendingOrderFragment.arguments = bundle
            return pendingOrderFragment
        }
    }

    override fun onCallback() {
        if (mViewModel.isDataLoading) return
        refreshAdapter(false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        mViewModel.productName = arguments?.getString(PARAM_PRODUCT_NAME,"")?:""
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        addPerformances()
        mBinding.mVsNoDataScroll.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                vs = VsLayoutNoDataScrollBinding.bind(inflated)
                vs?.mNoDataScrollView?.setHintMessage(getString(R.string.no_pending_orders))
            }
        })

        mAdapter = PendingOrderAdapter(requireContext(), mViewModel.orderList)
        mBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(requireActivity()).apply {
            this.setFrom("PendingOrderFragment")
        }
        mBinding.mRecyclerView.adapter = mAdapter
        mBinding.mRecyclerView.addItemDecoration(DividerItemDecoration(0.dp2px(), lastDividerSize = 12.dp2px()))
        mBinding.mRecyclerView.setEmptyView(mBinding.mVsNoDataScroll , { emptyViewVisible ->
            refreshNoDataView(emptyViewVisible)
        })

        if (abKeyRefreshOpt) {
            recyclerViewOpt()
        }
    }

    private fun addPerformances() {
        performManager.addPerformance(tradePermissionPerformance)
    }

    private fun createOrderObserver() {
        orderViewModel?.productDataChangeLieData?.observe(this) {
            refreshFilterSymbol(it.symbol)
        }
    }

    fun setOrderViewModel(orderViewModel: OrderViewModel) {
        this.orderViewModel = orderViewModel
        createOrderObserver()
    }

    private fun refreshNoDataView(emptyViewVisible: Boolean) {
        if (InitHelper.isNotSuccess()) return
        // demo账户
        if ("3" == UserDataUtil.mt4State()) {
            return
        }

        // 未显示空布局 or 有数据
        if (emptyViewVisible.not()) {
            return
        }

        val equity = if (UserDataUtil.isStLogin()) {
            VAUSdkUtil.stShareAccountBean().equity
        } else {
            VAUSdkUtil.shareAccountBean().equity
        }

        if (equity <= 0) {
            val hintTitle = "${getString(R.string.available_funds)}: ${equity.numCurrencyFormat()} ${UserDataUtil.currencyType()}"
            vs?.mNoDataScrollView?.showHintTitle(hintTitle)
            vs?.mNoDataScrollView?.setHintMessage(getString(R.string.deposit_funds_to_or_demo_trading))
            vs?.mNoDataScrollView?.setBottomBtnText(getString(R.string.deposit))
            vs?.mNoDataScrollView?.setBottomBtnViewClickListener {
                NewHtmlActivity.openActivity(requireActivity(), url = UrlConstants.HTML_FUND_DEPOSIT)
            }
        } else {
            vs?.mNoDataScrollView?.showHintTitle("")
            vs?.mNoDataScrollView?.setHintMessage(getString(R.string.no_pending_orders))
            vs?.mNoDataScrollView?.hideBtnTextView()
        }
    }

    //recyclerView优化
    private fun recyclerViewOpt() {
        mBinding.mRecyclerView.setHasFixedSize(true)
        //避免滑动动画会有延迟
        (mBinding.mRecyclerView.itemAnimator as? SimpleItemAnimator)?.supportsChangeAnimations = false
        refreshController = AdapterRefreshNotifyItemController(mBinding.mRecyclerView, mAdapter)
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.pendingOrderListLiveData.observe(this) {
            refreshAdapter(true)
            refreshFinished?.invoke()
        }
        mViewModel.pendingOrderCancelLiveData.observe(this) {
            if ("200" != it) {
                showTokenErrorDialog(it)
                return@observe
            }
            showDeleteSuccessDialog()
        }
        mViewModel.updatePendingOrderSuccessLiveData.observe(this) {
            dismissModifyDialog()
            showDealSuccessToast()
        }

        mViewModel.hintDataDialogLiveData.observe(this) {
            CenterActionDialog.Builder(requireActivity())
                .setContent(it)
                .setSingleButton()
                .build()
                .showDialog()
        }

        mViewModel.tokenErrorLiveData.observe(this) {
            showTokenErrorDialog(it)
        }
    }

    fun refreshFilterSymbol(filterSymbol: String) {
        mViewModel.productName = filterSymbol
        if (mViewModel.isHideOthers) {
            mViewModel.hideOtherSymbol()
            refreshAdapter(true)
        }
    }

    private fun hideOtherSymbol() {
        mViewModel.isHideOthers = mViewModel.isHideOthers.not()
        mViewModel.hideOtherSymbol()
        mBinding.ivHideOther.setImageResource(if (mViewModel.isHideOthers) R.drawable.icon2_cb_square else icon2CbSquareUncheck)
        refreshAdapter(true)
    }

    /**
     * 改单成功弹窗
     */
    private fun showDealSuccessToast() {
        val dialogTitle = getString(R.string.order_modified_successfully)
        ToastUtil.showToast(dialogTitle)
    }

    override fun initListener() {
        super.initListener()

        mBinding.ivHideOther.clickNoRepeat {
            hideOtherSymbol()
        }
        mBinding.tvHideOther.clickNoRepeat {
            hideOtherSymbol()
        }

        mAdapter?.setOnItemClickListener(object : PendingOrderAdapter.OnItemClickListener {
            override fun onStartKLine(position: Int) {
                openActivity(KLineActivity::class.java, Bundle().apply {
                    putString(
                        Constants.PARAM_PRODUCT_NAME,
                        mViewModel.orderList.getOrNull(position)?.symbol.ifNull()
                    )
                })
            }

            override fun onOrderIdClick(orderId: String) {
                // 复制订单号
                orderId.copyText(getString(R.string.number_copied))
            }

            override fun onModifyClick(position: Int) {
                showModifyDialog(mViewModel.orderList.getOrNull(position) ?: ShareOrderData())
                SensorsDataUtil.track(SensorsConstant.V3510.ORDER_CARD_MODIFY_BTN_CLICK)
            }

            override fun onDeleteClick(position: Int) {
                CenterActionDialog.Builder(requireActivity())
                    .setTitle(getString(R.string.confirmation))
                    .setContent(getString(R.string.are_you_sure_cancel_order_x,"#${mViewModel.orderList.getOrNull(position)?.order}"))
                    .setEndText(getString(R.string.yes_confirm))
                    .setOnEndListener {
                        mViewModel.pendingOrderCancel(position)
                    }.build()
                    .showDialog()
            }

        })

    }

    /**
     * 展示挂单列表页面
     */
    @SuppressLint("NotifyDataSetChanged")
    fun refreshAdapter(isFreshAll: Boolean) {

        lifecycleScope.launch(Dispatchers.Default) {
            //TODO 子线程和主线程操作同一个list  java.util.ConcurrentModificationException
            mViewModel.orderList.forEachIndexed { _, orderData ->
                VAUSdkUtil.symbolList().firstOrNull {
                    it.symbol == orderData.symbol
                }?.let {
                    orderData.ask = it.ask
                    orderData.bid = it.bid
                    orderData.digits = it.digits
                    orderData.roseType = if (it.roseUI == Constants.DOUBLE_LINE || it.rose == 0f) -1 else (if (it.rose > 0) 0 else 1)
                    orderData.currentPriceUI =
                        "${if (OrderUtil.isBuyOfOrder(orderData.cmd)) orderData.ask else orderData.bid}".formatProductPrice2(
                            orderData.digits
                        )
                    orderData.gapPriceUI = "${if (OrderUtil.isBuyOfOrder(orderData.cmd)) orderData.ask else orderData.bid}".mathSub(orderData.openPrice).replace("-", "").numFormat2(orderData.digits)
                    // 交易金额
                    if (PositionDataUtil.isAmountOpen()) {
                        orderData.volumeAmount = OrderUtil.getAmountFromVolume(
                            it,
                            orderData.volume.ifNull(),
                            orderData.cmd,
                            if ("6" == orderData.cmd || "7" == orderData.cmd)
                                orderData.stopLimitPrice
                            else
                                orderData.openPrice
                        )
                    }
                }
            }
            withContext(Dispatchers.Main) {
                if (isFreshAll) {
                    mAdapter?.notifyDataSetChanged()
                } else {
                    if (abKeyRefreshOpt && refreshController != null) {
                        refreshController?.refresh(recordRefreshPositionList())
                    }else{
                        for (index in mViewModel.orderList.indices) {
                            if (index < (mAdapter?.orderList?.size ?: 0)){
                                mAdapter?.notifyItemChanged(index, Constants.DEFAULT_PAYLOAD_KEY)
                            }
                        }
                    }
                }
            }
        }

    }

    /**
     * 显示改单弹窗
     */
    private fun showModifyDialog(orderBean: ShareOrderData) {
        mModifyDialog =  BottomModifyOrderDialog.Builder(requireActivity(), orderBean)
            .setAutoFocusEditText(false)
            .setViewMode(true)
            .moveUpToKeyboard(false)
            .setOnNextClick {
                mViewModel.orderData = orderBean
                // 提交改单
                mViewModel.tradeModifyOrder(it)
            }
            .build()
        mModifyDialog?.showDialog()
    }

    private fun dismissModifyDialog() {
        mModifyDialog?.dismissDialog()
    }

    //搜集这次刷新的position
    private fun recordRefreshPositionList() :MutableList<Int>{
        refreshPositionList.clear()
        for (index in mViewModel.orderList.indices) {
            if (index < (mAdapter?.orderList?.size ?: 0)){
                refreshPositionList.add(index)
            }
        }
        return refreshPositionList
    }

    private fun showDeleteSuccessDialog() {
        CenterActionWithIconDialog.Builder(requireActivity())
            // 挂单删除成功
            .setTitle(getString(R.string.delete_confirm))
            .setLottieIcon(R.raw.lottie_dialog_ok)
            .setSingleButton(true)
            .setSingleButtonText(getString(R.string.ok))
            .build()
            .showDialog()
    }

    private fun showTokenErrorDialog(msg: String?) {
        CenterActionDialog.Builder(requireActivity())
            .setContent(msg.ifNull())
            .setSingleButton(true)
            .setOnDismissListener {
                // 退出登录
                EventBus.getDefault().post(NoticeConstants.LOGOUT_ACCOUNT)
            }.build()
            .showDialog()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {

        if (!UserDataUtil.isLogin()) return

        if (tag == NoticeConstants.WS.CHANGE_OF_PENDING_ORDER ||
            tag == NoticeConstants.Init.DATA_SUCCESS_ORDER ||
            tag == NoticeConstants.SWITCH_ACCOUNT ||
            TextUtils.equals(tag, "true")
        ) {
            mViewModel.getPendingOrderList()
        }

    }

    override fun onResume() {
        super.onResume()
        if (!isDataLoad) {
            mViewModel.getPendingOrderList()
            isDataLoad = true
        }
        SDKIntervalUtil.instance.removeCallBack(this)
        SDKIntervalUtil.instance.addCallBack(this)
    }

    override fun onPause() {
        super.onPause()
        SDKIntervalUtil.instance.removeCallBack(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    override fun refreshData() {
        mViewModel.getPendingOrderList()
    }

}
