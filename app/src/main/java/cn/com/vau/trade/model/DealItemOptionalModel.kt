package cn.com.vau.trade.model

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.trade.OptionalBean
import cn.com.vau.data.trade.StOptionalBean
import cn.com.vau.trade.presenter.DealOptionalContract
import io.reactivex.disposables.Disposable

/**
 * Created by roy on 2018/11/6.
 * 自选管理
 */
class DealItemOptionalModel : DealOptionalContract.Model {

    override fun prodListApi(map: HashMap<String, Any>, baseObserver: BaseObserver<OptionalBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().prodListApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun accountProductMyApi(map: HashMap<String, Any>, baseObserver: BaseObserver<StOptionalBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getStHttpService().accountProductMyApi(map), baseObserver)
        return baseObserver.disposable
    }

}