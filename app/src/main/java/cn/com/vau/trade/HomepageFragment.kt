package cn.com.vau.trade

import HomepageContract
import android.annotation.SuppressLint
import android.os.Bundle
import android.view.*
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.*
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.application.*
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.base.fragment.BaseFrameFragment
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.*
import cn.com.vau.common.view.*
import cn.com.vau.common.vm.MainViewModel
import cn.com.vau.data.account.*
import cn.com.vau.data.enums.*
import cn.com.vau.data.init.ShareAccountInfoData
import cn.com.vau.databinding.*
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.page.login.activity.SignUpActivity
import cn.com.vau.page.notice.activity.NoticeActivity
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.page.user.loginPwd.LoginPwdActivity
import cn.com.vau.trade.activity.SearchProductActivity
import cn.com.vau.trade.fragment.deal.*
import cn.com.vau.trade.model.HomepageModel
import cn.com.vau.trade.presenter.HomepagePresenter
import cn.com.vau.trade.view.TradeSwitchModePopup
import cn.com.vau.util.*
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.opt.PerfTraceUtil
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.dialog.CenterActionWithIconDialog
import cn.com.vau.util.widget.dialog.base.BottomDialog
import com.bumptech.glide.request.RequestOptions
import com.google.android.material.appbar.AppBarLayout
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.enums.*
import com.youth.banner.adapter.BannerImageAdapter
import com.youth.banner.holder.BannerImageHolder
import kotlinx.coroutines.*
import org.greenrobot.eventbus.*
import org.json.JSONObject
import kotlin.math.abs

/**
 * Created by roy on 2018/10/16.
 * 交易
 */
class HomepageFragment : BaseFrameFragment<HomepagePresenter, HomepageModel>(), HomepageContract.View,
    SDKIntervalCallback {

    private val mBinding: FragmentHomeBinding by lazy {
        FragmentHomeBinding.inflate(
            layoutInflater
        )
    }
    private val shareAccountBean: ShareAccountInfoData by lazy { VAUSdkUtil.shareAccountBean() }
    private val viewModel: MainViewModel by activityViewModels()
    private var isViewCreated: Boolean = false
    private var isUIVisible: Boolean = false

    private val titleList = mutableListOf<String>()
    private var fragmentList = mutableListOf<Fragment>()
    private var loginStatus: EnumLoginStatus? = null
    private var userGroupChange = true
    private var willToTabGroupName = ""

    private val color_c1e1e1e_cebffffff by lazy {
        AttrResourceUtil.getColor(
            requireContext(),
            R.attr.color_c1e1e1e_cebffffff
        )
    }
    private val ce35728 by lazy { ContextCompat.getColor(requireContext(), R.color.ce35728) }
    private val c00c79c by lazy { ContextCompat.getColor(requireContext(), R.color.c00c79c) }

    private val marginLevelView by lazy { marginLevelPopup.getContentViewBinding() }
    private val marginLevelPopup by lazy {
        BottomDialog.Builder<LayoutBottomTipMarginLevelBinding>(requireActivity())
            .setTitle(getString(R.string.margin_level))
            .setViewBinding(LayoutBottomTipMarginLevelBinding::inflate)
            .build()
    }
    private var mHintMaintenanceView: HintMaintenanceView? = null
    private var mAccountKYCGuideView: AccountKYCVerifyView? = null

    //连接状态监听
    private val stateChangeCallback by lazy {
        object : DefaultUiLinkStateCallback(mBinding.tvConnecting, this){
            override fun onProductListError() {
                super.onProductListError()
                uiStateProductListError()
            }

            override fun onProductListSuccess() {
                showProductList()
            }
        }
    }

    private fun showProductList() {
        mBinding.fragmentDeal.mVsNoDataScroll.isVisible = VAUSdkUtil.symbolList().isEmpty()
        mBinding.fragmentDeal.mVsNoDataScroll2.isVisible = false
        if (userGroupChange) {
            userGroupChange = false
            tableUpdate()
            setLoginStatus()
            renderLayout()
        }
    }

    private fun uiStateProductListError() {
        mBinding.fragmentDeal.mVsNoDataScroll2.isVisible = true
    }

    override fun onCallback() {
        if (InitHelper.isNotSuccess()) return
        if (UserDataUtil.isLiveAccount() || UserDataUtil.isDemoAccount() || UserDataUtil.isLiveVirtualAccount()) {
            context?.let {
                // 只刷新需要刷新的布局
                val enableColorRes: Int = getEnableColor()
                refreshCollapsePnl(enableColorRes)
                refreshNotDepositEquity()
                refreshLoggedInBoard(enableColorRes)
                refreshOpenGuideBoard(enableColorRes)
                refreshVirtualBoard()
                // 行情维护
                showMaintenance()
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        isViewCreated = true
        lazyInitView()
        return mBinding.root
    }


    override fun initParam() {
        super.initParam()
        EventBus.getDefault().register(this)
        setLoginStatus()
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        super.initView()
        if (activity != null) {
            PerfTraceUtil.stopTraceOnFirstFrame(
                mBinding.root,
                requireActivity().intent,
                PerfTraceUtil.StartTrace.Perf_v6_Start_MainFirst_TradeFirst,
                PerfTraceUtil.StartTrace.Perf_v6_Start_AppCreate_TradeFirst
            )
        }
        if (VauApplication.abOptNetParallel) {
            LinkStateManager.registerCallback(stateChangeCallback)
        }
        mBinding.ivRedDot.isVisible = SpManager.getRedPointState(false)
        mBinding.fragmentDeal.dealProdAttrType.tvRose.text = getString(R.string.percent_change)
        initSortIcon()

        val selectedMode = SpManager.getTradeSwitchMode()
        when (selectedMode) {
            0 -> {
                mBinding.fragmentDeal.dealProdAttrType.run {
                    tvSell.isVisible = false
                    tvBuy.isVisible = false
                    tvPrice.isVisible = true
                }
            }

            1 -> {
                mBinding.fragmentDeal.dealProdAttrType.run {
                    tvSell.isVisible = true
                    tvBuy.isVisible = true
                    tvPrice.isVisible = false
                }
            }
        }

        mBinding.tvCollapsePnl.text = "${getString(R.string.floating_pnl)}: "

        marginLevelView.tvWarningContent.text =
            getString(R.string.the_accounts_available_to_you_some_positions)

        if (VauApplication.abOptNetParallel) {
            //判断WebSocket是否链接成功
            if (!LinkStateManager.isStateLinkSuccess()) {
                //当前没有连接成功   首页显示链接中
                mBinding.tvConnecting.text = "${getString(R.string.connecting)}..."
                mBinding.tvConnecting.visibility = View.VISIBLE
            }
        } else {
            //判断WebSocket是否链接成功（原逻辑：产品列表需要请求成功后才会链接webSocket，WebSocket链接成功后才是第三步）
            if (InitHelper.stepNum() == 1 || InitHelper.stepNum() == 2) {
                //WebSocket还没有成功
                mBinding.tvConnecting.text = "${getString(R.string.connecting)}..."
                mBinding.tvConnecting.visibility = View.VISIBLE
            }
        }

        //系统维护布局
        mBinding.mViewStubMarketMaintenance.setOnInflateListener { _, inflated ->
            if (inflated is HintMaintenanceView) {
                mHintMaintenanceView = inflated
            }
        }

        initTabLayout()
        mBinding.mBanner.setAdapter(object : BannerImageAdapter<String>(emptyList()) {
            override fun onBindView(
                holder: BannerImageHolder?,
                data: String?,
                position: Int,
                size: Int
            ) {
                val options = RequestOptions()
                    .placeholder(R.drawable.shape_placeholder)
                    .error(R.drawable.shape_placeholder)
                ImageLoaderUtil.loadImageWithOption(
                    requireContext(),
                    data,
                    holder?.imageView,
                    options
                )
            }
        }).setScrollTime(1000)
            .addBannerLifecycleObserver(this)

        val params = mBinding.collapsingToolbarLayout.layoutParams as AppBarLayout.LayoutParams
        params.scrollFlags =
            (AppBarLayout.LayoutParams.SCROLL_FLAG_SCROLL or AppBarLayout.LayoutParams.SCROLL_FLAG_EXIT_UNTIL_COLLAPSED)
        mBinding.collapsingToolbarLayout.layoutParams = params

        // 新手引导
        initTradesGuide()

        // 神策自定义埋点(v3500)
        // App_Tab 页面浏览 -> app内五个tab页面加载完成时触发
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.TAB_NAME, "Trades") // Tab 名称
        SensorsDataUtil.track(SensorsConstant.V3500.APP_TAB_PAGE_VIEW, properties)
    }

    override fun initData() {
        super.initData()
        if (UserDataUtil.isLogin()) {
            if (SpManager.isV1V2()) {
                mPresenter.userQueryUserLevel()
            } else {
                mPresenter.accountOpeningGuide()
            }
        }
    }

    @SuppressLint("ObsoleteSdkInt")
    private fun lazyInitView() {

        if (!isViewCreated || !isUIVisible) return

        isViewCreated = false
        isUIVisible = false

        renderLayout()
    }

    override fun initListener() {
        super.initListener()
        mBinding.ivProductSearch.setOnClickListener(this)
        mBinding.ivMessage.setOnClickListener(this)
        mBinding.ivBannerClose.setOnClickListener(this)
        mBinding.tvAccountStatus.setOnClickListener(this)
        mBinding.tvAccountId.setOnClickListener(this)
        mBinding.ivArrow.setOnClickListener(this)
        mBinding.tvCollapseAccountId.setOnClickListener(this)
        mBinding.ivCollapseArrow.setOnClickListener(this)
        mBinding.fragmentDeal.dealProdAttrType.tvSymbol.setOnClickListener(this)
        mBinding.fragmentDeal.dealProdAttrType.tvRose.clickNoRepeat(500) {
            TradeSortUtil.nextSort { sortMode ->
                initSortIcon(sortMode)
            }
        }

        mBinding.fragmentDeal.mVsNoDataScroll.setOnInflateListener(object :
            ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutNoDataScrollBinding.bind(inflated)
                vs.mNoDataScrollView.setBackgroundColor(
                    AttrResourceUtil.getColor(
                        requireContext(),
                        R.attr.mainLayoutBg
                    )
                )
                vs.mNoDataScrollView.setHintMessage(getString(R.string.no_quotes_for_this_account))
            }
        })

        mBinding.fragmentDeal.mVsNoDataScroll2.setOnInflateListener(object :
            ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutNoDataScrollBinding.bind(inflated)
                vs.mNoDataScrollView.setBackgroundColor(
                    AttrResourceUtil.getColor(
                        requireContext(),
                        R.attr.mainLayoutBg
                    )
                )
                vs.mNoDataScrollView.setHintMessage(getString(R.string.something_went_wrong_try_again))
                vs.mNoDataScrollView.setBottomBtnText(getString(R.string.try_again))
                vs.mNoDataScrollView.setBottomBtnViewClickListener {
                    if (VauApplication.abOptNetParallel) {
                        InitHelper.start()
                    } else {
                        // 金玉云：初始之务成，或败，若见复始之钮而能击之，则示无数据，需重行初始之请。其余情状，初始之务方行，毋须复始。
                        // 金玉说（初始化都完成了，但是列表还没有数据，肯定有问题，因此重新初始化）
                        //但是我有疑问：失败了内部会自动重新请求，没必要需要重新初始化请求
                        if (InitHelper.stepNum() == -1 || InitHelper.stepNum() == 0) {
                            InitHelper.start()
                        }
                    }
                }
            }
        })

        mBinding.viewStubKycGuide?.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                mAccountKYCGuideView = inflated as? AccountKYCVerifyView
                mAccountKYCGuideView?.setOnVerifyCallback {
                    (activity as? AppCompatActivity)?.let {
                        KycVerifyHelper.showKycDialog(
                            it,
                            mapOf(Constants.GoldParam.CODE to Constants.GoldParam.CODE_KYC)
                        )
                        mPresenter.sensorUpgradeClick("Verify")
                    }
                }
            }
        })

        mBinding.mBanner.addOnPageChangeListener(object : CustomViewPagerOnPageChangeListener() {
            override fun onPageSelected(position: Int) {
                mBinding.indicator.changeIndicator(position)
            }
        })

        mBinding.mBanner.setOnBannerListener { _, bannerPosition ->
            val bannerBean = mPresenter.bannerData?.eventsList?.elementAtOrNull(bannerPosition)
            val pushBean = bannerBean?.appJumpDefModel
            VAUStartUtil.openActivity(requireActivity(), pushBean)

            // 神策自定义埋点(v3500)
            // App_交易页Banner点击 -> 点击app交易页Banner位时触发
            val properties = JSONObject()
            properties.put(SensorsConstant.Key.MKT_ID, bannerBean?.eventId.ifNull()) // 素材id
            properties.put(SensorsConstant.Key.MKT_NAME, "") // 素材名称
            properties.put(SensorsConstant.Key.MKT_RANK, bannerPosition + 1) // 素材排序
            properties.put(SensorsConstant.Key.TARGET_URL, pushBean?.urls?.def.ifNull()) // 跳转链接
            SensorsDataUtil.track(SensorsConstant.V3500.APP_TRADES_BANNER_CLICK, properties)
        }

        mBinding.appBarLayout.addOnOffsetChangedListener { appbar, offset ->
//                if (offset == 0) {  // Expanded: 展开完毕
//                } else if (abs(offset) >= appbar.totalScrollRange) {    // Collapsed: 收缩完毕
//                } else {            // Idle: 中间
//                }
            val needShow = (abs(offset) >= appbar.totalScrollRange)
            if (needShow) {
                if (mBinding.clCollapsingAccountInfo.visibility == View.INVISIBLE && Constants.MARKET_MAINTAINING.not()
                    && (loginStatus == EnumLoginStatus.LOGIN_DEMO || loginStatus == EnumLoginStatus.LOGIN_LIVE)
                ) {
                    mBinding.clCollapsingAccountInfo.visibility = View.VISIBLE
                }
            } else {
                if (mBinding.clCollapsingAccountInfo.visibility == View.VISIBLE) {
                    mBinding.clCollapsingAccountInfo.visibility = View.INVISIBLE
                }
            }
        }

        viewModel.isDeposited.observe(this) {
            mPresenter.isDeposited = it
            assetsCardUpdate()
        }

        viewModel.bannerData.observe(this) {
            mPresenter.bannerData = it
            if (mPresenter.bannerData == null) {
                hideOperationBanner()
            } else {
                showOperationBanner()
            }
        }

        viewModel.loginStatus.observe(this) {
            when (it) {
                Constants.TOKEN_ERROR -> logout()
            }
        }
    }

    private fun initTabLayout() {
        mBinding.fragmentDeal.apply {
            val shareGoodList = VAUSdkUtil.shareGoodList()

            if (fragmentList.isEmpty()) {
                fragmentList.add(DealItemOptionalFragment())
                titleList.add(getString(R.string.watchlist))
            }
            if (fragmentList.size <= 1) {
                shareGoodList.forEachIndexed { index, (groupName) ->
                    fragmentList.add(DealItemFragment.newInstance(index))
                    titleList.add(
                        VAUSdkUtil.getGroupNameLanguage(requireContext(), groupName.ifNull())
                    )
                }
            }

            mViewPager.init(fragmentList, titleList, childFragmentManager, this@HomepageFragment)
            mTabLayout.setVp(mViewPager, titleList, TabType.TEXT_SCALE)
            // Crypto检测Open逻辑
            if (hasCrypto() && VAUSdkUtil.socketCurrentData != -1L) {
                val isWeekend = CalendarUtil.getInstance().isWeekend(VAUSdkUtil.socketCurrentData)
                EventBus.getDefault()
                    .post(if (isWeekend) NoticeConstants.Quotes.TRADE_SERVER_TIME_IS_WEEKEND else NoticeConstants.Quotes.TRADE_SERVER_TIME_IS_NOT_WEEKEND)
            }
        }
    }

    private fun setLoginStatus() {
        loginStatus = if (UserDataUtil.isLogin()) {
            if (UserDataUtil.isLiveVirtualAccount()) {
                EnumLoginStatus.LOGIN_VIRTUAL_ACCOUNT
            } else if (UserDataUtil.isDemoAccount()) {
                EnumLoginStatus.LOGIN_DEMO
            } else {
                EnumLoginStatus.LOGIN_LIVE
            }
        } else {
            if (SpManager.getExitStatus(false)) {
                EnumLoginStatus.LOGGED_IN
            } else {
                EnumLoginStatus.NEVER
            }
        }
    }

    private fun renderLayout() {
        // 展示顶部栏
        showAccountInfo()
        // 展示资产卡片
        showAssetsCard()
    }

    // 广告位
    private fun showOperationBanner() {
        val bannerUrlList = mutableListOf<String>()
        if (mPresenter.bannerData?.eventsList?.isNotEmpty() == true) {
            mPresenter.bannerData?.eventsList?.indices?.mapNotNullTo(bannerUrlList) {
                mPresenter.bannerData?.eventsList?.elementAtOrNull(it)?.imgUrl
            }
            if (bannerUrlList.isNotEmpty()) {
                mBinding.ivBannerClose.isVisible = "true" == mPresenter.bannerData?.showClose
                mBinding.indicator.isVisible = bannerUrlList.size > 1
                mBinding.indicator.initIndicatorCount(bannerUrlList.size)
                val lp = mBinding.topInfo.layoutParams as? ViewGroup.MarginLayoutParams
                lp?.bottomMargin = 16.dp2px()
                mBinding.topInfo.layoutParams = lp
                mBinding.mTradeGuideView.showBannerEffectLoggedInLayout(true)
                mBinding.mBanner.isVisible = true
                mBinding.mBanner.setDatas(bannerUrlList)
                mBinding.mBanner.start()
            } else {
                hideOperationBanner()
            }
        } else {
            hideOperationBanner()
        }
    }

    override fun hideOperationBanner() {
        mBinding.mBanner.stop()
        mBinding.ivBannerClose.isVisible = false
        mBinding.mBanner.isVisible = false
        mBinding.indicator.isVisible = false
        val lp = mBinding.topInfo.layoutParams as? ViewGroup.MarginLayoutParams
        lp?.bottomMargin = 8.dp2px()
        mBinding.topInfo.layoutParams = lp
        mBinding.mTradeGuideView.showBannerEffectLoggedInLayout(false)
    }

    override fun kycGuideInfo(kycInfo: KycVerifyLevelObj?) {
        val visible = kycInfo?.disable == false
        UserDataUtil.setKycLevel(kycInfo?.level.ifNull(0).toString())
        mBinding.viewStubKycGuide.isVisible = visible
        if (visible) {
            mAccountKYCGuideView?.setKycVerifyState(kycInfo.guidance)
        }
    }

    override fun goLiveAccount(isCompleteVirtual: Boolean) {
        (activity as? AppCompatActivity)?.let {
            KycVerifyHelper.showKycDialog(
                it,
                hashMapOf<String, Any>().apply {
                    put(Constants.GoldParam.CODE, if (isCompleteVirtual) Constants.GoldParam.CODE_COMPLETE_INFO else Constants.GoldParam.CODE_OPEN_ACCOUNT)
                }
            )
        }
    }

    override fun virtualAccountStateError() {
        UserDataUtil.exitVirtualAccount()
        activity?.let {
            CenterActionWithIconDialog.Builder(requireActivity())
                .setLottieIcon(R.raw.lottie_dialog_ok)
                .setTitle(getString(R.string.your_new_trading_is_ready))
                .setContent(getString(R.string.your_trading_account_to_head_list_page))
                .setSingleButton(true)
                .setSingleButtonText(getString(R.string.view_my_accounts))
                .setDismissOnBackPressed(false)
                .setOnSingleButtonListener {
                    openActivity(AccountManagerActivity::class.java, bundleOf(Constants.IS_FROM to 1))
                }.build().showDialog()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun showAccountInfo() {
        val isLogin =
            loginStatus == EnumLoginStatus.LOGIN_DEMO || loginStatus == EnumLoginStatus.LOGIN_LIVE || loginStatus == EnumLoginStatus.LOGIN_VIRTUAL_ACCOUNT

        mBinding.tvAccountId.isVisible = isLogin
        mBinding.ivProductSearch.isVisible = !Constants.MARKET_MAINTAINING

        when (loginStatus) {
            EnumLoginStatus.NEVER -> {
                mBinding.tvAccountStatus.text = getString(R.string.demo)
                mBinding.viewStubKycGuide.isVisible = false
            }

            EnumLoginStatus.LOGGED_IN -> {
                mBinding.tvAccountStatus.text = getString(R.string.log_in)
                mBinding.viewStubKycGuide.isVisible = false
            }

            EnumLoginStatus.LOGIN_VIRTUAL_ACCOUNT -> {
                // 检查虚拟账户状态
                mPresenter.queryVirtualAccount(true)
                mBinding.tvAccountStatus.text = getString(R.string.live)
                mBinding.tvAccountId.isVisible = false
            }

            EnumLoginStatus.LOGIN_DEMO, EnumLoginStatus.LOGIN_LIVE -> {
                // 检查主交易账户审核状态
                if (loginStatus == EnumLoginStatus.LOGIN_DEMO) {
                    mPresenter.queryFirstAccountAuditStatus()
                }
                mBinding.tvAccountStatus.text =
                    getString(if (loginStatus == EnumLoginStatus.LOGIN_DEMO) R.string.demo else R.string.live)
                mBinding.tvAccountId.text = UserDataUtil.accountCd()
                mBinding.tvCollapseAccountType.text =
                    getString(if (loginStatus == EnumLoginStatus.LOGIN_DEMO) R.string.demo else R.string.live)
                mBinding.tvCollapseAccountType.background =
                    ContextCompat.getDrawable(
                        requireContext(),
                        if (loginStatus == EnumLoginStatus.LOGIN_DEMO) R.drawable.shape_ce35728_r100
                        else R.drawable.shape_c00c79c_r100
                    )
                mBinding.tvCollapseAccountId.text = UserDataUtil.accountCd()
                // 显示PnL
                refreshCollapsePnl(getEnableColor())
            }

            else -> {}
        }
    }

    private fun showAssetsCard() {
        // 账户状态变化交互 交给封装组件去处理
        mBinding.mTradeGuideView
            .setOnRegisterCallback {
                openActivity(SignUpActivity::class.java)
                LogEventUtil.setLogEvent(BuryPointConstant.V344.GENERAL_TRADES_TOP_SIGNUP_LOGIN_BUTTON_CLICK)
            }
            .setOnLoginCallback {
                openActivity(LoginPwdActivity::class.java)
                LogEventUtil.setLogEvent(BuryPointConstant.V344.GENERAL_TRADES_TOP_LOGIN_BUTTON_CLICK)
            }
            .setOnGoLiveCallback {
                val isKYCProcess = SpManager.isV1V2()
                // KYC流程
                if (isKYCProcess) {
                    // -------查接口是去补全还是去开通主交易账户-------
                    // 获取虚拟账户virtualId
                    mPresenter.queryVirtualAccount(false)
                    mPresenter.sensorUpgradeClick("Go live")
                } else {
                    mPresenter.queryMT4AccountState(EnumLinkSkipState.DEFAULT)
                }
            }
            .setOnDepositCallback {
                NewHtmlActivity.openActivity(requireContext(), url = UrlConstants.HTML_FUND_DEPOSIT)
                if (SpManager.isV1V2()) {
                    mPresenter.sensorUpgradeClick("Deposit")
                }
            }
            .setOnMarginLevelClick {
                marginLevelView.tvLiquidationContent.text = getString(
                    R.string.your_positions_will_x_starting_opened_positions,
                    "${VAUSdkUtil.shareAccountBean().marginStopOut}%"
                )
                marginLevelView.tvPercentWarning.text =
                    "${VAUSdkUtil.shareAccountBean().marginCall}%"
                marginLevelView.tvPercentStopOut.text =
                    "${VAUSdkUtil.shareAccountBean().marginStopOut}%"
                marginLevelPopup?.show()
            }
            .setOnSetupCallback {
                (activity as? AppCompatActivity)?.let {
                    KycVerifyHelper.showKycDialog(
                        it,
                        mapOf(
                            Constants.GoldParam.CODE to Constants.GoldParam.CODE_COMPLETE_INFO,
                            // 已跟H5开发@Ian You确认不用传virtualId了，他们会自己查
                        )
                    )
                    mPresenter.sensorUpgradeClick("Setup Now")
                }
            }
            // 要考虑到执行这里的时候 其他参数还没有被赋值的情况
            .setAccountState(
                loginStatus,
                mPresenter.accountGuideData,
                mPresenter.isDeposited,
                shareAccountBean
            , mPresenter.accountAuditStatus)
    }

    @SuppressLint("SetTextI18n")
    private fun refreshCollapsePnl(enableColorRes: Int) {
        // 行情维护 || 尚未初始化完畢
        if (Constants.MARKET_MAINTAINING || InitHelper.isNotSuccess()) {
            mBinding.tvCollapsePnlValue.setTextDiff("...")
            mBinding.tvCollapsePnlValue.setTextColorDiff(color_c1e1e1e_cebffffff)
            return
        }
        lifecycleScope.launch(Dispatchers.Default) {
            val collapsePnlUi =
                "${if (shareAccountBean.profit > 0) "+" else ""}${shareAccountBean.profit.numCurrencyFormat2()}"
            withContext(Dispatchers.Main) {
                mBinding.tvCollapsePnlValue.setTextDiff(collapsePnlUi)
            }
        }
        mBinding.tvCollapsePnlValue.setTextColorDiff(enableColorRes)
    }

    private fun refreshNotDepositEquity() {
        mBinding.mTradeGuideView.refreshNotDepositEquity(shareAccountBean)
    }

    @SuppressLint("SetTextI18n")
    private fun refreshLoggedInBoard(enableColorRes: Int) {
        mBinding.mTradeGuideView.refreshLoggedInBoard(enableColorRes, shareAccountBean)
    }

    @SuppressLint("SetTextI18n")
    private fun refreshOpenGuideBoard(enableColorRes: Int) {
        mBinding.mTradeGuideView.refreshOpenGuideBoard(enableColorRes, shareAccountBean)
    }

    private fun refreshVirtualBoard() {
        mBinding.mTradeGuideView.refreshVirtualBoard(shareAccountBean)
    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {
            R.id.tvLogin -> openActivity(LoginActivity::class.java)

            R.id.tvLinkTittle -> {
                openActivity(LoginPwdActivity::class.java)
            }

            R.id.tvAccountStatus, R.id.ivArrow -> {
                when (loginStatus) {
                    EnumLoginStatus.NEVER -> {
                        openActivity(SignUpActivity::class.java)
                    }

                    EnumLoginStatus.LOGGED_IN -> {
                        openActivity(LoginPwdActivity::class.java)
                    }

                    else -> {       // 已登录
                        openActivity(AccountManagerActivity::class.java)
                    }
                }
                LogEventUtil.setLogEvent(BuryPointConstant.V346.GENERAL_TRADES_ACC_MGMT_BUTTON_CLICK)
            }

            R.id.tvAccountId, R.id.tvCollapseAccountId, R.id.ivCollapseArrow -> {
                openActivity(AccountManagerActivity::class.java)
                LogEventUtil.setLogEvent(BuryPointConstant.V346.GENERAL_TRADES_ACC_MGMT_BUTTON_CLICK)
            }

            R.id.ivBannerClose -> {
                // 关闭广告位
                mPresenter.imgCloseApi(getEventIdList())
                hideOperationBanner()
                LogEventUtil.setLogEvent(BuryPointConstant.V344.GENERAL_TRADES_BANNER_CLOSE_BUTTON_CLICK)
            }

            R.id.ivProductSearch -> {
                // 埋点
                LogEventUtil.setLogEvent(
                    BuryPointConstant.V342.GENERAL_SEARCH_BUTTON_CLICK, bundleOf(
                        "Position" to "trade",
                        "Type" to "-",
                        "Account_type" to when {
                            !UserDataUtil.isLogin() -> "-"
                            UserDataUtil.isDemoAccount() -> "Demo"
                            else -> "Live"
                        }
                    )
                )
                // 神策自定义埋点(v3500)
                // 点击搜索按钮 -> 点击搜索按钮时触发
                val properties = JSONObject()
                properties.put(
                    SensorsConstant.Key.CURRENT_PAGE_NAME,
                    javaClass.simpleName
                ) // 当前页面名称
                SensorsDataUtil.track(SensorsConstant.V3500.SEARCH_BTN_CLICK, properties)

                if (!UserDataUtil.isLogin()) {
                    openActivity(LoginActivity::class.java)
                    return
                }
                openActivity(SearchProductActivity::class.java)
            }

            R.id.ivMessage -> {
                // 埋点
                val properties = JSONObject()
                properties.put(
                    SensorsConstant.Key.MESSAGES_STATUS,
                    if (SpManager.getRedPointState(false)) "unread" else "read"
                )
                SensorsDataUtil.track(
                    SensorsConstant.V3540.HOMEPAGE_MESSAGES_ICON_CLICK,
                    properties
                )

                if (!UserDataUtil.isLogin()) {
                    openActivity(LoginActivity::class.java)
                    return
                }
                openActivity(NoticeActivity::class.java)
            }

            R.id.tvSymbol -> {
                val lightTheme = AppUtil.isLightTheme()
                val isShowed = SpManager.getTradeQuotesGuide()
                if (isShowed.not()) {
                    mBinding.fragmentDeal.mVsGuide1.isVisible = false
                    mBinding.fragmentDeal.mVsGuide2.isVisible = false
                    // 执行跳过
                    SpManager.putTradeQuotesGuide()
                }
                XPopup.Builder(context)
                    .isDestroyOnDismiss(false)
                    .atView(view)
                    .hasShadowBg(false)
                    .isLightStatusBar(lightTheme)
                    .popupPosition(PopupPosition.Bottom)
                    .popupAnimation(PopupAnimation.ScrollAlphaFromTop)
                    .offsetX(12.dp2px())
                    .asCustom(TradeSwitchModePopup(requireContext()))
                    .show()
            }
        }
    }

    private fun getEventIdList(): String {
        if (mPresenter.bannerData?.eventsList?.isNotEmpty() == true) {
            return mPresenter.bannerData?.eventsList?.map { it.eventId }?.joinToString(",")
                .ifNull("")
        }
        return ""
    }

    override fun tableUpdate() {
        val groupList = mutableListOf(getString(R.string.watchlist))
        VAUSdkUtil.shareGoodList().forEach {
            groupList.add(
                VAUSdkUtil.getGroupNameLanguage(
                    requireContext(),
                    it.groupname.ifNull()
                )
            )
        }
        // List.toSet() == List.toSet() 可保证不同排序下元素匹配与元素个数匹配
        if (titleList.size != 1 && groupList.toSet() != titleList.toSet()) {
            fragmentList.clear()
            titleList.clear()
            mBinding.fragmentDeal.mTabLayout.removeAllViews()
        }
        initTabLayout()
    }

    override fun skipOpenAccountActivity(
        linkSkipState: EnumLinkSkipState, objData: MT4AccountTypeObj?
    ) {

        // 默认正常跳转，直接走公共跳转
        if (linkSkipState == EnumLinkSkipState.DEFAULT) {

            if (objData?.applyTpe == 2 && objData.regulator == "1") {
                openActivity(AccountManagerActivity::class.java)
                return
            }

            VAUStartUtil.openAccountGuide(requireActivity(), objData ?: MT4AccountTypeObj())
            return
        }

        val objStatus = objData?.status

        // 成功开户 模拟/返佣 进入金
        if (objStatus == 5) {
            if (linkSkipState == EnumLinkSkipState.GOLDEN) {
                NewHtmlActivity.openActivity(requireContext(), url = UrlConstants.HTML_FUND_DEPOSIT)
            }
            return
        }

        if (objStatus == 3) {
            VAUStartUtil.dispatchOpenAccount(activity)
            return
        }

        /*
         开户审核中,只打开app
         1：账户未提交 (跳步数)
         2：账户审核，
         3：账户被挂起 {跳第一步}
         4：账户被拒绝(被拒绝，不存在这种情况，被拒绝时被弹出登陆，不能登陆)
         5：账户已通过
         6：账户待审核且身份证明未提交(跳身份证明)  数据不对，也是跳步数
         */
        if (objStatus == 2) {
            ToastUtil.showToast(getString(R.string.you_have_an_existing_processed))
            return
        }

        // 跳开户第几步
        VAUStartUtil.openAccountGuide(requireActivity(), objData ?: MT4AccountTypeObj())

    }

    private fun logout() {
        userGroupChange = true
        mPresenter.accountGuideData = null
        mPresenter.isDeposited = true
        mPresenter.bannerData = null
        hideOperationBanner()

        setLoginStatus()
        renderLayout()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onDataEvent(event: DataEvent) {
        when (event.tag) {
            // token 异常强制退出
            NoticeConstants.WS.LOGIN_ERROR_OF_TOKEN -> logout()
            NoticeConstants.SYNC_KYC_USER_LEVEL -> kycGuideInfo(event.data as? KycVerifyLevelObj)
        }
    }

    @SuppressLint("SetTextI18n")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.Init.APPLICATION_START -> {
                if (VauApplication.abOptNetParallel) return
                mBinding.tvConnecting.text = "${getString(R.string.connecting)}..."
                mBinding.tvConnecting.visibility = View.VISIBLE
            }

            NoticeConstants.Init.WS_SUCCESS_CONNECT -> {
                if (VauApplication.abOptNetParallel) return
                if (mBinding.tvConnecting.visibility != View.VISIBLE) {
                    return
                }
                mBinding.tvConnecting.text = getString(R.string.connected)
                lifecycleScope.launch {
                    delay(1000)
                    mBinding.tvConnecting.visibility = View.GONE
                }
            }
            // 心跳状态  [网络缓慢]
            NoticeConstants.WS.SOCKET_HEARTBEAT_ERROR -> {
                if (VauApplication.abOptNetParallel) return
                mBinding.tvConnecting.text = getString(R.string.slow_connection)
                mBinding.tvConnecting.visibility = View.VISIBLE
            }

            NoticeConstants.WS.SOCKET_HEARTBEAT_NORMAL -> {
                if (VauApplication.abOptNetParallel)  return
                if (mBinding.tvConnecting.text != getString(R.string.slow_connection)) return
                mBinding.tvConnecting.visibility = View.GONE
            }
            // WebSocket断开  [重连中]
            NoticeConstants.WS.SOCKET_DISCONNECTED -> {
                if (VauApplication.abOptNetParallel) return
                mBinding.tvConnecting.text = getString(R.string.reconnecting)
                mBinding.tvConnecting.visibility = View.VISIBLE
            }
            // 切换Buy/Sell模式
            NoticeConstants.Quotes.TRADE_SWITCH_MODE_BUYSELL -> {
                mBinding.fragmentDeal.dealProdAttrType.run {
                    tvSell.isVisible = true
                    tvBuy.isVisible = true
                    tvPrice.isVisible = false
                }
            }
            // 切换Classic模式
            NoticeConstants.Quotes.TRADE_SWITCH_MODE_CLASSIC -> {
                mBinding.fragmentDeal.dealProdAttrType.run {
                    tvSell.isVisible = false
                    tvBuy.isVisible = false
                    tvPrice.isVisible = true
                }
            }

            NoticeConstants.Init.DATA_ERROR_GOODS -> {
                if (VauApplication.abOptNetParallel) return
                uiStateProductListError()
            }

            // 产品列表刷新
            NoticeConstants.Init.DATA_SUCCESS_GOODS -> {
                if (VauApplication.abOptNetParallel) return
                showProductList()
            }
            // 切换账户 / 退出登录 / 用户变组 / 单点登录
            NoticeConstants.SWITCH_ACCOUNT, NoticeConstants.AFTER_LOGOUT_RESET, NoticeConstants.WS.LOGIN_ERROR_CHANGE_OF_GROUP -> {
                if (tag == NoticeConstants.SWITCH_ACCOUNT) {
                    // 检查KYC流程是否已完成
                    if (SpManager.isV1V2()) {
                        mPresenter.userQueryUserLevel()
                    } else {
                        mPresenter.accountOpeningGuide()
                    }
                }
                userGroupChange = true
                mPresenter.accountGuideData = null
                mPresenter.isDeposited = true
                mPresenter.bannerData = null
                hideOperationBanner()

                setLoginStatus()
                renderLayout()
                if (tag == NoticeConstants.AFTER_LOGOUT_RESET) {
                    initSortIcon()
                }
            }
            //NoticeConstants.UPLOAD_PHOTO_SUCCEED 指的是上传完身份证信息之后的回调
            NoticeConstants.REFRESH_OPEN_ACCOUNT_GUIDE, NoticeConstants.REFRESH_ACCOUNT_MANAGER, NoticeConstants.UPLOAD_PHOTO_SUCCEED -> {
                mPresenter.accountOpeningGuide()
            }

            NoticeConstants.REFRESH_PERSONAL_INFO_DATA -> {
                setLoginStatus()
                mPresenter.accountGuideData = null
                mPresenter.isDeposited = true
                mPresenter.bannerData = null
                hideOperationBanner()
                renderLayout()
            }
            // 显示隐藏小红点
            NoticeConstants.WS.POINT_REMIND_MSG_SHOW -> mBinding.ivRedDot.isVisible = true

            NoticeConstants.WS.POINT_REMIND_MSG_HIDE -> mBinding.ivRedDot.isVisible = false

            // 应用在后台放置超过一分钟
            NoticeConstants.APP_IN_BACKGROUND_MORE_THAN_1M -> {
//                Log.i("ApplicationInit", "[TradesFragment] APP_IN_BACKGROUND_MORE_THAN_1M: ")
                mBinding.mTradeGuideView.appInBackgroundMoreThan1m()
            }

            // 判断当前服务器时间是否是周末 是则Crypto产品组标签显示“Open” 不是则隐藏
            NoticeConstants.Quotes.TRADE_SERVER_TIME_IS_WEEKEND -> cryptoIsShowOpenLabel(true)
            NoticeConstants.Quotes.TRADE_SERVER_TIME_IS_NOT_WEEKEND -> cryptoIsShowOpenLabel(false)
        }

        if (tag.contains(NoticeConstants.FIREBASE_JUMP_TREAD_GROUP_NAME)) {
            val groupName = tag.replace(NoticeConstants.FIREBASE_JUMP_TREAD_GROUP_NAME, "")
            if (titleList.size < 2) {
                willToTabGroupName = groupName
            } else {
                fcmNoticeJump(groupName)
            }
        }
    }

    private fun fcmNoticeJump(tabStr: String) {
        val groupName = VAUSdkUtil.getGroupNameLanguage(requireContext(), tabStr)
        titleList.indexOfFirst { it == groupName }.let {
            if (it == -1)
                mBinding.fragmentDeal.mViewPager.setCurrentItem(0, true)
            else
                mBinding.fragmentDeal.mViewPager.setCurrentItem(it, true)
        }
    }

    fun fcmPushTradeToTab() {
        if (willToTabGroupName.isNotEmpty()) {
            val groupName = willToTabGroupName
            willToTabGroupName = ""
            fcmNoticeJump(groupName)
        }
    }

    private fun initSortIcon(mode: Int? = null) {
        val sortMode = mode ?: SpManager.getTradeSortRose()
        val drawable = ContextCompat.getDrawable(
            requireContext(), AttrResourceUtil.getDrawable(
                requireContext(),
                when (sortMode) {
                    1 -> R.attr.imgDownSort
                    2 -> R.attr.imgUpSort
                    else -> R.attr.imgNotSort
                }
            )
        )
        mBinding.fragmentDeal.dealProdAttrType.tvRose.setCompoundDrawablesWithIntrinsicBounds(
            if (LanguageHelper.isRtlLanguage()) drawable else null,
            null,
            if (LanguageHelper.isRtlLanguage()) null else drawable,
            null
        )
    }

    private fun showMaintenance() {
        if (Constants.MARKET_MAINTAINING && mBinding.fragmentDeal.root.isVisible) {
            mBinding.fragmentDeal.root.isVisible = false
            mBinding.mViewStubMarketMaintenance.isVisible = true
            mHintMaintenanceView?.setTimeText(Constants.MAINTENANCE_MSG.ifEmpty { "" })
        } else if (!Constants.MARKET_MAINTAINING && !mBinding.fragmentDeal.root.isVisible) {
            mBinding.fragmentDeal.root.isVisible = true
            mBinding.mViewStubMarketMaintenance.isVisible = false
        }
    }

    @SuppressLint("SetTextI18n")
    override fun onVisibleToUserChanged(isVisibleToUser: Boolean, invokeInResumeOrPause: Boolean) {
        super.onVisibleToUserChanged(isVisibleToUser, invokeInResumeOrPause)
        if (isVisibleToUser) {
            SDKIntervalUtil.instance.removeCallBack(this)
            SDKIntervalUtil.instance.addCallBack(this)
            if (mBinding.mBanner.isVisible) {
                mBinding.mBanner.start()
            }
            isUIVisible = true
            lazyInitView()
        } else {
            SDKIntervalUtil.instance.removeCallBack(this)
            mBinding.mBanner.stop()
        }
    }

    @SuppressLint("StringFormatInvalid")
    override fun assetsCardUpdate() {
        if (UserDataUtil.isLogin()) {
            showAssetsCard()
        } else {
            setLoginStatus()
            renderLayout()
        }
    }

    private fun getEnableColor(): Int {
        return if (shareAccountBean.profit < 0)
            ce35728
        else if (shareAccountBean.profit > 0)
            c00c79c
        else
            color_c1e1e1e_cebffffff
    }

    /**
     * 新手引导
     */
    private fun initTradesGuide() {
        // ViewStub必须先设置setOnInflateListener，才能设置显示隐藏逻辑，否则里面方法不走
        mBinding.fragmentDeal.mVsGuide1.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutTradesGuide1Binding.bind(inflated)
                vs.tvSkip.setOnClickListener {
                    mBinding.fragmentDeal.mVsGuide1.isVisible = false
                    // 执行跳过
                    SpManager.putTradeQuotesGuide()
                }
                vs.tvNext.setOnClickListener {
                    mBinding.fragmentDeal.mVsGuide1.isVisible = false
                    mBinding.fragmentDeal.mVsGuide2.isVisible = true
                }
            }
        })
        mBinding.fragmentDeal.mVsGuide2.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutTradesGuide2Binding.bind(inflated)
                vs.tvNext.setOnClickListener {
                    mBinding.fragmentDeal.mVsGuide2.isVisible = false
                    // 执行跳过
                    SpManager.putTradeQuotesGuide()
                }
            }
        })

        mBinding.fragmentDeal.mVsGuide1.isVisible = SpManager.getTradeQuotesGuide().not()
    }

    /**
     * Crypto tab 是否显示 Open 标签
     */
    private fun cryptoIsShowOpenLabel(isShow: Boolean) {
        val index = titleList.indexOfFirst { it == getString(R.string.crypto) }
        if (index != -1) {
            val tvOpen = mBinding.fragmentDeal.mTabLayout.getChildAt(index)
                ?.findViewById<TextView>(R.id.tvOpen)
            tvOpen?.isVisible = isShow
        }
    }

    private fun hasCrypto(): Boolean {
        val index = titleList.indexOfFirst { it == getString(R.string.crypto) }
        return index != -1
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
        SDKIntervalUtil.instance.removeCallBack(this)
        if (VauApplication.abOptNetParallel) {
            LinkStateManager.unregisterCallback(stateChangeCallback)
        }
    }

}