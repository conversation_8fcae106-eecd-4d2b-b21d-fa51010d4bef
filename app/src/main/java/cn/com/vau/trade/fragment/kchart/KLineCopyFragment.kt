package cn.com.vau.trade.fragment.kchart

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import cn.com.vau.MainActivity
import cn.com.vau.R
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmFragment
import cn.com.vau.data.enums.EnumStrategyFollowState
import cn.com.vau.data.strategy.SearchStrategyBean
import cn.com.vau.databinding.FootRecyclerKlineViewMoreBinding
import cn.com.vau.databinding.FragmentRefreshBinding
import cn.com.vau.page.StickyEvent
import cn.com.vau.signals.stsignal.activity.StStrategyDetailsActivity
import cn.com.vau.signals.stsignal.adapter.StSearchSignalAdapter
import cn.com.vau.trade.model.KLineViewModel
import cn.com.vau.trade.st.StrategyOrderBaseData
import cn.com.vau.trade.st.activity.StStrategyOrdersActivity
import cn.com.vau.trade.viewmodel.KLineCopyViewModel
import cn.com.vau.util.ifNull
import cn.com.vau.util.observeUIState
import cn.com.vau.util.setNbOnItemChildClickListener
import cn.com.vau.util.setNbOnItemClickListener
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.NoDataView
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject
import kotlin.getValue

/**
 * @description:
 * @author: GG
 * @createDate: 2024 10月 21 15:08
 * @updateUser:
 * @updateDate: 2024 10月 21 15:08
 */
class KLineCopyFragment : BaseMvvmFragment<FragmentRefreshBinding, KLineCopyViewModel>() {

    private val activityViewModel: KLineViewModel by activityViewModels()

    private val footView by lazy {
        FootRecyclerKlineViewMoreBinding.inflate(layoutInflater, mBinding.root, false)
    }
    private val adapter by lazy {
        StSearchSignalAdapter().apply {
            setEmptyView(NoDataView(requireContext()).apply {
                setHintMessage(getString(R.string.no_records_found))
            })
        }
    }

    private var prodName: String = ""

    override fun initParam(savedInstanceState: Bundle?) {
        prodName = activityViewModel.symbol
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        mBinding.mRecyclerView.adapter = adapter

        adapter.removeAllFooterView()
        adapter.addFooterView(footView.root)
    }

    override fun lazyLoadData() {
        super.lazyLoadData()
        if (adapter.data.isEmpty()) {
            mViewModel.getTopTraderData(prodName)
        }
    }

    /**
     * 跳转策略详情
     */
    private fun jumpStrategies(data: SearchStrategyBean?) {
        if (data?.pendingApplyApproval == true) {
            openActivity(StStrategyOrdersActivity::class.java, Bundle().apply {
                putSerializable("data_strategy", StrategyOrderBaseData().apply {
                    this.type = EnumStrategyFollowState.PENDING_REVIEW
                    this.signalStrategyId = data.signalId
                    this.portfolioId = data.followPortFolioId
                    this.followRequestId = data.followRequestId
                })
            })
        } else {
            if (data?.isFollowed == true) {
                openActivity(StStrategyOrdersActivity::class.java, Bundle().apply {
                    putSerializable("data_strategy", StrategyOrderBaseData().apply {
                        this.type = EnumStrategyFollowState.OPEN
                        this.signalStrategyId = data.signalId
                        this.portfolioId = data.followPortFolioId
                        this.followRequestId = data.followRequestId
                    })
                })
            } else {
                StStrategyDetailsActivity.open(requireContext(), data?.signalId)
                logEvent(data?.signalId)
            }
        }
    }

    /**
     * 跳转策略详情
     */
    private fun jumpStrategies(strategyId: String?) {
        StStrategyDetailsActivity.open(requireContext(), strategyId)
        logEvent(strategyId)
    }

    override fun initListener() {
        super.initListener()
        mBinding.mRefreshLayout.setEnableLoadMore(false)
        mBinding.mRefreshLayout.setOnRefreshListener {
            mViewModel.getTopTraderData(prodName)
        }
        adapter.setNbOnItemClickListener { _, _, position ->
            //跳转策略详情
            adapter.data.getOrNull(position)?.let {
                jumpStrategies(it.signalId)
                // 埋点
                SensorsDataUtil.track(SensorsConstant.V3510.COPY_TAB_PAGE_STRATEGY_CLICK, JSONObject().apply {
                    put(SensorsConstant.Key.STRATEGY_TITLE, it.signalName.ifNull())
                })
            }
        }

        //点击button 跳转
        adapter.setNbOnItemChildClickListener { _, view, position ->
            when (view.id) {
                R.id.tvButton -> {
                    adapter.data.getOrNull(position)?.let {
                        jumpStrategies(it)
                    }
                }
            }
        }
        footView.tvTopTraderMore.setOnClickListener {
            openActivity(MainActivity::class.java)
            EventBus.getDefault().postSticky(
                StickyEvent(NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_COMMUNITY)
            )
            LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_KLINE_VIEW_MORE_BTN_CLICK)
        }
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.uiListLiveData.observeUIState(viewLifecycleOwner, adapter, mBinding.mRefreshLayout)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.STStrategy.CHANGE_OF_ST_COPY_TRADING_ORDERS -> {
                mViewModel.getTopTraderData(prodName)
            }
            NoticeConstants.EVENT_KLINE_SWITCH_PRODUCT -> {
                prodName = activityViewModel.symbol
                mViewModel.getTopTraderData(prodName)
            }
        }
    }

    private fun logEvent(strategyId: String?) {
        LogEventUtil.setLogEvent(
            BuryPointConstant.V348.CT_STRATEGY_PAGE_VIEW, bundleOf(
                "Type_of_account" to when {
                    !UserDataUtil.isLogin() -> BuryPointConstant.AccountType.NOLOGIN
                    UserDataUtil.isStLogin() -> "Copy Trading"
                    UserDataUtil.isDemoAccount() -> BuryPointConstant.AccountType.DEMO
                    else -> BuryPointConstant.AccountType.LIVE
                },
                "Position" to "Kline",
                "Strategy_ID" to strategyId.ifNull()
            )
        )
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    companion object {
        @JvmStatic
        fun newInstance(): KLineCopyFragment {
            return KLineCopyFragment()
        }
    }

}