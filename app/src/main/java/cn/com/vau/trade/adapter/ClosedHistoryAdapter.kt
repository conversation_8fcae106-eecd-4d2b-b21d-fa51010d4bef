package cn.com.vau.trade.adapter

import android.graphics.drawable.Drawable
import android.widget.TextView
import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.data.trade.PartyCloseItemBean
import cn.com.vau.util.*
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class ClosedHistoryAdapter :
    BaseQuickAdapter<PartyCloseItemBean, BaseViewHolder>(R.layout.item_partially_closed) {
    private val currencyType by lazy { UserDataUtil.currencyType() }
    private val c00c79c by lazy { ContextCompat.getColor(context, R.color.c00c79c) }
    private val cf44040 by lazy { ContextCompat.getColor(context, R.color.cf44040) }
    private val drawableExpand: Drawable? by lazy {
        ContextCompat.getDrawable(context, R.drawable.draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff)?.apply {
            setBounds(0, 0, intrinsicWidth, intrinsicHeight)
        }
    }

    private val drawableClosed: Drawable? by lazy {
        ContextCompat.getDrawable(context, R.drawable.draw_bitmap2_arrow_top10x10_c1e1e1e_cebffffff)?.apply {
            setBounds(0, 0, intrinsicWidth, intrinsicHeight)
        }
    }

    init {
        addChildClickViewIds(R.id.titleClickView, R.id.tvDealNum)
    }

    override fun convert(holder: BaseViewHolder, item: PartyCloseItemBean) {
        holder.setGone(R.id.expandGP, item.isExpand.not())
        holder.getView<TextView>(R.id.tvPnlShow).setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, if (item.isExpand) drawableClosed else drawableExpand, null)
        holder.setText(R.id.tvVolume, "${item.closedVolume ?: "--"} ${context.getString(R.string.lots)}")
            .setText(R.id.tvClosePrice, item.closePrice ?: "--")
            .setText(R.id.tvPnl, "${item.closePnl?.numCurrencyFormat(currencyType).ifNull("--")} $currencyType".arabicReverseTextByFlag(" "))
            .setText(R.id.tvCharges, "${item.commission?.numCurrencyFormat(currencyType).ifNull("--")} $currencyType".arabicReverseTextByFlag(" "))
            .setText(R.id.tvSwap, "${item.swap?.numCurrencyFormat(currencyType).ifNull("--")} $currencyType".arabicReverseTextByFlag(" "))
            .setText(R.id.tvNetPnl, "${item.closeNetPnl?.numCurrencyFormat(currencyType).ifNull("--")} $currencyType".arabicReverseTextByFlag(" "))
            .setText(R.id.tvDealNum, "#${item.tradeDealId.ifNull()}")
            .setText(R.id.tvTime, item.closeTime)
            .setText(R.id.tvPnlShow, "${item.closePnl?.numCurrencyFormat(currencyType).ifNull("--")} $currencyType".arabicReverseTextByFlag(" "))
            .setTextColor(R.id.tvPnlShow, if (item.closePnl.toDoubleCatching() >= 0) c00c79c else cf44040)
            .setText(R.id.tvPartiallyClose,if (item.partClose == true) context.getString(R.string.partially_closed) else context.getString(R.string.closed))
    }
}