package cn.com.vau.trade.perform

import android.annotation.SuppressLint
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.ext.launchActivity
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.performance.AbsPerformance
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.FragmentOrderThemeBinding
import cn.com.vau.trade.activity.KLineActivity
import cn.com.vau.trade.data.AccountInfoItemBean
import cn.com.vau.trade.data.AccountInfoType
import cn.com.vau.trade.data.ProductState
import cn.com.vau.trade.data.toProductState
import cn.com.vau.trade.dialog.BottomSymbolSearchDialog
import cn.com.vau.trade.ext.SymbolSearchConstants.FROM_TRADE_ORDER
import cn.com.vau.trade.ext.copyData
import cn.com.vau.trade.ext.toAccountInfoColor
import cn.com.vau.trade.ext.toMarginRiskLevel
import cn.com.vau.trade.view.TradeProductTitleView
import cn.com.vau.trade.viewmodel.OrderViewModel
import cn.com.vau.util.arabicReverseTextByFlag
import cn.com.vau.util.ifNull
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * Created by array on 2025/5/12 14:31
 * Desc: 产品对+账户信息
 */
class TradeProductTitlePerformance(
    val fragment: Fragment,
    private val orderViewMode: OrderViewModel,
    private val mBinding: FragmentOrderThemeBinding,
    private val onExpandChange: ((isExpand: Boolean) -> Unit)? = null
) : AbsPerformance() {

    private var mViewModel: ProductTitleViewModel? = null

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        mViewModel = ViewModelProvider(fragment)[ProductTitleViewModel::class.java]
        EventBus.getDefault().register(this)
        initListener()
        setData()
    }

    private fun setProductTitleData(productData: ShareProductData) {
        mBinding.mProductTitleView.setProductName(productData.symbol)
    }

    fun initListener() {
        /**
         * 点击: 选择产品
         */
        mBinding.mProductTitleView.setOnProductClick {
            showSearchDialog()
            orderViewMode.tradePageProductClick()
        }

        /**
         * 点击: 资产区
         */
        mBinding.mProductTitleView.setOnAccountInfoClick { expandState ->
            when (expandState) {
                TradeProductTitleView.ExpandState.Expanded -> {
                    onExpandChange?.invoke(true)
                }

                TradeProductTitleView.ExpandState.Collapsed -> {
                    onExpandChange?.invoke(false)
                }
            }
        }

        /**
         * 点击跳转: K线页
         */
        mBinding.mProductTitleView.setOnKLineClick {
            orderViewMode.productData?.let {
                fragment.launchActivity<KLineActivity>(requireLogin = true) {
                    putExtra(Constants.PARAM_PRODUCT_NAME, it.symbol)
                }
            }
            orderViewMode.tradePageKChartBtnClick()
        }

        /**
         * LiveData: 切换产品
         */
        orderViewMode.productDataChangeLieData.observe(fragment) {
            setProductTitleData(it)
        }

        /**
         * LiveData: 更新资产区信息
         */
        orderViewMode.accountInfoItemLiveData.observe(fragment) {
            setAccountInfo(it)
        }
    }

    fun setData() {
        val product = mViewModel?.findProduct(SpManager.getTradeProductSymbol())
        if (product != null) {
            SpManager.putTradeProductSymbol(product.symbol)
            orderViewMode.setProduceData(product)
        }
        orderViewMode.changeProductState(product.toProductState())
    }

    /**
     * 设置账户信息
     */
    private fun setAccountInfo(bean: AccountInfoItemBean) {
        mBinding.mProductTitleView.setAccountInfoTitle(bean.type.toTitle())
        mBinding.mProductTitleView.setAccountInfoValue(bean.value)
        mBinding.mProductTitleView.setAccountInfoValueColor(bean.type.toAccountInfoColor(fragment.requireActivity(), VAUSdkUtil.shareAccountBean().copyData()))
        mBinding.mProductTitleView.setRiskVisible(bean.type == AccountInfoType.MarginLevel)
        updateMarginRiskLevel(bean.type)
    }

    /**
     * 账户信息item标题
     */
    private fun AccountInfoType.toTitle(): String {
        return when (this) {
            AccountInfoType.Equity -> "${fragment.getString(R.string.equity)} (${UserDataUtil.currencyType()})".arabicReverseTextByFlag(" ").ifNull()
            AccountInfoType.FloatingPnL -> "${fragment.getString(R.string.floating_pnl)} (${UserDataUtil.currencyType()})".arabicReverseTextByFlag(" ").ifNull()
            AccountInfoType.MarginLevel -> fragment.getString(R.string.margin_level)
            AccountInfoType.Credit -> "${fragment.getString(R.string.credit)} (${UserDataUtil.currencyType()})".arabicReverseTextByFlag(" ").ifNull()
            AccountInfoType.MarginAndFreeMargin -> "${"${fragment.getString(R.string.margin)}/${fragment.getString(R.string.free_margin)}"} (${UserDataUtil.currencyType()})".arabicReverseTextByFlag(" ").ifNull()
            AccountInfoType.Balance -> "${fragment.getString(R.string.balance)} (${UserDataUtil.currencyType()})".arabicReverseTextByFlag(" ").ifNull()
        }
    }

    /**
     * 更新保证金水平风险等级
     */
    private fun updateMarginRiskLevel(type: AccountInfoType) {
        if (type == AccountInfoType.MarginLevel) {
            mBinding.mProductTitleView.setMarginRiskLevel(VAUSdkUtil.shareAccountBean().copyData().toMarginRiskLevel())
        }
    }

    /**
     * 搜索产品弹窗
     */
    private fun showSearchDialog() {
        BottomSymbolSearchDialog.Builder(fragment.requireActivity())
            .moveUpToKeyboard(false)
            .setViewMode(true)
            .setDestroyOnDismiss(true)
            .setFrom(FROM_TRADE_ORDER)
            .setOnSelectItem {
                SpManager.putTradeProductSymbol(it.symbol)
                orderViewMode.setProduceData(it)
                orderViewMode.changeProductState(it.toProductState())
            }
            .build()
            .showDialog()
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        EventBus.getDefault().unregister(this)
    }

    @SuppressLint("SetTextI18n")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            // 切换账户 || 退出登录
            NoticeConstants.SWITCH_ACCOUNT, NoticeConstants.AFTER_LOGOUT_RESET -> {
                mBinding.mProductTitleView.setOriginalState()
                orderViewMode.productData = null
                orderViewMode.changeProductState(ProductState.NoProduct)
            }
            // 初始化结束
            NoticeConstants.Init.ACCOUNT_INFO_SUCCESS -> {
                setData()
            }
        }
    }

}

class ProductTitleViewModel : BaseViewModel() {
    fun findProduct(targetSymbol: String?): ShareProductData? {
        return when {
            targetSymbol.isNullOrEmpty() -> null
            else -> VAUSdkUtil.symbolList().firstOrNull { it.symbol == targetSymbol }
        } ?: VAUSdkUtil.symbolList().firstOrNull { it.enable == "2" } // 如果targetSymbol为空或未匹配，选择第一个完全访问的产品
    }

}