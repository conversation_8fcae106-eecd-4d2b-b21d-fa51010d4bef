package cn.com.vau.trade.perform

import cn.com.vau.common.performance.AbsPerformance
import cn.com.vau.databinding.DialogTakeProfitStopLossBinding
import cn.com.vau.trade.ext.toTraceModeType
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import org.json.JSONObject

class TpSlTracePerformance(val mBinding: DialogTakeProfitStopLossBinding) : AbsPerformance() {

    /**
     * 止盈止损弹窗Confirm按钮点击
     */
    fun traceConfirmClick() {

        val modeTp = mBinding.viewTakeProfit.getCurrentComputeMode()
        val modeSl = mBinding.viewStopLoss.getCurrentComputeMode()
        val selectedTp = mBinding.viewTakeProfit.isSelectedItem()
        val selectedSl = mBinding.viewStopLoss.isSelectedItem()

        val properties = JSONObject()
        properties.put(SensorsConstant.Key.IS_SLECTED_TP, if (selectedTp) 1 else 0)
        if (selectedTp) {
            properties.put(SensorsConstant.Key.TP_TYPE, modeTp.toTraceModeType())
        }
        properties.put(SensorsConstant.Key.IS_SLECTED_SL, if (selectedSl) 1 else 0)
        if (selectedSl) {
            properties.put(SensorsConstant.Key.SL_TYPE, modeSl.toTraceModeType())
        }
        SensorsDataUtil.track(SensorsConstant.ORDER_POSITION.TP_SLPOPUP_CONFIRM, properties)
    }

}