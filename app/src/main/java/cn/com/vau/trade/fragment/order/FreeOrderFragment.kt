package cn.com.vau.trade.fragment.order

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.*
import cn.com.vau.R
import cn.com.vau.common.base.fragment.BaseFragment
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.common.utils.SDKIntervalUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.data.BaseBean
import cn.com.vau.data.trade.FreeOrdersBean
import cn.com.vau.databinding.FragmentFreeOrderBinding
import cn.com.vau.databinding.VsLayoutNoDataScrollBinding
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.trade.activity.KLineActivity
import cn.com.vau.trade.interfac.RefreshInterface
import cn.com.vau.ui.deal.adapter.FreeStockOrderRecyclerAdapter
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.CenterActionWithIconDialog
import io.reactivex.disposables.Disposable
import org.greenrobot.eventbus.*

/**
 * 订单/股票
 */
class FreeOrderFragment : BaseFragment(), SDKIntervalCallback,RefreshInterface{

    private val mBinding by lazy { FragmentFreeOrderBinding.inflate(layoutInflater) }

    private var mAdapter: FreeStockOrderRecyclerAdapter? = null
    var dataList: ArrayList<FreeOrdersBean.Obj> = arrayListOf()

    var refreshFinished: (() -> Any?)? = null

    override fun onCallback() {
        if (dataList.size <= 0) return

        for (dataBean in dataList) {
            val shareSymbolBean =
                VAUSdkUtil.symbolList().firstOrNull { it.symbol == dataBean.symbol }
                    ?: continue

            dataBean.profit = VAUSdkUtil.getProfitLoss(
                shareSymbolBean,
                "0",
                dataBean.volume.ifNull(),
                "0"
            ).toDouble()

        }

        refreshAdapter(false)
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun refreshAdapter(state: Boolean) {
        if (state) {
            mAdapter?.notifyDataSetChanged()
        } else {
            for (x in dataList.indices) {
                mAdapter?.notifyItemChanged(x, Constants.DEFAULT_PAYLOAD_KEY)
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = mBinding.root

    override fun initParam() {
        super.initParam()
        EventBus.getDefault().register(this)
    }

    override fun initView() {
        super.initView()

        mBinding.mVsNoDataScroll.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutNoDataScrollBinding.bind(inflated)
                vs.mNoDataScrollView.setHintMessage(getString(R.string.no_positions))
            }
        })

        mAdapter = FreeStockOrderRecyclerAdapter(requireContext(), dataList)
        mBinding.mRecyclerView.adapter = mAdapter
        mBinding.mRecyclerView.setEmptyView(mBinding.mVsNoDataScroll)
    }

    override fun initData() {
        super.initData()
        showNetDialog()
        stockListDetail(false)
    }

    override fun initListener() {
        super.initListener()

        mAdapter?.setOnItemClickListener(object : FreeStockOrderRecyclerAdapter.OnItemClickListener {
            override fun onCloseOrderClick(position: Int) {
                closeStock(position)
            }

            override fun onStartKLine(position: Int) {
                openActivity(KLineActivity::class.java, Bundle().apply {
                    putString(
                        Constants.PARAM_PRODUCT_NAME,
                        dataList.getOrNull(position)?.symbol.ifNull()
                    )
                })
            }
        })
    }

    private fun stockListDetail(finishRefresh: Boolean) {
        val paramMap = HashMap<String, Any>()
        paramMap["login"] = UserDataUtil.accountCd()
        paramMap["serverID"] = UserDataUtil.serverId()
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().stockActivityStockListDetail(paramMap),
            object : BaseObserver<FreeOrdersBean>() {
                @SuppressLint("NotifyDataSetChanged")
                override fun onNext(dataBean: FreeOrdersBean?) {
                    hideNetDialog()
                    if ("V00000" != dataBean?.resultCode) {
                        ToastUtil.showToast(dataBean?.msgInfo)
                        return
                    }

                    dataList.clear()
                    dataList.addAll(dataBean.data?.obj ?: ArrayList())
                    mAdapter?.notifyDataSetChanged()

                    onCallback()

                    if (finishRefresh)
                        refreshFinished?.invoke()
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    rxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    hideNetDialog()
                }
            })
    }

    private fun closeStock(closeStockPosition: Int) {
        showNetDialog()
        val orderId = dataList.elementAtOrNull(closeStockPosition)?.order ?: "-1"
        val paramMap = HashMap<String, Any>()
        paramMap["login"] = UserDataUtil.accountCd()
        paramMap["serverID"] = UserDataUtil.serverId()
        paramMap["token"] = UserDataUtil.loginToken()
        paramMap["order"] = orderId
        paramMap["symbol"] = dataList.elementAtOrNull(closeStockPosition)?.name ?: "-1"
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().stockActivityCloseStock(paramMap),
            object : BaseObserver<BaseBean>() {
                @SuppressLint("NotifyDataSetChanged")
                override fun onNext(dataBean: BaseBean?) {
                    hideNetDialog()
                    if ("V00000" != dataBean?.resultCode) {
                        ToastUtil.showToast(dataBean?.msgInfo ?: "")
                        return
                    }
                    dataList.removeAll {
                        it.order == orderId
                    }
                    mAdapter?.notifyDataSetChanged()
                    CenterActionWithIconDialog.Builder(requireActivity())
                        .setTitle(getString(R.string.close_confirmed))
                        .setLottieIcon(R.raw.lottie_dialog_ok)
                        .setSingleButton(true)
                        .setSingleButtonText(getString(R.string.ok))
                        .build()
                        .showDialog()
                    // 平仓成功发通知
                    EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_OPEN_ORDER)

                }

                override fun onHandleSubscribe(d: Disposable?) {
                    rxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    hideNetDialog()
                }
            }
        )
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    @SuppressLint("NotifyDataSetChanged")
    fun onMsgEvent(msgTag: String) {
        if (msgTag == NoticeConstants.WS.CHANGE_OF_ORDER_FREE ||
            msgTag == NoticeConstants.Init.DATA_REQUEST_ORDER ||
            msgTag == NoticeConstants.WS.CHANGE_OF_PENDING_ORDER
        )
            stockListDetail(false)
        if (msgTag == NoticeConstants.SWITCH_ACCOUNT || msgTag == NoticeConstants.AFTER_LOGOUT_RESET) {
            dataList.clear()
            mAdapter?.notifyDataSetChanged()
        }
    }

    override fun onVisibleToUserChanged(isVisibleToUser: Boolean, invokeInResumeOrPause: Boolean) {
        super.onVisibleToUserChanged(isVisibleToUser, invokeInResumeOrPause)
        if (isVisibleToUser) {
            SDKIntervalUtil.instance.removeCallBack(this)
            SDKIntervalUtil.instance.addCallBack(this)
        } else {
            SDKIntervalUtil.instance.removeCallBack(this)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    companion object {
        fun newInstance() = FreeOrderFragment()
    }

    override fun refreshData() {
        stockListDetail(true)
    }

}
