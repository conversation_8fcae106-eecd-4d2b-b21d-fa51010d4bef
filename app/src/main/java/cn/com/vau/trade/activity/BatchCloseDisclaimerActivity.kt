package cn.com.vau.trade.activity

import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.databinding.ActivityDisclaimerBatchCloseBinding
import cn.com.vau.util.clickNoRepeat

class BatchCloseDisclaimerActivity :
    BaseMvvmActivity<ActivityDisclaimerBatchCloseBinding, BaseViewModel>() {
    override fun initView() {
        mBinding.mHeaderBar.setTitleText(getString(R.string.disclaimer))
    }

}