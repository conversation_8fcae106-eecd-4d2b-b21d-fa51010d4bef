package cn.com.vau.trade.perform

import cn.com.vau.common.performance.AbsPerformance
import cn.com.vau.databinding.DialogBottomSymbolSearchBinding
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import org.json.JSONObject

/**
 * Created by array on 2025/4/24 15:27
 * Desc: 埋点功能
 */
class SSTracePerformance : AbsPerformance() {

    /**
     * item点击事件
     */
    fun traceSearchResultClick(searchKey: String, symbol: String, groupName: String, position: Int, isDefault: String) {

        val properties = JSONObject()
        properties.put(SensorsConstant.Key.ORIGINAL_KEY_WORD, searchKey)
        properties.put(SensorsConstant.Key.CLICK_PRODUCT, symbol)
        properties.put(SensorsConstant.Key.PRODUCT_GROUP, groupName)
        properties.put(SensorsConstant.Key.POSITION_RANK, position)
        properties.put(SensorsConstant.Key.IS_DEFAULT, isDefault)
        SensorsDataUtil.track(SensorsConstant.SymbolSearch.CANDLESTICKSEARCH_RESULTCLICK, properties)
    }

}