package cn.com.vau.trade.presenter

import cn.com.vau.common.base.mvp.BaseModel
import cn.com.vau.common.base.mvp.BasePresenter
import cn.com.vau.common.base.mvp.BaseView
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.trade.StOptionalBean
import io.reactivex.disposables.Disposable

/**
 * 交易自选
 */
interface StDealOptionalContract {

    interface Model : BaseModel {
        fun accountProductMyApi(map: HashMap<String, Any>, baseObserver: BaseObserver<StOptionalBean>): Disposable
    }

    interface View : BaseView {
        fun refreshAdapter(state:Boolean)
        fun initAdapterData(state:Boolean)
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun getSTOptionalProdListApi()
    }

}
