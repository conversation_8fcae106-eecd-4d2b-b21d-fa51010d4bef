package cn.com.vau.trade.perform

import android.annotation.SuppressLint
import android.view.View
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import cn.com.vau.R
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.performance.AbsPerformance
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.databinding.DialogTakeProfitStopLossBinding
import cn.com.vau.trade.dialog.BottomTakeProfitStopLossDialog
import cn.com.vau.trade.viewmodel.TakeProfitStopLossViewModel
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.setTextColorDiff
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.CenterActionWithIconDialog
import org.greenrobot.eventbus.EventBus

/**
 * 处理提交逻辑
 */
class TpSlNextPerformance(
    val activity: FragmentActivity,
    val dialog: BottomTakeProfitStopLossDialog,
    val mBinding: DialogTakeProfitStopLossBinding,
    val mViewModel: TakeProfitStopLossViewModel,
    var tpSetEnable: Boolean = true,
    var slSetEnable: Boolean = true,
) : AbsPerformance(), View.OnClickListener {

    private val color_cebffffff_c1e1e1e by lazy { AttrResourceUtil.getColor(activity, R.attr.color_cebffffff_c1e1e1e) }
    private val color_c731e1e1e_c61ffffff by lazy { AttrResourceUtil.getColor(activity, R.attr.color_c731e1e1e_c61ffffff) }
    private val draw_shape_c1e1e1e_cebffffff_r100 by lazy { ContextCompat.getDrawable(activity, R.drawable.draw_shape_c1e1e1e_cebffffff_r100) }
    private val draw_shape_c0a1e1e1e_c0affffff_r100 by lazy { ContextCompat.getDrawable(activity, R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100) }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        initView()
        createObserver()
    }

    @SuppressLint("SetTextI18n")
    fun initView() {
        mBinding.tvNext.setOnClickListener(this)

    }

    fun createObserver() {
        mViewModel.stDealSuccessLiveData.observe(activity) {
            showDealSuccessDialog()
        }

        mViewModel.tokenErrorLiveData.observe(dialog) {
            CenterActionDialog.Builder(activity)
                .setContent(it.ifNull())
                .setSingleButton(true)
                .setOnSingleButtonListener {
                    // 退出登录
                    EventBus.getDefault().post(NoticeConstants.LOGOUT_ACCOUNT)
                }.build()
                .showDialog()
        }

        mViewModel.hintDataLiveData.observe(dialog) {
            CenterActionDialog.Builder(activity)
                .setContent(it)
                .setSingleButton()
                .build().showDialog()
        }

        mViewModel.dealSuccessLiveData.observe(dialog) {
            showDealSuccessDialog()
        }

    }

    override fun onClick(view: View?) {
        when (view?.id) {
            R.id.tvNext -> {
                submitOrder()
            }
        }
    }

    fun updateNextStyle() {

        mBinding.tvNext.apply {
            val isEnable = tpSetEnable && slSetEnable
            setTextColorDiff(if (isEnable) color_cebffffff_c1e1e1e else color_c731e1e1e_c61ffffff)
            background = if (isEnable) draw_shape_c1e1e1e_cebffffff_r100 else draw_shape_c0a1e1e1e_c0affffff_r100
        }
    }

    private fun submitOrder() {
        if (mViewModel.productData == null) return

        dialog.tracePerformance.traceConfirmClick()

        mViewModel.tpParam = mBinding.viewTakeProfit.getPriceEditText()
        mViewModel.slParam = mBinding.viewStopLoss.getPriceEditText()

        val tpRange = dialog.takeProfitPerformance.getTakeProfitRange()
        val slRange = dialog.stopLossPerformance.getStopLossRange()

        val tpCompareResults = if ("1" == mViewModel.orderBean?.cmd) 1 else -1

        if (mBinding.viewTakeProfit.isSelectedItem() && mViewModel.tpParam.mathCompTo(tpRange) == tpCompareResults) {
            ToastUtil.showToast(activity.getString(R.string.error_take_profit_range))
            return
        }

        if (mBinding.viewStopLoss.isSelectedItem() && mViewModel.slParam.mathCompTo(slRange) == -tpCompareResults) {
            ToastUtil.showToast(activity.getString(R.string.error_stop_loss_range))
            return
        }
        if (UserDataUtil.isStLogin()) {
            mViewModel.stTradePositionUpdate()
        } else {
            mViewModel.tradeOrdersUpdate()
        }
    }

    fun showDealSuccessDialog() {
        if (mViewModel.orderBean == null) return

        dialog.dismissDialog()
        val dialogTitle = activity.getString(R.string.set_up_successfully)
        val dialogContent =
            "${mViewModel.orderBean!!.symbol} ${mViewModel.orderBean!!.volume} ${activity.getString(R.string.lot)}\n${
                if (OrderUtil.isBuyOfOrder(
                        mViewModel.orderBean!!.cmd
                    )
                ) "Buy Market" else "Sell Market"
            }\n${activity.getString(R.string.order_number)} #${mViewModel.orderBean!!.order}"
        CenterActionWithIconDialog.Builder(activity)
            .setTitle(dialogTitle)
            .setContent(dialogContent)
            .setSingleButton(true)
            .build()
            .showDialog()

    }

}