package cn.com.vau.trade.st.presenter

import cn.com.vau.R
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.BaseBean
import cn.com.vau.data.init.StShareStrategyData
import cn.com.vau.trade.st.contract.StCopyTradingPositionsOpenContract
import cn.com.vau.util.*
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import com.google.gson.JsonObject
import io.reactivex.disposables.Disposable
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.util.concurrent.CopyOnWriteArrayList

class StCopyTradingPositionsOpenPresenter : StCopyTradingPositionsOpenContract.Presenter() {

    var stShareFollowStrategyList: CopyOnWriteArrayList<StShareStrategyData> =
        VAUSdkUtil.stShareStrategyList()

    val popTitleList: ArrayList<String> = arrayListOf()

    var currentPosition: Int = 0

    override fun initParam() {
        popTitleList.apply {
            add(context.getString(R.string.add_funds))
            add(context.getString(R.string.remove_funds))
            add(context.getString(R.string.pause_copying))
            add(context.getString(R.string.stop_copy))
            add(context.getString(R.string.more_settings))
        }
    }

    // 暂停跟单
    override fun stAccountPauseFollowing(portfolioId: String) {

        mView?.showNetDialog()

        val strategyData = stShareFollowStrategyList.firstOrNull {
            it.strategyId == portfolioId
        }

        val jsonObject = JsonObject()
        jsonObject.addProperty("accountId", UserDataUtil.stAccountId())
        jsonObject.addProperty("portfolioId", strategyData?.portfolioId ?: "")
        val requestBody =
            jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        val dealLog = "copy trader:${strategyData?.strategyName ?: ""}"
        val startTimeMillis = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog(dealLog, "pause copy", startTimeMillis)

        mModel.stAccountPauseFollowing(requestBody, object : BaseObserver<BaseBean>() {
            override fun onNext(baseData: BaseBean?) {

                mView?.hideNetDialog()

                if ("200" != baseData?.code) {
                    ToastUtil.showToast(baseData?.msg)
                    DealLogUtil.saveFailedDealLog(
                        dealLog, baseData?.code ?: "", "pause copy", startTimeMillis
                    )
                    return
                }

                strategyData?.followingStatus = "2"
                mView?.showPauseOrResumeFollowDialog(true)

                DealLogUtil.saveSuccessDealLog(
                    dealLog, "pause copy", startTimeMillis
                )

            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
                DealLogUtil.saveFailedDealLog(
                    dealLog, "-1", "pause copy", startTimeMillis
                )
            }

        })

    }

    // 恢复跟单
    override fun stAccountResumeFollowing(portfolioId: String) {

        mView?.showNetDialog()

        val strategyData = stShareFollowStrategyList.firstOrNull {
            it.strategyId == portfolioId
        }

        val jsonObject = JsonObject()
        jsonObject.addProperty("accountId", UserDataUtil.stAccountId())
        jsonObject.addProperty("portfolioId", strategyData?.portfolioId ?: "")
        val requestBody =
            jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        val dealLog = "copy trader:${strategyData?.strategyName ?: ""}"
        val startTimeMillis = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog(dealLog, "resume copy", startTimeMillis)

        mModel.stAccountResumeFollowing(requestBody, object : BaseObserver<BaseBean>() {
            override fun onNext(baseData: BaseBean?) {

                mView?.hideNetDialog()

                if ("200" != baseData?.code) {
                    ToastUtil.showToast(baseData?.msg)
                    DealLogUtil.saveFailedDealLog(
                        dealLog, baseData?.code ?: "", "resume copy", startTimeMillis
                    )
                    return
                }

                strategyData?.followingStatus = "1"
                mView?.showPauseOrResumeFollowDialog(false)

                DealLogUtil.saveSuccessDealLog(
                    dealLog, "resume copy", startTimeMillis
                )

            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }

        })

    }

    // 停止跟单
    override fun stAccountRemoveFollower(portfolioId: String) {

        mView?.showNetDialog()

        val strategyData = stShareFollowStrategyList.firstOrNull {
            it.strategyId == portfolioId
        }

        val dealLog = "copy trader:${strategyData?.strategyName ?: ""}"
        val startTimeMillis = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog(
            dealLog, "stop copy", startTimeMillis
        )

        mModel.stAccountRemoveFollower(
            strategyData?.portfolioId ?: "", object : BaseObserver<BaseBean>() {
                override fun onNext(baseData: BaseBean?) {

                    mView?.hideNetDialog()

                    if ("200" != baseData?.code) {
                        ToastUtil.showToast(baseData?.msg)
                        DealLogUtil.saveFailedDealLog(
                            dealLog, baseData?.code ?: "", "stop copy", startTimeMillis
                        )
                        // 埋点
                        sensorsCopyTradingStopCopyResult(strategyData?.strategyName.ifNull(), strategyData?.strategyId.ifNull(), false)
                        return
                    }

                    mView?.showRemoveFollowerResponseDialog(0 != (strategyData?.positions?.size ?: 0))

                    // 埋点
                    sensorsCopyTradingStopCopyResult(strategyData?.strategyName.ifNull(), strategyData?.strategyId.ifNull(), true)
                    DealLogUtil.saveSuccessDealLog(
                        dealLog, "stop copy", startTimeMillis
                    )

                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    mView?.hideNetDialog()
                    DealLogUtil.saveFailedDealLog(
                        dealLog, "-1", "stop copy", startTimeMillis
                    )
                }
            }
        )

    }

    /**
     * 用户系统设置
     */
    override fun userSetItemset(value : Int) {
        val paramMap = hashMapOf<String, Any>()
        paramMap["userToken"] = UserDataUtil.loginToken()
        paramMap["code"] = Constants.KEY_FAST_CLOSE_ST
        paramMap["value"] = value
        mModel?.userSetItemset(paramMap, object : BaseObserver<BaseBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: BaseBean?) {
                if ("00000000" == dataBean?.resultCode) {
                    UserDataUtil.setFastStopCopyState("1")
                }
            }
        })
    }

    private fun sensorsCopyTradingStopCopyResult(strategyName: String, strategyId: String, isSuccess: Boolean) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.SUBMIT_RESULT, if (isSuccess) "success" else "failure")
        properties.put(SensorsConstant.Key.TARGET_NAME, strategyName)
        properties.put(SensorsConstant.Key.STRATEGY_ID, strategyId)
        SensorsDataUtil.track(SensorsConstant.V3610.COPYTRADINGSTOPCOPY_RESULT, properties)
    }

    fun sensorsCopyTradingCopierManageClick(position: Int, shareData: StShareStrategyData?) {
        val properties = JSONObject()
        properties.put("action_name", when (position) {
            0 -> "Add_funds"
            1 -> "Remove_funds"
            2 -> if ("1" == shareData?.followingStatus) "Pause_Copying" else "Resume_Copy"
            3 -> "Stop_Copy"
            4 -> "More_Settings"
            else -> "None"
        }) // 平台类型
        properties.put(SensorsConstant.Key.TARGET_NAME, shareData?.strategyName.ifNull())
        properties.put(SensorsConstant.Key.STRATEGY_ID, shareData?.strategyId.ifNull())
        SensorsDataUtil.track(SensorsConstant.V3610.COPYTRADINGCOPIER_MANAGE_CLICK, properties)
    }
}