package cn.com.vau.trade.bean.kchart

import androidx.annotation.Keep
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import com.google.gson.Gson
import java.io.Serializable

@Keep
data class KLineSettingData(
    var mainChartName: String = "MA",
    var subChartName: String = "VOL",
    var mainChartNameList: MutableList<String> = mutableListOf("MA"),//主指标
    var subChartNameList: MutableList<String> = mutableListOf("VOL"),//副指标
    var askLineDisplay: Boolean = true,
    var bidLineDisplay: Boolean = true,
    var positionLineDisplay: Boolean = true,
    var tpLineDisplay: Boolean = true,
    var slLineDisplay: Boolean = true,
): Serializable {
    companion object {
        fun getUserData(): KLineSettingData {
            return if (UserDataUtil.isLogin()) {
                val userData = SpManager.getKlineSettingData("${UserDataUtil.userId()}_","")
                if (userData.isNotEmpty()) {
//                    LogUtil.d("wj", "getHistoryData: 获取到userid: ${userid}的数据")
                    runCatching {
                        Gson().fromJson(userData, KLineSettingData::class.java)
                    }.getOrDefault(KLineSettingData())
                } else {
                    val defaultData = getNotLoginData()
                    defaultData.save()
                    defaultData
                }
            } else {
                getNotLoginData()
            }
        }
        private fun getNotLoginData(): KLineSettingData {
            val defaultData = SpManager.getKlineSettingData("default_")
            return if (defaultData.isNotEmpty()) {
//                LogUtil.d("wj", "getNotLoginData: 获取到未登录数据")
                runCatching {
                    Gson().fromJson(defaultData, KLineSettingData::class.java)
                }.getOrDefault(KLineSettingData())
            } else {
//                LogUtil.d("wj", "getNotLoginData: 什么数据都没获取到，返回初始化数据")
                KLineSettingData()
            }
        }
    }

    fun save() {
        val ext = if (UserDataUtil.isLogin()) UserDataUtil.userId() else "default"
        SpManager.putKlineSettingData("${ext}_",Gson().toJson(this))
//        LogUtil.d("wj", "save: 保存数据为 ${ext}_${StorageConstants.KLINE_SETTING_DATA}")
    }
}
