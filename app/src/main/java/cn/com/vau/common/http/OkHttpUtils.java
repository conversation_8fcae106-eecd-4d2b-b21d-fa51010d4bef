package cn.com.vau.common.http;

import android.util.Log;

import com.kit.AppKitManager;

import cn.com.vau.BuildConfig;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class OkHttpUtils {
    /**
     * 默认超时时间
     */
    private static final int DEFAULT_TIMEOUT = 60;
    /**
     * 最大空闲连接数
     */
    private static final int MAX_IDLE_CONNECTIONS = 8;
    /**
     * 空闲连接数保持存活的时长
     */
    private static final int KEEP_ALIVE_DURATION = 15;

    private static volatile OkHttpClient INSTANCE;

    /**
     * 记录接口返回状态是否是401
     */
    public static boolean isStatus4001 = false;

    public static boolean debug = BuildConfig.DEBUG;

    /**
     * 获取OkHttpClient单例对象
     *
     * @return
     */
    public static OkHttpClient getClient() {
        if (INSTANCE == null) {
            synchronized (OkHttpClient.class) {
                if (INSTANCE == null) {
                    INSTANCE = createOkHttpClient();
                }
            }
        }
        return INSTANCE;
    }

    /**
     * 创建OkHttpClient对象
     *
     * @return
     */
    private static OkHttpClient createOkHttpClient() {
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
//                .sslSocketFactory(sslParams.ssLSocketFactory, sslParams.trustManager)
//                .hostnameVerifier(SSLUtils.unSafeHostnameVerifier)
//                .addInterceptor(new ApiInterceptor())
//                .connectionPool(new ConnectionPool(MAX_IDLE_CONNECTIONS,
//                        KEEP_ALIVE_DURATION, TimeUnit.SECONDS))
                .addInterceptor(new AppendHeaderParamInterceptor())
                .addInterceptor(new HttpCodeInterceptor())
                .addInterceptor(new NetPerformanceInterceptor())
                .retryOnConnectionFailure(true)//错误重联
                .connectTimeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS);

        if (debug) {
            builder.addInterceptor(new HttpLoggingInterceptor( message ->
                    Log.i("okhttp", message)
            ).setLevel(HttpLoggingInterceptor.Level.BODY));
//                    .addNetworkInterceptor(new StethoInterceptor());

            Interceptor chuckInterceptor = AppKitManager.getInstance().getChuckInterceptor();
            if (chuckInterceptor != null) {
                builder.addInterceptor(chuckInterceptor);
            }

        }
        return builder.build();
    }
}
