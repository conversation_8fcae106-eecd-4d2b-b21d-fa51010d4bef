package cn.com.vau.common.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.data.init.ShareAccountInfoData
import cn.com.vau.databinding.IncludeVirtualAccountLayoutBinding
import cn.com.vau.util.numCurrencyFormat2
import cn.com.vau.util.setTextDiff
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class AccountVirtualMT5View @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private var mBinding: IncludeVirtualAccountLayoutBinding? = null

    private var onNextCallback: (() -> Unit)? = null

    init {
        mBinding = IncludeVirtualAccountLayoutBinding.inflate(LayoutInflater.from(context), this)
        mBinding?.tvSetupAccount?.setOnClickListener {
            onNextCallback?.invoke()
        }
    }

    fun refreshVirtualEquity(shareAccountBean: ShareAccountInfoData) {
        // 行情维护 || 尚未初始化完畢
        if (Constants.MARKET_MAINTAINING || InitHelper.isNotSuccess()) {
            mBinding?.tvEquity?.setTextDiff("...")
            mBinding?.tvCurrency?.setTextDiff("")
            return
        }
        MainScope().launch {
            val equityUi = withContext(Dispatchers.Default) {
                shareAccountBean.equity.numCurrencyFormat2()
            }
            mBinding?.tvEquity?.setTextDiff(equityUi)
        }
        mBinding?.tvCurrency?.setTextDiff(UserDataUtil.currencyType())
    }

    fun appInBackgroundMoreThan1m() {
        mBinding?.tvEquity?.text = "..."
        mBinding?.tvCurrency?.text = ""
    }

    // 补全账户信息回调
    fun setOnVirtualSetupCallback(callback: (() -> Unit)?) {
        onNextCallback = callback
    }
}