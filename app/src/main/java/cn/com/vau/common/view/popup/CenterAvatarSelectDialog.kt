package cn.com.vau.common.view.popup

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import androidx.recyclerview.widget.GridLayoutManager
import cn.com.vau.common.view.GridSpacingItemDecoration
import cn.com.vau.common.view.popup.adapter.AvatarSelectAdapter
import cn.com.vau.data.strategy.SelectAllPicBean
import cn.com.vau.databinding.DialogCenterAvatarSelectBinding
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.base.*

/**
 * @description:
 * @author: gold.guo
 * @createDate: 2024 4.4 星期四 16:28
 * @updateUser:
 * @updateDate: 2024 4.4 星期四 16:28
 */
@SuppressLint("ViewConstructor")
class CenterAvatarSelectDialog(
    context: Context,
    maxHeight: Int
) : CenterDialog<DialogCenterAvatarSelectBinding>(context, DialogCenterAvatarSelectBinding::inflate, maxHeight = maxHeight) {

    private var callback: ((String?) -> Unit)? = null

    private val adapter: AvatarSelectAdapter by lazy { AvatarSelectAdapter() }

    override fun setContentView() {
        super.setContentView()
        mContentBinding.apply {
            rvList.layoutManager = GridLayoutManager(context, 3)
            rvList.addItemDecoration(GridSpacingItemDecoration(3, 20.dp2px(), 20.dp2px()))
            rvList.adapter = adapter

            adapter.setOnItemClickListener { _, _, position ->
                adapter.selectUrl = adapter.data.getOrNull(position)?.url
                adapter.notifyDataSetChanged()
            }

            tvLeft.setOnClickListener {
                dismiss()
            }

            tvRight.setOnClickListener {
                callback?.invoke(adapter.selectUrl)
                dismiss()
            }
        }
    }

    /**
     * 有头像数据
     */
    fun isHasData() = adapter.data.isNotEmpty()

    fun setSelectItem(url: String?) {
        adapter.selectUrl = url
        adapter.notifyDataSetChanged()
    }

    fun setData(dataList: MutableList<SelectAllPicBean.Obj>? = null) {
        adapter.setList(dataList)
    }

    fun confirmCallback(callback: ((String?) -> Unit)?) {
        this.callback = callback
    }

    class Builder(activity: Activity) : IBuilder<DialogCenterAvatarSelectBinding, Builder>(activity) {

        override fun createDialog(context: Context): CenterDialog<DialogCenterAvatarSelectBinding> {
            return CenterAvatarSelectDialog(context, (0.45 * screenHeight).toInt())
        }

        override fun build(): CenterAvatarSelectDialog {
            return super.build() as CenterAvatarSelectDialog
        }
    }

}