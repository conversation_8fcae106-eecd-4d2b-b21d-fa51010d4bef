package cn.com.vau.common.mvvm.base

import android.app.Activity
import android.content.*
import android.content.res.Configuration
import android.os.*
import android.util.Log
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.viewbinding.ViewBinding
import cn.com.vau.*
import cn.com.vau.common.base.mvvm.ILoading
import cn.com.vau.common.mvvm.ext.inflateBindingWithGeneric
import cn.com.vau.common.view.dialog.CommonProcessDialog
import cn.com.vau.util.*
import cn.com.vau.util.language.MultiLanguages
import org.greenrobot.eventbus.*

/**
 * 不包含VM的基类，适用于不需要ViewModel的Activity
 */
abstract class BaseMvvmBindingActivity<VB : ViewBinding> : AppCompatActivity(), ILoading, View.OnClickListener {

    internal val mBinding: VB by lazy {
        inflateBindingWithGeneric(0, layoutInflater)
    }

    internal val mLoadingDialog by lazy { CommonProcessDialog(this) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initTheme()
        setContentView(mBinding.root)
        ActivityManagerUtil.getInstance().addActivity(this)
        RefreshUtil.setHeader(this)
        RefreshUtil.setFooter(this)
        if (registerBackPressEvent()) {
            // 在Activity中处理返回手势
            onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    handleBackPressEvent()
                }
            })
        }
    }

    override fun onPostCreate(savedInstanceState: Bundle?) {
        super.onPostCreate(savedInstanceState)
        initParam(savedInstanceState)
        initView()
        initFont()
        initData()
        createObserver()
        initListener()
    }

    override fun onClick(view: View?) {
    }

    override fun onResume() {
        super.onResume()
        if (BuildConfig.DEBUG) {
            Log.d("当前ActivityName", this.localClassName)
        }
    }

    override fun onStart() {
        super.onStart()
        if (useEventBus() && !EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    override fun attachBaseContext(newBase: Context?) {
        // 绑定语种
        super.attachBaseContext(MultiLanguages.attach(newBase))
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        MultiLanguages.updateAppLanguage(this)
    }

    open fun initTheme() {
        val isLight = AppUtil.isLightTheme()
        setTheme(if (isLight) R.style.AppTheme else R.style.TintAppTheme) // 默认白
        setDarkStatusIcon(isLight)
    }

    /**
     * 获取浅色模式下的StatusBar颜色，因为UI改版主题色和原来的不一致，StatusBar的颜色也要做相应的改变
     */
    open fun getLightStatusBarColor(): Int {
        return R.color.cffffff
    }

    /**
     * 获取深色模式下的StatusBar颜色，因为UI改版主题色和原来的不一致，StatusBar的颜色也要做相应的改变
     */
    open fun getHintStatusBarColor(): Int {
        return R.color.c1a1d20
    }

    private fun setDarkStatusIcon(bDark: Boolean) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val decorView = window.decorView
            if (decorView != null) {
                var vis = decorView.systemUiVisibility
                vis = if (bDark) {
                    vis or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
                } else {
                    vis and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv()
                }
                decorView.systemUiVisibility = vis
            }
        }
    }

    fun openActivity(toActivity: Class<out Activity?>?) {
        openActivity(toActivity, null)
    }

    fun openActivity(toActivity: Class<out Activity?>?, parameter: Bundle?) {
        noRepeat {
            val intent = Intent(this, toActivity)
            if (parameter != null) {
                intent.putExtras(parameter)
            }
            startActivity(intent)
        }
    }

    fun openActivity(cls: Class<*>?, bundle: Bundle?, requestCode: Int) {
        val intent = Intent()
        intent.setClass(this, cls!!)
        if (bundle != null) {
            intent.putExtras(bundle)
        }
        startActivityForResult(intent, requestCode)
    }

    fun clearTopToMain() {
        val intent = Intent(this, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
        startActivity(intent)
    }

    /**
     * 展示进度框
     */
    override fun showLoadDialog() {
        if (isFinishing || isDestroyed || mLoadingDialog.isShowing) return
        if (!mLoadingDialog.isShowing) {
            mLoadingDialog.show()
        }
    }

    /**
     * 隐藏进度框
     */
    override fun hideLoadDialog() {
        try {
            if (mLoadingDialog.isShowing) mLoadingDialog.dismiss()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 将非该Activity绑定的ViewModel添加 loading回调 防止出现请求时不显示 loading 弹窗bug
     * @param viewModels Array<out BaseViewModel>
     */
    protected fun addLoadingObserve(vararg viewModels: BaseViewModel?) {
        viewModels.forEach {
            it?.let { viewModel ->
                viewModel.loadingChange.dialogLiveData.observe(this) {
                    if (it) { //显示弹框
                        showLoadDialog()
                    } else { //关闭弹窗
                        hideLoadDialog()
                    }
                }
            }
        }
    }

    /**
     * event bus事件处理
     *
     * @param appEvent
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    open fun onMsgEvent(eventTag: String) {
    }

    /**
     * 初始化参数
     */
    open fun initParam(savedInstanceState: Bundle?) {}

    abstract fun initView()

    /**
     * 初始化字体设置
     */
    open fun initFont() {}

    /**
     * 初始化数据
     */
    open fun initData() {}

    /**
     * 创建LiveData数据观察者
     */
    open fun createObserver() {}

    /**
     * 事件监听
     */
    open fun initListener() {}

    /**
     * 是否使用EventBus
     */
    open fun useEventBus(): Boolean = false

    /**
     * 是否需要注册物理返回事件监听
     * * PS:如果为true则需要重写[handleBackPressEvent]方法自己处理返回逻辑，否则会无法返回
     */
    open fun registerBackPressEvent(): Boolean = false

    /**
     * 自定义物理返回事件
     */
    open fun handleBackPressEvent() {}

    override fun onDestroy() {
        super.onDestroy()
        if (useEventBus()) {
            EventBus.getDefault().unregister(this)
        }
        ActivityManagerUtil.getInstance().removeActivity(this)
        hideLoadDialog()
    }

}