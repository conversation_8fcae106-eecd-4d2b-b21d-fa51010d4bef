package cn.com.vau.common.view

import android.content.Context
import android.util.AttributeSet
import cn.com.vau.util.language.MultiLanguages
import com.github.lzyzsd.jsbridge.BridgeWebView

/**
 * Filename: VauBridgeWebView
 * Author: GG
 * Date: 2024/5/28
 * Description:
 */
class VauBridgeWebView : BridgeWebView {

    constructor (context: Context) : super(context, null)

    constructor (context: Context, attrs: AttributeSet?) : super(context, attrs, 0)

    constructor (context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        // 修复 WebView 初始化时会修改 Activity 语种配置的问题
        MultiLanguages.updateAppLanguage(context)
    }
}