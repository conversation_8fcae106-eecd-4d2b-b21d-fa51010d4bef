package cn.com.vau.common.view.popup

import android.content.Context
import android.view.View
import cn.com.vau.R
import cn.com.vau.databinding.PopupCameraBottomBinding
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lxj.xpopup.core.BottomPopupView

class CameraBottomPop (
    context: Context,
    val title: String?,
    val dataList: ArrayList<String>,
    private val itemClick: ((Int) -> Unit?)? = null
) : BottomPopupView(context) {

    override fun getImplLayoutId(): Int = R.layout.popup_camera_bottom

//    override fun getMaxHeight(): Int = (ScreenUtil.getScreenHeight(context) * 0.5).toInt()

    override fun onCreate() {
        super.onCreate()
        val mBinding = PopupCameraBottomBinding.bind(popupImplView)
        mBinding.tvTitle.text = title
        mBinding.ivRight.visibility = View.VISIBLE
        val adapter = CameraBottomAdapter()
        mBinding.mRecyclerView.adapter = adapter
        adapter.setNewInstance(dataList)

        mBinding.ivRight.setOnClickListener {
            dismiss()
        }
        adapter.setOnItemClickListener { _, _, position ->
            itemClick?.invoke(position)
            dismiss()
        }
    }
}

class CameraBottomAdapter: BaseQuickAdapter<String, BaseViewHolder>(R.layout.item_text_with_right_arrow) {
    override fun convert(holder: BaseViewHolder, item: String) {
        holder.setText(R.id.tv_content, item)
    }
}