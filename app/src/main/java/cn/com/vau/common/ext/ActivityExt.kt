package cn.com.vau.common.ext

import android.app.Activity
import android.content.Intent
import androidx.fragment.app.Fragment
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.page.user.login.LoginActivity


/**
 * 启动Activity的扩展函数（支持Bundle传参）
 * @param T 目标Activity类型，必须是Activity的子类
 * @param block 配置Intent的lambda，可用于添加Bundle参数
 *
 * 使用示例：
 * 1. 无参数启动：launchActivity<LoginActivity>()
 * 2. 带参数启动：
 *    launchActivity<DetailActivity> {
 *        putExtra("key1", "value1")
 *        putExtra("key2", 123)
 *        addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
 *    }
 */
inline fun <reified T : Activity> Activity.launchActivity(
    requireLogin: Boolean = false,// 是否检查登录
    crossinline block: Intent.() -> Unit = {}
) {
    if (requireLogin && !UserDataUtil.isLogin()) {
        startActivity(Intent(this, LoginActivity::class.java))
        return
    }
    Intent(this, T::class.java).apply {
        block()
        startActivity(this)
    }
}

/**
 * 启动Activity的扩展函数（支持Bundle传参）
 * @param T 目标Activity类型，必须是Activity的子类
 * @param block 配置Intent的lambda，可用于添加Bundle参数
 *
 * 使用示例：
 * 1. 无参数启动：launchActivity<LoginActivity>()
 * 2. 带参数启动：
 *    launchActivity<LoginActivity> {
 *        putExtra("key1", "value1")
 *        putExtra("key2", 123)
 *    }
 */
inline fun <reified T : Activity> Fragment.launchActivity(
    requireLogin: Boolean = false, // 是否检查登录
    crossinline block: Intent.() -> Unit = {}
) {
    if (requireLogin && !UserDataUtil.isLogin()) {
        startActivity(Intent(requireActivity(), LoginActivity::class.java))
        return
    }
    Intent(requireActivity(), T::class.java).apply {
        block()
        startActivity(this)
    }
}