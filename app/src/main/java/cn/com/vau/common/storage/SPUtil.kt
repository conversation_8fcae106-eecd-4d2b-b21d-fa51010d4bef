package cn.com.vau.common.storage

import android.content.Context
import androidx.annotation.RestrictTo
import cn.com.vau.common.application.VauApplication
import cn.com.vau.util.ifNull

/**
 * 保存数据的工具类
 */
@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP_PREFIX)
object SPUtil {

    private const val FILE_NAME = "save_file_name"

    private val sharedPreferences by lazy { VauApplication.context.getSharedPreferences(FILE_NAME, Context.MODE_PRIVATE) }

    private val editor by lazy { sharedPreferences.edit() }

    /****
    private var sharedPreferences: SharedPreferences by Delegates.notNull<SharedPreferences>()
    fun init(context: Context) {
        sharedPreferences = context.getSharedPreferences(FILE_NAME, Context.MODE_PRIVATE)
    }****/

    /**
     * 保存数据
     * data可空
     */
    @JvmStatic
    fun <T : Any?> saveData(key: String, data: T?) {

        when (data) {
            is Int -> editor.putInt(key, data)
            is Boolean -> editor.putBoolean(key, data)
            is String -> editor.putString(key, data)
            is Float -> editor.putFloat(key, data)
            is Long -> editor.putLong(key, data)
            else -> editor.putString(key, getElseValue(data))
        }
        editor.apply()
    }

    /**
     * 获取数据
     * 默认参数不能为空
     */
    @JvmStatic
    fun <T : Any> getData(key: String, defValue: T): T {

        return when (defValue) {
            is Int -> sharedPreferences.getInt(key, defValue) as T
            is Boolean -> sharedPreferences.getBoolean(key, defValue) as T
            is String -> sharedPreferences.getString(key, defValue) as T
            is Float -> sharedPreferences.getFloat(key, defValue) as T
            is Long -> sharedPreferences.getLong(key, defValue) as T
            else -> sharedPreferences.getString(key, getElseValue(defValue)) as T
        }
    }

    /**
     * 获取除了几个基础类型以为类型的数据的默认值，用来保存和填充取值的默认值
     */
    private fun <T> getElseValue(defValue: T?): String {
        return defValue.ifNull().toString()
    }

    /**
     * 根据key清除数据
     */
    fun clearDataByKey(key: String?) {
        editor.remove(key)
        editor.apply()
    }
}
