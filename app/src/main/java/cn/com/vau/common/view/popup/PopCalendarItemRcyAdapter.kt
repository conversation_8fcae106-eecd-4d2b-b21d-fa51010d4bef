package cn.com.vau.common.view.popup

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.ui.common.DateEntity
import cn.com.vau.util.AttrResourceUtil

/**
 * Created by roy on 2018/10/25.
 */
class PopCalendarItemRcyAdapter(
        var mContext: Context,
        var dataList: ArrayList<DateEntity>?
) : RecyclerView.Adapter<PopCalendarItemRcyAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val holder = ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_recycler_calendar_item, parent, false))
        holder.itemView.setOnClickListener {
            if (dataList?.elementAtOrNull(holder.adapterPosition)?.type ?: 0 != 1) return@setOnClickListener
            mOnItemClickListener?.onItemClick(holder.adapterPosition)
        }
        return holder
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bindTo(mContext, dataList?.elementAtOrNull(position))
    }

    override fun getItemCount(): Int {
        return dataList?.size ?: 0
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {

        @SuppressLint("UseCompatLoadingForDrawables")
        fun bindTo(mContext: Context, dataBean: DateEntity?) {

            dataBean?.run {
                val tvDate = itemView.findViewById<TextView>(R.id.tvDate)
                tvDate.setTextColor(when (dataBean.type) {
                    0, -1 -> AttrResourceUtil.getColor(mContext, R.attr.color_c1e1e1e_cebffffff)
                    // -1 -> R.color.gray_636C70
                    -2 -> AttrResourceUtil.getColor(mContext, R.attr.color_c1e1e1e_cebffffff)
                    2 -> ContextCompat.getColor(mContext, R.color.cffffff)
                    else -> AttrResourceUtil.getColor(mContext, R.attr.color_c1e1e1e_cebffffff)
                })

//                itemView.tvDate.setTextColor(ContextCompat.getColor(mContext, when (dataBean.type) {
//                    0, -1 -> AttrResourceUtil.getColor(mContext, R.attr.colorBottomTextMain)
//                    // -1 -> R.color.gray_636C70
//                    -2 -> AttrResourceUtil.getColor(mContext, R.attr.colorTextMain)
//                    2 -> R.color.white
//                    else -> AttrResourceUtil.getColor(mContext, R.attr.colorTextMain)
//                }))
                tvDate.setBackgroundResource(when (dataBean.type) {
//                    0 -> R.drawable.shape_10171f_r24
                    2 -> R.drawable.shape_ce35728_r100
                    else -> R.color.transparent
                })
                tvDate.text = dataBean.dateContent
            }

        }

    }

    private var mOnItemClickListener: OnItemClickListener? = null

    interface OnItemClickListener {
        fun onItemClick(position: Int)
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        mOnItemClickListener = onItemClickListener
    }

}