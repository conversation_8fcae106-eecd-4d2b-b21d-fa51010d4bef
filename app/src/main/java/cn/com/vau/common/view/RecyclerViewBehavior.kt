package cn.com.vau.common.view

import android.content.Context
import android.util.AttributeSet
import android.view.*
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.appbar.AppBarLayout

class RecyclerViewBehavior(context: Context?, attrs: AttributeSet?) : AppBarLayout.ScrollingViewBehavior(context, attrs) {

    override fun onInterceptTouchEvent(parent: CoordinatorLayout, child: View, ev: MotionEvent): Boolean {
        when (ev.action) {
            MotionEvent.ACTION_DOWN -> {
                if (!onTouchedSelf(child, ev)) {
                    setNestedScrollingStop(child)
                }
            }
        }
        return super.onInterceptTouchEvent(parent, child, ev)
    }

    private fun onTouchedSelf(view: View, ev: MotionEvent): Boolean {
        val selfRect = IntArray(2)
        view.getLocationOnScreen(selfRect)
        return ev.rawY > selfRect[1]
    }

    private fun setNestedScrollingStop(parentView: View?) {
        when (parentView) {
            is RecyclerView -> {
                parentView.stopNestedScroll()
                parentView.stopScroll()
            }
            is ViewPager -> {
                setNestedScrollingStop(parentView.getChildAt(parentView.currentItem))
            }
            is ViewPager2 -> {
                for (index in 0 until parentView.childCount) {
                    (parentView.getChildAt(index) as? ViewGroup)?.let {
                        setNestedScrollingStop(it)
                    }
                }
            }
            else -> {
            }
        }
    }
}