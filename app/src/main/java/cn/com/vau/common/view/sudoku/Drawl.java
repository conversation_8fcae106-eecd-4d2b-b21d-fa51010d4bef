package cn.com.vau.common.view.sudoku;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.util.Pair;
import android.view.MotionEvent;
import android.view.View;

import androidx.core.content.ContextCompat;

import java.util.ArrayList;
import java.util.List;

import cn.com.vau.R;
import cn.com.vau.util.AppUtil;
import cn.com.vau.util.DistKt;

public class Drawl extends View {

    private int mov_x;
    private int mov_y;
    private Paint paint;
    private Canvas canvas; // 画布
    private Bitmap bitmap; // 位图

    private List<Point> list;
    private List<Pair<Point, Point>> lineList;

    private Point currentPoint;
    private GestureCallBack callBack;

    private StringBuilder passWordSb;

    private String passWord;

    public Drawl(Context context, List<Point> list, GestureCallBack callBack) {
        super(context);
        paint = new Paint(Paint.DITHER_FLAG);
        bitmap = Bitmap.createBitmap(DistKt.getScreenDisplay(context)[0], DistKt.getScreenDisplay(context)[1], Bitmap.Config.ARGB_8888);
        canvas = new Canvas();
        canvas.setBitmap(bitmap);

        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(6);
        paint.setColor(ContextCompat.getColor(context, AppUtil.isLightTheme() ? R.color.c1e1e1e : R.color.cebffffff));
        paint.setAntiAlias(true);

        this.list = list;
        this.lineList = new ArrayList<Pair<Point, Point>>();
        this.callBack = callBack;

        this.passWordSb = new StringBuilder();

    }

    @Override
    protected void onDraw(Canvas canvas) {
        canvas.drawBitmap(bitmap, 0, 0, null);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        try {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:

                    mov_x = (int) event.getX();
                    mov_y = (int) event.getY();

                    currentPoint = getPointAt(mov_x, mov_y);
                    if(currentPoint != null) {
                        currentPoint.setHighLighted(true);
                        passWordSb.append(currentPoint.getNum());
                    }
                    invalidate();
                    break;
                case MotionEvent.ACTION_MOVE:

                    clearScreenAndDrawList();

                    Point pointAt = getPointAt((int) event.getX(), (int) event.getY());

                    if(currentPoint == null && pointAt == null) {
                        return true;
                    } else {
                        if(currentPoint == null) {
                            currentPoint = pointAt;
                            currentPoint.setHighLighted(true);
                            passWordSb.append(currentPoint.getNum());
                        }
                    }

                    if(pointAt == null || currentPoint.equals(pointAt) || pointAt.isHighLighted()) {
                        canvas.drawLine(
                                currentPoint.getCenterX(), currentPoint.getCenterY(),
                                event.getX(), event.getY(),
                                paint
                        );
                    } else {

                        Point testAt = getPointAt(
                                (currentPoint.getCenterX() + pointAt.getCenterX()) / 2,
                                (currentPoint.getCenterY() + pointAt.getCenterY()) / 2
                        );
                        if(testAt != null && !testAt.isHighLighted()) {

                            canvas.drawLine(
                                    currentPoint.getCenterX(), currentPoint.getCenterY(),
                                    testAt.getCenterX(), testAt.getCenterY(),
                                    paint);
                            testAt.setHighLighted(true);

                            Pair<Point, Point> pair = new Pair(currentPoint, testAt);
                            lineList.add(pair);

                            currentPoint = testAt;
                            passWordSb.append(currentPoint.getNum());
                        }

                        canvas.drawLine(
                                currentPoint.getCenterX(), currentPoint.getCenterY(),
                                pointAt.getCenterX(), pointAt.getCenterY(),
                                paint);

                        pointAt.setHighLighted(true);

                        Pair<Point, Point> pair = new Pair(currentPoint, pointAt);
                        lineList.add(pair);

                        currentPoint = pointAt;
                        passWordSb.append(currentPoint.getNum());
                    }
                    invalidate();
                    break;
                case MotionEvent.ACTION_UP:// 当手指抬起的时候
                    callBack.checkedSuccess(passWordSb.toString());
                    passWordSb = new StringBuilder();
                    lineList.clear();
                    clearScreenAndDrawList();
                    for (Point p : list) {
                        p.setHighLighted(false);
                    }
                    invalidate();
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    private Point getPointAt(int x, int y) {

        for (Point point : list) {
            int leftX = point.getLeftX();
            int rightX = point.getRightX();
            if(!(x >= leftX && x < rightX)) {
                continue;
            }

            int topY = point.getTopY();
            int bottomY = point.getBottomY();
            if(!(y >= topY && y < bottomY)) {
                continue;
            }

            return point;
        }

        return null;
    }

    private void clearScreenAndDrawList() {
        canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR);
        for (Pair<Point, Point> pair : lineList) {
            canvas.drawLine(
                    pair.first.getCenterX(), pair.first.getCenterY(),
                    pair.second.getCenterX(), pair.second.getCenterY(),
                    paint);
        }
    }

    public interface GestureCallBack {
        public abstract void checkedSuccess(String password);

        public abstract void checkedFail();
    }

}

