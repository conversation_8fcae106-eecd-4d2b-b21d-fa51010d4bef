package cn.com.vau.common.view.popup

import android.app.Activity
import android.content.Context
import androidx.annotation.Keep
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.os.bundleOf
import androidx.recyclerview.widget.GridLayoutManager
import androidx.viewpager2.widget.ViewPager2
import cn.com.vau.R
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.view.CustomItemDecoration
import cn.com.vau.databinding.DialogBottomKLineNewGuideBinding
import cn.com.vau.util.dp2px
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.widget.dialog.base.BottomDialog
import cn.com.vau.util.widget.dialog.base.IBuilder
import cn.com.vau.util.widget.dialog.base.IDialog
import com.bumptech.glide.Glide
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * Filename: KLineNewGuidePopup
 * Author: GG
 * Date: 2024/5/3
 * Description:
 */
class BottomKLineNewGuideDialog private constructor(context: Context) : BottomDialog<DialogBottomKLineNewGuideBinding>(context, DialogBottomKLineNewGuideBinding::inflate) {

    private var data: MutableList<NewGuideData>? = null

    private val indicatorAdapter: IndicatorAdapter by lazy { IndicatorAdapter() }
    private val dataAdapter: DataAdapter by lazy { DataAdapter() }
    private var currentPosition: Int = 0

    private val buryPointPositionArray by lazy { mutableListOf<Int>() }

    private val vauPUrl by lazy {
        mutableListOf<String>().apply {
            for (i in 3..6) {
                add("https://vau-usa.oss-us-east-1.aliyuncs.com/activity/configure_image/20240527/Webp/$i.webp")
            }
        }
    }

    private val vauDUrl by lazy {
        mutableListOf<String>().apply {
            for (i in 3..6) {
                add("https://app-vau-test.s3.ap-southeast-1.amazonaws.com/activity/configure_image/20240523/$i.webp")
            }
        }
    }

    private val titleList by lazy {
        mutableListOf(
//            context.getString(R.string.select_a_mode),
//            context.getString(R.string.display_up_to_5_trend_lines),
            context.getString(R.string.set_stop_loss_and_take_profit_levels),
            context.getString(R.string.easily_view_and_modify_your_charts),
            context.getString(R.string.set_your_preferred_indicators),
            context.getString(R.string.draw_your_own_charts),
        )
    }

    private val desList by lazy {
        mutableListOf(
//            context.getString(R.string.tap_on_settings_or_to_landscape_mode),
//            context.getString(R.string.choose_which_lines_toggle_remove_them),
            context.getString(R.string.easily_set_and_step_take_step_to_simply_the_x),
            context.getString(R.string.move_back_and_this_available_same_product),
            context.getString(R.string.toggle_between_main_explore_you_extra_lines),
            context.getString(R.string.draw_trendlines_highlight_and_your_charts),
        )
    }

    override fun setContentView() {
        super.setContentView()
        mContentBinding.apply {
            data = if (HttpUrl.official) {
                createData(vauPUrl)
            } else {
                createData(vauDUrl)
            }
            val gridLayoutManager = GridLayoutManager(context, (data?.size ?: 0))
            rvIndicator.layoutManager = gridLayoutManager
            rvIndicator.adapter = indicatorAdapter
            rvIndicator.addItemDecoration(CustomItemDecoration(4.dp2px()))

            indicatorAdapter.setNewInstance(data)
            dataAdapter.setNewInstance(data)
            mViewPager.adapter = dataAdapter
            buryPoint(0)
            mViewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    updateShowStatus(position)
                    buryPoint(position)
                }
            })
            tvNext.setOnClickListener {
                if (currentPosition == ((data?.size ?: 0) - 1)) {
                    dismiss()
                } else {
                    currentPosition++
                    mViewPager.currentItem = currentPosition
                    updateShowStatus(currentPosition)
                }
            }
        }
    }

    private fun createData(urlList: MutableList<String>): MutableList<NewGuideData> {
        return urlList.mapIndexed { index, url ->
            NewGuideData(
                title = titleList.getOrNull(index), imgUrl = url, description = desList.getOrNull(index), isShow = index == 0
            )
        }.toMutableList()
    }

    private fun buryPoint(position: Int) {
        //重复曝光的不算
        if (buryPointPositionArray.contains(position)) return
        LogEventUtil.setLogEvent(BuryPointConstant.V3474.TRADE_KLINE_USER_GUIDE_PAGE_VIEW, bundleOf("Page_name" to "Page${position + 1}"))
        buryPointPositionArray.add(position)
    }

    private fun updateShowStatus(position: Int) {
        currentPosition = position
        data = data?.mapIndexed { index, item ->
            item.apply {
                isShow = index <= position
            }
        }?.toMutableList()

        if (currentPosition == ((data?.size ?: 0) - 1)) {
            mContentBinding.tvNext.text = context.getString(R.string.get_started)
        } else {
            mContentBinding.tvNext.text = context.getString(R.string.next)
        }
        indicatorAdapter.setList(data)
    }

    @Keep
    data class NewGuideData(val title: String? = null, val imgUrl: String? = null, val description: String? = null, var isShow: Boolean = false)

    inner class IndicatorAdapter : BaseQuickAdapter<NewGuideData, BaseViewHolder>(R.layout.item_recycler_k_line_new_guide_indicator) {
        override fun convert(holder: BaseViewHolder, item: NewGuideData) {
            holder.setBackgroundResource(
                R.id.viewLine, if (item.isShow) {
                    R.drawable.draw_shape_c1e1e1e_cebffffff_r100
                } else {
                    R.drawable.draw_shape_c1f1e1e1e_c1fffffff_r100
                }
            )
        }

    }

    inner class DataAdapter : BaseQuickAdapter<NewGuideData, BaseViewHolder>(R.layout.item_recycler_k_line_new_guide) {
        override fun convert(holder: BaseViewHolder, item: NewGuideData) {
            holder.setText(R.id.tvTitle, item.title).setText(R.id.tvData, item.description)

            item.imgUrl?.let {
                if ((context as? Activity)?.isDestroyed == true || (context as? Activity)?.isFinishing == true)
                    return
                val mAnimationView = holder.getView<AppCompatImageView>(R.id.ivData)
                Glide.with(context)
                    .load(it)
                    .into(mAnimationView)
            }
        }
    }

    class Builder(activity: Activity) : IBuilder<DialogBottomKLineNewGuideBinding, Builder>(activity) {
        override fun createDialog(context: Context): IDialog<DialogBottomKLineNewGuideBinding> {
            return BottomKLineNewGuideDialog(activity)
        }

        override fun build(): BottomKLineNewGuideDialog {
            return super.build() as BottomKLineNewGuideDialog
        }
    }
}