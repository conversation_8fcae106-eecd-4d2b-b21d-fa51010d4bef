package cn.com.vau.common.view

import android.content.Context
import android.graphics.*
import android.os.*
import android.text.*
import android.util.*
import android.view.*
import android.view.inputmethod.*
import androidx.core.content.res.ResourcesCompat
import cn.com.vau.R
import cn.com.vau.util.*
import java.util.*

/**
 * Created by Arron on 2016/11/21 0021.
 * 密码输入框
 */
class PasswordView : View {

    var mode: Mode? = null //样式模式
        set(value) {
            field = value
            postInvalidate()
        }
    private var gestureDetector: GestureDetector? = null
    private var passwordLength = 6 //密码个数
    private var cursorFlashTime = 500 //光标闪动间隔时间
    private var passwordPadding = 0 //每个密码间的间隔
    private var passwordSize = dp2px(40f) //单个密码大小
    private var borderColor = AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff) //边框颜色
    private var borderWidth = dp2px(0.75f) //下划线粗细
    private var cursorPosition = 0 //光标位置
    private var cursorWidth = dp2px(2f) //光标粗细
    private var cursorHeight = 0 //光标长度
    private var cursorColor = context.getColor(R.color.ce35728) //光标颜色
    private var passwordColor = AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff) //密码文字颜色
    private var rectFillColor = AttrResourceUtil.getColor(context, R.attr.color_c0a1e1e1e_c262930) //rect模式填充颜色
    private var isCursorShowing = false //光标是否正在显示
    private var isCursorEnable = false //是否开启光标
    private var isInputComplete = false //是否输入完毕
    private var cipherTextSize = 0 //密文符号大小
    private var cipherEnable = false //是否开启密文
    private var password: MutableList<String?> = mutableListOf()//密码数组
    private var inputManager: InputMethodManager? = null
    private var passwordListener: PasswordListener? = null
    private val paint: Paint by lazy { Paint() }
    private var timer: Timer? = null
    private var timerTask: TimerTask? = null
    private var customTypeface: Typeface? = null
    private var rectIsFill: Boolean = true
    private var rectRound: Float = 10f.dp2px()

    constructor(context: Context?) : super(context)

    enum class Mode(val mode: Int) {
        /**
         * 下划线样式
         */
        UNDERLINE(0),

        /**
         * 边框样式
         */
        RECT(1);

        companion object {
            fun formMode(mode: Int): Mode {
                for (m in values()) {
                    if (mode == m.mode) {
                        return m
                    }
                }
                throw IllegalArgumentException()
            }
        }
    }

    /**
     * 当前只支持从xml中构建该控件
     *
     * @param context
     * @param attrs
     */
    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        readAttribute(attrs)
    }

    private fun readAttribute(attrs: AttributeSet?) {
        if (attrs != null) {
            val typedArray = context.obtainStyledAttributes(attrs, R.styleable.PasswordView)
            mode = Mode.formMode(typedArray.getInteger(R.styleable.PasswordView_mode, Mode.RECT.mode))
            passwordLength = typedArray.getInteger(R.styleable.PasswordView_passwordLength, passwordLength)
            cursorFlashTime = typedArray.getInteger(R.styleable.PasswordView_cursorFlashTime, cursorFlashTime)
            borderWidth = typedArray.getDimensionPixelSize(R.styleable.PasswordView_borderWidth, borderWidth)
            cursorWidth = typedArray.getDimensionPixelSize(R.styleable.PasswordView_cursorWidth, cursorWidth)
            borderColor = typedArray.getColor(R.styleable.PasswordView_borderColor, borderColor)
            passwordColor = typedArray.getColor(R.styleable.PasswordView_passwordColor, passwordColor)
            cursorColor = typedArray.getColor(R.styleable.PasswordView_cursorColor, cursorColor)
            rectFillColor = typedArray.getColor(R.styleable.PasswordView_rectFillColor, rectFillColor)
            isCursorEnable = typedArray.getBoolean(R.styleable.PasswordView_isCursorEnable, true)
            rectIsFill = typedArray.getBoolean(R.styleable.PasswordView_rectIsFill, rectIsFill)
            rectRound = typedArray.getDimensionPixelSize(R.styleable.PasswordView_rectRound, rectRound.toInt()).toFloat()
            //如果为边框样式，则padding 默认置为0
            passwordPadding = if (mode == Mode.UNDERLINE) {
                typedArray.getDimensionPixelSize(R.styleable.PasswordView_passwordPadding, dp2px(15f))
            } else {
                typedArray.getDimensionPixelSize(R.styleable.PasswordView_passwordPadding, dp2px(12f))
            }
            cipherEnable = typedArray.getBoolean(R.styleable.PasswordView_cipherEnable, cipherEnable)
            typedArray.recycle()
        }
        init()
    }

    private fun init() {
        gestureDetector = GestureDetector(context, GestureListener())
        setFocusableInTouchMode(true)
        val MyKeyListener = MyKeyListener()
        setOnKeyListener(MyKeyListener)
        inputManager = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        customTypeface = ResourcesCompat.getFont(context, R.font.gilroy_semi_bold)
        paint.setTypeface(customTypeface)
        paint.isAntiAlias = true
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val widthMode = MeasureSpec.getMode(widthMeasureSpec)
        var width = 0
        when (widthMode) {
            MeasureSpec.UNSPECIFIED, MeasureSpec.AT_MOST ->                 //没有指定大小，宽度 = 单个密码框大小 * 密码位数 + 密码框间距 *（密码位数 - 1）
                width = passwordSize * passwordLength + passwordPadding * (passwordLength - 1)

            MeasureSpec.EXACTLY -> {
                //指定大小，宽度 = 指定的大小
                width = MeasureSpec.getSize(widthMeasureSpec)
                //密码框大小等于 (宽度 - 密码框间距 *(密码位数 - 1)) / 密码位数
                passwordSize = (width - passwordPadding * (passwordLength - 1)) / passwordLength
            }
        }
        setMeasuredDimension(width, passwordSize)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        //文本大小
        cipherTextSize = dp2px(18f)
        //光标宽度
//        cursorWidth = 3;
        //光标长度
        cursorHeight = passwordSize / 2
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (mode == Mode.UNDERLINE) {
            //绘制下划线
            drawUnderLine(canvas, paint)
        } else {
            //绘制方框
            drawRect(canvas, paint)
        }
        //绘制光标
        drawCursor(canvas, paint)
        //绘制密码文本
        drawCipherText(canvas, paint)
    }

    internal inner class MyKeyListener : OnKeyListener {
        override fun onKey(v: View, keyCode: Int, event: KeyEvent): Boolean {
            val action = event.action
            if (action == KeyEvent.ACTION_MULTIPLE && keyCode == KeyEvent.KEYCODE_UNKNOWN) {
                copyFromClip()
                return true
            }
            if (action == KeyEvent.ACTION_DOWN) {
                if (keyCode == KeyEvent.KEYCODE_DEL) {
                    /**
                     * 删除操作
                     */
                    if (TextUtils.isEmpty(password.getOrNull(0))) {
                        return true
                    }
                    val deleteText = delete()
                    if (passwordListener != null && !TextUtils.isEmpty(deleteText)) {
                        passwordListener?.passwordChange(deleteText)
                    }
                    postInvalidate()
                    return true
                }
                if (keyCode >= KeyEvent.KEYCODE_0 && keyCode <= KeyEvent.KEYCODE_9) {
                    /**
                     * 只支持数字
                     */
                    if (isInputComplete) {
                        return true
                    }
                    val addText = add((keyCode - 7).toString() + "")
                    if (passwordListener != null && !TextUtils.isEmpty(addText)) {
                        passwordListener?.passwordChange(addText)
                    }
                    postInvalidate()
                    return true
                }
                if (keyCode == KeyEvent.KEYCODE_PASTE) {
                    copyFromClip()
                    return true
                }
                if (keyCode == KeyEvent.KEYCODE_ENTER) {
                    /**
                     * 确认键
                     */
                    if (passwordListener != null) {
                        passwordListener?.keyEnterPress(getPassword(), isInputComplete)
                    }
                    return true
                }
            }
            return false
        }
    }

    /**
     * 删除
     */
    private fun delete(): String? {
        var deleteText: String? = null
        if (cursorPosition > 0) {
            deleteText = password.getOrNull(cursorPosition - 1)
            password[cursorPosition - 1] = null
            cursorPosition--
        } else if (cursorPosition == 0) {
            deleteText = password.getOrNull(cursorPosition)
            password[cursorPosition] = null
        }
        isInputComplete = false
        return deleteText
    }

    /**
     * 增加
     */
    private fun add(c: String): String? {
        var addText: String? = null
        if (cursorPosition < passwordLength) {
            addText = c
            password.add(cursorPosition, c)
            cursorPosition++
            if (cursorPosition == passwordLength) {
                isInputComplete = true
                if (passwordListener != null) {
                    passwordListener?.passwordComplete()
                }
            }
        }
        return addText
    }

    /**
     * 获取密码
     */
    fun getPassword(): String {
        val stringBuffer = StringBuffer()
        for (c in password) {
            if (TextUtils.isEmpty(c)) {
                continue
            }
            stringBuffer.append(c)
        }
        return stringBuffer.toString()
    }

    /**
     * 绘制密码替代符号
     *
     * @param canvas
     * @param paint
     */
    private fun drawCipherText(canvas: Canvas, paint: Paint) {
        //画笔初始化
        paint.setColor(passwordColor)
        paint.textSize = cipherTextSize.toFloat()
        paint.textAlign = Paint.Align.CENTER
        paint.style = Paint.Style.FILL
        // 文字居中的处理
        val r = Rect()
        canvas.getClipBounds(r)
        val cHeight = r.height()
        paint.getTextBounds(CIPHER_TEXT, 0, CIPHER_TEXT.length, r)
        //        float y = cHeight / 2f + r.height() / 2f - r.bottom;
        val y = cHeight / 2f - r.bottom

        //根据输入的密码位数，进行for循环绘制
        for (i in password.indices) {
            if (!TextUtils.isEmpty(password[i])) {
                // x = paddingLeft + 单个密码框大小/2 + ( 密码框大小 + 密码框间距 ) * i
                // y = paddingTop + 文字居中所需偏移量
                if (cipherEnable) {
                    //没有开启明文显示，绘制密码密文
                    if (layoutDirection == LayoutDirection.RTL) {
                        canvas.drawText(
                            CIPHER_TEXT,
                            (getPaddingLeft() + passwordSize / 2 + (passwordSize + passwordPadding) * (passwordLength - i - 1)).toFloat(),
                            paddingTop + y, paint
                        )
                    } else {
                        canvas.drawText(
                            CIPHER_TEXT,
                            (getPaddingLeft() + passwordSize / 2 + (passwordSize + passwordPadding) * i).toFloat(),
                            paddingTop + y, paint
                        )
                    }

                } else {
                    //明文显示，直接绘制密码
                    if (layoutDirection == LayoutDirection.RTL) {
                        canvas.drawText(
                            password.getOrNull(i) ?: "",
                            (getPaddingLeft() + passwordSize / 2 + (passwordSize + passwordPadding) * (passwordLength - i - 1)).toFloat(),
                            paddingTop + y,
                            paint
                        )
                    } else {
                        canvas.drawText(
                            password.getOrNull(i) ?: "",
                            (getPaddingLeft() + passwordSize / 2 + (passwordSize + passwordPadding) * i).toFloat(),
                            paddingTop + y,
                            paint
                        )
                    }

                }
            }
        }
    }

    /**
     * 绘制光标
     *
     * @param canvas
     * @param paint
     */
    private fun drawCursor(canvas: Canvas, paint: Paint) {
        //画笔初始化
        //光标未显示 && 开启光标 && 输入位数未满 && 获得焦点
        if (isCursorEnable && !isInputComplete && hasFocus()) {
            // 起始点x = paddingLeft + 单个密码框大小 / 2 + (单个密码框大小 + 密码框间距) * 光标下标
            // 起始点y = paddingTop + (单个密码框大小 - 光标大小) / 2
            // 终止点x = 起始点x
            // 终止点y = 起始点y + 光标高度 passwordSize / 2
//            canvas.drawLine(
//                    (getPaddingLeft() + passwordSize / 2) + (passwordSize + passwordPadding) * cursorPosition,
//                    getPaddingTop() + (passwordSize - cursorHeight) / 2,
//                    (getPaddingLeft() + passwordSize / 2) + (passwordSize + passwordPadding) * cursorPosition,
//                    getPaddingTop() + (passwordSize + cursorHeight) / 2,
//                    paint
//            );
            // 只有中间光标闪烁
            if (!isCursorShowing) {
                // 绘制输入框中间的光标
                paint.setColor(cursorColor)
                paint.strokeWidth = cursorWidth.toFloat()
                paint.style = Paint.Style.FILL
                val x = if (layoutDirection == LayoutDirection.RTL) {
                    val rtlX = width - (paddingEnd + passwordSize / 2) - (passwordSize + passwordPadding) * cursorPosition
                    rtlX.toFloat()
                } else {
                    (paddingStart + passwordSize / 2) + (passwordSize + passwordPadding) * cursorPosition.toFloat()
                }
                canvas.drawLine(
                    x,
                    (paddingTop + (passwordSize - cursorHeight) / 2).toFloat(),
                    x,
                    (paddingTop + (passwordSize + cursorHeight) / 2).toFloat(),
                    paint
                )
            }

            // 绘制底部或边框光标
            if (mode == Mode.UNDERLINE) {
                drawUnderlineCursor(canvas, paint)
            } else {
                drawRectCursor(canvas, paint)
            }
        }
    }

    private fun drawUnderlineCursor(canvas: Canvas, paint: Paint) {
        paint.setColor(borderColor)
        paint.strokeWidth = borderWidth.toFloat()
        paint.style = Paint.Style.FILL
        if (layoutDirection == LayoutDirection.RTL) {
            canvas.drawLine(
                (getPaddingLeft() + (passwordSize + passwordPadding) * (passwordLength - cursorPosition - 1)).toFloat(),
                (paddingTop + passwordSize).toFloat(),
                (getPaddingLeft() + (passwordSize + passwordPadding) * (passwordLength - cursorPosition - 1) + passwordSize).toFloat(),
                (paddingTop + passwordSize).toFloat(),
                paint
            )
        } else {
            canvas.drawLine(
                (getPaddingLeft() + (passwordSize + passwordPadding) * cursorPosition).toFloat(),
                (paddingTop + passwordSize).toFloat(),
                (getPaddingLeft() + (passwordSize + passwordPadding) * cursorPosition + passwordSize).toFloat(),
                (paddingTop + passwordSize).toFloat(),
                paint
            )
        }
    }

    private fun drawRectCursor(canvas: Canvas, paint: Paint) {
        paint.setColor(borderColor)
        paint.strokeWidth = borderWidth.toFloat()
        val halfStrokeWidth = borderWidth.toFloat() / 2
        paint.style = Paint.Style.STROKE
        if (layoutDirection == LayoutDirection.RTL) {
            canvas.drawRoundRect(
                (getPaddingLeft() + halfStrokeWidth + (passwordSize + passwordPadding) * (passwordLength - cursorPosition - 1)),
                paddingTop + halfStrokeWidth,
                (getPaddingLeft() - halfStrokeWidth + (passwordSize + passwordPadding) * (passwordLength - cursorPosition - 1) + passwordSize),
                (paddingTop + passwordSize - halfStrokeWidth), rectRound, rectRound,
                paint
            )
        } else {
            canvas.drawRoundRect(
                (getPaddingLeft() + halfStrokeWidth + (passwordSize + passwordPadding) * cursorPosition),
                paddingTop + halfStrokeWidth,
                (getPaddingLeft() - halfStrokeWidth + (passwordSize + passwordPadding) * cursorPosition + passwordSize),
                (paddingTop + passwordSize - halfStrokeWidth), rectRound, rectRound,
                paint
            )
        }
    }

    /**
     * 绘制密码框下划线
     *
     * @param canvas
     * @param paint
     */
    private fun drawUnderLine(canvas: Canvas, paint: Paint) {
        //画笔初始化
        paint.setColor(borderColor)
        paint.strokeWidth = borderWidth.toFloat()
        paint.style = Paint.Style.FILL
        for (i in 0 until passwordLength) {
            //根据密码位数for循环绘制直线
            // 起始点x为paddingLeft + (单个密码框大小 + 密码框边距) * i , 起始点y为paddingTop + 单个密码框大小
            // 终止点x为 起始点x + 单个密码框大小 , 终止点y与起始点一样不变
            canvas.drawLine(
                (getPaddingLeft() + (passwordSize + passwordPadding) * i).toFloat(), (paddingTop + passwordSize).toFloat(),
                (getPaddingLeft() + (passwordSize + passwordPadding) * i + passwordSize).toFloat(), (paddingTop + passwordSize).toFloat(),
                paint
            )
        }
    }

    private fun drawRect(canvas: Canvas, paint: Paint) {
        paint.setColor(rectFillColor)
        paint.strokeWidth = 3f
        paint.style = if (rectIsFill) Paint.Style.FILL else Paint.Style.STROKE
        //        RectF rect;
        for (i in 0 until passwordLength) {
            var startX = getPaddingLeft() + (passwordSize + passwordPadding) * i
            if (i == 0) startX = (startX + 1.5f).toInt()
            val startY = paddingTop
            val stopX = getPaddingLeft() + (passwordSize + passwordPadding) * i + passwordSize
            val stopY = paddingTop + passwordSize
            //            rect = new RectF(startX, startY, stopX-2.5f, stopY-2.5f);
//            canvas.drawRect(rect, paint);
            canvas.drawRoundRect(startX.toFloat(), startY + 1.5f, stopX - 1.5f, stopY - 1.5f, rectRound, rectRound, paint)
        }
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        try {
            gestureDetector?.onTouchEvent(event)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return true
    }

    override fun onWindowFocusChanged(hasWindowFocus: Boolean) {
        super.onWindowFocusChanged(hasWindowFocus)
        if (!hasWindowFocus) {
            inputManager?.hideSoftInputFromWindow(this.windowToken, 0)
        }
    }

    fun showSoftInput() {
        requestFocus()
        inputManager?.showSoftInput(this, 0)
    }

    fun hiddenSoftInputFromWindow() {
        inputManager?.hideSoftInputFromWindow(this.windowToken, 0)
    }

    fun clearInput() {
        password.clear()
        isInputComplete = false
        cursorPosition = 0
        invalidate()
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        //cursorFlashTime为光标闪动的间隔时间
        timerTask = object : TimerTask() {
            override fun run() {
                isCursorShowing = !isCursorShowing
                postInvalidate()
            }
        }
        timer = Timer()
        timer?.scheduleAtFixedRate(timerTask, 0, cursorFlashTime.toLong())
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        //        timerTask.cancel();
//        timer.cancel();
        if (timer != null) {
            timerTask?.cancel()
            timer?.cancel()
            timer?.purge()
            timer = null
        }
    }

    private fun dp2px(dp: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (dp * scale + 0.5f).toInt()
    }

    private fun sp2px(spValue: Float): Int {
        val fontScale = context.resources.displayMetrics.scaledDensity
        return (spValue * fontScale + 0.5f).toInt()
    }

    override fun onCreateInputConnection(outAttrs: EditorInfo): InputConnection {
        //Passing FALSE as the SECOND ARGUMENT (fullEditor) to the constructor
        // will result in the key events continuing to be passed in to this
        // view.  Use our special BaseInputConnection-derived view
        val baseInputConnection = InputConnectionAccomodatingLatinIMETypeNullIssues(this, false)

        //In some cases an IME may be able to display an arbitrary label for a
        // command the user can perform, which you can specify here.  A null value
        // here asks for the default for this key, which is usually something
        // like Done.
        outAttrs.actionLabel = null

        //Special content type for when no explicit type has been specified.
        // This should be interpreted (by the IME that invoked
        // onCreateInputConnection())to mean that the target InputConnection
        // is not rich, it can not process and show things like candidate text
        // nor retrieve the current text, so the input method will need to run
        // in a limited "generate key events" mode.  This disables the more
        // sophisticated kinds of editing that use a text buffer.
        outAttrs.inputType = InputType.TYPE_CLASS_NUMBER

        //This creates a Done key on the IME keyboard if you need one
        outAttrs.imeOptions = EditorInfo.IME_ACTION_DONE
        return baseInputConnection

//        outAttrs.inputType = InputType.TYPE_CLASS_NUMBER; //输入类型为数字
//        return super.onCreateInputConnection(outAttrs);
    }

    fun setPasswordListener(passwordListener: PasswordListener?) {
        this.passwordListener = passwordListener
    }

    fun setPasswordSize(passwordSize: Int) {
        this.passwordSize = passwordSize
        postInvalidate()
    }

    fun setPasswordLength(passwordLength: Int) {
        this.passwordLength = passwordLength
        postInvalidate()
    }

    fun setCursorColor(cursorColor: Int) {
        this.cursorColor = cursorColor
        postInvalidate()
    }

    fun setCursorEnable(cursorEnable: Boolean) {
        isCursorEnable = cursorEnable
        postInvalidate()
    }

    fun setCipherEnable(cipherEnable: Boolean) {
        this.cipherEnable = cipherEnable
        postInvalidate()
    }

    /**
     * 密码监听者
     */
    interface PasswordListener {
        /**
         * 输入/删除监听
         *
         * @param changeText 输入/删除的字符
         */
        fun passwordChange(changeText: String?)

        /**
         * 输入完成
         */
        fun passwordComplete()

        /**
         * 确认键后的回调
         *
         * @param password   密码
         * @param isComplete 是否达到要求位数
         */
        fun keyEnterPress(password: String?, isComplete: Boolean)
    }

    override fun onSaveInstanceState(): Parcelable? {
        val bundle = Bundle()
        bundle.putParcelable("superState", super.onSaveInstanceState())
        bundle.putStringArray("password", password.toTypedArray())
        bundle.putInt("cursorPosition", cursorPosition)
        return bundle
    }

    override fun onRestoreInstanceState(state: Parcelable) {
        var state: Parcelable? = state
        if (state is Bundle) {
            val bundle = state
            password = bundle.getStringArray("password")?.toMutableList() ?: mutableListOf()
            cursorPosition = bundle.getInt("cursorPosition")
            state = bundle.getParcelable("superState")
        }
        super.onRestoreInstanceState(state)
    }

    private fun copyFromClip() {
        //如果剪切板内不是6个就不进行复制
        if (ClipBoardUtil.paste().trim().length != 6)
            return
        clearInput()
        ClipBoardUtil.paste()
            .trim()
            .takeWhile { RegexUtil.isNumeric(it.toString()) }
            .take(6)
            .forEach { c ->
                add(c.toString())
            }
    }

    inner class GestureListener : GestureDetector.SimpleOnGestureListener() {
        override fun onLongPress(e: MotionEvent) {
            //剪切板上有内容才弹窗
//            if (ClipBoardUtil.paste().trim().length == 6) {
//                val locations = IntArray(2)
//                <EMAIL>(locations)
//                XPopup.Builder(context)
//                    .popupPosition(PopupPosition.Left)
//                    .atPoint(PointF(locations[0].toFloat(), locations[1].toFloat()))
//                    .offsetY(<EMAIL> + 20)
//                    .isViewMode(true)
//                    .hasShadowBg(false)
//                    .asCustom(CopyPopup(context).setClick {
//                        copyFromClip()
//                    })
//                    .show()
//            }
        }

        override fun onDown(e: MotionEvent): Boolean {

            /**
             * 弹出软键盘
             */
            requestFocus()
            inputManager?.showSoftInput(this@PasswordView, InputMethodManager.SHOW_FORCED)
            return true
        }
    }

    companion object {
        private const val CIPHER_TEXT = "*" //密文符号
    }
}
