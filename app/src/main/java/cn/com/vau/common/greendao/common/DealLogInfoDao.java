package cn.com.vau.common.greendao.common;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import cn.com.vau.common.greendao.dbUtils.DealLogInfo;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "DEAL_LOG_INFO".
*/
public class DealLogInfoDao extends AbstractDao<DealLogInfo, Long> {

    public static final String TABLENAME = "DEAL_LOG_INFO";

    /**
     * Properties of entity DealLogInfo.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "_id");
        public final static Property TimeStamp = new Property(1, String.class, "timeStamp", false, "TIME_STAMP");
        public final static Property Date = new Property(2, String.class, "date", false, "DATE");
        public final static Property Log = new Property(3, String.class, "log", false, "LOG");
        public final static Property Tel = new Property(4, String.class, "tel", false, "TEL");
    }


    public DealLogInfoDao(DaoConfig config) {
        super(config);
    }
    
    public DealLogInfoDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"DEAL_LOG_INFO\" (" + //
                "\"_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"TIME_STAMP\" TEXT," + // 1: timeStamp
                "\"DATE\" TEXT," + // 2: date
                "\"LOG\" TEXT," + // 3: log
                "\"TEL\" TEXT);"); // 4: tel
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"DEAL_LOG_INFO\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, DealLogInfo entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String timeStamp = entity.getTimeStamp();
        if (timeStamp != null) {
            stmt.bindString(2, timeStamp);
        }
 
        String date = entity.getDate();
        if (date != null) {
            stmt.bindString(3, date);
        }
 
        String log = entity.getLog();
        if (log != null) {
            stmt.bindString(4, log);
        }
 
        String tel = entity.getTel();
        if (tel != null) {
            stmt.bindString(5, tel);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, DealLogInfo entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String timeStamp = entity.getTimeStamp();
        if (timeStamp != null) {
            stmt.bindString(2, timeStamp);
        }
 
        String date = entity.getDate();
        if (date != null) {
            stmt.bindString(3, date);
        }
 
        String log = entity.getLog();
        if (log != null) {
            stmt.bindString(4, log);
        }
 
        String tel = entity.getTel();
        if (tel != null) {
            stmt.bindString(5, tel);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public DealLogInfo readEntity(Cursor cursor, int offset) {
        DealLogInfo entity = new DealLogInfo( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // timeStamp
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // date
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // log
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4) // tel
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, DealLogInfo entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setTimeStamp(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setDate(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setLog(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setTel(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(DealLogInfo entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(DealLogInfo entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(DealLogInfo entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
