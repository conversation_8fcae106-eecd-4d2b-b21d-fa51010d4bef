package cn.com.vau.common.utils

import android.content.Intent
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.common.view.dialog.CommonProcessDialog
import cn.com.vau.common.view.dialog.GenericDialog
import cn.com.vau.data.account.AsicQuestionData
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.page.user.question.AsicQuestionnaireActivity
import cn.com.vau.util.LogUtil
import cn.com.vau.util.widget.dialog.CenterActionDialog
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.launch

/**
 * Filename: AsicQuestionUtil
 * Author: GG
 * Date: 2024/7/29
 * Description:
 */
class AsicQuestionUtil(private val activity: FragmentActivity) {

    private var loadNetDialog: CommonProcessDialog? = null

    private fun showNetDialog() {
        try {
            if (loadNetDialog == null) {
                loadNetDialog = CommonProcessDialog(activity)
            }
            if (loadNetDialog?.isShowing == false) {
                loadNetDialog?.show()
            }
        } catch (e: Exception) {
            LogUtil.i("BaseFragment --- showNetDialog() --- " + e.message)
        }
    }

    private fun hideNetDialog() {
        if (loadNetDialog != null && loadNetDialog?.isShowing == true)
            loadNetDialog?.dismiss()
    }

    /**
     * 展示 跳转 调查问券页面的弹窗， 并且有跳转h5页面的title
     */
    fun showQuestionDialog() {
        // 此弹窗为标题+叹号图标的，问过超哥，目前弹窗暂不支持，也去找Shelly Huang和一个负责弹窗UI的leader确认，结果为暂不修改
        GenericDialog.Builder()
            .setTitle(activity.getString(R.string.asic_questionnaire))
            .setTitleEndRes(R.drawable.draw_bitmap2_info14x14_c731e1e1e_c61ffffff)
            .setTitleEndResClick {
                NewHtmlActivity.openActivity(activity, url = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/nativeTitle/pro/tc")
            }
            .setShowCloseIcon(true)
            .setDetail(activity.getString(R.string.please_click_get_suitability_test))
            .setIsOneButton(true)
            .setOneButtonText(activity.getString(R.string.get_started))
            .setOneButtonClick {
                requestLimitNumberOfAnswers()
            }
            .show(activity)
    }

    /**
     * 请求是否可以跳转调查问券页面，并添加回调方法，没有的话默认跳转账户列表页面
     */
    private fun requestLimitNumberOfAnswers(passCallback: (() -> Unit)? = null) {
        activity.lifecycleScope.launch(CoroutineExceptionHandler { _, _ ->
            run {
                hideNetDialog()
            }
        }) {
            showNetDialog()
            val data: ApiResponse<AsicQuestionData> = baseService.limitNumberOfAnswers(UserDataUtil.userId())

            hideNetDialog()
            if (data.data?.obj?.answer == true) {
                if (passCallback == null) {
                    val intent = Intent(activity, AsicQuestionnaireActivity::class.java)
                    activity.startActivityForResult(intent, Constants.SUBMIT_SUCCESS)
                } else {
                    passCallback.invoke()
                }
            } else {
                val dialog = CenterActionDialog.Builder(activity)
                    .setTitle(activity.getString(R.string.you_have_not_passed_the_questionnaire))
                    .setSingleButton(true)
                    .setSingleButtonText(activity.getString(R.string.confirm))
                    .setOnSingleButtonListener {
                        jump()
                    }
                    .build()
                val contentTextView = dialog.getContentViewBinding().tvDetail
                val wholeText = data.data?.obj?.incorrectAnswerPrompt ?: ""
                val spannableString = SpannableString(wholeText)
                val span = data.data?.obj?.jumpLink ?: ""
                val index = wholeText.indexOf(span)
                if (index != -1 && (index + span.length) <= wholeText.length) {
                    spannableString.setSpan(object : ClickableSpan() {
                        override fun onClick(widget: View) {
                            dialog.dismissDialog()
                            jump()
                        }

                        override fun updateDrawState(ds: TextPaint) {
                            ds.isUnderlineText = true
                            ds.color = ContextCompat.getColor(activity, R.color.ce35728)
                        }
                    }, index, (index + span.length), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                    contentTextView.movementMethod = LinkMovementMethod.getInstance()
                    contentTextView.highlightColor = ContextCompat.getColor(activity, R.color.transparent)
                }
                (dialog as CenterActionDialog).setContent(spannableString)
                dialog.showDialog()
            }
        }

    }

    private fun jump() {
        if (activity !is AccountManagerActivity) {
            val intent = Intent(activity, AccountManagerActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
            }
            activity.startActivity(intent)
            activity.finish()
        }
    }
}