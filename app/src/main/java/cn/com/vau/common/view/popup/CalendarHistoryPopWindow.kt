package cn.com.vau.common.view.popup

import android.annotation.SuppressLint
import android.content.Context
import android.view.*
import android.widget.PopupWindow
import cn.com.vau.R
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.timeSelection.PickerDateUtil
import cn.com.vau.databinding.PopCalendarOrderHistoryBinding
import cn.com.vau.ui.common.*
import cn.com.vau.util.*
import java.util.Calendar

/**
 * Created by roy on 2018/12/5.
 * 选择日期 -- 历史订单
 */
class CalendarHistoryPopWindow(val context: Context) : PopupWindow() {

    private var adapter: PopHistoryOrderCalendarRcyAdapter? = null
    private val dataList by lazy { mutableListOf<CalendarPopData>() }
    private val popView by lazy { PopCalendarOrderHistoryBinding.inflate(LayoutInflater.from(context)) }

    init {
        initParam()
        initView()
        initListener()
    }

    private fun initParam() {
        this.contentView = popView.root
        this.width = screenWidth
        this.height = ViewGroup.LayoutParams.MATCH_PARENT
        // 设置PopupWindow弹出窗体可点击
        this.isFocusable = true
        this.animationStyle = R.style.popupAnimStyleBottom

        initDateData()

    }

    @SuppressLint("CheckResult")
    private fun initDateData() {

        dataList.clear()

        val currentDateStr = PickerDateUtil.longTimeToString(System.currentTimeMillis(), "dd/MM/yyyy")

        val twoDateBean = CalendarUtil.getInstance().getMonthDiffBefore(currentDateStr, -12)

        val tLastDay = CalendarUtil.getInstance().getMonthLastDay(twoDateBean.year, twoDateBean.mon)
        initTwoMonthsAgoDate(twoDateBean, tLastDay)

        var oLastDay = CalendarUtil.getInstance().getMonthLastDay(twoDateBean.year, twoDateBean.mon)

        for (index in -11..-1) {
            val forOneDateBean = CalendarUtil.getInstance().getMonthDiffBefore(currentDateStr, index)
            val forOLastDay = CalendarUtil.getInstance().getMonthLastDay(forOneDateBean.year, forOneDateBean.mon)

            if (index == -1)
                oLastDay = CalendarUtil.getInstance()
                    .getMonthLastDay(forOneDateBean.year, forOneDateBean.mon)

            // 周 MTWTFSS
            val forOList = getWeekList()
            val forOFirstDayWeek = CalendarUtil.getInstance()
                .getDayOfWeek(
                    "01/${forOneDateBean.mon.fill0()}/${forOneDateBean.year}",
                    "dd/MM/yyyy"
                )
            if (forOFirstDayWeek != 1) {
                var fillDate = tLastDay - forOFirstDayWeek + 1 + 1
                for (weekIndex in 1 until forOFirstDayWeek) {
                    forOList.add(DateEntity(-1, fillDate.toString()))
                    fillDate++
                }
            }
            for (tIndex in 1..forOLastDay) {
                val likeDateStr =
                    "${tIndex.fill0()}/${forOneDateBean.mon.fill0()}/${forOneDateBean.year}"
                forOList.add(
                    DateEntity(
                        0, tIndex.toString(), likeDateStr
                    )
                )
            }
            val forOLastDayWeek = CalendarUtil.getInstance()
                .getDayOfWeek(
                    "$forOLastDay/${forOneDateBean.mon.fill0()}/${forOneDateBean.year}",
                    "dd/MM/yyyy"
                )
            if (forOLastDayWeek != 7) {
                var fillDate = 1
                for (weekIndex in forOLastDayWeek + 1..7) {
                    forOList.add(DateEntity(-1, fillDate.toString()))
                    fillDate++
                }
            }
            dataList.add(CalendarPopData(CalendarUtil.getInstance().getFormatYearMon(forOneDateBean), forOList))
        }

        val currDateBean = CalendarUtil.getInstance().getCurrentMonthFirstDay()
        val currentMonthFirstDayOfWeek = CalendarUtil.getInstance().getCurrentMonthFirstDayOfWeek()

        val cList = getWeekList()
        if (currentMonthFirstDayOfWeek != 1) {
            var fillDate = oLastDay - currentMonthFirstDayOfWeek + 1 + 1
            for (weekIndex in 1 until currentMonthFirstDayOfWeek) {
                cList.add(DateEntity(-1, fillDate.toString()))
                fillDate++
            }
        }
        for (index in 1..Calendar.getInstance().get(Calendar.DAY_OF_MONTH)) {
            val likeDateStr = "${index.fill0()}/${currDateBean.mon.fill0()}/${currDateBean.year}"
            cList.add(DateEntity(0, index.toString(), likeDateStr))
        }

        dataList.add(
            CalendarPopData(
                CalendarUtil.getInstance().getFormatYearMon(currDateBean),
                cList
            )
        )

    }

    private fun initTwoMonthsAgoDate(twoDateBean: LikeDate, tLastDay: Int) {

        if (twoDateBean.day == tLastDay) return

        twoDateBean.day += 1

        val tList = getWeekList()

        val tFirstDayWeek =
            CalendarUtil.getInstance().getDayOfWeek(twoDateBean.likeDate, "dd/MM/yyyy") + 1
        if (tFirstDayWeek != 1) {
            for (weekIndex in 1 until tFirstDayWeek) tList.add(DateEntity(""))
        }
        for (index in twoDateBean.day..tLastDay) {
            val likeDateStr = "${index.fill0()}/${twoDateBean.mon.fill0()}/${twoDateBean.year}"
            tList.add(DateEntity(0, index.toString(), likeDateStr))
        }
        val tLastDayWeek = CalendarUtil.getInstance()
            .getDayOfWeek("$tLastDay/${twoDateBean.mon.fill0()}/${twoDateBean.year}", "dd/MM/yyyy")
        if (tLastDayWeek != 7) {
            var fillDate = 1
            for (weekIndex in tLastDayWeek + 1..7) {
                tList.add(DateEntity(-1, fillDate.toString()))
                fillDate++
            }
        }
        // 年月
        dataList.add(
            CalendarPopData(
                CalendarUtil.getInstance().getFormatYearMon(twoDateBean),
                tList
            )
        )
    }

    private fun getWeekList() = arrayListOf<DateEntity>().apply {
        add(DateEntity("MON"))
        add(DateEntity("TUE"))
        add(DateEntity("WED"))
        add(DateEntity("THU"))
        add(DateEntity("FRI"))
        add(DateEntity("SAT"))
        add(DateEntity("SUN"))
    }

    private fun initView() {
        val linearLayoutManager = WrapContentLinearLayoutManager(context)
        linearLayoutManager.stackFromEnd = true
        popView.rcvCalendar.layoutManager = linearLayoutManager
        adapter = PopHistoryOrderCalendarRcyAdapter(context, dataList)
        popView.rcvCalendar.adapter = adapter
        popView.rcvCalendar.scrollToPosition(adapter?.itemCount ?: (1 - 1))
    }

    private var startPosition = -1
    private var startItemPosition = -1
    private var endPosition = -1
    private var endItemPosition = -1

    private fun initListener() {
        popView.mHeaderBar.setStartBackIconClickListener {
            dismiss()
        }

        adapter?.setOnItemClickListener(object :
            PopHistoryOrderCalendarRcyAdapter.OnItemClickListener {
            @SuppressLint("NotifyDataSetChanged")
            override fun onItemClick(position: Int, itemPosition: Int) {
                // start已选  end未选
                if (startPosition == -1) {
                    setStartDataPosition(position, itemPosition)
                    return
                }
                // start  end 都选中
                if (startPosition != -1 && endPosition == -1) {
                    if (position == startPosition) {
                        if (itemPosition == startItemPosition) return
                        if (itemPosition < startItemPosition) {
                            clearDataListType()
                            setStartDataPosition(position, itemPosition)
                            return
                        }
                        if (itemPosition > startItemPosition) {
                            setEndDataPosition(position, itemPosition)
                            return
                        }
                    } else { // 月份不等

                        if (position < startPosition) {
                            clearDataListType()
                            setStartDataPosition(position, itemPosition)
                            return
                        }
                        if (position > startPosition) {
                            setEndDataPosition(position, itemPosition)
                            return
                        }

                    }

                    if (position < startPosition) {
                        startPosition = position
                        startItemPosition = itemPosition
                        clearDataListType()
                        val startSelectData = dataList.getOrNull(position)?.dateList?.getOrNull(itemPosition)
                        startSelectData?.type = 2
                        adapter?.notifyDataSetChanged()
                        return
                    }

                    setEndDataPosition(position, itemPosition)
                    return

                }

                // 都选了
                startPosition = position
                startItemPosition = itemPosition
                endPosition = -1
                endItemPosition = -1
                clearDataListType()
                setStartDataPosition(position, itemPosition)

                adapter?.notifyDataSetChanged()

            }
        })
        popView.tvConfirm.setOnClickListener {
            mOnPopClickListener?.onConfirm(
                dataList.getOrNull(startPosition)?.dateList?.getOrNull(startItemPosition)?.likeDate.ifNull(),
                dataList.getOrNull(endPosition)?.dateList?.getOrNull(endItemPosition)?.likeDate.ifNull()
            )
            dismiss()
        }
    }

    @SuppressLint("NotifyDataSetChanged", "SetTextI18n")
    private fun setStartDataPosition(position: Int, itemPosition: Int) {
        startPosition = position
        startItemPosition = itemPosition
        val startSelectData = dataList.getOrNull(position)?.dateList?.getOrNull(itemPosition)
        startSelectData?.type = 2
        popView.tvConfirm.visibility = View.GONE
        popView.tvStartDate.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff))
        popView.tvEndDate.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_ca61e1e1e_c99ffffff))
        val dateStartArray = startSelectData?.likeDate?.split("/")
        popView.tvStartDate.text = "${dateStartArray?.get(0) ?: ""} ${dataList.getOrNull(position)?.yearMonth ?: ""}"
        popView.tvEndDate.text = context.getString(R.string.select_date)
        popView.tvStartDate.setTextAppearance(R.style.bold_semi_font)
        popView.tvEndDate.setTextAppearance(R.style.medium_font)
        adapter?.notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged", "SetTextI18n", "NewApi")
    private fun setEndDataPosition(position: Int, itemPosition: Int) {
        endPosition = position
        endItemPosition = itemPosition
        val endSelectData = dataList.getOrNull(position)?.dateList?.getOrNull(itemPosition)
        endSelectData?.type = 2

        // 设置中间数据类型
        for ((outIndex, outValue) in dataList.withIndex()) {

            if (outIndex < startPosition || outIndex > endPosition) continue

            for ((index, value) in outValue.dateList.withIndex()) {

                if (startPosition == endPosition) {
                    if (index in (startItemPosition + 1) until endItemPosition) {
                        value.type = 3
                    }
                } else {
                    if (outIndex == startPosition) {
                        if (index > startItemPosition)
                            if (value.type == 0) value.type = 3
                    } else if (outIndex == endPosition) {
                        if (index < endItemPosition)
                            if (value.type == 0) value.type = 3
                    } else {
                        if (value.type == 0) value.type = 3
                    }
                }

            }
        }
        popView.tvConfirm.visibility = View.VISIBLE

        popView.tvEndDate.setTextColor(
            AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
        )
        val dateEndArray = endSelectData?.likeDate?.split("/")
        popView.tvEndDate.text = "${dateEndArray?.get(0) ?: ""} ${dataList.getOrNull(position)?.yearMonth ?: ""}"
        popView.tvEndDate.setTextAppearance(R.style.bold_semi_font)

        adapter?.notifyDataSetChanged()
    }

    private fun clearDataListType() {
        for (outDataBean in dataList) {
            for (dataBean in outDataBean.dateList) {
                if (dataBean.type == 2 || dataBean.type == 3) dataBean.type = 0
            }
        }
    }

    private var mOnPopClickListener: OnPopClickListener? = null

    interface OnPopClickListener {
        fun onConfirm(dateStartStr: String, dateEndStr: String)
    }

    fun setOnPopClickListener(mOnPopClickListener: OnPopClickListener) {
        this.mOnPopClickListener = mOnPopClickListener
    }

}