package cn.com.vau.common.storage

object StorageConstants {

    /**------------------------------------ region 退出登录时清除开始 --------------------------------------**/
    /**
     * 神策 -> 邮箱加密字段
     */
    const val SENSORS_DATA_EMAIL_EVENT_ID = "sensors_data_email_event_id"

    /**
     * 神策 -> crm id
     */
    const val CRM_USER_ID = "crm_user_id"

    /**
     * 策略草稿列表本地存储的key
     */
    const val STRATEGY_LIST_DRAFT = "strategy_list_draft"

    /**
     * 创建策略 审核模式 是否提示 可以选择自动拒绝
     */
    const val STRATEGY_AUTO_REJECT = "strategy_auto_reject"

    /**
     * 手势密码
     */
    const val PATTERN_UNLOCK = "pattern_unlock"

    /**
     * 解锁时长
     */
    const val UNLOCK_TIME = "unlock_time"

    /**
     * 是否已解锁
     */
    const val APP_LOCK_ORDER = "app_lock_order"

    /**
     *  0未设置 1手势 2指纹
     */
    const val SECURITY_OPEN_SET_STATE = "security_open_set_state"

    /**
     * 解锁设置 0无解锁 1启动  2交易，订单，我的
     */
    const val SECURITY_SET_STATE = "security_set_state"

    /**
     * 分享时 ib 账户设置是否展示 账号
     */
    const val IB_SHARE_SHOW_ACCOUNT = "ib_share_show_account"

    /**
     * sumsub  是否已经同意人脸识别相关功能
     */
    const val SUMSUB_AGREE = "sumsub_agree"

    /**
     * 价格提醒创建页面 提醒弹窗是否展示
     */
    const val PRICE_ALERT_NOTIFICATION_ENABLE_TIPS = "price_alert_notification_dialog_show"

    /**
     * ib账户分享页面，保存上次选中的账户号
     */
    const val INVITATION_LAST_SELECT_ACCOUNT = "invitation_last_select_account"

    /**
     * 保存当前IB账户code
     */
    const val IB_COMMISSION_ACCOUNT = "ib_commission_account"

    /**
     * 保存当前IB账户币种
     */
    const val IB_COMMISSION_CURRENCY = "ib_commission_currency"

    /**
     * 是否展示跟单相关页面
     */
    const val SHOW_ST_ENTRANCE = "show_st_entrance"

    /**
     * * 保存监管
     * * 三个品牌所有监管值说明
     * * 1：ASIC，2：CIMA，8：VFSC，11：FSA，12：SVG，13：FCA，14：VFSC2
     * * 其中AU用户监管有：ASIC，VFSC，FCA，VFSC2
     * * PU用户监管有：SVG，FSA
     * * VT用户监管有：CIMA
     * * VJP用户监管有：SVG
     */
    const val SUPERVISE_NUM = "supervise_num"

    /**
     * 交易杠杆
     */
    const val LEVERAGE_TRADE = "leverage_trade"

    /**
     * 接口返回的开户状态
     */
    const val SKIP_TYPE_OPEN_ACCOUNT = "skip_type_open_account"

    /**
     * Telegram 授权数据
     */
    const val TELEGRAM_AUTHORIZE_DATA = "telegram_authorize_data"

    /**
     * 切换行情显示模式     0:Classic    1:Buy/Sell
     */
    const val TRADE_SWITCH_MODE = "trade_switch_mode"

    /**
     * 按行情涨跌幅排序     0:不排序    1:箭头向上(按涨幅由低到高)    2:箭头向下(按涨幅由高到低)
     */
    const val TRADE_SORT_ROSE = "trade_sort_rose"

    /**
     * 最近一次切换的产品
     */
    const val TRADE_PRODUCT_SYMBOL = "trade_product_symbol"

    /**
     * 分享设置
     */
    const val SHARE_SETTING = "share_setting"

    /**------------------------------------ endregion 退出登录时清除结束 --------------------------------------**/
    /**------------------------------------ region 切换账户时清除开始 --------------------------------------**/

    /**
     * in app 的消息是否已经展示过
     */
    const val MESSAGE_IN_APP_IS_SHOW = "message_in_app_is_show"

    /**
     * 是否已经切换语言
     */
    const val INTERNATIONALIZATION_SWITCH = "internationalization_switch"

    /**
     * 用户手机号登录的 手机号 或者 Facebook 三方登录获取到的手机号， 目前暂时没有Facebook登录的流程
     */
    const val USER_TEL = "user_tel"

    /**
     * 分享请求的默认数据
     */
    const val REFEREES_INFO = "referees_info"

    /**
     * ib账号 分享请求的默认数据
     */
    const val REFEREES_INFO_IB = "referees_info_ib"

    /**------------------------------------ endregion 切换账户时清除结束 --------------------------------------**/
    /**----------------------------------- region 无需清除---开始 -----------------------------------------**/

    /**
     * WebSocket推送过来的未读状态
     */
    const val RED_POINT_STATE = "red_point_state"

    /**
     * StWebSocket推送过来的未读状态
     */
    const val POINT_REMIND_PROMO_SHOW = "point_remind_promo_show"

    /**
     * 国家代码
     */
    const val COUNTRY_CODE = "country_code"

    /**
     * 国家区号
     */
    const val COUNTRY_NUM = "country_num"

    /**
     * 国家名字
     */
    const val COUNTRY_NAME = "country_name"

    /**
     * 注册邀请码
     */
    const val INVITE_CODE_REGISTER = "invite_code_register"

    /**
     * 信号源 id （ 分享链接带注册邀请码，如未登录会使用注册邀请码 ）
     */
    const val ID_SIGNAL_SOURCE = "id_signal_source"

    /**
     * appsFlyer来的链接
     */
    const val URL_H5 = "url_h5"

    /**
     * 风控sessionID，每次启动app获取保存到缓存，请求接口时带到header里
     */
    const val TMX_SESSION_ID = "tmx_session_id"

    /**
     * 启动图片
     */
    const val APP_START_UP_IMAGE_URL = "app_start_up_image_url"

    /**
     * 是否跟随系统主题
     */
    const val STYLE_FOLLOW_SYSTEM = "style_follow_system"

    /**
     * app主题
     */
    const val STYLE_STATE = "style_state"

    /**
     * 资产卡片默认展开（Demo、Live、跟单账户Manual-Trading的订单页）
     */
    const val ASSET_CARD_EXPANDS_BY_DEFAULT = "order_asset_card_expands_by_default"

    /**
     * app是否首次启动
     */
    const val APP_START_FIRST = "app_start_first"

    /**
     * apk是否首次安装
     */
    const val INSTALL_APK_FIRST = "install_apk_first"

    /**
     * app flyer 中获取的 渠道 信息 以及推广过来的数据
     */
    const val CXD = "cxd"
    const val CID = "cid"
    const val RAF = "raf"
    const val LS = "ls"
    const val CP = "cp"
    const val AGENTACCOUNTNUM = "agentAccountNum"
    const val LIVESTREAM = "Livestream"

    /**
     * 设备唯一标识
     */
    const val UUID = "uuid"

    /**
     * google 广告id
     */
    const val GOOGLE_ADVERTISING_ID = "google_advertising_id"

    /**
     * appsflyer id
     */
    const val APPSFLYER_ID = "appsflyer_id"

    /**
     * fcm token
     */
    const val TOKEN_FCM = "token_fcm"

    /**
     * 渠道类型
     */
    const val CHANNEL_TYPE = "channel_type"

    /**
     * firebase app instance id
     */
    const val FIREBASE_APP_INSTANCE_ID_CACHE = "firebase_app_instance_id_cache"

    // 拼接Key
    const val TRADING_VIEW_SETTING_DATA = "trading_view_setting_data2"

    // 拼接Key
    const val KLINE_SETTING_DATA = "kline_setting_data"

    // 注意与KLINE_SETTING_DATA的区分
    const val KLINE_VIEW_SETTING_DATA = "kline_view_setting_data"

    // 拼接Key
    const val TRADING_VIEW_DRAWING_DATA = "trading_view_drawing_data"

    // 拼接Key
    const val USER_TFA_BINDED = "_user_2fa_binded"

    /**
     * 是否选中TradingView模式
     */
    const val SELECT_TRADING_VIEW_MODE = "select_trading_view_mode"

    /**
     * 账户列表CopyTrading熊猫图url
     */
    const val ACCOUNT_MANAGE_COPY_TRADING_URL = "account_manage_copy_trading_url"

    // 新用户进入时显示的弹窗，仅显示一次，本地保存，
    const val NEW_USER_DIALOG = "new_user_dialog"

    // 服务端双域名配置
    const val SERVER_BASE_URL_TEST = "server_base_url_test"
    const val SERVER_BASE_URL_PROD = "server_base_url_prod"

    /**
     * app的当前语言的position
     */
    const val LANGUAGE_SELECT = "language_select"

    /**
     * 切换测试环境服务线路Index
     */
    const val SWITCH_TEST_HTTP_URL_INDEX = "switch_test_http_url_index"

    /**
     * 策略成功下单时保存其模式 下次下单时优先选择
     */
    const val STRATEGY_ORDER_COPY_MODE = "strategy_order_copy_mode"

    /**
     * 策略成功下单时保存其0滑点开关状态 下次下单时优先选择
     */
    const val STRATEGY_ORDER_SLIPPAGE_PROTECTION_STATUS = "strategy_order_slippage_protection_status"

    /**
     * 未登录状态
     */
    const val EXIT_STATUS = "exit_status"

    /**
     * 未登录导航
     */
    const val LOGOUT_GUIDE_NUM = "logout_guide_num"

    const val LOGOUT_GUIDE_DAY = "logout_guide_day"

    /**
     * K线图类型
     */
    const val CHART_TYPE_POSITION = "chart_type_position"
    const val CHART_TYPE_TEXT = "chart_type_text"

    /**
     * 语言语种
     */
    const val LANGUAGE_LANG = "language_lang"

    /**
     * 语言地区
     */
    const val LANGUAGE_REGION = "language_region"

    /**
     * 显示在app端的语言名称
     */
    const val LANGUAGE_SHOW_NAME = "language_show_name"

    /**
     * 是否开启通知
     */
    const val NOTICE_NOTIFICATION_DIALOG_SHOW = "notice_notification_dialog_show"

    /**
     * Zendesk 初始化已使用的ChannelKey
     */
    const val ZENDESK_INITIALIZED_CHANNELKEY = "zendesk_initialized_channelkey"

    /**
     * 设备硬件标识
     */
    const val DEVICE_ID = "device_id"

    /**
     * 首页行情引导图
     */
    const val TRADE_QUOTES_GUIDE = "trade_quotes_guide"

    /**
     * 下单单位存储
     * 交易单位，1 手数，2 金额
     */
    const val OPEN_POSITION_UNIT = "open_position_unit"

    /**
     * 存储K线选择产品引导图状态（仅限3.60.0版本）
     */
    const val KLINE_GUIDE_SYMBOL = "kline_guide_symbol"

    /**
     * 交易权限
     */
    const val TRADE_PERMISSION = "trade_permission"

    /**
     * k线画图工具类配置
     */
    const val KLINE_DRAW_TOOLS_CONFIG = "kline_draw_tools_config"
    const val KLINE_DRAW_TOOLS_POSITION = "kline_draw_tools_position"
    const val KLINE_SCALE = "kline_scale"
    const val KLINE_SHOW_DRAW = "kline_show_draw"

    /**
     * 策略详情页回报率选择的周期
     */
    const val RETURN_RATE_CYCLE = "return_rate_cycle"

    /**
     * 在同一账号本地记录下单或更新时用户设置的"等比例占用保证金"模式的倍数值
     */
    const val FORMULA_COPY_MODE_VALUE = "formula_copy_mode_value"

    /**
     * 系统截图分享 开关
     */
    const val SCREENSHOT_SHARE_ENABLE = "screenshot_share_enable"

    /**----------------------------------- endregion 无需清除----结束 --------------------------------------**/
    /**----------------------------------- region 业务自己负责清理的临时存储--开始 -------------------------**/

    /**
     * 入金选择渠道时临时存储的入金金额
     */
    const val DEPOSIT_RESET_PAY_METHOD = "deposit_reset_pay_method"

    /**
     * 搜索过的历史产品名
     */
    const val SP_KEY_SEARCH_HISTORY = "search_history_key"

    /**
     * 短信验证码id
     */
    const val SMS_CODE_ID = "smsCodeId"

    /**
     * 分享
     */
    const val SHARE_TITLE = "shareTitle"

    /**
     * App切到后台时的时间戳
     */
    const val SECURITY_CODE_TIME = "security_code_time"

    /**
     *  注册源
     */
    const val RESOURCE_CODE_REGISTER = "resource_code_register"

    /**------------------------------------- endregion 业务自己负责清理的临时存储--结束 -------------------------**/

}