package cn.com.vau.common.greendao.dbUtils;

import androidx.annotation.NonNull;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Id;

import cn.com.vau.common.view.popup.bean.SelectTitle;

@Entity
public class UserPhoneHistory implements SelectTitle {
    @Id
    private Long id;
    private String phoneNumber;

    @Generated(hash = 1740205720)
    public UserPhoneHistory(Long id, String phoneNumber) {
        this.id = id;
        this.phoneNumber = phoneNumber;
    }

    @Generated(hash = 168785175)
    public UserPhoneHistory() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    @NonNull
    @Override
    public String getTitle() {
        return phoneNumber;
    }
}
