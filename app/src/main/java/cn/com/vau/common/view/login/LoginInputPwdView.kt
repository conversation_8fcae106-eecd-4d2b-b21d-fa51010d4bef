package cn.com.vau.common.view.login

import android.annotation.SuppressLint
import android.content.Context
import android.text.InputFilter
import android.text.method.*
import android.util.AttributeSet
import android.view.*
import android.widget.EditText
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.withStyledAttributes
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import cn.com.vau.R
import cn.com.vau.databinding.IncludeLoginInputPwdBinding
import cn.com.vau.util.*

/**
 * author：lvy
 * date：2025/02/10
 * desc：登录注册模块输入密码布局
 */
class LoginInputPwdView : ConstraintLayout {

    private val mBinding by lazy { IncludeLoginInputPwdBinding.inflate(LayoutInflater.from(context), this) }

    private var hintText: String? = null // 提示文字

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int = 0) : super(context, attrs, defStyleAttr) {
        // 自定义属性
        context.withStyledAttributes(attrs, R.styleable.LoginInputPwdView) {
            // hint文字
            hintText = getString(R.styleable.LoginInputPwdView_ipv_hintText)
        }

        initView()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initView() {
        // 提示文字
        if (!hintText.isNullOrBlank()) {
            mBinding.etInput.hint = hintText
        }
        // 输入框监听
        mBinding.etInput.doAfterTextChanged {
            afterTextChangedListener.invoke(it.toString())
            mBinding.ivClear.isVisible = !it.isNullOrBlank()
        }
        // 输入框获取焦点监听
        mBinding.etInput.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                mBinding.ivClear.isVisible = !mBinding.etInput.text.isNullOrBlank()
            } else {
                mBinding.ivClear.isVisible = false
            }
            mBinding.bgView.isSelected = hasFocus
            onFocusChangeListener.invoke(hasFocus)
        }
        // 输入框触摸监听
        mBinding.etInput.setOnTouchListener { _, event ->
            onTouchListener.invoke(event)
        }
        // 清除按钮点击
        mBinding.ivClear.setOnClickListener {
            mBinding.etInput.text.clear()
        }
        // 密码明文暗文显示
        mBinding.ivShowPwd.setOnClickListener {
            if (mBinding.etInput.transformationMethod == HideReturnsTransformationMethod.getInstance()) { // 显示密码
                mBinding.ivShowPwd.setImageResource(R.drawable.draw_bitmap2_password_hide_c731e1e1e_c61ffffff)
                mBinding.etInput.transformationMethod = PasswordTransformationMethod.getInstance()
            } else { // 隐藏密码
                mBinding.ivShowPwd.setImageResource(R.drawable.draw_bitmap2_password_show_c731e1e1e_c61ffffff)
                mBinding.etInput.transformationMethod = HideReturnsTransformationMethod.getInstance()
            }
            mBinding.etInput.setSelectionEnd()
        }
    }

    fun setInputText(text: String?) {
        mBinding.etInput.setText(text.ifNull())
        mBinding.etInput.setSelectionEnd()
    }

    fun getInputText(): String {
        return mBinding.etInput.text.toString().trim()
    }

    fun getEditTextView(): EditText {
        return mBinding.etInput
    }

    fun setHintText(text: String?) {
        mBinding.etInput.hint = text
    }

    fun setFilter(filter: InputFilter?) {
        val array = arrayOf(filter)
        mBinding.etInput.filters = array
    }

    fun clearText() {
        mBinding.etInput.text.clear()
    }

    // 输入框监听
    private var afterTextChangedListener: (String) -> Unit = {}
    fun afterTextChangedListener(e: (String) -> Unit) {
        this.afterTextChangedListener = e
    }

    // 输入框焦点变化监听
    private var onFocusChangeListener: (Boolean) -> Unit = {}
    fun onFocusChangeListener(e: (Boolean) -> Unit) {
        this.onFocusChangeListener = e
    }

    // 输入框触摸监听
    private var onTouchListener: (MotionEvent) -> Boolean = { false }
    fun onTouchListener(e: (MotionEvent) -> Boolean) {
        this.onTouchListener = e
    }
}