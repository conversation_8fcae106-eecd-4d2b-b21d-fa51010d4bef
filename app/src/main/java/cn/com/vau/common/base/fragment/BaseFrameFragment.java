package cn.com.vau.common.base.fragment;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.os.Bundle;
import android.util.Log;

import cn.com.vau.BuildConfig;
import cn.com.vau.common.base.mvp.BaseModel;
import cn.com.vau.common.base.mvp.BasePresenter;
import cn.com.vau.common.base.mvp.BaseView;
import cn.com.vau.common.base.rx.TUtil;

/**
 * Created by Administrator on 2016/12/28.
 */
public abstract class BaseFrameFragment<P extends BasePresenter, M extends BaseModel> extends BaseFragment implements BaseView {

    public P mPresenter;
    public M mModel;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mPresenter = TUtil.getT(this, 0);
        mModel = TUtil.getT(this, 1);

        if (mPresenter != null && mModel != null) {
            mPresenter.attachVM(this, mModel);
        }
        // mPresenter or mModel is null, 交由业务层创建实例
        else {
            mPresenter = createPresenter();
            mModel = createModel();
            mPresenter.attachVM(this, mModel);
        }
    }

    protected P createPresenter() {
        return null;
    }

    protected M createModel() {
        return null;
    }

    @SuppressLint("LogNotTimber")
    @Override
    public void onResume() {
        super.onResume();
        if (BuildConfig.DEBUG) {
            Log.d("当前的Fragment", getClass().getSimpleName());
        }
    }

    @Override
    public void onDestroy() {
        if (mPresenter != null) mPresenter.detachVM();
        super.onDestroy();
    }

    @Override
    public Activity getAc() {
        return getActivity();
    }
}
