package cn.com.vau.common.view.dialog

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.*
import cn.com.vau.R
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.databinding.DialogSelectMonthYearBinding
import cn.com.vau.util.*
import io.reactivex.Observable
import io.reactivex.disposables.Disposable

/**
 * 输入描述
 * Created by THINKPAD on 2020/5/10.
 */
class SelectMonthYearDialog(context: Context) : Dialog(context, R.style.commonDialog) {
    private var listener: OnSelectListener? = null
    private var monthData = mutableListOf<String>()
    private var yearData = mutableListOf<String>()
    private var selectYear: String? = null
    private var selectMonth: String? = null

    private val binding by lazy { DialogSelectMonthYearBinding.inflate(LayoutInflater.from(context)) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        initView()
        initListener()
    }

    private fun initView() {
        val lp = window?.attributes
        lp?.width = WindowManager.LayoutParams.MATCH_PARENT
        lp?.height = WindowManager.LayoutParams.WRAP_CONTENT
        lp?.gravity = Gravity.BOTTOM
        window?.attributes = lp
        setCanceledOnTouchOutside(true)
        setCancelable(true)

        selectYear = TimeUtil.getNowTime("yyyy")
        selectMonth = TimeUtil.getNowTime("MM")
        yearData()
        monthData()
        binding.pvMonth.setData(monthData)
        binding.pvYear.setData(yearData)
        selectMonth = monthData.elementAtOrNull(0) ?: ""
        selectYear = yearData.elementAtOrNull(0) ?: ""


        binding.pvMonth.setSelected(0)
        binding.pvYear.setSelected(0)

    }

    private fun initListener() {
        binding.pvMonth.setOnSelectListener { it, position ->
            val isContainToyear1 = selectMonth.toIntCatching() >= TimeUtil.getNowTime("MM").toIntCatching()
            val isContainToyear2 = it.toIntCatching() >= TimeUtil.getNowTime("MM").toIntCatching()
            selectMonth = it
            if (isContainToyear1 != isContainToyear2) {
                yearData()
                binding.pvYear.setData(yearData)
                var position = yearData.indexOf(selectYear)
                if (position == -1) {
                    selectYear = yearData.elementAtOrNull(0) ?: ""
                    position = 0
                }
                binding.pvYear.setSelected(position)
            }
        }

        binding.pvYear.setOnSelectListener { it, position ->
            val isContainToyear1 = selectYear.toIntCatching() == TimeUtil.getNowTime("yyyy").toIntCatching()
            val isContainToyear2 = it.toIntCatching() == TimeUtil.getNowTime("yyyy").toIntCatching()
            selectYear = it
            if (isContainToyear1 != isContainToyear2) {
                monthData()
                binding.pvMonth.setData(monthData)

                var position = monthData.indexOf(selectMonth)
                if (position == -1) {
                    selectMonth = monthData.elementAtOrNull(0) ?: ""
                    position = 0
                }
                binding.pvMonth.setSelected(position)
            }
        }
        binding.tvFinish.setOnClickListener {
            listener?.onSelect(selectYear?.substring(2), selectMonth)
            dismiss()
        }
    }

    fun yearData() {
        yearData.clear()
        val currentYear = TimeUtil.getNowTime("yyyy").toIntCatching()
        val isOutMonth = selectMonth.toIntCatching() >= TimeUtil.getNowTime("MM").toIntCatching()
        val startYear = if (isOutMonth) currentYear else currentYear + 1
        val endYear = if (isOutMonth) 10 else 11

        Observable.range(startYear, endYear)
            .subscribe(object : BaseObserver<Int>() {
                override fun onHandleSubscribe(d: Disposable?) {
                }

                override fun onNext(it: Int) {
                    yearData.add(it.toString())
                }

                override fun onComplete() {
                    super.onComplete()
                    disposable.dispose()
                }
            })
    }

    fun monthData() {
        monthData.clear()
        val isCurrentYear = selectYear.toIntCatching() == TimeUtil.getNowTime("yyyy").toIntCatching()
        val startMonth = if (isCurrentYear) TimeUtil.getNowTime("MM").toIntCatching() else 1
        val endMonth = if (isCurrentYear) 13 - TimeUtil.getNowTime("MM").toIntCatching() else 12
        Observable.range(startMonth, endMonth)
            .subscribe(object : BaseObserver<Int>() {
                override fun onHandleSubscribe(d: Disposable?) {
                }

                override fun onNext(it: Int) {

                    monthData.add(if (it < 10) "0$it" else it.toString())
                }

                override fun onComplete() {
                    super.onComplete()
                    disposable.dispose()
                }
            })
    }

    interface OnSelectListener {
        fun onSelect(selectYear: String?, selectMonth: String?)
    }

    fun setOnSelectListener(listener: OnSelectListener): SelectMonthYearDialog {
        this.listener = listener
        return this
    }
}
