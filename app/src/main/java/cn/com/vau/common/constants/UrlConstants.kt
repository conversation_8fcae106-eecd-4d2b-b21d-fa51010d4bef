package cn.com.vau.common.constants

import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.http.HttpUrl.BaseHtmlUrl
import cn.com.vau.common.http.HttpUrl.htmlUrlPrefix

/**
 * Filename: UrlConstants
 * Author: GG
 * Date: 2025/3/25
 * Description:
 */
object UrlConstants {

    /**
     * tnc地址 只有跟单才能进入查看
     */
    val TNC_URL = "$BaseHtmlUrl$htmlUrlPrefix/support/copyTrading?lang=en"

    /**
     * apk更新地址 通过official区分测试地址还是生产地址
     */
    val APK_UPDATE_URL = if (HttpUrl.official)
        "https://vantagemarketsapp.com/web/h5/support/updateapp/index.html"
    else
        "https://hytech-au1-h5.app-alpha.com/web/h5/support/updateapp/index.html"

    /**
     * 谷歌验证器的 url
     */
    const val URL_AUTHENTICATOR = "https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2&pli=1"

    /**
     * h5的转账页面
     */
    val HTML_FUND_TRANSFER = BaseHtmlUrl + "h5/funds/active/transfer"

    /**
     * h5的入金页面
     */
    val HTML_FUND_DEPOSIT = BaseHtmlUrl + "h5/funds/active/deposit"

    /**
     * 策略的分润结算url
     */
    val HTML_SIGNALSTMT = "$BaseHtmlUrl$htmlUrlPrefix/nativeTitle/signalStmt"

    /**
     * GS活动专区聚合页
     */
    val HTML_GS_AGGREGATION = "$BaseHtmlUrl$htmlUrlPrefix/gsAggregationPage"

    /**
     * 跟随嗯 分润结算url
     */
    val HTML_FOLLOWERSTMT = "$BaseHtmlUrl$htmlUrlPrefix/nativeTitle/followerStmt"

    val URL_BANK_VERIFY = BaseHtmlUrl + "h5/funds/active/bank_channel_auth/financialWorkInformation"

    val PASSKEY_LEAN_MORE_URL = "${BaseHtmlUrl}${htmlUrlPrefix}/nativeTitle/tcModulesTc"

    /**
     * 黄金开户的url
     */
    val URL_GOLD_OPEN_ACCOUNT = "${BaseHtmlUrl}h5/feature/kyc"

    const val UN_LOGIN_QR_CODE = "https://vau-usa.oss-us-east-1.aliyuncs.com/activity/configure_image/********/share_qr_code.png"

}