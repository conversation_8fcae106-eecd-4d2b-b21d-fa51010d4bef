package cn.com.vau.common.view

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.application.VauApplication
import cn.com.vau.util.dp2px

class LandKlineSideDecoration() : RecyclerView.ItemDecoration() {

    private val dp12 = 12.dp2px()
    private val dp6 = 6.dp2px()

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        super.getItemOffsets(outRect, view, parent, state)
        val position = parent.getChildAdapterPosition(view)
        val itemCount = parent.adapter?.itemCount ?: 0

        if (position == 0) {
            outRect.top = dp12
            outRect.bottom = dp6
        } else if(position == itemCount - 1) {
            outRect.top = dp6
            outRect.bottom = dp12
        } else{
            outRect.top = dp6
            outRect.bottom = dp6
        }
    }
}
