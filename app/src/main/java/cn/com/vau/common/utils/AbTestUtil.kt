package cn.com.vau.common.utils

import android.annotation.SuppressLint
import cn.com.vau.R
import cn.com.vau.util.LogUtil
import cn.com.vau.util.widget.FirebaseManager
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.ConfigUpdate
import com.google.firebase.remoteconfig.ConfigUpdateListener
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.FirebaseRemoteConfigException
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfigSettings

@SuppressLint("StaticFieldLeak")
object AbTestUtil {

    private const val TAG = "xinhuan AbTestUtil"

    private var remoteConfig: FirebaseRemoteConfig? = null

    fun init() {
        try {
            remoteConfig = Firebase.remoteConfig
            //设置提取时间间隔，否则默认12小时更新
            val configSettings = remoteConfigSettings {
                minimumFetchIntervalInSeconds = 30
            }
            remoteConfig?.setConfigSettingsAsync(configSettings)

            //加载默认的
            remoteConfig?.setDefaultsAsync(R.xml.remote_config_defaults)

            //实时监听更新
            remoteConfig?.addOnConfigUpdateListener(object : ConfigUpdateListener {
                override fun onUpdate(configUpdate: ConfigUpdate) {
                    //下次有新版本的 Remote Config 发布时，运行应用并监听更改的设备便会调用 ConfigUpdateListener
                    LogUtil.d(TAG, "Updated keys: " + configUpdate.updatedKeys);

                }

                override fun onError(error: FirebaseRemoteConfigException) {
                    LogUtil.w(
                        TAG,
                        "Config update error with code: " + error.code + " error:" + error
                    )
                }
            })
            //app启动，首次加载服务端数据
            remoteConfig?.fetchAndActivate()
        } catch (e: Exception) {
            FirebaseManager.recordException(Exception("AbTestUtil exception=${e}"))
        }

    }

    @JvmStatic
    fun getBoolean(key: String): Boolean {
        try {
            if (remoteConfig == null) {
                throw Exception("You should call the init method to initialize before using it")
            }
            return remoteConfig!!.getBoolean(key)
        } catch (e: Exception) {
            FirebaseManager.recordException(Exception("AbTestUtil exception=${e}"))
        }
        return false

    }
}