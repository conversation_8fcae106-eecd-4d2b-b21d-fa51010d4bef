package cn.com.vau.common.http

import cn.com.vau.data.StringBean
import cn.com.vau.data.account.*
import cn.com.vau.data.strategy.StStrategyCopyLoadData
import cn.com.vau.data.strategy.StStrategyCopySubmitData
import com.google.gson.JsonObject
import io.reactivex.Flowable
import okhttp3.MultipartBody
import retrofit2.http.*

/**
 * Filename: FlowableService.kt
 * Author: GG
 * Date: 2024/2/21
 * Description:
 */
interface FlowableService {

    /**
     * 验证邮箱是否符合要求
     */
    @FormUrlEncoded
    @POST("emailIsExist")
    fun emailIsExist(@FieldMap map: HashMap<String, Any>): Flowable<CheckEmailData>

    /**
     * 查询国家地区信息
     */
    @POST("queryAddress")
    fun queryAddress(@QueryMap map: HashMap<String, Any>): Flowable<ResidenceBean>

    /**
     * 获取开户已经填写的信息
     */
    @FormUrlEncoded
    @POST("getData")
    fun getData(@FieldMap map: HashMap<String, Any>): Flowable<GetProcessData>

    /**
     * 提交开户信息
     */
    @FormUrlEncoded
    @POST("process")
    fun process(@FieldMap map: HashMap<String, Any>): Flowable<SaveProcessData>

    /**
     * 获取开户需要的数据，如性别 、 idType等
     */
    @GET("getAccountSelect")
    fun getAccountSelect(): Flowable<AccoSelectData>

    /**
     * 获取交易平台的 货币类型
     */
    //    @Headers("apiVer: v7")
    @POST("getPlatFormAccountTypeCurrency")
    fun getPlatFormAccountTypeCurrency(@QueryMap map: HashMap<String, String>): Flowable<PlatFormAccountData>

    /**
     * 获取交易平台的数据 如 mt4、mt5等
     */
    @Headers("apiVer: v1")
    @POST("getAccountTypeTitle")
    fun getAccountTypeTitle(): Flowable<PlatformTypeTitleData>

    /**
     * 文件上传
     */
    @POST("file/fileUpload")
    fun fileFileUpload(@Body body: MultipartBody?): Flowable<UploadImageBean>

    /**
     * 获取开户的验证状态
     */
    @POST("getAuditStatus")
    fun getAuditStatus(@Query("token") token: String): Flowable<AuditStatusData>

    /**
     * 策略下单页面 加载页面接口（改为-kyc结尾接口，后端会判断监管）
     */
    @Headers("Content-Type: application/json", "Accept: application/json")
    @POST("strategy-copy/submit-load-kyc")
    fun strategyCopySubmitLoad(@Body data: JsonObject): Flowable<StStrategyCopyLoadData>

    /**
     * 提交跟随策略审核申请（改为-kyc结尾接口，后端会判断监管）
     */
    @Headers("Content-Type: application/json", "Accept: application/json")
    @POST("strategy-copy/submit-kyc")
    fun strategyCopySubmit(@Body data: JsonObject): Flowable<StStrategyCopySubmitData>

    /**
     *  策略订单修改页面，获取策略订单数据 加载页面的接口
     */
    @Headers("Content-Type: application/json", "Accept: application/json")
    @GET("strategy-copy/settings")
    fun strategyCopySettings(
        @Query("type") type: String?,
        @Query("id") id: String?
    ): Flowable<StStrategyCopyLoadData>

    /**
     * 更新跟随策略  更新策略订单数据
     */
    @Headers("Content-Type: application/json", "Accept: application/json")
    @POST("strategy-copy/update")
    fun strategyCopyUpdate(@Body data: JsonObject): Flowable<StringBean>

    /**
     * 是否可以开通/显示钱包
     */
    @POST("account/open-account-validate")
    fun accountOpenAccountValidate(): Flowable<OpenWalletBean>

}