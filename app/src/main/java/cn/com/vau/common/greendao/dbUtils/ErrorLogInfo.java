package cn.com.vau.common.greendao.dbUtils;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.ToOne;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.DaoException;
import cn.com.vau.common.greendao.common.DaoSession;
import cn.com.vau.common.greendao.common.ExtendInfoDao;
import cn.com.vau.common.greendao.common.ErrorLogInfoDao;

/**
 * 输入描述
 * Created by THINKPAD on 2019/12/11.
 */
@Entity
public class ErrorLogInfo {
    @Id(autoincrement = true)
    private Long id;
    private String mt4Id;
    private String serverId;
    private String logType;
    private String host;
    private String reqPath;
    private String deviceNet;
    private String reqTime;
    private String timeZone;
    private String errorInfo;
    private String deviceIp;
    private String paramInfo;

    private Long extendInfoId;//外键  详细信息id
    @ToOne(joinProperty = "extendInfoId")
    private ExtendInfo extendInfo;
    /** Used to resolve relations */
    @Generated(hash = 2040040024)
    private transient DaoSession daoSession;
    /** Used for active entity operations. */
    @Generated(hash = 489817046)
    private transient ErrorLogInfoDao myDao;

    @Generated(hash = 1433940733)
    public ErrorLogInfo(Long id, String mt4Id, String serverId, String logType, String host,
            String reqPath, String deviceNet, String reqTime, String timeZone,
            String errorInfo, String deviceIp, String paramInfo, Long extendInfoId) {
        this.id = id;
        this.mt4Id = mt4Id;
        this.serverId = serverId;
        this.logType = logType;
        this.host = host;
        this.reqPath = reqPath;
        this.deviceNet = deviceNet;
        this.reqTime = reqTime;
        this.timeZone = timeZone;
        this.errorInfo = errorInfo;
        this.deviceIp = deviceIp;
        this.paramInfo = paramInfo;
        this.extendInfoId = extendInfoId;
    }

    @Generated(hash = 759402256)
    public ErrorLogInfo() {
    }

    @Generated(hash = 260672186)
    private transient Long extendInfo__resolvedKey;

    public Long getExtendInfoId() {
        return extendInfoId;
    }

    public void setExtendInfoId(Long extendInfoId) {
        this.extendInfoId = extendInfoId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMt4Id() {
        return mt4Id;
    }

    public void setMt4Id(String mt4Id) {
        this.mt4Id = mt4Id;
    }

    public String getServerId() {
        return serverId;
    }

    public void setServerId(String serverId) {
        this.serverId = serverId;
    }

    public String getLogType() {
        return logType;
    }

    public String getDeviceIp() {
        return deviceIp;
    }

    public void setDeviceIp(String deviceIp) {
        this.deviceIp = deviceIp;
    }

    public String getParamInfo() {
        return paramInfo;
    }

    public void setParamInfo(String paramInfo) {
        this.paramInfo = paramInfo;
    }

    public void setLogType(String logType) {
        this.logType = logType;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getReqPath() {
        return reqPath;
    }

    public void setReqPath(String reqPath) {
        this.reqPath = reqPath;
    }

    public String getDeviceNet() {
        return deviceNet;
    }

    public void setDeviceNet(String deviceNet) {
        this.deviceNet = deviceNet;
    }

    public String getReqTime() {
        return reqTime;
    }

    public void setReqTime(String reqTime) {
        this.reqTime = reqTime;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public String getErrorInfo() {
        return errorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        this.errorInfo = errorInfo;
    }

    /** To-one relationship, resolved on first access. */
    @Generated(hash = 87197744)
    public ExtendInfo getExtendInfo() {
        Long __key = this.extendInfoId;
        if (extendInfo__resolvedKey == null
                || !extendInfo__resolvedKey.equals(__key)) {
            final DaoSession daoSession = this.daoSession;
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            ExtendInfoDao targetDao = daoSession.getExtendInfoDao();
            ExtendInfo extendInfoNew = targetDao.load(__key);
            synchronized (this) {
                extendInfo = extendInfoNew;
                extendInfo__resolvedKey = __key;
            }
        }
        return extendInfo;
    }

    /** called by internal mechanisms, do not call yourself. */
    @Generated(hash = 1580131944)
    public void setExtendInfo(ExtendInfo extendInfo) {
        synchronized (this) {
            this.extendInfo = extendInfo;
            extendInfoId = extendInfo == null ? null : extendInfo.getId();
            extendInfo__resolvedKey = extendInfoId;
        }
    }

    /**
     * Convenient call for {@link org.greenrobot.greendao.AbstractDao#delete(Object)}.
     * Entity must attached to an entity context.
     */
    @Generated(hash = 128553479)
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }
        myDao.delete(this);
    }

    /**
     * Convenient call for {@link org.greenrobot.greendao.AbstractDao#refresh(Object)}.
     * Entity must attached to an entity context.
     */
    @Generated(hash = 1942392019)
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }
        myDao.refresh(this);
    }

    /**
     * Convenient call for {@link org.greenrobot.greendao.AbstractDao#update(Object)}.
     * Entity must attached to an entity context.
     */
    @Generated(hash = 713229351)
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }
        myDao.update(this);
    }

    /** called by internal mechanisms, do not call yourself. */
    @Generated(hash = 431955724)
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getErrorLogInfoDao() : null;
    }


}
