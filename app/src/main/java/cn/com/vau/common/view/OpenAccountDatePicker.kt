package cn.com.vau.common.view

import android.annotation.SuppressLint
import android.app.DatePickerDialog
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import cn.com.vau.R
import cn.com.vau.databinding.LayoutEditTextOpenAccountBinding
import cn.com.vau.util.TimeUtil
import java.util.Calendar

class OpenAccountDatePicker constructor(context: Context, attrs: AttributeSet? = null) : ConstraintLayout(context, attrs),
    VerifyComponent {

    private var parent: LayoutEditTextOpenAccountBinding
    private var mustFill = false
    private var hintTxt = ""

    init {
        parent = LayoutEditTextOpenAccountBinding.inflate(LayoutInflater.from(context), this, true)
        val attr = context.obtainStyledAttributes(attrs, R.styleable.OpenAccount_Option_Text)
        mustFill = attr.getBoolean(R.styleable.OpenAccount_Option_Text_must_fill, false)
        hintTxt = attr.getString(R.styleable.OpenAccount_Option_Text_hint_text) ?: ""
        initView()
        initListener()
        attr.recycle()
    }

    @SuppressLint("SetTextI18n")
    private fun initView() {
        parent.tvHint.text = "$hintTxt${if (mustFill) "*" else ""}"
        parent.mEditText.hint = hintTxt
        parent.mEditText.isFocusable = false
        parent.mEditText.isClickable = false
        parent.mEditText.isFocusableInTouchMode = false
    }

    private fun initListener() {
        parent.mEditText.keyListener = null
        parent.mEditText.setOnClickListener {
            showSelectDateDialog()
        }
    }

    private fun showSelectDateDialog() {
        val calendar = Calendar.getInstance()
        val dateDialog = DatePickerDialog(
            context, R.style.VFXDateDialogTheme,
            { _, year, monthOfYear, dayOfMonth ->
                setText(
//                    "${if (dayOfMonth < 10) "0" else ""}$dayOfMonth/${if (monthOfYear < 9) "0" else ""}${monthOfYear + 1}/$year"
                    "$year-${if (monthOfYear < 9) "0" else ""}${monthOfYear + 1}-${if (dayOfMonth < 10) "0" else ""}$dayOfMonth"
                )
            }, calendar.get(Calendar.YEAR), calendar
                .get(Calendar.MONTH), calendar
                .get(Calendar.DAY_OF_MONTH)
        )

        dateDialog.datePicker.maxDate = TimeUtil.getBeforeYearTime(18)
        dateDialog.show()
    }

    fun text(): String {
        return parent.mEditText.text.toString()
    }

    fun setText(text: String) {
        parent.mEditText.setText(text)
        callback?.verify(text.isNotEmpty())
    }

    @SuppressLint("SetTextI18n")
    fun initMustFill(mustFill: Boolean) {
        if (this.mustFill == mustFill) return
        this.mustFill = mustFill
        parent.tvHint.text = "$hintTxt${if (mustFill) "*" else ""}"
    }

    override var callback: VerifyCallBack? = null
    override fun getVerify(): Boolean = parent.mEditText.text.toString().isNotEmpty()
}