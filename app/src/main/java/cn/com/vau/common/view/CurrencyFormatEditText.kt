package cn.com.vau.common.view

import android.content.Context
import android.text.*
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatEditText
import androidx.core.widget.doOnTextChanged
import cn.com.vau.util.ifNull

class CurrencyFormatEditText : AppCompatEditText, VerifyComponent {

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        init()
    }

    private var currencyType: String = ""

    fun setCurrencyType(currencyType: String) {
        this.currencyType = currencyType
        if (currencyType == "JPY" || currencyType == "USC") {
            this.inputType = InputType.TYPE_CLASS_NUMBER
        } else {
            this.inputType = InputType.TYPE_CLASS_NUMBER or InputType.TYPE_NUMBER_FLAG_DECIMAL
        }
    }

    private fun init() {
        // 限制最大输入20字符
        filters = arrayOf(InputFilter.LengthFilter(20))
        // 输入监听
        doOnTextChanged { text, _, _, _ ->
            var textStr = text ?: ""
            val decimalCount = when (currencyType) {
                    "JPY", "USC" -> 0
                    "BTC", "ETH" -> 8
                    else -> 2
                }
            // 删除“.”后面超过2位后的数据
            if (textStr.toString().contains(".")) {
                if (textStr.length - 1 - textStr.toString().indexOf(".") > decimalCount) {
                    textStr = textStr.toString().subSequence(
                        0,
                        textStr.toString().indexOf(".") + decimalCount + 1
                    )
                    setText(textStr)
                    setSelection(textStr.length.coerceAtMost(getText()?.length.ifNull())) //光标移到最后
                    return@doOnTextChanged
                }
            }
            // 如果"."在起始位置,则起始位置自动补0
            if (textStr.toString().trim().startsWith(".")) {
                textStr = "0$textStr"
                setText(textStr)
                setSelection(textStr.length.coerceAtMost(getText()?.length.ifNull()))
                return@doOnTextChanged
            }
            // 如果起始位置为 0,且第二位跟的不是".",则无法后续输入
            if (textStr.toString().startsWith("0") && textStr.toString().trim().length > 1 && !textStr.toString().contains(".")) {
                textStr = removeFirstZero(textStr)
                setText(textStr)
                setSelection(textStr.length.coerceAtMost(getText()?.length.ifNull()))
                return@doOnTextChanged
            }
            callback?.verify(textStr.isNotEmpty())
        }
    }

    private fun removeFirstZero(textStr: CharSequence): CharSequence {
        if (textStr.isEmpty()) {
            return "0"
        }
        if (!textStr.startsWith("0")) {
            return textStr
        }
        return removeFirstZero(textStr.substring(1))
    }

    override var callback: VerifyCallBack? = null
    override fun getVerify(): Boolean = this.text.toString().isNotEmpty()
}