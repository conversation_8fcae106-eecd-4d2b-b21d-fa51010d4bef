package cn.com.vau.common.view

import android.content.Context
import android.view.*

class SwipeGestureDetector(context: Context, private val listener: OnSwipeGestureListener) : View.OnTouchListener {

	private val gestureDetector: GestureDetector
	private var initialY: Float = 0f

	init {
		gestureDetector = GestureDetector(context, GestureListener())
	}

    override fun onTouch(view: View, motionEvent: MotionEvent?): <PERSON><PERSON><PERSON> {
        try {
            return motionEvent?.let {
                when (it.action) {
                    MotionEvent.ACTION_UP -> listener.up()
                }
                gestureDetector.onTouchEvent(it)
            } ?: false
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

    private inner class GestureListener : GestureDetector.SimpleOnGestureListener() {

        override fun onDown(event: MotionEvent): Bo<PERSON>an {
            initialY = event.y
            return true
        }

        override fun onScroll(downEvent: MotionEvent?, moveEvent: MotionEvent, distanceX: Float, distanceY: Float): <PERSON><PERSON><PERSON> {
            try {
                val currentY = moveEvent.y
                val deltaY = currentY - initialY
                listener.onScroll(deltaY)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return true
        }

		override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
			listener.click()
			return super.onSingleTapConfirmed(e)
		}
	}

    interface OnSwipeGestureListener {

        fun onScroll(distanceY: Float)

		fun up()

		fun click()
	}
}