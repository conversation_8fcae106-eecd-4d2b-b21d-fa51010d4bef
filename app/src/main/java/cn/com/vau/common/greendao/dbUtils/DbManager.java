package cn.com.vau.common.greendao.dbUtils;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.text.TextUtils;

import org.jetbrains.annotations.NotNull;

import java.util.List;

import cn.com.vau.common.greendao.common.DaoMaster;
import cn.com.vau.common.greendao.common.DaoSession;
import cn.com.vau.common.greendao.common.DealLogInfoDao;
import cn.com.vau.common.greendao.common.ErrorLogInfoDao;
import cn.com.vau.common.greendao.common.ExtendInfoDao;
import cn.com.vau.common.greendao.common.StAccountInfoDetailDao;
import cn.com.vau.common.greendao.common.UserEmailHistoryDao;
import cn.com.vau.common.greendao.common.UserInfoDetailDao;
import cn.com.vau.common.greendao.common.UserPhoneHistoryDao;

/**
 * 数据库操作类
 * Created by THINKPAD on 2018/10/23.
 */

public class DbManager {
    private static DbManager instance;
    private DaoSession daoSession;
    private UserInfoDetail userInfo;
    private StAccountInfoDetail stAccountInfo;
    private DbManager() {
    }

    public static DbManager getInstance() {
        if (instance == null)
            instance = new DbManager();
        return instance;
    }

    public void initGreenDao(Context mContext) {
        MySQLiteOpenHelper helper = new MySQLiteOpenHelper(mContext, "vau.db", null);
        SQLiteDatabase db = helper.getWritableDatabase();
        DaoMaster daoMaster = new DaoMaster(db);
        daoSession = daoMaster.newSession();
    }

    public DaoSession getDao() {
        return daoSession;
    }

    public UserInfoDetail getUserInfo() {
        if (userInfo == null) {
            UserInfoDetailDao dao = daoSession.getUserInfoDetailDao();
            List<UserInfoDetail> userInfoDetails = dao.loadAll();
            if (userInfoDetails != null && userInfoDetails.size() > 0)
                userInfo = userInfoDetails.get(0);
            else {
                userInfo = new UserInfoDetail();
                userInfo.setServerId("5");
                dao.insert(userInfo);
            }
        }
        return userInfo;
    }

    public synchronized StAccountInfoDetail getStAccountInfo() {
        if (stAccountInfo == null) {
            StAccountInfoDetailDao dao = daoSession.getStAccountInfoDetailDao();
            List<StAccountInfoDetail> stAccountInfoDetail = dao.loadAll();
            if (stAccountInfoDetail != null && stAccountInfoDetail.size() > 0)
                stAccountInfo = stAccountInfoDetail.get(0);
            else {
                stAccountInfo = new StAccountInfoDetail();
                dao.insert(stAccountInfo);
            }
        }
        return stAccountInfo;
    }

    public boolean isLogin() {
        return !TextUtils.isEmpty(UserDataUtil.accountCd()) || !TextUtils.isEmpty(UserDataUtil.stAccountId());
    }

    public void loginOut() {
        getDao().getUserInfoDetailDao().deleteAll();
        userInfo = null;
    }

    public void stLoginOut() {
        getDao().getStAccountInfoDetailDao().deleteAll();
        stAccountInfo = null;
    }

    public List<UserPhoneHistory> getUserPhoneHistory(String keyWord) {
        UserPhoneHistoryDao dao = daoSession.getUserPhoneHistoryDao();
        if (TextUtils.isEmpty(keyWord))
            return dao.loadAll();
        else
            return dao.queryBuilder().where(UserPhoneHistoryDao.Properties.PhoneNumber.like("%" + keyWord + "%")).list();
    }

    public List<UserEmailHistory> getUserEmailHistory(String keyWord) {
        UserEmailHistoryDao dao = daoSession.getUserEmailHistoryDao();
        if (TextUtils.isEmpty(keyWord))
            return dao.loadAll();
        else
            return dao.queryBuilder().where(UserEmailHistoryDao.Properties.Email.like("%" + keyWord + "%")).list();
    }

    public void saveUserEmailHistory(UserEmailHistory userEmailHistory) {
        try {
            UserEmailHistoryDao dao = daoSession.getUserEmailHistoryDao();
            List<UserEmailHistory> load = dao.queryBuilder().where(UserEmailHistoryDao.Properties.Email.eq(userEmailHistory.getEmail())).list();
            if (load == null || load.size() == 0) {
                dao.save(userEmailHistory);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void saveDealLog(DealLogInfo info) {
        DealLogInfoDao dealLogInfoDao = daoSession.getDealLogInfoDao();
        dealLogInfoDao.save(info);
    }

    public void saveUserPhoneHistory(UserPhoneHistory userPhoneHistory) {
        try {
            UserPhoneHistoryDao dao = daoSession.getUserPhoneHistoryDao();
            List<UserPhoneHistory> load = dao.queryBuilder().where(UserPhoneHistoryDao.Properties.PhoneNumber.eq(userPhoneHistory.getPhoneNumber())).list();
            if (load == null || load.size() == 0) {
                dao.save(userPhoneHistory);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<DealLogInfo> getDealLogList(String term) {
        //        DbManager.getInstance().stAccountInfo.tel
        DealLogInfoDao infoDao = daoSession.getDealLogInfoDao();
        return infoDao.queryBuilder()
                .where(
                        DealLogInfoDao.Properties.Date.like(term + "%"),
                        DealLogInfoDao.Properties.Tel.eq(UserDataUtil.userTel())
                ).list();
    }

    /**
     * 获取两个时间段内的所有的订单数据
     *
     * @param startDate 起始时间 格式举例：23/01/2022 08:00:00
     * @param endData   结束时间 格式举例：23/02/2023 11:00:00
     * @return 订单数据
     */
    public List<DealLogInfo> getDealLogListByStartAndEndDate(String startDate, String endData) {
        DealLogInfoDao infoDao = daoSession.getDealLogInfoDao();
        return infoDao.queryBuilder()
                .where(
                        DealLogInfoDao.Properties.Date.between(startDate, endData),
                        DealLogInfoDao.Properties.Tel.eq(UserDataUtil.userTel())
                ).list();
    }

    public long saveErrorLog(ErrorLogInfo info) {
        ErrorLogInfoDao errorLogInfoDao = daoSession.getErrorLogInfoDao();
        return errorLogInfoDao.insert(info);
    }

    public long saveExtendInfo(ExtendInfo info) {
        ExtendInfoDao extendInfoDao = daoSession.getExtendInfoDao();
        return extendInfoDao.insert(info);
    }

    public long getNextExtendId() {
        ExtendInfoDao extendInfoDao = daoSession.getExtendInfoDao();
        return extendInfoDao.queryBuilder().list().size() + 1;
    }

    public List<ErrorLogInfo> getErrorLogInfoList() {
        ErrorLogInfoDao errorLogInfoDao = daoSession.getErrorLogInfoDao();
        return errorLogInfoDao.queryBuilder().offset(0).limit(50).list();
    }

    public void deleteErrorLog(List<? extends ErrorLogInfo> errorLogInfoList) {
        ErrorLogInfoDao errorLogInfoDao = daoSession.getErrorLogInfoDao();
        ExtendInfoDao extendInfoDao = daoSession.getExtendInfoDao();
        for (int i = 0; i < errorLogInfoList.size(); i++) {
            ErrorLogInfo errorLogInfo = errorLogInfoList.get(i);
            errorLogInfoDao.deleteByKey(errorLogInfo.getId());
            if (errorLogInfo.getExtendInfo() != null)
                extendInfoDao.deleteByKey(errorLogInfo.getExtendInfoId());

        }
    }

    public void saveErrorLog(@NotNull List<? extends ErrorLogInfo> errorLogInfoList) {
        ErrorLogInfoDao errorLogInfoDao = daoSession.getErrorLogInfoDao();
        for (int i = 0; i < errorLogInfoList.size(); i++) {
            errorLogInfoDao.save(errorLogInfoList.get(i));
        }
    }
}
