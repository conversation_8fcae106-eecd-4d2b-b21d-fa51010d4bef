package cn.com.vau.common.utils

import cn.com.vau.util.mathAdd
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.mathDiv
import cn.com.vau.util.mathMul
import cn.com.vau.util.mathSub
import cn.com.vau.util.toDoubleCatching

/**
 * 跟单信用金工具类
 */
object CreditBalanceUtil {

    fun getAvailableBalance(equity: Double, marginUsed: Double, credit: Double, totalProfit: Double): Double {
        /**
         * Equity - 110% * marginUsed - Credit - floatPnl(订单总盈亏)
         */
        if (marginUsed == 0.0) {
            /**
             * 用户有对冲持仓 且占用保证金为0时
             * FreeMargin = Equity - marginUsed
             * FreeMargin - Credit
             */
            val availableBalance = equity.mathSub(credit)
            return availableBalance.coerceAtLeast(0.0)
        }
        val marginLevel = equity / marginUsed * 100
        if (marginLevel.toString().mathCompTo("110") == 1) {
            val availableBalance = equity.mathSub(1.1.mathMul(marginUsed)).mathSub(credit).mathSub(totalProfit)
            return availableBalance.coerceAtLeast(0.0)
        } else {
            // 小于等于110% 不进行计算 直接返回0
            return 0.0
        }
    }

    fun getAvailableCredit(availableBalance: Double, equity: Double, credit: Double): Float {
        /**
         * AvailableBalance * Credit / (Equity - Credit)
         */
        val diff = equity.mathSub(credit)
        if (diff == 0.0) {
            return 0f
        }
        val availableCredit = availableBalance.mathMul(credit).mathDiv(diff.toFloat(), 8)
        return availableCredit.coerceAtLeast(0f).coerceAtMost(credit.toFloat())
    }

    fun getUsedBalance(investment: String, equity: Double, credit: Double): Float {
        /**
         * investment * (Equity - Credit) / Equity
         */
        val investmentDouble = investment.toDoubleCatching()
        if (equity == 0.0) {
            return 0f
        }
        val usedBalance = investmentDouble.mathMul(equity.mathSub(credit)).mathDiv(equity, 8)
        return usedBalance.coerceAtLeast(0f)
    }

    fun getRemovableBalance(maxRemovableInvestment: Double, equity: Double, credit: Double): Float {
        /**
         * maxRemovableInvestment * (Equity - Credit) / Equity
         */
        if (equity == 0.0) {
            return 0f
        }
        val removableBalance = maxRemovableInvestment.mathMul(equity.mathSub(credit)).mathDiv(equity, 8)
        return removableBalance.coerceAtLeast(0f)
    }

    fun getMaxRemovableInvestmentA(equity: Double, marginUsed: Double): Double {
        /**
         * 获取Max Removable Investment的比较值A
         * Equity - 110% * marginUsed
         */
        val investmentA = equity.mathSub(1.1.mathMul(marginUsed))
        return investmentA.coerceAtLeast(0.0)
    }

    fun getMaxRemovableInvestmentB(balance: Double, credit: Double, minAllocatedMoney: Double): Double {
        /**
         * 获取Max Removable Investment的比较值B
         * Balance + Credit - 跟单门槛
         */
        val investmentB = balance.mathAdd(credit).mathSub(minAllocatedMoney)
        return investmentB.coerceAtLeast(0.0)
    }
}