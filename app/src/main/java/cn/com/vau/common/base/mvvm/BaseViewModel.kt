package cn.com.vau.common.base.mvvm

import androidx.lifecycle.*
import cn.com.vau.util.LogUtil
import io.reactivex.*
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.*
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.*

@Deprecated("3.51.0版本开始请使用cn.com.vau.common.mvvm.base包下相关基类，旧的这些会慢慢替换掉")
open class BaseViewModel : ViewModel() {
    private val disposables = CompositeDisposable()
    val showLoadingData = MutableLiveData<Boolean>(false)
    var loading: ILoading? = null   // 待新架构优化

    protected open fun <T> threadToMain(): FlowableTransformer<T, T> {
        return FlowableTransformer { upstream: Flowable<T> ->
            upstream.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
        }
    }

    protected open fun <T> applyLoading(showLoading: Boolean): FlowableTransformer<T, T>? {
        return FlowableTransformer { upstream: Flowable<T> ->
            upstream.doOnSubscribe {
                if (showLoading)
                    showLoadingData.value = true
            }.doFinally {
                if (showLoading)
                    showLoadingData.value = false
            }
        }
    }

    fun showLiveDataLoading() {
        showLoadingData.value = true
    }

    fun hideLiveDataLoading() {
        showLoadingData.value = false
    }

    fun addDisposable(disposable: Disposable?) {
        disposables.add(disposable)
    }

    //*************************三个方法配套使用***************************
    fun setLoadingHandler(loading: ILoading) {
        this.loading = loading
    }

    fun showLoading() {
        loading?.showLoadDialog()
    }

    fun hideLoading() {
        loading?.hideLoadDialog()
    }
    //*************************三个方法配套使用***************************

    fun <T> Flowable<T>.subscribeBy(onNext: (t: T) -> Unit, onError: (t: Throwable) -> Unit) {
        this.subscribe({
            onNext.invoke(it)
        }, {
            onError.invoke(it)
        }).apply { addDisposable(this) }
    }

    fun <T> Flowable<T>.subscribeBy(onNext: (t: T) -> Unit) {
        this.subscribe({
            onNext.invoke(it)
        }, {
            LogUtil.e("wj", "[BaseViewModel] exception: ${it.message}")
            hideLoading()
        }).apply { addDisposable(this) }
    }

    // TODO Felix 非继承BaseMvvmXXX 的Activity和Fragment 需要注意接口请求失败时，loading 不会消失
    fun <T> Flowable<T>.bindTLiveData(
        liveData: MutableLiveData<T>,
        callback: (() -> Unit)? = null
    ) {
        this.subscribe({
            liveData.value = it
        }, {
            LogUtil.e("wj", "[BaseViewModel] exception: ${it.message}")
            hideLoading()
            callback?.invoke()
        }).apply { addDisposable(this) }
    }

    override fun onCleared() {
        super.onCleared()
        disposables?.dispose()
    }

    fun launch(
        block: suspend CoroutineScope.() -> Unit,
        onError: (e: Throwable) -> Unit = { _: Throwable -> },
        onComplete: () -> Unit = {},
        isShowLoading: Boolean = true,
    ) {
        viewModelScope.launch(
            CoroutineExceptionHandler { _, throwable ->
                run {
                    // 这里统一处理错误
                    onError(throwable)
                }
            }
        ) {
            try {
                if (isShowLoading)
                    showLoadingData.value = true
                block.invoke(this)
                if (isShowLoading)
                    showLoadingData.value = false
            } finally {
                onComplete()
                if (isShowLoading)
                    showLoadingData.value = false
            }
        }
    }
}