package cn.com.vau.common.http

/**
 * author：lvy
 * date：2024/8/29
 * desc：方便直接调用到对应接口方法
 */

/**
 * 非交易的请求体，对应的BaseUrlService里都是非交易的接口方法
 */
val baseService by lazy {
    NetworkApi.getApi(BaseUrlService::class.java, HttpUrl.BaseUrl)
}

/**
 * 交易的请求体，对应的BaseTradingUrlService里都是交易的接口方法
 */
val tradingService by lazy {
    NetworkApi.getApi(BaseTradingUrlService::class.java, HttpUrl.BaseTradingUrl)
}

/**
 * 跟单的请求体，对应的BaseStTradingUrlService里都是跟单的接口方法
 */
val stTradingService by lazy {
    NetworkApi.getApi(BaseStTradingUrlService::class.java, HttpUrl.BaseStTradingUrl)
}