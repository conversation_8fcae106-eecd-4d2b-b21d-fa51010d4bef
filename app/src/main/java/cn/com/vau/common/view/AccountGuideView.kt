package cn.com.vau.common.view

import android.content.Context
import android.text.StaticLayout
import android.util.AttributeSet
import android.view.*
import androidx.annotation.StringRes
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.data.account.AccountOpeningGuideObj
import cn.com.vau.data.init.ShareAccountInfoData
import cn.com.vau.databinding.IncludeFragmentTradesLoginBinding
import cn.com.vau.util.*
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.tracking.*
import kotlinx.coroutines.*
import java.util.*
import kotlin.toString

class AccountGuideView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private var mBinding: IncludeFragmentTradesLoginBinding? = null

    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff) }
    private var onGoLiveCallback: (() -> Unit)? = null

    init {
        mBinding = IncludeFragmentTradesLoginBinding.inflate(LayoutInflater.from(context), this)
        mBinding?.ivAccountRight?.setOnClickListener {
            // 开通真实账户
            onGoLiveCallback?.invoke()
            // 埋点
            buryPoint()
        }
    }

    fun setKycDemoStatus() {
        // 前提 accountAuditStatus 是 4:Pending后用户重新修改资料  9:重新开户  -1:未提交 状态之一
        // 只显示Go Live 按钮
        mBinding?.ivAccountRight?.text = getString(R.string.go_live)
        mBinding?.titleTop?.text = getString(R.string.start_trading_live)
        setTextScrollingProcessing(
            arrayListOf(
                getString(R.string.one_click_trading),
                getString(R.string.global_market_access),
                getString(R.string.spreads_from2)
            )
        )
    }

    fun setNormalDemoStatus(guideObj: AccountOpeningGuideObj?) {
        // Demo开户引导
        when {
            // 未完全开户
            1 == guideObj?.type -> {
                mBinding?.ivAccountRight?.text = getString(R.string.tv_continue)
                mBinding?.titleTop?.text = getString(R.string.you_almost_there)
                setTextScrollingProcessing(openAccountGuideTextList(guideObj))
            }
            // 開戶資料有誤
            3 == guideObj?.type -> {
                mBinding?.ivAccountRight?.text = getString(R.string.re_submit)
                mBinding?.titleTop?.text = getString(R.string.incomplete_application)
                setTextScrollingProcessing(arrayListOf(getString(R.string.update_your_information)))
            }
            // 身份证明有误
            4 == guideObj?.type -> {
                mBinding?.ivAccountRight?.text = getString(R.string.upload)
                mBinding?.titleTop?.text = getString(R.string.unsuccessful_application)
                setTextScrollingProcessing(arrayListOf(getString(R.string.re_upload_documents)))
            }

            (2 == guideObj?.type) && (guideObj.step.equals("0")) -> {
                mBinding?.ivAccountRight?.text = getString(R.string.go_live)
                mBinding?.titleTop?.text = getString(R.string.start_trading_live)
                setTextScrollingProcessing(
                    arrayListOf(
                        getString(R.string.one_click_trading),
                        getString(R.string.global_market_access),
                        getString(R.string.spreads_from2)
                    )
                )
            }
        }
    }

    fun setLiveStatus(guideObj: AccountOpeningGuideObj?) {
        // Live开户引导
        when {
            // 未完全开户
            1 == guideObj?.type -> {
                mBinding?.ivAccountRight?.text = getString(R.string.tv_continue)
                mBinding?.titleTop?.text = getString(R.string.you_almost_there)
                setTextScrollingProcessing(openAccountGuideTextList(guideObj))
            }
            // 開戶資料有誤
            3 == guideObj?.type -> {
                mBinding?.ivAccountRight?.text = getString(R.string.re_submit)
                mBinding?.titleTop?.text = getString(R.string.incomplete_application)
                setTextScrollingProcessing(arrayListOf(getString(R.string.update_your_information)))
            }
            // 身份证明有误
            4 == guideObj?.type -> {
                mBinding?.ivAccountRight?.text = getString(R.string.upload)
                mBinding?.titleTop?.text = getString(R.string.unsuccessful_application)
                setTextScrollingProcessing(arrayListOf(getString(R.string.re_upload_documents)))
            }

            (2 == guideObj?.type) && (guideObj.step.equals("0")) -> {
                mBinding?.ivAccountRight?.text = getString(R.string.go_live)
                mBinding?.titleTop?.text = getString(R.string.start_trading_live)
                setTextScrollingProcessing(
                    arrayListOf(
                        getString(R.string.one_click_trading),
                        getString(R.string.global_market_access),
                        getString(R.string.spreads_from2)
                    )
                )
            }
        }
    }

    fun refreshOpenGuideBoard(enableColorRes: Int, shareAccountBean: ShareAccountInfoData) {
        // 行情维护 || 尚未初始化完畢
        if (Constants.MARKET_MAINTAINING || InitHelper.isNotSuccess()) {
            // 开户引导布局
            mBinding?.tvEquity?.setTextDiff("...")
            mBinding?.tvCurrency?.setTextDiff("")
            mBinding?.tvFloatingPnL?.setTextDiff("...")
            mBinding?.tvFloatingPnL?.setTextColorDiff(color_c1e1e1e_cebffffff)
            return
        }
        // 开户引导布局-账户信息
        MainScope().launch {
            val equityUi = withContext(Dispatchers.Default) { shareAccountBean.equity.numCurrencyFormat2() }
            val floatingPnLUi = withContext(Dispatchers.Default) { shareAccountBean.profit.numCurrencyFormat2() }
            mBinding?.tvEquity?.setTextDiff(equityUi)
            mBinding?.tvFloatingPnL?.setTextDiff("${if (shareAccountBean.profit > 0) "+" else ""}${floatingPnLUi}")
        }

        mBinding?.tvCurrency?.setTextDiff(UserDataUtil.currencyType())
        mBinding?.tvFloatingPnL?.setTextColorDiff(enableColorRes)
    }

    fun appInBackgroundMoreThan1m() {
        mBinding?.tvEquity?.text = "..."
        mBinding?.tvCurrency?.text = ""
        mBinding?.tvFloatingPnL?.setTextColor(color_c1e1e1e_cebffffff)
        mBinding?.tvFloatingPnL?.text = "..."
    }

    private fun setTextScrollingProcessing(textIds: ArrayList<String>) {
        mBinding?.apply {
            vrAccountStatus.isVisible = true
            vrAccountStatus.viewTreeObserver.addOnGlobalLayoutListener(object :
                ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {

                    val textList = ArrayList<String>()

                    textIds.forEach { textContent ->
                        val layout = StaticLayout.Builder.obtain(
                            textContent,
                            0,
                            textContent.length,
                            vrAccountStatus.getTextView().paint,
                            vrAccountStatus.getTextView().measuredWidth
                        ).build()

                        val lineCount = layout.lineCount

                        for (i in 0 until lineCount) {
                            val lineStart = layout.getLineStart(i)
                            val lineEnd = layout.getLineEnd(i)
                            val lineText = textContent.substring(lineStart, lineEnd)

                            // 添加换行符
                            if (i < lineCount - 1) {
                                textList.add(lineText)
                            } else {
                                textList.add(lineText)
                            }
                        }
                    }
                    vrAccountStatus.setList(textList)
                    vrAccountStatus.startScroll()
                    // 移除监听器，避免重复调用
                    vrAccountStatus.viewTreeObserver.removeOnGlobalLayoutListener(this)
                }
            })
        }
    }

    // 未完全开户TextList
    private fun openAccountGuideTextList(accountGuideData: AccountOpeningGuideObj?): ArrayList<String> {
        val textIds: ArrayList<String> = arrayListOf()
        textIds.add(getString(R.string.few_more_steps_to_go_live))
        var textContent = ""
        //判断语言类型，如果是中文需要去掉&这个符号
        val languageType = LanguageHelper.getAppLocale() != Locale.CHINA
        //asic 是 1 其他的是else
        if (accountGuideData?.regulator == 1) {
            when (accountGuideData.step) {
                /**
                 * 【vfsc】
                完成了 1-1  基础信息页面   显示时间 4分钟
                完成了 1-2  居住地 页面     显示时间 3分钟30秒
                完成了 2-1  资金信息 页面  显示时间 3分钟
                完成了 2-2  交易信息 页面  显示时间 2分钟30秒
                完成了 3     账户类型 页面  显示时间 2分钟
                完成了 4     条款 页面         显示时间 1分钟30秒
                跳转       Green id/上传身份页 页面  显示时间  1分钟

                【asic】
                完成了 1-1   基础信息页面  显示时间 3分钟30秒
                完成了 1-2   居住地 页面    显示时间  3分钟
                完成了 2      资金信息 页面 显示时间 2分钟30秒
                完成了 3      账户类型 页面 显示时间 2分钟
                完成了 4      条款 页面        显示时间 1分钟30秒
                跳转        Green id/上传身份页 页面  显示时间 1分钟
                 */

                "1-1" -> { //【vfsc】 完成了 1-1  基础信息页面   显示时间 4分钟   【asic】 完成了 1-1   基础信息页面  显示时间 3分钟30秒
                    val showTime = "3" + getString(R.string.min) + "${if (languageType) " & " else ""}30" + getString(R.string.secs)
                    textContent = if (accountGuideData.regulator == 1) {
                        getString(R.string.estimated_x_left, showTime)
                    } else {
                        showTime
                    }
                }

                "1-2" -> {
                    val content = getString(R.string.estimated_x_minutes_left, "3")
                    textContent = if (accountGuideData.regulator == 1) {
                        content
                    } else {
                        getString(R.string.estimated_x_left, content)
                    }
                }

                "2", "2-1" -> {
                    val showTime = "2" + getString(R.string.min) + "${if (languageType) " & " else ""}30" + getString(R.string.secs)
                    textContent = if (accountGuideData.regulator == 1) {
                        getString(R.string.estimated_x_left, showTime)
                    } else {
                        showTime
                    }
                }

                "2-2" -> {
                    val content = "2" + getString(R.string.min) + "${if (languageType) " & " else ""}30" + getString(R.string.secs)
                    textContent = getString(
                        R.string.estimated_x_left, content
                    )
                }

                "3" -> textContent = getString(
                    R.string.estimated_x_minutes_left, "2"
                )

                "4" -> {
                    val content = "1" + getString(R.string.min) + "${if (languageType) " & " else ""}30" + getString(R.string.secs)
                    textContent = getString(
                        R.string.estimated_x_left, content
                    )
                }

                else -> textContent = getString(
                    R.string.estimated_x_minutes_left, "1"
                )
            }
        } else {
            textContent = when (accountGuideData?.step) {
                "0" -> buildString {
                    append(getString(R.string.global_market_access))
                    append(getString(R.string.spreads_from2))
                    append(getString(R.string.one_click_trading))
                }

                "1" -> {
                    val text = buildString {
                        append("3")
                        append(getString(R.string.min))
                        if (languageType) append(" & ")
                        append("30")
                        append(getString(R.string.secs))
                    }
                    getString(R.string.estimated_x_left, text)
                }

                else -> {
                    val text = buildString {
                        append("2")
                        append(getString(R.string.min))
                        if (languageType) append(" & ")
                        append("30 ")
                        append(getString(R.string.secs))
                    }
                    getString(R.string.estimated_x_left, text)
                }

            }
        }
        textIds.add(textContent)
        return textIds
    }

    // 开通真实账户回调
    fun setOnGoLiveCallback(callback: (() -> Unit)?) {
        onGoLiveCallback = callback
    }

    override fun setVisibility(visibility: Int) {
        super.setVisibility(visibility)
        if (visibility == GONE || visibility == INVISIBLE) {
            mBinding?.vrAccountStatus?.stopScroll()
        }
    }

    private fun buryPoint() {
        val buttonText = mBinding?.ivAccountRight?.text.toString()
        if (buttonText == getString(R.string.go_live)) {
            LogEventUtil.setLogEvent(BuryPointConstant.V344.GENERAL_TRADES_TOP_LIVE_BUTTON_CLICK)
        }
        if (buttonText == getString(R.string.tv_continue)) {
            LogEventUtil.setLogEvent(
                BuryPointConstant.V334.REGISTER_LIVE_LVL2_BUTTON_CLICK,
                hashMapOf("Position" to "Trades")
            )
        }
    }

    private fun getString(@StringRes resId: Int, formatText: String? = null): String {
        return if (formatText == null) context?.getString(resId).ifNull()
        else context?.getString(resId, formatText).ifNull()
    }
}