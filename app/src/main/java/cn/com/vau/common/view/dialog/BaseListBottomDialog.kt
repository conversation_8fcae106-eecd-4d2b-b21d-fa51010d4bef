package cn.com.vau.common.view.dialog

import android.annotation.SuppressLint
import android.content.Context
import cn.com.vau.R
import cn.com.vau.common.base.dialog.BaseBottomDialog
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.popup.PopListBottomRcyAdapter
import cn.com.vau.databinding.PopupBaseListBottomBinding
import cn.com.vau.page.common.BaseListBean
import cn.com.vau.util.AttrResourceUtil

class BaseListBottomDialog(context: Context) : BaseBottomDialog<PopupBaseListBottomBinding>(context) {

    private var adapter: PopListBottomRcyAdapter? = null

    private var dataList: ArrayList<String> = arrayListOf()
    private var popTitle: String = ""
    private var currentSelectIndex: Int = 0
    private var itemType: Int = 0
    private var isShowDoneBtn: Boolean = true

    override fun onCreate() {
        super.onCreate()
        initView()
        initData()
        initListener()
    }

    @SuppressLint("SetTextI18n")
    private fun initView() {
        adapter = PopListBottomRcyAdapter(context, dataList)
        mBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(context)
        mBinding.mRecyclerView.adapter = adapter
    }

    @SuppressLint("SetTextI18n", "NotifyDataSetChanged")
    fun initData() {
        mBinding.tvTitle.text = popTitle

        mBinding.mRecyclerView.post {
            if (dataList.size > 6) {
                val itemView = mBinding.mRecyclerView.getChildAt(0)
                itemView?.let {
                    val height = it.height * 6
                    val layoutParams = mBinding.mRecyclerView.layoutParams
                    layoutParams?.height = height
                    mBinding.mRecyclerView.layoutParams = layoutParams
                }
            }
        }
        adapter?.setItemType(itemType)
        adapter?.setSelectIndex(currentSelectIndex)
        adapter?.notifyDataSetChanged()

    }

    private fun initListener() {
        adapter?.setOnItemClickListener(object : PopListBottomRcyAdapter.OnItemClickListener {
            @SuppressLint("NotifyDataSetChanged")
            override fun onItemClick(position: Int, itemType: Int) {
                if (itemType == 1) {
                    mOnPopClickListener?.onItemClick(position)
                    dismiss()
                } else {
                    currentSelectIndex = position
                    mOnPopClickListener?.onItemClick(position)
                    dismiss()
                }
            }
        })
        mBinding.tvDone.setOnClickListener {
            dismiss()
        }

    }

    private var mOnPopClickListener: OnPopClickListener? = null

    interface OnPopClickListener {
        fun onItemClick(position: Int)
    }

    fun setOnPopClickListener(mOnPopClickListener: OnPopClickListener): BaseListBottomDialog {
        this.mOnPopClickListener = mOnPopClickListener
        return this
    }

    fun setBaseListData(
        dataList: List<BaseListBean>,
        selectIndex: Int,
        popTitle: String
    ): BaseListBottomDialog {
        return setBaseListData(dataList, selectIndex, popTitle, 0)
    }

    fun setBaseListData(
        dataList: List<BaseListBean>,
        selectIndex: Int,
        popTitle: String,
        itemType: Int
    ): BaseListBottomDialog {
        this.dataList.clear()
        for (dataBean in dataList) {
            this.dataList.add(dataBean.getShowItemValue())
        }
        currentSelectIndex = selectIndex
        this.itemType = itemType
        isShowDoneBtn = itemType == 0
        this.popTitle = popTitle
        return this

    }

    fun setData(
        dataList: List<String>,
        selectIndex: Int,
        popTitle: String
    ): BaseListBottomDialog {
        this.dataList.clear()
        this.dataList.addAll(dataList)
        currentSelectIndex = selectIndex
        this.popTitle = popTitle
        return this
    }

    fun setData(
        dataList: List<String>,
        popTitle: String,
        itemType: Int
    ): BaseListBottomDialog {
        this.dataList.clear()
        this.dataList.addAll(dataList)
        currentSelectIndex = -1
        this.itemType = itemType
        isShowDoneBtn = itemType == 0
        this.popTitle = popTitle
        return this
    }

    override fun getLayoutId(): Int {
        return R.layout.popup_base_list_bottom
    }

    override fun bindLayout(): PopupBaseListBottomBinding {
        return PopupBaseListBottomBinding.bind(popupImplView)
    }

    override fun getNavigationBarColor(context: Context): Int {
        return AttrResourceUtil.getColor(context, R.attr.mainLayoutBg)
    }

    override fun showDialog() {
        if (dialog != null) {
            initData()
        }
        popup.show()
    }
}
