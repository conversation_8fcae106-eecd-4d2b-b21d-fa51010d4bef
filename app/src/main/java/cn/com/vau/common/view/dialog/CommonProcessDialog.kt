package cn.com.vau.common.view.dialog

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.animation.LinearInterpolator
import android.widget.ImageView
import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.util.AppUtil

/**
 * description:通用Loading对话框
 */
class CommonProcessDialog(context: Context) : Dialog(context, R.style.LoadRequestDialog) {

    private var cancelAble: Boolean = true

    private var canceledOnTouchOutside: Boolean = false

    private var loadingView: ImageView? = null
    private val loadingDrawable by lazy {
        if (AppUtil.isLightTheme())
            ContextCompat.getDrawable(context, R.drawable.drawable_loading)
        else
            ContextCompat.getDrawable(context, R.drawable.drawable_loading_night)
    }

    private var shouldPropagateBack = false
    private var onBackPressedListener: (() -> Unit)? = null

    @SuppressLint("MissingInflatedId")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_loading_net_lottie)
        loadingView = findViewById(R.id.loading_view)
        loadingView?.setImageDrawable(loadingDrawable)
        setCanceledOnTouchOutside(canceledOnTouchOutside)
        setCancelable(cancelAble)
    }

    override fun show() {
        if (context is Activity && (!(context as Activity).isFinishing || !(context as Activity).isDestroyed)) {
            return
        }
        super.show()
        loadingView
            ?.animate()
            ?.rotation(360_000f)
            ?.setDuration(900_000L)
            ?.setInterpolator(LinearInterpolator())?.start()
    }

    override fun dismiss() {
        if (context is Activity && (!(context as Activity).isFinishing || !(context as Activity).isDestroyed)) {
            return
        }
        super.dismiss()
        loadingView?.animate()?.cancel()
    }

    fun setBackPropagation(enable: Boolean): CommonProcessDialog {
        shouldPropagateBack = enable
        return this
    }

    override fun onBackPressed() {
        if (shouldPropagateBack) {
            dismiss()
            onBackPressedListener?.invoke()
        } else {
            super.onBackPressed()
        }
    }

    fun setOnBackPressedListener(onBackPressedListener: (() -> Unit)?): CommonProcessDialog {
        this.onBackPressedListener = onBackPressedListener
        return this
    }

    fun setCancelAble(cancelAble: Boolean): CommonProcessDialog {
        this.cancelAble = cancelAble
        return this
    }

    fun setCanceledOutside(canceledOnTouchOutside: Boolean): CommonProcessDialog {
        this.canceledOnTouchOutside = canceledOnTouchOutside
        return this
    }
}