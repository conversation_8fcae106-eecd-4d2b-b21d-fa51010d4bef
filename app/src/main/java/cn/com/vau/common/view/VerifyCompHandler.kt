package cn.com.vau.common.view

import android.view.View
import android.widget.TextView
import cn.com.vau.R
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.ifNull

/**
 * 验证组件 v1.1
 * * 方式一：通过给定的 VerifyComponent 组件数组，分别以判断各个组件的状态决定提交按钮的状态
 * （适用于页面中有很多必填项组件）
 *
 * * 方式二：通过直接给定 初始状态(Boolean) 和设置最终状态(Boolean) 来直接控制提交按钮的状态
 * （适用于页面中只有一个元素影响提交按钮状态）
 */
class VerifyCompHandler {

    private var listener: (() -> Unit)? = null
    private var viewList = mutableListOf<VerifyComponent>()
    private var commitView: View? = null
    private var resultArray: Array<Boolean?>? = null
    private var state: Boolean = false

    // 方式一：用法: add -> to -> submit
    fun add(vararg views: VerifyComponent): VerifyCompHandler {
        reset()
        viewList.clear()
        viewList.addAll(views)
        return handle()
    }

    // 方式一：用法: add -> to -> submit
    fun add(views: MutableList<VerifyComponent>): VerifyCompHandler {
        reset()
        viewList.clear()
        viewList.addAll(views)
        return handle()
    }

    // 方式二：用法: with -> to -> submit
    fun with(initState: Boolean = false): VerifyCompHandler {
        state = initState
        return this
    }

    // 方式二：用法: with -> to -> submit -> change
    fun change(changeState: Boolean) {
        state = changeState
        immediatelyCheck()
    }

    fun to(next: View): VerifyCompHandler {
        commitView = next
        return this
    }

    fun submit(listener: (() -> Unit)? = null) {
        this.listener = listener
        immediatelyCheck()
    }

    private fun handle(): VerifyCompHandler {
        if (viewList.size > 0) {
            resultArray = arrayOfNulls(viewList.size)
            viewList.forEachIndexed { index, component ->
                component.setCallbacks(object : VerifyCallBack {
                    override fun verify(verify: Boolean) {
                        if (verify != resultArray?.get(index)) {
                            resultArray?.set(index, verify)
                            check()
                        }
                    }
                })
            }
        }
        return this
    }

    private fun reset() {
        viewList.forEach { it.setCallbacks(null) }
        viewList.clear()
        commitView = null
        resultArray = null
        state = false
        listener = null
    }

    private fun check() {
        if (resultArray != null) {
            var success = true
            for (result in resultArray ?: arrayOf()) {
                if (!result.ifNull(false)) {
                    success = false
                    break
                }
            }
            commitView?.setOnClickListener {
                if (success) {
                    listener?.invoke()
                }
            }
            commitView?.setBackgroundResource(
                if (success)
                    R.drawable.draw_shape_c1e1e1e_cebffffff_r100
                else
                    R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100
            )

            (commitView as? TextView)?.let {
                it.setTextColor(
                    if (success)
                        AttrResourceUtil.getColor(it.context, R.attr.color_cebffffff_c1e1e1e)
                    else
                        AttrResourceUtil.getColor(it.context, R.attr.color_c731e1e1e_c61ffffff)
                )
            }

        }
    }

    private fun immediatelyCheck() {
        if (viewList.size > 0 && resultArray.isNullOrEmpty().not()) {
            viewList.forEachIndexed { index, component ->
                resultArray?.set(index, component.getVerify())
            }
            check()
        } else {
            commitView?.setOnClickListener {
                if (state) {
                    listener?.invoke()
                }
            }
            commitView?.setBackgroundResource(
                if (state)
                    R.drawable.draw_shape_c1e1e1e_cebffffff_r100
                else
                    R.drawable.draw_shape_c1f1e1e1e_c1fffffff_r100
            )
            (commitView as? TextView)?.let {
                it.setTextColor(
                    if (state)
                        AttrResourceUtil.getColor(it.context, R.attr.color_cebffffff_c1e1e1e)
                    else
                        AttrResourceUtil.getColor(it.context, R.attr.color_c731e1e1e_c61ffffff)
                )
            }

        }
    }
}