package cn.com.vau.common.http;

import android.annotation.SuppressLint;
import android.text.TextUtils;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.Objects;

import cn.com.vau.*;
import cn.com.vau.common.application.VauApplication;
import cn.com.vau.common.constants.Constants;
import cn.com.vau.common.greendao.dbUtils.UserDataUtil;
import cn.com.vau.common.storage.SpManager;
import cn.com.vau.util.*;
import cn.com.vau.util.language.LanguageHelper;
import io.reactivex.*;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import okhttp3.*;

/**
 * 拦截器
 */
public class AppendHeaderParamInterceptor implements Interceptor {
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request original = chain.request();

        String ts = System.currentTimeMillis() - Constants.INSTANCE.getOffServerTime() + "";
        String model = TextUtils.isEmpty(AppUtil.getSystemModel()) ? "Android" : AppUtil.getSystemModel();
        String ipAddress = IPUtil.getIPAddress(VauApplication.context);
        if (ipAddress == null) ipAddress = "0.0.0.0";
        //        LogUtils.w(ipAddress);
        String uuid = AppUtil.getUUID();
        String sign = ts + Constants.PRODUCT_NAME + model + ipAddress + uuid + HttpUrl.INSTANCE.getMd5Salt();

        String appsflyerId = SpManager.getAppsFlyerId();
        String originApiver = original.headers().get("apiVer");

        String appInstanceId = TextUtils.isEmpty(Constants.INSTANCE.getAppInstanceId()) ? SpManager.getFirebaseAppInstanceIdCache("") :
                Constants.INSTANCE.getAppInstanceId();

        Request request = original.newBuilder()
                .header("product", Constants.PRODUCT_NAME)
                // 接口版本
                .header("apiVer", (TextUtils.isEmpty(originApiver) ? HttpUtil.getApiVer(original.url().toString()) : Objects.requireNonNull(originApiver)))
                .header("ts", ts)
                .header("sign", MD5Util.parseStrToMd5U32(sign))
                // app 版本
                .header("appVersion", AppUtil.getVersionName())
                // 手机型号
                .header("model", model)
                // 手机系统版本
                .header("osVersion", AppUtil.getSystemVersion())
                .header("uuid", uuid)
                .header("IP", ipAddress)
                // 语言
                .header("language", LanguageHelper.getAppLanguageCode())
                .header("promoteId", SpManager.getGoogleAdvertisingId())
                .header("systemType", "android")
                // 时区
                .header("timeZone", AppUtil.getTimeZoneRawOffsetToHour() + "")
                .header("appTime", TimeUtil.formatHttpHeader())
                // 主题
                .header("theme", AppUtil.isLightTheme() ? "0" : "1")
                // 登录 token
                .header("app-token", UserDataUtil.loginToken()) // app-token 与后端沟通后在SSO需求二期时会删除，一期先做稳定版本暂不可删
                .header("xtoken", UserDataUtil.xToken()) // accessToken
                .header("x-source", JwtUtil.createToken())
                .header("branchversion", !HttpUrl.official ? "test-1.2.2" : "") // FIXME: 2025/5/15 test lvyang 后台要求测试环境先加这个
                // 跟单 token
                .header("st-token", UserDataUtil.stToken())
                // 交易 token
                .header("trade-token", UserDataUtil.tradeToken())
                .header("appsflyer_id", appsflyerId)
                .header("Content-Encoding", "gzip")
                // Firebase AppInstanceId
                .header("app-instance-id", appInstanceId)
                // 9.26,kai di 需求在接口header中添加渠道信息，如果有的话就传，没有传空字符串
                .header("app-store", SpManager.getChannelType(""))
                // 判别设备是否是模拟器 及 设备唯一标识
                .header("keyChainUUID", AppUtil.getUniqueID())
                .header("isSimulator", BuildConfig.DEBUG ? "false" : AppUtil.isEmulator() ? "true" : "false")
                // 风控
                .header("threat-session-id", SpManager.INSTANCE.getTmxSessionId(""))
                .method(original.method(), original.body())
                .build();

        try {
//            TMXHelper.INSTANCE.sendBehaviorSecDataWithUrl(original.url().toString()); // 风控，筛选url发送用户行为分析数据
            Response response = chain.proceed(request);
            return response.newBuilder().body(ResponseBody.create(response.body().contentType(), response.body().string())).build();
        } catch (SocketTimeoutException e) {
            showErrorToast(request.url().uri().toString());
            throw e;
        }
    }

    @SuppressLint("CheckResult")
    private void showErrorToast(final String host) {
        Observable.create((ObservableOnSubscribe<Boolean>) e -> {
                    if (!e.isDisposed()) {
                        e.onNext(HttpUtil.isUrlNeedToast(host));
                    }
                }).subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(aBoolean -> {
                    if (aBoolean)
                        ToastUtil.showToast(VauApplication.context.getString(R.string.network_interrupted_please_check));
                    else
                        ToastUtil.showToast(VauApplication.context.getString(R.string.slow_or_no_internet_connection));
                });
    }

}
