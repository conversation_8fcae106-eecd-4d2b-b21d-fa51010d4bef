package cn.com.vau.common.base.activity;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import cn.com.vau.BuildConfig;
import cn.com.vau.MainActivity;
import cn.com.vau.R;
import cn.com.vau.common.base.BaseFuncIml;
import cn.com.vau.common.view.dialog.CommonProcessDialog;
import cn.com.vau.util.ActivityManagerUtil;
import cn.com.vau.util.AppUtil;
import cn.com.vau.util.RefreshUtil;
import cn.com.vau.util.language.MultiLanguages;
import io.reactivex.disposables.CompositeDisposable;

/**
 * Created by z on 2016/12/26.
 * base
 */
public class BaseActivity extends AppCompatActivity implements BaseFuncIml, View.OnClickListener {

    public String TAG = getClass().getSimpleName();

    public Context context;
    private CompositeDisposable mRxManager;
    private CommonProcessDialog loadNetDialog;

    @Override
    protected void attachBaseContext(Context newBase) {
        // 绑定语种
        super.attachBaseContext(MultiLanguages.attach(newBase));
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        MultiLanguages.updateAppLanguage(this);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        boolean isLight = AppUtil.isLightTheme();
        setTheme(isLight ? R.style.AppTheme : R.style.TintAppTheme);    // 默认白
        setDarkStatusIcon(isLight);
        super.onCreate(savedInstanceState);

        ActivityManagerUtil.getInstance().addActivity(this);
        context = this;
        RefreshUtil.setHeader(this);
        RefreshUtil.setFooter(this);
    }

    /**
     * 获取浅色模式下的StatusBar颜色，因为UI改版主题色和原来的不一致，StatusBar的颜色也要做相应的改变
     */
    protected int getLightStatusBarColor() {
        return R.color.cffffff;
    }

    /**
     * 获取深色模式下的StatusBar颜色，因为UI改版主题色和原来的不一致，StatusBar的颜色也要做相应的改变
     */
    protected int getHintStatusBarColor() {
        return R.color.c1a1d20;
    }

    @Override
    protected void onPostCreate(@Nullable Bundle savedInstanceState) {
        super.onPostCreate(savedInstanceState);
        initParam();
        initView();
        initFont();
        initData();
        initListener();
    }

    @SuppressLint("SourceLockedOrientationActivity")
    @Override
    public void initParam() {
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
    }

    @Override
    public void initView() {
    }

    @Override
    public void initFont() {
    }

    @Override
    public void initData() {
    }

    @Override
    public void initListener() {
    }

    @Override
    public void onClick(View view) {

    }

    public void clearTopToMain() {
        Intent intent = new Intent(this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        startActivity(intent);
    }

    public void openActivity(Class<? extends Activity> toActivity) {
        openActivity(toActivity, null);
    }

    public void openActivity(Class<? extends Activity> toActivity, Bundle parameter) {
        Intent intent = new Intent(this, toActivity);
        if (parameter != null) {
            intent.putExtras(parameter);
        }
        startActivity(intent);
    }

    public void openActivity(Class<?> cls, Bundle bundle, int requestCode) {
        Intent intent = new Intent();
        intent.setClass(this, cls);
        if (bundle != null) {
            intent.putExtras(bundle);
        }
        startActivityForResult(intent, requestCode);
    }

    public CompositeDisposable getRxManager() {
        if (mRxManager == null)
            mRxManager = new CompositeDisposable(); // 管理订阅者
        return mRxManager;
    }

    public void showNetDialog() {
        if (loadNetDialog == null) {
            loadNetDialog = new CommonProcessDialog(this);
        }
        if (loadNetDialog.isShowing() || this.isFinishing() || this.isDestroyed()) {
            return;
        }
        if (!loadNetDialog.isShowing()) {
            loadNetDialog.show();
        }
    }

    public void hideNetDialog() {
        if (loadNetDialog != null && loadNetDialog.isShowing())
            loadNetDialog.dismiss();
    }

    @SuppressLint("LogNotTimber")
    @Override
    protected void onResume() {
        super.onResume();
        if (BuildConfig.DEBUG) {
            Log.d("当前ActivityName", this.getLocalClassName());
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mRxManager != null) mRxManager.clear();
        ActivityManagerUtil.getInstance().removeActivity(this);
        hideNetDialog();
    }

    public void setDarkStatusIcon(boolean bDark) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            View decorView = getWindow().getDecorView();
            if (decorView != null) {
                int vis = decorView.getSystemUiVisibility();
                if (bDark) {
                    vis |= View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
                } else {
                    vis &= ~View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
                }
                decorView.setSystemUiVisibility(vis);
            }
        }
    }

}
