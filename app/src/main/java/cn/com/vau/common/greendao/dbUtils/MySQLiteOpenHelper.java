package cn.com.vau.common.greendao.dbUtils;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;

import com.github.yuweiguocn.library.greendao.MigrationHelper;

import org.greenrobot.greendao.database.Database;

import cn.com.vau.common.greendao.common.DaoMaster;
import cn.com.vau.common.greendao.common.DealLogInfoDao;
import cn.com.vau.common.greendao.common.ErrorLogInfoDao;
import cn.com.vau.common.greendao.common.ExtendInfoDao;
import cn.com.vau.common.greendao.common.UserEmailHistoryDao;
import cn.com.vau.common.greendao.common.UserInfoDetailDao;
import cn.com.vau.common.greendao.common.UserPhoneHistoryDao;

public class MySQLiteOpenHelper extends DaoMaster.OpenHelper {
    public MySQLiteOpenHelper(Context context, String name, SQLiteDatabase.CursorFactory factory) {
        super(context, name, factory);
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {

        MigrationHelper.migrate(db, new MigrationHelper.ReCreateAllTableListener() {
                    @Override
                    public void onCreateAllTables(Database db, boolean ifNotExists) {
                        DaoMaster.createAllTables(db, ifNotExists);
                    }

                    @Override
                    public void onDropAllTables(Database db, boolean ifExists) {
                        DaoMaster.dropAllTables(db, ifExists);
                    }
                },
                UserInfoDetailDao.class,
                UserEmailHistoryDao.class,
                UserPhoneHistoryDao.class,
                DealLogInfoDao.class,
                ErrorLogInfoDao.class,
                ExtendInfoDao.class);
    }
}
