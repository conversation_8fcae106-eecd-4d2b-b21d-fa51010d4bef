package cn.com.vau.page.user.openAccountSecond

import android.widget.TextView
import cn.com.vau.R
import cn.com.vau.common.view.CustomTextWatcher
import cn.com.vau.data.account.EmploymentQuestionObj
import cn.com.vau.util.ifNull
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class OpenAccountSecondAdapter(
    /**
     * 所有题目选项 <questionId, questionOptions.id> <题目id，选项id>
     */
    val employmentSelected: HashMap<Int, Int>
): BaseQuickAdapter<EmploymentQuestionObj, BaseViewHolder>(R.layout.item_open_question) {

    override fun convert(holder: BaseViewHolder, item: EmploymentQuestionObj) {
        val hintText = item.desc
        // 题目文字
        val etEmploymentStatus = holder.getView<TextView>(R.id.etEmploymentStatus)
        etEmploymentStatus.hint = hintText
        // 顶部标题
        holder.setText(R.id.tvEmploymentStatusHint, hintText)
        // 是否已经回答过此题
        val optionId = employmentSelected.getOrElse(item.questionId.ifNull()){-1}
        val isAnswered = optionId != -1
        holder.setGone(R.id.tvEmploymentStatusHint, !isAnswered)
        holder.setText(R.id.etEmploymentStatus, if (isAnswered) {
            item.questionOptions?.find { it.id == optionId }?.desc ?: ""
        } else "")

        etEmploymentStatus.addTextChangedListener(object : CustomTextWatcher() {
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                holder.setGone(R.id.tvEmploymentStatusHint, s?.toString().isNullOrEmpty())
            }
        })
    }
}