package cn.com.vau.page.setting.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.init.AppVersionObj
import cn.com.vau.data.profile.UserSettingObjBean
import cn.com.vau.util.AppUtil
import cn.com.vau.util.ToastUtil

/**
 * author：lvy
 * date：2024/12/07
 * desc：设置页面
 */
class SettingViewModel : BaseViewModel() {

    val checkVerLiveData = MutableLiveData<AppVersionObj?>() // 检查版本号成功
    val syncUserSettingLiveData = MutableLiveData<MutableList<UserSettingObjBean>?>() // 获取用户设置信息成功
    val unbindSrcLiveData = MutableLiveData<Boolean>() // 解绑/删除账户成功

    var checkVersionMsgInfo: String? = null

    init {
        if (UserDataUtil.isLogin()) {
            getUserSettingInfo()
        }
        checkVersionApi() // 检查版本号
    }

    /**
     * 检查版本号
     */
    fun checkVersionApi() {
        val paramMap = hashMapOf<String, Any>()
        paramMap.put("versionName", AppUtil.getVersionName())
        paramMap.put("apkType", "android")
        requestNet({ baseService.apkCheckVersionApi(paramMap) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            val objBean = it.data?.obj
            if (objBean?.id == 1) {
                checkVersionMsgInfo = it.getResponseMsg()
                return@requestNet
            }
            checkVerLiveData.value = objBean
        })
    }

    /**
     * 获取用户设置信息
     *
     * PS：这个接口暂时没用到，安卓端语言和主题设置全是本地保存的 20250113
     */
    fun getUserSettingInfo() {
        val paramMap = hashMapOf<String, Any>()
        paramMap.put("userToken", UserDataUtil.loginToken())
        requestNet({ baseService.usersetUserSetItemsApi(paramMap) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            syncUserSettingLiveData.value = it.data?.obj
            val item = it.data?.obj?.firstOrNull { item -> item.code == Constants.SETTING_SCREEN_SHARE }
            val isCan = item?.value != "0" || SpManager.getScreenshotShareEnable()
            SpManager.putScreenshotShareEnable(isCan)
            sendEvent(DataEvent(EVENT_SCREENSHOT, isCan))
            if (isCan != (item?.value != "0")) {
                userSetItemSet(true)
            }
        })
    }

    /**
     * 设置通知 后台截屏分享开关
     */
    fun userSetItemSet(isCan: Boolean) {
        SpManager.putScreenshotShareEnable(isCan)
        val paramMap = hashMapOf<String, Any>()
        paramMap["userToken"] = UserDataUtil.loginToken()
        paramMap["code"] = Constants.SETTING_SCREEN_SHARE
        paramMap["value"] = if (isCan) "1" else "0"
        requestNet({ baseService.usersetItemsetApi(paramMap) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                sendEvent(DataEvent(EVENT_SCREENSHOT, !isCan))
                return@requestNet
            }

            sendEvent(DataEvent(EVENT_SCREENSHOT, isCan))
        }, onError = {
            sendEvent(DataEvent(EVENT_SCREENSHOT, !isCan))
        })
    }

    /**
     * 用户删除账户后，删除手机号
     */
    fun unbindPhoneApi() {
        val param = HashMap<String, Any>()
        param["userId"] = UserDataUtil.userId() // 用户id，未登录传-1
        requestNet({ baseService.usersetDeletePhoneApi(param) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            unbindSrcLiveData.value = true
        }, isShowDialog = true)
    }

    companion object {

        const val EVENT_SCREENSHOT = "event_screenshot"
    }
}