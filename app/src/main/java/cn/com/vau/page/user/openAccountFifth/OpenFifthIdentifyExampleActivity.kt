package cn.com.vau.page.user.openAccountFifth

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseFrameActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.view.popup.BottomSelectPopup
import cn.com.vau.data.account.RealAccountCacheObj
import cn.com.vau.databinding.ActivityOpenFifthIdentifyExampleBinding
import cn.com.vau.profile.adapter.SelectAccountAdapter
import cn.com.vau.profile.adapter.SelectBean
import cn.com.vau.util.createCameraRequestForUri
import cn.com.vau.util.createPhotoRequestForUri
import cn.com.vau.util.createTempImageUri
import cn.com.vau.util.launchPhoto

/**
 * 上传身份证明页面
 */
class OpenFifthIdentifyExampleActivity :
    BaseFrameActivity<OpenAccountFifthPresenter, OpenAccountFifthModel>(),
    OpenAccountFifthContract.View {

    var identityAdapter: OpenUploadImageAdapter? = null // 身份证明图片适配器
    private val binding by lazy { ActivityOpenFifthIdentifyExampleBinding.inflate(layoutInflater) }

    private val pickImage = createPhotoRequestForUri { uri ->
        mPresenter.saveFilePath(uri)
    }

    private val cameraUri by lazy { createTempImageUri() }

    private val pickCamera = createCameraRequestForUri { isSuccess ->
        if (isSuccess) {
            mPresenter.saveFilePath(cameraUri)
        }
    }

    private val typePopup: BottomSelectPopup? by lazy {
        val typeAdapter: SelectAccountAdapter<SelectBean> by lazy { SelectAccountAdapter(isChangeSelectTextColor = false) }
        typeAdapter.setNewInstance(
            mutableListOf(
                SelectBean(getString(R.string.camera)),
                SelectBean(getString(R.string.photo_library)),
            )
        )
        typeAdapter.setOnItemClickListener { _, _, position ->
            if (position == 0) {
                pickCamera.launch(cameraUri)
            } else {
                pickImage.launchPhoto()
            }
            typePopup?.dismiss()
        }
        BottomSelectPopup.build(this, getString(R.string.add_picture_from), typeAdapter)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
    }

    override fun initParam() {
        super.initParam()
        mPresenter.pageType = intent?.extras?.getInt("pageType") ?: 1
    }

    @SuppressLint("ObsoleteSdkInt")
    override fun initView() {
        super.initView()

        initPageType()

//        identityAdapter = OpenUploadImageAdapter(this, mPresenter.identityPathList)
//        mRecyclerView.layoutManager = GridLayoutManager(this, 3)
//        mRecyclerView.adapter = identityAdapter

        mPresenter.getRealInfo()

    }

    override fun initListener() {
        super.initListener()
        binding.tvNext.setOnClickListener(this)
        binding.ivBack.setOnClickListener(this)

//        identityAdapter?.listener = object : OpenUploadImageAdapter.OnItemClickListener {
//            override fun onItemClick(position: Int, type: Int) {
//                if (type == 0) {
//                    mPresenter.onItemSelect(position)
//                } else { // 删除
//                    mPresenter.deleteItem(position)
//                }
//            }
//        }
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.ivBack -> goBack()
            R.id.tvNext -> {
                goBack()
                //mPresenter.initUpload()
            }
        }
    }

    // 默认是护照，2 驾照，3 身份证
    private fun initPageType() {
        val supervisionType = SpManager.getSuperviseNum("")
        when (mPresenter.pageType) {
            1 -> {
                binding.tvUploadTip1.text = getString(R.string.at_least_x_months_validity, if (supervisionType == "1") "3" else "6")
                binding.ivUploadTip1.setImageResource(if (supervisionType == "1") R.drawable.img_asic_quick_tips_3_months else R.drawable.img_asic_quick_tips_6_months)
                binding.tvUploadTip2.text = getString(R.string.must_show_full_clearly)
                binding.tvUploadTip3.text = getString(R.string.must_show_passport_clearly)
            }

            2 -> {
                binding.ivSamplePic.setImageResource(R.drawable.img_asic_sample_driver_license)
                binding.ivUploadTip1.setImageResource(R.drawable.img_asic_quick_tips_uptodate)
                binding.tvUploadTip1.text = getString(R.string.id_must_be_date)
                binding.ivUploadTip2.setImageResource(R.drawable.img_asic_quick_tips_drivers_license)
                binding.tvUploadTip2.text = getString(R.string.must_show_full_number)
                binding.ivUploadTip3.visibility = View.GONE
                binding.tvUploadTip3.visibility = View.GONE
            }

            3 -> {
                binding.ivSamplePic.setImageResource(R.drawable.img_asic_sample_id)
                binding.ivUploadTip1.setImageResource(R.drawable.img_asic_quick_tips_uptodate)
                binding.tvUploadTip1.text = getString(R.string.id_must_be_date)
                binding.ivUploadTip2.setImageResource(R.drawable.img_asic_quick_tips_passport)
                binding.tvUploadTip2.text = getString(R.string.must_show_full_number)
                binding.ivUploadTip3.visibility = View.GONE
                binding.tvUploadTip3.visibility = View.GONE
            }
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            goBack()
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    private fun goBack() {
        finish()
    }

    override fun showBottomDialog() {
        typePopup?.show()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun updateAdapter() {
        identityAdapter?.notifyDataSetChanged()
    }

    override fun showUploadingActivity() {

    }

    override fun showRealInfo(data: RealAccountCacheObj?) {
        val supervisionType = SpManager.getSuperviseNum("")
        when (mPresenter.pageType) {
            1 -> {
                binding.tvUploadTip1.text = getString(R.string.at_least_x_months_validity, if (supervisionType == "1") "3" else "6")
                binding.ivUploadTip1.setImageResource(if (supervisionType == "1") R.drawable.img_asic_quick_tips_3_months else R.drawable.img_asic_quick_tips_6_months)
            }
        }
    }

}