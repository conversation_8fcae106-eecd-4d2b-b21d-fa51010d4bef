package cn.com.vau.page.user.openAccoGuide

import cn.com.vau.common.base.mvvm.BaseRepository
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.ServiceManager
import cn.com.vau.data.account.AccoSelectData
import cn.com.vau.data.account.AuditStatusData
import cn.com.vau.data.account.CheckEmailData
import cn.com.vau.data.account.GetProcessData
import cn.com.vau.data.account.OpenWalletBean
import cn.com.vau.data.account.PlatFormAccountData
import cn.com.vau.data.account.PlatformTypeTitleData
import cn.com.vau.data.account.ResidenceBean
import cn.com.vau.data.account.SaveProcessData
import cn.com.vau.data.account.UploadImageBean
import io.reactivex.Flowable
import okhttp3.MultipartBody
import retrofit2.http.FieldMap

class OpenAccoRepository : BaseRepository() {

    fun checkEmail(@FieldMap map: HashMap<String, Any>): Flowable<CheckEmailData> {
        return ServiceManager.getInstance().baseUrlService.emailIsExist(map)
            .compose(threadToMain())
    }

    fun queryResidence(map: HashMap<String, Any>): Flowable<ResidenceBean> {
        return ServiceManager.getInstance().baseUrlService.queryAddress(map)
            .compose(threadToMain())
    }

    fun getAccountSelect(): Flowable<AccoSelectData> {
        return ServiceManager.getInstance().baseUrlService.getAccountSelect()
            .compose(threadToMain())
    }

    fun getProcess(map: HashMap<String, Any>): Flowable<GetProcessData> {
        return ServiceManager.getInstance().baseUrlService.getData(map)
            .compose(threadToMain())
    }

    fun saveProcess(map: HashMap<String, Any>): Flowable<SaveProcessData> {
        return ServiceManager.getInstance().baseUrlService.process(map)
            .compose(threadToMain())
    }

    fun saveProcessID3(map: HashMap<String, Any>): Flowable<SaveProcessData> {
        return ServiceManager.getInstance().baseUrlService.process(map)
            .compose(threadToMain())
    }

    fun getPlatFormAccountTypeCurrency(map: HashMap<String, String>): Flowable<PlatFormAccountData> {
        return ServiceManager.getInstance().baseUrlService.getPlatFormAccountTypeCurrency(map)
            .compose(threadToMain())
    }

    fun getAccountTypeTitle(): Flowable<PlatformTypeTitleData> {
        return ServiceManager.getInstance().baseUrlService.getAccountTypeTitle()
            .compose(threadToMain())
    }

    fun uploadFile(body: MultipartBody): Flowable<UploadImageBean> {
        return ServiceManager.getInstance().baseUrlService.fileFileUpload(body)
            .compose(threadToMain())
    }

    fun getAuditStatus(): Flowable<AuditStatusData> {
        return ServiceManager.getInstance().baseUrlService.getAuditStatus(UserDataUtil.loginToken())
            .compose(threadToMain())
    }

    fun accountOpenAccountValidate(): Flowable<OpenWalletBean> {
        return ServiceManager.getInstance().baseUrlService.accountOpenAccountValidate()
            .compose(threadToMain())
    }
}