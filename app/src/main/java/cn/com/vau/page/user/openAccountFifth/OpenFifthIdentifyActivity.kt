package cn.com.vau.page.user.openAccountFifth

import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseFrameActivity
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.popup.BottomSelectPopup
import cn.com.vau.data.account.RealAccountCacheObj
import cn.com.vau.databinding.ActivityOpenFifthIdentifyBinding
import cn.com.vau.page.user.asicOpenAccount.activity.OpenAccountFifthActivity
import cn.com.vau.profile.adapter.SelectAccountAdapter
import cn.com.vau.profile.adapter.SelectBean
import cn.com.vau.util.*

/**
 * 上传身份证明页面
 */
class OpenFifthIdentifyActivity : BaseFrameActivity<OpenAccountFifthPresenter, OpenAccountFifthModel>(), OpenAccountFifthContract.View {

    private var identityAdapter: OpenUploadImageAdapter? = null // 身份证明图片适配器
    private val binding by lazy { ActivityOpenFifthIdentifyBinding.inflate(layoutInflater) }

    private val pickImage = createPhotoRequestForUri { uri ->
        selectAndUpload(uri)
    }

    private val cameraUri by lazy { createTempImageUri() }

    private val pickCamera = createCameraRequestForUri { isSuccess ->
        if (isSuccess) {
            selectAndUpload(cameraUri)
        }
    }

    private val typePopup: BottomSelectPopup? by lazy {
        val typeAdapter: SelectAccountAdapter<SelectBean> by lazy { SelectAccountAdapter(isChangeSelectTextColor = false) }
        typeAdapter.setNewInstance(
            mutableListOf(
                SelectBean(getString(R.string.camera)),
                SelectBean(getString(R.string.photo_library)),
            )
        )
        typeAdapter.setOnItemClickListener { _, _, position ->
            if (position == 0) {
                pickCamera.launch(cameraUri)
            } else {
                pickImage.launchPhoto()
            }

            typePopup?.dismiss()
        }
        BottomSelectPopup.build(this, getString(R.string.add_picture_from), typeAdapter)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        BarUtil.setBarColorAndIconColor(window, false)
    }

    override fun initParam() {
        super.initParam()

        mPresenter.skipType = intent?.extras?.getInt("skipType") ?: 1

        mPresenter.pageType = intent?.extras?.getInt("pageType") ?: 1
        mPresenter.currentUploadType = 1
        mPresenter.dialogTittle = intent?.extras?.getString("dialogTittle") ?: ""
        if (TextUtils.isEmpty(mPresenter.dialogTittle))
            mPresenter.dialogTittle = getString(R.string.thank_you_for_email)
    }

    @SuppressLint("ObsoleteSdkInt")
    override fun initView() {
        super.initView()
        initPageType()

        identityAdapter = OpenUploadImageAdapter(
            this, mPresenter.identityPathList,
            if (mPresenter.pageType == 1)
                getString(R.string.upload_passport)
            else if (mPresenter.pageType == 2)
                getString(R.string.upload_drivers_licence)
            else
                getString(R.string.upload_id)
        )

        val layoutManager = WrapContentLinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
        layoutManager.stackFromEnd = true
        binding.mRecyclerView.layoutManager = layoutManager
        binding.mRecyclerView.adapter = identityAdapter
        mPresenter.getRealInfo()

        // 神策自定义埋点(v3500)，页面浏览事件
        mPresenter.sensorsTrack()
    }

    override fun initListener() {
        super.initListener()
        binding.tvNext.setOnClickListener(this)
        binding.ivBack.setOnClickListener(this)
        binding.tvSeeExample.setOnClickListener(this)
        identityAdapter?.listener = object : OpenUploadImageAdapter.OnItemClickListener {
            override fun onItemClick(position: Int, type: Int) {
                if (type == 0) {
                    mPresenter.onItemSelect(position)
                } else { // 删除
                    mPresenter.deleteItem(position)
                }
            }
        }

    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.ivBack -> goBack()
            R.id.tvSeeExample -> {
                val bundle = Bundle()
                bundle.putInt("pageType", mPresenter.pageType)
                openActivity(OpenFifthIdentifyExampleActivity::class.java, bundle)
            }

            R.id.tvNext -> {
                mPresenter.initUpload()
            }
        }
    }

    override fun showUploadingActivity() {
        openActivity(UploadingActivity::class.java, null, Constants.SUBMIT_SUCCESS)
    }

    // 默认是护照，2 驾照，3 身份证
    @SuppressLint("SetTextI18n")
    private fun initPageType() {
        val supervisionType = SpManager.getSuperviseNum("")
        when (mPresenter.pageType) {
            1 -> {
                binding.tvUploadTip1.text = "• ${getString(R.string.at_least_x_months_validity, if (supervisionType == "1") "3" else "6")}"
                binding.tvUploadTip2.text = "• ${getString(R.string.must_show_full_clearly)}"
                binding.tvUploadTip3.text = "• ${getString(R.string.must_show_passport_clearly)}"
            }

            2 -> {
                binding.ivRegisterTop.setImageResource(R.drawable.img_asic_drivers_licence)
                binding.tvTitle.text = getString(R.string.drivers_licence)
                binding.tvUploadTip1.text = "• ${getString(R.string.id_must_be_date)}"
                binding.tvUploadTip2.text = "• ${getString(R.string.must_show_full_number)}"
                binding.tvUploadTip3.visibility = View.GONE
            }

            3 -> {
                binding.ivRegisterTop.setImageResource(R.drawable.img_asic_photo_id)
                binding.tvTitle.text = getString(R.string.national_id)
                binding.tvUploadTip1.text = "• ${getString(R.string.national_id)}"
                binding.tvUploadTip2.text = "• ${getString(R.string.state_issued_id)}"
                binding.tvUploadTip3.text = "• ${getString(R.string.military_id)}"
                binding.tvUploadTip4.text = "• ${getString(R.string.immigration_id)}"
                binding.tvUploadTip4.visibility = View.VISIBLE
            }
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            goBack()
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    private fun goBack() {
        finish()
        if (ActivityManagerUtil.getInstance().secondActivity !is OpenAccountFifthActivity) {
            openActivity(OpenAccountFifthActivity::class.java)
        }
    }

    @SuppressLint("SetTextI18n")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (resultCode) {
            Constants.SUBMIT_SUCCESS -> {
                mPresenter.nextStep()
            }
        }
    }

    override fun showBottomDialog() {
        typePopup?.show()
    }

    private fun selectAndUpload(uri: Uri?) {
        // 例如 LocalMedia 里面返回三种path
        // 1.media.getPath(); 为原图path
        // 2.media.getCutPath();为裁剪后path，需判断media.isCut();是否为true
        // 3.media.getCompressPath();为压缩后path，需判断media.isCompressed();是否为true
        // 如果裁剪并压缩了，已取压缩路径为准，因为是先裁剪后压缩的
        mPresenter.saveFilePath(uri)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun updateAdapter() {
        identityAdapter?.notifyDataSetChanged()
        binding.mRecyclerView.smoothScrollToPosition(identityAdapter?.itemCount ?: 0)
        binding.tvNext.setBackgroundResource(
            if (mPresenter.identityPathList.isNotEmpty())
                R.drawable.bitmap_icon2_next_active
            else
                R.drawable.bitmap_icon2_next_inactive
        )
    }

    @SuppressLint("SetTextI18n")
    override fun showRealInfo(data: RealAccountCacheObj?) {

        val supervisionType = SpManager.getSuperviseNum("")
        when (mPresenter.pageType) {
            1 -> binding.tvUploadTip1.text = "• ${getString(R.string.at_least_x_months_validity, if (supervisionType == "1") "3" else "6")}"
        }

    }

}