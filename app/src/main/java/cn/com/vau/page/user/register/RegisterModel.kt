package cn.com.vau.page.user.register

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.BaseBean
import cn.com.vau.data.account.*
import cn.com.vau.data.init.TradeAccountLoginBean
import io.reactivex.disposables.Disposable
import okhttp3.RequestBody


/**
 * Created by Haipeng on 2017/10/12.
 * 1
 *
 */
class RegisterModel : RegistestContract.Model {
    override fun getCodeApi(map: HashMap<String, Any>, baseObserver: BaseObserver<VerificationCodeData>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().getTelRegisterSmsApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun checkSmsCodeApi(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().smsValidateSmsRegisterCodeApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun registerAcountApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<LoginBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().registerNewApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun thirdpartyRegisterApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<LoginBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().thirdpartyRegisterApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun bindMT4Login(body: RequestBody, baseObserver: BaseObserver<TradeAccountLoginBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService2().tradeAccountLogin(body), baseObserver)
        return baseObserver.disposable
    }

    override fun checkEmail(map: HashMap<String, Any>, baseObserver: BaseObserver<RealAccountCacheBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().isExistEmail(map), baseObserver)
        return baseObserver.disposable
    }

//    override fun getAreaCode(baseObserver: BaseObserver<SelectCountryNumberBean>): Disposable {
//        HttpUtils.loadData(RetrofitHelper.getHttpService().selectCountryNumberClassifyScreening(), baseObserver)
//        return baseObserver.disposable
//    }
}
