package cn.com.vau.page.login.viewmodel.activity

import androidx.lifecycle.*
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.SelectCountryNumberObjDetail
import cn.com.vau.page.login.SignUpRequestBean
import cn.com.vau.util.ifNull

/**
 * author：lvy
 * date：2025/03/12
 * desc：ASIC：注册第二步 -> 输入手机号
 */
class SignUpAsicViewModel : BaseViewModel() {

    var signUpRequestBean: SignUpRequestBean? = null // 注册的实体bean

    // 国家区号设置成功
    private val _areaCodeLiveData = MutableLiveData<String>()
    val areaCodeLiveData: LiveData<String> = _areaCodeLiveData

    /**
     * 设置国家区号，在选择国家区号页面选择回调后会调用
     */
    fun setAreaCodeData(countryBean: SelectCountryNumberObjDetail) {
        signUpRequestBean?.countryCode = countryBean.countryCode
        signUpRequestBean?.countryNum = countryBean.countryNum
        signUpRequestBean?.countryName = countryBean.countryName
        SpManager.putCountryNum(countryBean.countryNum.ifNull())
        SpManager.putCountryName(countryBean.countryName.ifNull())
        SpManager.putCountryCode(countryBean.countryCode.ifNull())
        _areaCodeLiveData.value = signUpRequestBean?.countryNum.ifNull(Constants.defaultCountryNum)
    }

    /**
     * 设置默认的国家区号
     * 这个页面只有ASIC才能进来了，所以只设置默认的即可
     */
    fun setDefaultAreaCode() {
        signUpRequestBean?.countryCode = Constants.defaultCountryCode
        signUpRequestBean?.countryNum = Constants.defaultCountryNum
        signUpRequestBean?.countryName = Constants.defaultCountryName
        SpManager.putCountryNum(Constants.defaultCountryNum)
        SpManager.putCountryName(Constants.defaultCountryName)
        SpManager.putCountryCode(Constants.defaultCountryCode)
        _areaCodeLiveData.value = signUpRequestBean?.countryNum.ifNull(Constants.defaultCountryNum)
    }
}