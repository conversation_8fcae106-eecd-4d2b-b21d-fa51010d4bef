package cn.com.vau.page.common.selectNation
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.util.ToastUtil
import cn.com.vau.data.account.SelectNationalityBean
import io.reactivex.disposables.Disposable


/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
class SelectNationalityPresenter : SelectNationalityContract.Presenter() {
    /**
     * 获取国籍数据
     */
    override fun getNationalityData() {
        mView?.showNetDialog()
        mModel?.getNationalityData(object : BaseObserver<SelectNationalityBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                getNationalityData()
            }

            override fun onNext(data: SelectNationalityBean) {
                mView?.hideNetDialog()
                if (data.resultCode == "V00000")
                    mView?.updateData(data.data?.obj ?: listOf())
                else
                    ToastUtil.showToast(data.msgInfo)
            }

        })
    }

}