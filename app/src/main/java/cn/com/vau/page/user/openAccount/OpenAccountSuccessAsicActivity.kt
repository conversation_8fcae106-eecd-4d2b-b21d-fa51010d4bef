package cn.com.vau.page.user.openAccount

import android.annotation.SuppressLint
import android.content.Intent
import android.view.View
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.UrlConstants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.common.utils.AsicQuestionUtil
import cn.com.vau.databinding.ActivityOpenAccountSuccessAsicBinding
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import org.json.JSONObject

class OpenAccountSuccessAsicActivity : BaseMvvmBindingActivity<ActivityOpenAccountSuccessAsicBinding>() {

    private val type by lazy { intent?.extras?.getInt("type") ?: 1 } // type:4 代表是从greenid途径过来的ASIC用户
    private val isAppraisal by lazy { intent?.extras?.getBoolean("isAppraisal", true) ?: true }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        if (isAppraisal) {
            if (type == 1) {
                mBinding.mImageFilterView.setImageResource(AttrResourceUtil.getDrawable(this, R.attr.imgAlertOk))
                mBinding.tvTitle.text = getString(R.string.congratulations)
                mBinding.tvOpenAccountHint.text = getString(R.string.you_have_opened_vantage_successfully)
            }
            if (type == 3) {
                mBinding.tvNext.text = getString(R.string.get_started)
                mBinding.ivBack.visibility = View.GONE
            }
            if (type == 4) {
                mBinding.tvNext.text = getString(R.string.get_started)
            }
        } else {
            // 未做过问卷或测评过的
            mBinding.mImageFilterView.setImageResource(AttrResourceUtil.getDrawable(this, R.attr.imgAlertFail))
            mBinding.tvTitle.text = getString(R.string.successful_submission)   // 因为从上一个页面提交成功过来的
            mBinding.tvOpenAccountHint.text = "${getString(R.string.your_trading_journey_is_about_to_begin)} ${getString(R.string.to_comply_with_please_please_suitability_quiz)}"
            mBinding.tvNext.text = getString(R.string.asic_questionnaire)
        }
    }

    override fun initListener() {
        super.initListener()
        mBinding.ivBack.setOnClickListener {
            openActivity(AccountManagerActivity::class.java)
            finish()
        }

        mBinding.tvNext.setOnClickListener {
            if (isAppraisal) {
                when (type) {
                    3, 4 -> openActivity(AccountManagerActivity::class.java)
                    else -> {
                        NewHtmlActivity.openActivity(this, url = UrlConstants.HTML_FUND_DEPOSIT)
                    }
                }
                finish()
            } else {
                AsicQuestionUtil(this@OpenAccountSuccessAsicActivity).showQuestionDialog()

                // 神策自定义埋点(v3500)，点击事件
                sensorsTrackClick()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (resultCode) {
            Constants.SUBMIT_SUCCESS -> {
                openActivity(AccountManagerActivity::class.java)
                finish()
            }
        }
    }

    override fun registerBackPressEvent(): Boolean = true

    override fun handleBackPressEvent() {
        super.handleBackPressEvent()
        openActivity(AccountManagerActivity::class.java)
        finish()
    }

    /**
     * 神策自定义埋点(v3500)
     * 开户及验证页面点击 -> 开户验证页面按钮点击成功时触发
     */
    private fun sensorsTrackClick() {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.IDENTITY_LEVEL, "ASIC") // 验证阶段
        properties.put(SensorsConstant.Key.IDENTITY_STEP, "") // 验证步骤
        properties.put(SensorsConstant.Key.BUTTON_NAME, "Start Questionnaire") // 按钮名称
        SensorsDataUtil.track(SensorsConstant.V3500.OPEN_IDENTITY_PAGE_CLICK, properties)
    }

}