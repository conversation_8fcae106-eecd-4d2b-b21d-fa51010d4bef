package cn.com.vau.page.user.openAccountFifth

import android.net.Uri
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.*
import cn.com.vau.page.login.activity.*
import cn.com.vau.page.user.asicOpenAccount.activity.OpenAccountFifthActivity
import cn.com.vau.page.user.asicOpenAccount.activity.OpenAccountFirstActivity
import cn.com.vau.page.user.asicOpenAccount.activity.OpenAccountFirstSecondActivity
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.page.user.loginPwd.LoginPwdActivity
import cn.com.vau.page.user.openAccountForth.OpenAccountForthActivity
import cn.com.vau.page.user.openAccountSecond.OpenAccountSecondActivity
import cn.com.vau.page.user.openAccountThird.OpenAccountThirdActivity
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

/**
 * Created by Haipeng on 2017/10/12.
 */
class OpenAccountFifthPresenter : OpenAccountFifthContract.Presenter() {

    var isFrom = -1

    //  1：可交易真实账号开通成功，2：检查有交易账号，建议入金加速审核，3：检查无交易账号，提交成功等待审核
    var skipType = 1
    var pageType = 1
    var currentSelectPosition = 0 // 当前上传图片的下标

    var currentUploadType = 1 // 1身份证明照片  2地址证明
    var currentUploadIndex = 0

    var dialogTittle: String? = null
    var isAppraisal: Boolean? = null    // 是否测评过

    var identityList = arrayListOf<String>() // 身份证明图片保存地址
    var identityOOSList = arrayListOf<String>() // 身份证明图片保存地址
    var identityPathList = arrayListOf<Uri?>() // 身份证明本地图片数据源

    /**
     *  type当前上传图片的类型
     *  position 当前选择的第几个
     */
    override fun onItemSelect(position: Int) {
        currentSelectPosition = position
        initPermission()

        // 神策自定义埋点(v3500)，点击事件
        if (position == 0) {
            sensorsTrackClick("Upload Document")
        } else {
            sensorsTrackClick("Add More")
        }
    }

    override fun deleteItem(position: Int) {
        identityPathList.removeAt(position)
        mView?.updateAdapter()
    }

    /**
     * 选择照片
     * selectType 0相机  1相册
     */
    override fun onSelectMthod(selectType: Int) {
    }

    override fun initUpload() {
        identityList.clear()
        identityOOSList.clear()
        currentUploadIndex = 0
        if (identityPathList.isEmpty()) {
            ToastUtil.showToast(
                context.getString(
                    if (currentUploadType == 1)
                        R.string.please_upload_your_proof_of_id
                    else
                        R.string.please_upload_your_proof_of_address
                )
            )
            return
        }
        mView?.showUploadingActivity()
        uploadFile()

        // 神策自定义埋点(v3500)，点击事件
        sensorsTrackClick("Next")
    }

    /**
     * 上传图片
     * tradeType 0身份证明照片  1地址证明
     * posotion 上传的position
     * 文件地址
     */
    override fun uploadFile() {
        val uri = identityPathList.getOrNull(currentUploadIndex)
        uri?.let { uri ->
            (mView.ac as AppCompatActivity).lifecycleScope.launch {
                ImageUtil.compressImageToStream(uri = uri)?.let { inputStream ->

                    val byteArray = inputStream.readBytes()

                    val requestBody = byteArray.toRequestBody("multipart/form-data".toMediaTypeOrNull(), 0, byteArray.size)

                    val builder = MultipartBody.Builder()
                        .setType(MultipartBody.FORM)//表单类型
                        .addFormDataPart(
                            "token",
                            UserDataUtil.loginToken()
                        ) // ParamKey.TOKEN 自定义参数key常量类，即参数名
                    builder.addFormDataPart("imgFile", "Avatar${System.currentTimeMillis()}.jpg", requestBody) // imgfile 后台接收图片流的参数名
                    val parts = builder.build()

                    // vau/file/fileUpload
                    mModel?.uploadFile(parts, object : BaseObserver<UploadImageBean>() {
                        override fun onNext(data: UploadImageBean) {
                            if (data.resultCode != "V00000") {
                                ToastUtil.showToast(data.msgInfo)
                                EventBus.getDefault().post(NoticeConstants.UPLOAD_PHOTO_FAIL)
                                return
                            }

                            identityList.add(data.data?.obj?.imgFile ?: "")
                            identityOOSList.add(data.data?.obj?.imgFileoos ?: "")
                            currentUploadIndex++
                            if (currentUploadIndex <= identityPathList.size - 1) {
                                uploadFile()
                            } else {
                                saveRealInfo()
                            }

                        }

                        override fun onHandleSubscribe(d: Disposable?) {
                            mRxManager.add(d)
                        }

                        override fun onError(e: Throwable?) {
                            super.onError(e)
                            EventBus.getDefault().post(NoticeConstants.UPLOAD_PHOTO_FAIL)
                            mView?.hideNetDialog()
                        }
                    })
                }
            }
        }
    }

    override fun saveFilePath(uri: Uri?) {
        identityPathList.add(uri)
        mView?.updateAdapter()
    }

    /**
     * 权限申请
     */
    override fun initPermission() {
        if (mView == null)
            return
        // 校验权限 （读写和相机权限）
        PermissionUtil.checkPermissionWithCallback(mView.ac, *Constants.PERMISSION_STORAGE, Constants.PERMISSION_CAMERA) { isGranted ->
            if (isGranted)
                mView?.showBottomDialog()
            else
                ToastUtil.showToast(mView.ac.getString(R.string.please_give_us_settings))
        }
    }

    override fun getRealInfo() {

        val params = hashMapOf<String, Any>()
        params.put("token", UserDataUtil.loginToken())
        params.put("step", "4")

        mModel?.getRealInfo(params, object : BaseObserver<RealAccountCacheBean>() {
            override fun onNext(data: RealAccountCacheBean) {

                mView?.hideNetDialog()

                if (data.resultCode != "V00000") {
                    ToastUtil.showToast(data.msgInfo)
                    return
                }

                val cacheData = data.data?.obj
                SpManager.putSuperviseNum(cacheData?.supervisionType ?: "0")
                cacheData?.list?.forEach {
                    identityList.add(it.crmAddress ?: "")
                    identityOOSList.add(it.oosAddress ?: "")
                }
                mView?.showRealInfo(cacheData)

            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    override fun saveRealInfo() {

        if (identityList.size == 0) {
            ToastUtil.showToast(context.getString(R.string.please_upload_your_proof_of_id))
            return
        }

        val params = hashMapOf<String, Any>()
        params.put("token", UserDataUtil.loginToken())
        params.put("step", if (currentUploadType == 1) "5-2" else "5-4")
        params.put("supervisionType", SpManager.getSuperviseNum(""))

        identityList.forEachIndexed { index, s ->
            params.put("fiveList[$index].crmAddress", s)
            params.put("fiveList[$index].oosAddress", identityOOSList.elementAtOrNull(index) ?: "")
        }

        // 保存缓存信息 vau/process
        mModel?.saveRealInfo(params, object : BaseObserver<RealAccountCacheBean>() {
            override fun onNext(data: RealAccountCacheBean) {

                //mView?.hideNetDialog()

                if (data.resultCode != "V00000") {
                    ToastUtil.showToast(data.msgInfo)
                    EventBus.getDefault().post(NoticeConstants.UPLOAD_PHOTO_FAIL)
                    return
                }
                //绑定神策业务ID，场景5
                SensorsDataUtil.bindBusinessIdForMerge(data.data?.emailEventID)

                if (currentUploadType == 2) {   // 进行到最后一步（上传地址证明）后，去拿接口中是否做过测评的字段
                    isAppraisal = data.data?.obj?.isAppraisal
                }
                EventBus.getDefault().post(NoticeConstants.UPLOAD_PHOTO_SUCCEED)
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                //mView?.hideNetDialog()
                EventBus.getDefault().post(NoticeConstants.UPLOAD_PHOTO_FAIL)
            }
        })

    }

    override fun nextStep() {
        super.nextStep()
        if (currentUploadType == 1) {
            val bundle = Bundle()
            bundle.putInt("skipType", skipType)
            bundle.putInt(Constants.IS_FROM, 0)
            openActivity(OpenFifthAddressSelectActivity::class.java, bundle)
        } else {
            mView?.registerRealAccountSuccess()
            ActivityManagerUtil.getInstance().finishActivity(OpenAccountFifthActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(OpenFifthIdentifyActivity::class.java)
            ActivityManagerUtil.getInstance()
                .finishActivity(OpenFifthAddressSelectActivity::class.java)

            ActivityManagerUtil.getInstance().finishActivity(OpenAccountFirstActivity::class.java)
            ActivityManagerUtil.getInstance()
                .finishActivity(OpenAccountFirstSecondActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(OpenAccountSecondActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(OpenAccountThirdActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(OpenAccountForthActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(LoginActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(LoginPwdActivity::class.java)
            // ASIC注册的几个页面
            ActivityManagerUtil.getInstance().finishActivity(SignUpActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(SignUpAsicActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(InputCodeActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(VerifyEmailCodeActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(VerifySmsCodeActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(SignUpAsicPwdActivity::class.java)
        }
    }

    /**
     * 神策自定义埋点(v3500)
     * 开户及验证页面浏览 -> 开户验证页面加载完成时触发
     */
    fun sensorsTrack() {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.IDENTITY_LEVEL, "ASIC") // 验证阶段
        SensorsDataUtil.track(SensorsConstant.V3500.OPEN_IDENTITY_PAGE_VIEW, properties)
    }

    /**
     * 神策自定义埋点(v3500)
     * 开户及验证页面点击 -> 开户验证页面按钮点击成功时触发
     */
    fun sensorsTrackClick(buttonName: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.IDENTITY_LEVEL, "ASIC") // 验证阶段
        properties.put(SensorsConstant.Key.IDENTITY_STEP, "") // 验证步骤
        properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName) // 按钮名称
        SensorsDataUtil.track(SensorsConstant.V3500.OPEN_IDENTITY_PAGE_CLICK, properties)
    }
}