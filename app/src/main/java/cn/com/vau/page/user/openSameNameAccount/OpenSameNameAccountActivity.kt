package cn.com.vau.page.user.openSameNameAccount

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.*
import android.widget.LinearLayout
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.base.activity.BaseFrameActivity
import cn.com.vau.common.constants.*
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.view.popup.*
import cn.com.vau.common.view.popup.adapter.AccountTypeTipAdapter
import cn.com.vau.common.view.popup.bean.AccountTypeTipBean
import cn.com.vau.data.account.PlatFormAccountData
import cn.com.vau.databinding.ActivityOpenSameNameAccountBinding
import cn.com.vau.page.html.*
import cn.com.vau.profile.adapter.SelectAccountAdapter
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.dialog.*
import org.greenrobot.eventbus.EventBus
import java.util.Locale

/**
 * 开通同名账户
 * Created by Haipeng.
 */
class OpenSameNameAccountActivity : BaseFrameActivity<OpenSameNameAccountPresenter, OpenSameNameAccountModel>(), OpenSameNameAccountContract.View {

    private val typePopup: BottomSelectPopup? by lazy { BottomSelectPopup.build(this) }
    private var from = ""
    private val mBinding by lazy { ActivityOpenSameNameAccountBinding.inflate(layoutInflater) }
    private val isASIC by lazy { SpManager.getSuperviseNum() == "1" }
    private val bgSelect by lazy { AppCompatResources.getDrawable(this, R.drawable.draw_shape_stroke_c1e1e1e_cebffffff_solid_c0a1e1e1e_c0affffff_r10) }
    private val bgUnSelect by lazy { AppCompatResources.getDrawable(this, R.drawable.draw_shape_c0a1e1e1e_c0affffff_r10) }

    // 选择mt4平台时需要弹框提示用户
    private val selectMt4ConfirmDialog by lazy {
        BottomSelectMt4ConfirmDialog.Builder(this).build()
    }

    // mt4平台选择Cent类型账户需要提示文案的类型编号列表
    private val mt4CentAccountTipsList by lazy {
        arrayListOf(20, 19, 22, 23)
    }

    /**
     * 平台列表数据
     */
    private val platFormAdapter: SelectAccountAdapter<PlatFormAccountData.Obj> by lazy {
        SelectAccountAdapter<PlatFormAccountData.Obj>(isChangeSelectTextColor = false).apply {
            setNewInstance(mPresenter.platFormDataList)
            selectTitle = data.getOrNull(mPresenter.platFormIndex)?.getShowItemValue()
            setOnItemClickListener { _, _, position ->
                changePlatformRefreshData(position)
                typePopup?.dismiss()

                // 选择mt4平台时需要弹框提示用户
                val platFormName = mPresenter.platFormDataList.elementAtOrNull(position)?.platFormName
                if ("mt4".equals(platFormName, true)) {
                    showSelectMt4ConfirmPopup()
                }
                // 选中跟单账户时，下方 Account Terms and Conditions 文案需要变成超链接
                agreement2Text("mts".equals(platFormName, true))
            }
        }
    }

    /**
     * 账户类型列表数据
     */
    private val accountTypeAdapter: SelectAccountAdapter<PlatFormAccountData.PlatFormAccountType> by lazy {
        SelectAccountAdapter<PlatFormAccountData.PlatFormAccountType>(isChangeSelectTextColor = false).apply {
            setNewInstance(mPresenter.accountTypeDataList)
            selectTitle = data.getOrNull(mPresenter.accountTypeIndex)?.getShowItemValue()
            setOnItemClickListener { _, _, position ->
                mPresenter.accountTypeIndex = position
                refreshAccountTypeView()
                mPresenter.initAccountCurrencyData()
                selectTitle = data.getOrNull(mPresenter.accountTypeIndex)?.getShowItemValue()
                notifyDataSetChanged()
                mt4CentAccountShowTips()
                typePopup?.dismiss()
            }
        }
    }

    /**
     * 货币类型列表数据
     */
    private val accountCurrencyAdapter: SelectAccountAdapter<PlatFormAccountData.Currency> by lazy {
        SelectAccountAdapter<PlatFormAccountData.Currency>(isChangeSelectTextColor = false).apply {
            setNewInstance(mPresenter.accountCurrencyDataList)
            selectTitle = data.getOrNull(mPresenter.accountCurrencyIndex)?.getShowItemValue()
            setOnItemClickListener { _, _, position ->
                typePopup?.dismiss()
                if (mPresenter.accountCurrencyIndex == position) return@setOnItemClickListener
                mPresenter.accountCurrencyIndex = position
                selectTitle = data.getOrNull(mPresenter.accountCurrencyIndex)?.getShowItemValue()
                notifyDataSetChanged()
                refreshAccountCurrencyView()

            }
        }
    }

    // 选择交易平台的提示弹框适配器
    private val platTypeTipAdapter: AccountTypeTipAdapter by lazy {
        AccountTypeTipAdapter().apply {
            val metaList = arrayListOf<AccountTypeTipBean>()
            mPresenter.platFormDataList.forEach {
                if ("mt5" == it.platFormName?.lowercase(Locale.getDefault())) {
                    metaList.add(
                        AccountTypeTipBean(
                            title = "PLATFORM 5th",
                            tip1 = getString(R.string.comprehensive_features_ideal_for_experienced_traders),
                            tip2 = getString(R.string.covers_a_wider_etfs_and_more),
                            tip3 = getString(R.string.flexible_trading_options_indicators_and_timeframes),
                            labelBgRes = R.drawable.shape_ce35728_r100,
                            labelStr = getString(R.string.recommended)
                        )
                    )
                }
                if ("mts" == it.platFormName?.lowercase(Locale.getDefault())) {
                    metaList.add(
                        AccountTypeTipBean(
                            title = "Copy Trading",
                            tip1 = getString(R.string.save_time_studying_perfect_of_traders),
                            tip2 = getString(R.string.earn_up_to_ideal_their_capital),
                            labelBgRes = R.drawable.shape_c034854_r100,
                            labelStr = getString(R.string.popular)
                        )
                    )
                }
                if ("mt4" == it.platFormName?.lowercase(Locale.getDefault())) {
                    metaList.add(
                        AccountTypeTipBean(
                            title = "PLATFORM 4th",
                            tip1 = getString(R.string.fewer_built_in_features),
                            tip2 = getString(R.string.limited_product_diversity),
                        )
                    )
                }
            }
            setList(metaList)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
    }

    override fun initParam() {
        super.initParam()
        from = intent.extras?.getString("from", "-1").ifNull("-1")
        mPresenter.isSelectedCopyTrading = intent.extras?.getBoolean("isSelectedCopyTrading", false)
        mPresenter.isOnlyCopyTrading = "238" == from
        mPresenter.fromWallet = intent.extras?.getBoolean("fromWallet", false) == true
        mPresenter.defaultSelectAccountTypeNum = intent.extras?.getString("defaultSelectAccountTypeNum")
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        super.initView()
        mBinding.run {
            if (!isASIC) {
                etNotes.hint = getString(R.string.eg_ib_mam_server_location)
            }
            tvMetaTraderDes.setOnClickListener(this@OpenSameNameAccountActivity)
            tvMetaTraderType.setOnClickListener(this@OpenSameNameAccountActivity)
            tvAccountTypeDesc.setOnClickListener(this@OpenSameNameAccountActivity)
            tvAccountType.setOnClickListener(this@OpenSameNameAccountActivity)
            tvCurrencyType.setOnClickListener(this@OpenSameNameAccountActivity)
            llNotes.setOnClickListener(this@OpenSameNameAccountActivity)
            tvNext.setOnClickListener(this@OpenSameNameAccountActivity)
        }
        showAgreement()
    }

    override fun initData() {
        super.initData()
        mPresenter.getPlatFormAccountTypeCurrency()
        mPresenter.accountOpenAccountValidate()
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initListener() {
        mBinding.etNotes.doAfterTextChanged {
            val count = it?.length ?: 0
            mBinding.tvCharCount.text = "$count/50"
            mPresenter.notes = it.toString()
        }
        mBinding.root.viewTreeObserver.addOnGlobalFocusChangeListener { oldFocus, newFocus ->
            when (newFocus) {
                mBinding.etNotes -> addFocusBg(mBinding.llNotes)
            }
        }
        KeyboardUtil.registerSoftInputChangedListener(this) { height ->
            if (height == 0) {
                mBinding.root.requestFocus()
                removeFocusBg(mBinding.llNotes)
                mBinding.etNotes.clearFocus()
            }
        }
    }

    private fun addFocusBg(view: LinearLayout) {
        // 键盘可见
        view.background = bgSelect
    }

    private fun removeFocusBg(view: LinearLayout) {
        view.background = bgUnSelect
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            // 平台
            R.id.tvMetaTraderDes -> {
                if (platTypeTipAdapter.data.isNotEmpty()) {
                    typePopup?.setTitle(getString(R.string.platform))
                    typePopup?.setAdapter(platTypeTipAdapter)
                    typePopup?.show()
                }
            }

            R.id.tvMetaTraderType -> {
                if (mPresenter.isOnlyCopyTrading.not()) {
                    showAccountMetaTraderDialog()
                }
            }

            R.id.tvAccountTypeDesc -> {
                if (mPresenter.accountTypeDataList.isNotEmpty()) {
                    typePopup?.setTitle(getString(R.string.glossary))
                    typePopup?.setAdapter(getAccountTypeTipAdapter())
                    typePopup?.show()
                }
            }

            R.id.tvAccountType -> {
                if (mPresenter.accountTypeDataList.isNotEmpty()) {
                    showAccountTypeDialog()
                }
            }

            R.id.tvCurrencyType -> {
                if (mPresenter.accountCurrencyDataList.isNotEmpty()) {
                    showCurrencyDialog()
                }
            }

            R.id.llNotes -> {
                KeyboardUtil.showSoftInput(this)
                mBinding.etNotes.requestFocus()
            }

            R.id.tvNext -> {
                val checked = mBinding.cbAgreement.isChecked
                if (!checked) { // 未选择协议
                    ToastUtil.showToast(context.getString(R.string.please_read_and_agreements))
                    return
                }
                /*
                 点击submit按钮逻辑：
                 先检查用户是否参与了NDB活动，如果参与了活动，弹框提示用户让用户退出活动，用户退出成功再去请求开通同名账户接口；
                 如果用户未参与NDB活动，直接请求开通同名账户接口。
                */
                noRepeat(1000) {
                    mPresenter.checkNdbPromoApi() //检查是否参与了NDB活动
                    val name = mPresenter.platFormDataList.getOrNull(mPresenter.platFormIndex)?.platFormName
                    if (name == "mts") {
                        LogEventUtil.setLogEvent(BuryPointConstant.V347.ST_REGISTER_3)
                    }
                }
            }
        }
    }

    private fun getAccountTypeTipAdapter(): AccountTypeTipAdapter {
        val plat = mPresenter.platFormDataList.getOrNull(mPresenter.platFormIndex)
        return AccountTypeTipAdapter().apply {
            val supervise = SpManager.getSuperviseNum("")
            val isMt4 = "mt4" == plat?.platFormName?.lowercase(Locale.getDefault())
            val dataList = mutableListOf(
                AccountTypeTipBean(
                    title = getString(R.string.standard_stp_account),
                    tip1 = getString(R.string.spreads_from),
                    tip2 = getString(R.string.trade_with_commissions),
                    tip3 = getString(R.string.up_to_leverage),
                ),
                AccountTypeTipBean(
                    title = if (supervise == "1") {
                        "RAW CLASSIC ${getString(R.string.account)}"
                    } else {
                        getString(R.string.raw_ecn_account)
                    },
                    tip1 = getString(R.string.spreads_from2),
                    tip2 = when (supervise) {
                        // ASIC
                        "1" -> getString(R.string.trade_with_x_commissions_side, "2.5")
                        // FCA
                        "13" -> getString(R.string.trade_with_x_commissions_side, "1")
                        // VFSC || VFSC2
                        else ->
                            if (isMt4) getString(R.string.trade_with_x_per_opening_trade, "6")
                            else getString(R.string.trade_with_x_commissions_side, "3")
                    },
                    tip3 = getString(R.string.up_to_leverage),
                ),
            ).apply {
                if (supervise != "1") { //ASIC监管不需要加下面的内容
                    this.add(
                        AccountTypeTipBean(
                            title = getString(R.string.swap_free_account),
                            tip1 = getString(R.string.islamic_stp_islamic_available),
                            tip2 = getString(R.string.up_to_leverage),
                        )
                    )
                }
            }
            setNewInstance(dataList)
        }
    }

    override fun refreshPlatFormView() {
        // 平台名字
        mBinding.tvMetaTraderType.text = mPresenter.platFormDataList.elementAtOrNull(mPresenter.platFormIndex)?.displayPlatFormName ?: ""
        // 是否显示平台 ( 只有一个平台时不显示 )
//        ctlMetaTrader.isVisible = mPresenter.platFormDataList.size > 1

        mPresenter.initAccountTypeData()
    }

    // 更新账户类型信息
    override fun refreshAccountTypeView() {
        mBinding.tvAccountType.text = mPresenter.accountTypeDataList.elementAtOrNull(mPresenter.accountTypeIndex)?.accountTypeName ?: ""
    }

    override fun refreshAccountCurrencyView() {
        mBinding.tvCurrencyType.text = mPresenter.accountCurrencyDataList.elementAtOrNull(mPresenter.accountCurrencyIndex)?.currencyName
    }

    // 平台
    private fun showAccountMetaTraderDialog() {
        platFormAdapter.selectTitle = platFormAdapter.data.getOrNull(mPresenter.platFormIndex)?.getShowItemValue()
        typePopup?.setTitle(getString(R.string.platform))
        typePopup?.setAdapter(platFormAdapter)
        typePopup?.show()
    }

    // 账户类型
    override fun showAccountTypeDialog() {
        accountTypeAdapter.selectTitle = accountTypeAdapter.data.getOrNull(mPresenter.accountTypeIndex)?.getShowItemValue()
        typePopup?.setTitle(getString(R.string.account_type))
        typePopup?.setAdapter(accountTypeAdapter)
        typePopup?.show()
    }

    // 货币
    override fun showCurrencyDialog() {
        accountCurrencyAdapter.selectTitle = accountCurrencyAdapter.data.getOrNull(mPresenter.accountCurrencyIndex)?.getShowItemValue()
        typePopup?.setTitle(getString(R.string.currency))
        typePopup?.setAdapter(accountCurrencyAdapter)
        typePopup?.show()
    }

    override fun changeButtonState(isClickable: Boolean) {
        mBinding.tvNext.isClickable = isClickable
        mBinding.tvNext.isFocusable = isClickable
    }

    /**
     * 选中 Copy Trading 账户时，则下方的 Account Terms and Conditions 需要变成可点击的超链接，点击之后展示跟单TnC全文
     * 3.56.0需求 3.9 跟单账户开同名账户时增加对TnC的展示 Yunyu Wang
     */
    private fun agreement2Text(isCopyTrading: Boolean) {
        // asic有自己的文案，不需要处理
        if (!isASIC) {
            // 切换成非跟单账户时，需要还原文案
            val urlText = getString(R.string.account_terms_and_conditions)
            mBinding.tvAgreement2.text = getString(R.string.i_we_agree_future, urlText)
            // 跟单账户才变超链接
            if (isCopyTrading) {
                val urlTextColor = AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
                mBinding.tvAgreement2.set(urlText, urlTextColor) {
                    NewHtmlActivity.openActivity(this, title = urlText, url = UrlConstants.TNC_URL)
                }
            }
        }
    }

    /**
     * 检查NDB活动结果
     *
     * status: 1：正在参加活动 ；0：未参加活动
     */
    override fun checkNdbPromoResult(status: Int?) {
        if (status == 1) {
            showNDBPromoDialog()
        } else {
            mPresenter.openSameAccount()
        }
    }

    /**
     * 退出NDB活动成功
     */
    override fun exitNdbPromoSuc() {
        mPresenter.openSameAccount()
    }

    override fun showWallet() {
        mBinding.viewSplit1.isVisible = mPresenter.withWallet
        mBinding.viewSplit2.isVisible = mPresenter.withWallet
        mBinding.vsWalletDesc.isVisible = mPresenter.withWallet
        if (!isASIC) { // 非ASIC
            if (mPresenter.withWallet) {
                mBinding.tvWalletAgreement6.isVisible = true
                mBinding.tvWalletAgreement6.text = getString(
                    R.string.i_we_acknowledge_that_understood_crypto_wallet,
                    getString(R.string.privacy_statement),
                    getString(R.string.customer_agreement)
                )
                mBinding.tvWalletAgreement6
                    .set(getString(R.string.privacy_statement)) {
                        goWebView(
                            "${HttpUrl.officialWebUrl}pdf/wallet-privacy-notice/",
                            -2
                        )
                    }
                    .set(getString(R.string.customer_agreement)) {
                        goWebView(
                            "${HttpUrl.officialWebUrl}pdf/wallet-client-agreement/",
                            -2
                        )
                    }
            }
        }
    }

    /**
     * 如果参加了NDB活动，点击submit时的弹框
     */
    private fun showNDBPromoDialog() {
        BottomAgreeRejectDialog.Builder(this)
            .setTitle(getString(R.string.ndb_promo))
            .setContent(getString(R.string.you_have_participated_temporarily_additional_account))
            .setAgree(false)
            .setNeedAgreeOnEndClick(true)
            .setAgreeText(getString(R.string.opt_out_ndb_additional_account))
            .setAgreeDescribe(getString(R.string.opt_out_ndb_all_immediately_closed))
            .setStartText(getString(R.string.cancel))
            .setEndText(getString(R.string.ok))
            .setEndListener { dialog, isSelect ->
                if (!isSelect) return@setEndListener
                mPresenter.exitNdbPromoApi()
            }.build().showDialog()
    }

    override fun showOpenSuccessDialog() {
        if (mPresenter.fromWallet) {
            CenterActionWithIconDialog.Builder(this)
                .setLottieIcon(R.raw.lottie_dialog_ok)
                .setTitle(getString(R.string.account_opening_application_submitted))//设置则展示标题，否则不展示
                .setContent(getString(R.string.you_will_receive_once_your_be_activated)) //设置内容
                .setSingleButton(true) //展示一个按钮，默认两个按钮
                .setSingleButtonText(getString(R.string.got_it)) //设置单个按钮文本
                //如果展示一个按钮，点击监听使用setOnSingleButtonListener
                .setOnSingleButtonListener { textView ->
                    //默认关闭
                    EventBus.getDefault().post(DataEvent(NoticeConstants.HTML_JUMP_OPEN_ACCOUNT_BACK))
                    finish()
                }
                .build()
                .showDialog()
        } else {
            CenterActionWithIconDialog.Builder(this)
                .setLottieIcon(R.raw.lottie_dialog_ok)
                .setTitle(getString(R.string.congratulations))//设置则展示标题，否则不展示
                .setContent(getString(R.string.you_ll_receive_confirmation_account)) //设置内容
                .setSingleButton(true) //展示一个按钮，默认两个按钮
                .setSingleButtonText(getString(R.string.ok)) //设置单个按钮文本
                //如果展示一个按钮，点击监听使用setOnSingleButtonListener
                .setOnSingleButtonListener { textView ->
                    //默认关闭
                    EventBus.getDefault().post(NoticeConstants.REFRESH_ACCOUNT_MANAGER)
                    finish()
                }
                .build()
                .showDialog()
        }
    }

    /**
     * 根据监管显示不同文案
     */
    @SuppressLint("SetTextI18n")
    private fun showAgreement() {
        mBinding.tvAgreement6.isVisible = isASIC
        mBinding.tvAgreement7.isVisible = isASIC
        mBinding.tvAgreement8.isVisible = isASIC
        if (isASIC) { //ASIC
            showTextAndJump()
        } else {
            mBinding.tvAgreement1.text = "${getString(R.string.i_we_acknowledge_and_fx_x, getString(R.string.app_name))}"
            agreement2Text(mPresenter.isSelectedCopyTrading == true) // 一些页面进入开同名页面会默认选中跟单平台，这里统一处理
            mBinding.tvAgreement3.text = "${getString(R.string.i_we_understand_cfds)}"
            mBinding.tvAgreement4.text = "${getString(R.string.i_we_understand_products)}"
            mBinding.tvAgreement5.text = "${getString(R.string.i_we_hereby_x_data_verification, getString(R.string.app_name))}"
        }
    }

    /**
     * 协议内容加跳转
     */
    private fun showTextAndJump() {
        val urlTextColor = AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
        mBinding.tvAgreement2
            .set("Vantage Global Prime Pty Ltd's Financial Services Guide", urlTextColor) {
                goWebView("https://www.vantagemarkets.com/au-resources/pdf/Vantage_Global_Prime_Financial_Services_Guide.pdf?v=20220104", -3)
            }
            .set("Product Disclosure Statement", urlTextColor) {
                goWebView("https://www.vantagemarkets.com/au-resources/pdf/Vantage_Global_Prime_Pty_Ltd_Product_Disclosure_Statement_Retail_Client.pdf", -3)
            }
            .set("Terms and Conditions", urlTextColor) {
                goWebView("https://www.vantagemarkets.com/au-resources/pdf/Vantage_Global_Prime_Pty_Ltd_Retail_Client_Terms_and_Conditions.pdf", -3)
            }
            .set("Privacy Policy", urlTextColor) {
                goWebView("https://www.vantagemarkets.com/au-resources/pdf/Vantage_Global_Prime_Pty_Ltd_Privacy_Policy.pdf?v=20220104", -3)
            }
            .set("Vantage FX Pty Ltd's Privacy Policy", urlTextColor) {
                goWebView("https://www.vantagemarkets.com/au-resources/pdf/Vantage_FX_Pty_Ltd_Privacy_Policy.pdf?v=20220104", -3)
            }
            .set("Target Market Determination", urlTextColor) {
                goWebView("https://www.vantagemarkets.com/au-resources/pdf/Vantage_Target_Market_Determination.pdf?v=20220916", -3)
            }
        mBinding.tvAgreement8
            .set("Vantage's deposits and withdrawals policy", urlTextColor) {
                goWebView("https://www.vantagemarkets.com/en-au/deposit-withdrawals-policy/", -2)
            }
    }

    private fun goWebView(url: String, tradeType: Int) {
        val bundle = Bundle()
        bundle.putString("title", getString(R.string.app_name_upper))
        bundle.putString("url", url)
        // 需确保要跳转的页面是pdf的加载页面时传true
        bundle.putBoolean("isPDFOnly", true)
        bundle.putInt("tradeType", tradeType)
        openActivity(HtmlActivity::class.java, bundle)
    }

    /**
     * 选择mt4平台时需要弹框提示用户
     */
    private fun showSelectMt4ConfirmPopup() {
        mt4CentAccountShowTips()
        selectMt4ConfirmDialog.setTitle(getString(R.string.are_you_sure_select_platform_4th))
        // 切换mt5
        selectMt4ConfirmDialog.switchMt5Listener {
            var selectIndex = 0
            for ((index, bean) in mPresenter.platFormDataList.withIndex()) {
                if ("mt5".equals(bean.platFormName, true)) {
                    selectIndex = index // 找到第一个mt5账号
                    break
                }
            }
            changePlatformRefreshData(selectIndex)
            selectMt4ConfirmDialog.dismissDialog()
        }
        // 继续mt4
        selectMt4ConfirmDialog.continueMt4Listener {
            selectMt4ConfirmDialog.dismissDialog()
        }
        selectMt4ConfirmDialog.showDialog()
    }

    /**
     * 切换平台后刷新数据
     */
    private fun changePlatformRefreshData(position: Int) {
        mPresenter.platFormIndex = position
        mPresenter.accountTypeIndex = 0
        mPresenter.accountCurrencyIndex = 0
        refreshPlatFormView()
        platFormAdapter.selectTitle = platFormAdapter.data.getOrNull(position)?.getShowItemValue()
        platFormAdapter.notifyDataSetChanged()
        mt4CentAccountShowTips()
    }

    /**
     * mt4平台选择 Cent 账号需要展示提示文案
     */
    private fun mt4CentAccountShowTips() {
        if (SpManager.isV1V2()) { // v1 v2监管才有此提示
            val platFormName = mPresenter.platFormDataList.elementAtOrNull(mPresenter.platFormIndex)?.platFormName
            val isMt4 = "mt4" == platFormName?.lowercase(Locale.getDefault())
            if (isMt4) {
                val accountTypeNum = mPresenter.accountTypeDataList.elementAtOrNull(mPresenter.accountTypeIndex)?.accountTypeNum
                mBinding.tvMt4CentAccountTips.isVisible = mt4CentAccountTipsList.contains(accountTypeNum)
            } else {
                mBinding.tvMt4CentAccountTips.isVisible = false
            }
        }
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        KeyboardUtil.hideSoftKeyboard(this, mBinding.root, event)
        return super.dispatchTouchEvent(event)
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        KeyboardUtil.hideSoftKeyboard(this, mBinding.root, event)
        return super.onTouchEvent(event)
    }

    override fun onDestroy() {
        super.onDestroy()
        KeyboardUtil.unregisterSoftInputChangedListener(this.window)
    }

}
