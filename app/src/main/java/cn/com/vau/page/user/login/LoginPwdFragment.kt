package cn.com.vau.page.user.login

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity.RESULT_OK
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.NavHostFragment
import cn.com.vau.R
import cn.com.vau.common.base.fragment.BaseFrameFragment
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.PasskeyRegisterData
import cn.com.vau.data.account.SelectCountryNumberObjDetail
import cn.com.vau.data.profile.TelegramGetBotIdObjBean
import cn.com.vau.databinding.FragmentLoginPwdBinding
import cn.com.vau.page.common.selectArea.SelectAreaCodeActivity
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.login.LoginHandleType
import cn.com.vau.page.login.SendCodeType
import cn.com.vau.page.login.SignUpRequestBean
import cn.com.vau.page.login.ThirdHandleType
import cn.com.vau.page.login.activity.SignUpActivity
import cn.com.vau.page.login.activity.VerifyEmailCodeActivity
import cn.com.vau.page.login.activity.VerifyEmailCodeActivity.VerifyEmailCodeType
import cn.com.vau.page.user.login.model.LoginPwdVM
import cn.com.vau.page.user.loginPwd.LoginPwdContract
import cn.com.vau.page.user.loginPwd.LoginPwdModel
import cn.com.vau.page.user.loginPwd.LoginPwdPresenter
import cn.com.vau.util.ActivityManagerUtil
import cn.com.vau.util.CaptchaUtil
import cn.com.vau.util.GsonUtil
import cn.com.vau.util.KeyboardUtil
import cn.com.vau.util.PassKeyCredentialHelper
import cn.com.vau.util.TabType
import cn.com.vau.util.ifNull
import cn.com.vau.util.init
import cn.com.vau.util.setVp
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.dialog.CenterActionDialog
import com.netease.nis.captcha.Captcha
import com.netease.nis.captcha.CaptchaListener
import kotlinx.coroutines.launch
import org.json.JSONObject

@SuppressLint("SetTextI18n")
class LoginPwdFragment :
    BaseFrameFragment<LoginPwdPresenter, LoginPwdModel>(),
    LoginPwdContract.View {

    private val mBinding: FragmentLoginPwdBinding by lazy { FragmentLoginPwdBinding.inflate(layoutInflater) }
    private val mViewModel: LoginPwdVM by activityViewModels()

    private var captcha: Captcha? = null

    private val mobileFragment by lazy { LoginPwdMobileFragment.newInstance() }
    private val emailFragment by lazy { LoginPwdEmailFragment.newInstance() }

    private val passKeyCredentialHelper: PassKeyCredentialHelper by lazy {
        PassKeyCredentialHelper(requireActivity())
    }

    private var userAccount = ""
    private var userPwd = ""

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View = mBinding.root

    override fun initParam() {
        super.initParam()
        mPresenter.handleType = ac.intent.getIntExtra(Constants.HANDLE_TYPE, 0)
        val formType = activity?.intent?.getIntExtra("from_type", 0)
        if (formType != 0) {
            goBind(
                Bundle().apply {
                    putString(Constants.USER_EMAIL, activity?.intent?.getStringExtra(Constants.USER_EMAIL))
                    putString(Constants.USER_PWD, activity?.intent?.getStringExtra(Constants.USER_PWD))
                    putInt(Constants.HANDLE_TYPE, 1)
                }
            )
        }
        mPresenter.viewModel = mViewModel
    }

//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        // RegisterPresenter 获取验证码时如果手机号存在，引导用户进行邮箱登录
//        if (mPresenter.handleType == 99) {  //3.10 直接跳转到 Email
//            mBinding.mTabLayout.post {
//                mBinding.mViewPager2.setCurrentItem(1, false)
//            }
//        }
//    }

    override fun initView() {
        initTabLayout()
        when (mPresenter.handleType) {
            1 -> { // 三方登录
                mBinding.mHeaderBar.setTitleText(getString(R.string.link_your_account))
                mViewModel.setHintStr("")
                mViewModel.setNextStr(getString(R.string.link_now))
                mViewModel.hideBottomView()
            }
            // 注册时发现是假删除的账号，所以跳转到邮箱登录，并弹出后台返回的msg
            99 -> { // CRM有电话号码的账户 （3.10 1.5)
//                mViewModel.setHintStr(getString(R.string.maintenance_note))
//                mViewModel.setHintStrColor(ContextCompat.getColor(requireContext(), R.color.ce35728))
                mBinding.mTabLayout.post {
                    mBinding.mViewPager2.setCurrentItem(1, false)
//                    ac.intent.getStringExtra(Constants.DATA_MSG)?.let {
//                        ToastUtil.showToast(it)
//                    }
                }
            }

            else -> {
                mViewModel.setNextStr(getString(R.string.log_in))
                mViewModel.setHintStr(getString(R.string.after_applying_you_by_email))
            }
        }
    }

    override fun initData() {
        mPresenter.getLocalTel()
//        mPresenter.initFacebookInfo()
    }

    private fun initTabLayout() {
        val fragments = arrayListOf<Fragment>()
        fragments.add(mobileFragment)
        fragments.add(emailFragment)

        val titleList = mutableListOf(
            getString(R.string.phone_number),
            getString(R.string.email)
        )
        mBinding.mViewPager2.init(fragments, titleList, childFragmentManager, this)
        mBinding.mTabLayout.setVp(mBinding.mViewPager2, titleList, TabType.LINE_INDICATOR) {
            mPresenter.nextType = it.ifNull(0)
        }
        mBinding.mViewPager2.setCurrentItem(mPresenter.nextType, false)
    }

    override fun initListener() {
        super.initListener()
        mBinding.mHeaderBar.run {
            setStartBackIconClickListener {
                KeyboardUtil.hideSoftInput(ac)
                ac.finish()
            }
            setEndIconClickListener {
                openActivity(HelpCenterActivity::class.java)
            }
        }

        // 国家码
        mobileFragment.areaCodeClickListener {
            val bundle = Bundle()
            bundle.putString("selectAreaCode", mPresenter.areaCodeData.countryNum ?: Constants.defaultCountryNum)
            openActivity(SelectAreaCodeActivity::class.java, bundle, Constants.SELECT_AREA)
        }
        // 忘记密码
        mobileFragment.forgetPwdClickListener { type, userAccount ->
            this.userAccount = userAccount
            forgetPwdClick(type)
        }
        emailFragment.forgetPwdClickListener { type, userAccount ->
            this.userAccount = userAccount
            forgetPwdClick(type)
        }
        // 下一步
        mobileFragment.nextClickListener { userAccount, password ->
            this.userAccount = userAccount
            this.userPwd = password
            nextClick(1)
        }
        emailFragment.nextClickListener { userAccount, password ->
            this.userAccount = userAccount
            this.userPwd = password
            nextClick(2)
        }
        // 注册
        mobileFragment.registerClickListener {
            registerClick(it)
        }
        emailFragment.registerClickListener {
            registerClick(it)
        }
        // passkey
        mobileFragment.setCheckPasskeyListener {
            this.userAccount = it
            startCheckPasskey(it, "")
        }
        emailFragment.setCheckPasskeyListener {
            this.userAccount = it
            startCheckPasskey("", it)
        }
        // Telegram登录
        mobileFragment.telegramClickListener {
            mPresenter.telegramGetBotIdApi()
            // 神策自定义埋点
            mPresenter.sensorsTrackThirdLogin("mobile_login_page")
        }
        emailFragment.telegramClickListener {
            mPresenter.telegramGetBotIdApi()
            // 神策自定义埋点
            mPresenter.sensorsTrackThirdLogin("email_login_page")
        }
    }

    private fun startCheckPasskey(phone: String, email: String) {
        checkSupportPasskey { suppor ->
            if (suppor) {
//                showNetDialog()
                passKeyLoginData(phone, email)
            } else {
//                hideNetDialog()
//                showSoftInput(0)
            }
        }
    }

    private fun passKeyLoginData(phone: String, email: String) {
        mPresenter.passKeyLoginDataApi(phone, email)
    }

    override fun startPasskey(jsonData: String, passkeyId: String) {
        getSaveCredentials(jsonData, passkeyId)
    }

    override fun showSoftInput(delay: Long) {
        if (mBinding.mViewPager2.currentItem == 0) {
            mobileFragment.showPwdSoft(delay)
        } else {
            emailFragment.showPwdSoft(delay)
        }
    }

    private fun checkSupportPasskey(checkResult: (support: Boolean) -> Unit) {
        lifecycleScope.launch {
            val supportPasskey = passKeyCredentialHelper.isSupportPassKey()
            checkResult.invoke(supportPasskey)
        }
    }

    private fun getSaveCredentials(jsonData: String, passkeyId: String) {
        lifecycleScope.launch {
            val data = passKeyCredentialHelper.getSavedCredentials(jsonData)
            if (data == null) {
//                hideNetDialog()
//                showSoftInput()
                return@launch
            }
            // 调用登录接口
            val reponesData = GsonUtil.toBean(jsonData, PasskeyRegisterData::class.java)
            mPresenter.passkeyLoginApi(userAccount, reponesData?.challenge ?: "", data, passkeyId)
        }
    }

    /**
     * 忘记密码
     */
    private fun forgetPwdClick(nextType: Int) {
        mPresenter.nextType = nextType
        NavHostFragment
            .findNavController(this)
            .navigate(R.id.actionForgetFirstPwd, getTypeBundle())
    }

    /**
     * 下一步
     */
    private fun nextClick(type: Int) {
        mPresenter.pwdLoginApi(userAccount, userPwd, "")

        // 神策自定义埋点(v3500)
        // 登录按钮点击 -> 登录页面按钮点击时触发
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BUTTON_NAME, if (type == 1) "Mobile Login" else "Email Login") // 按钮名称
        SensorsDataUtil.track(SensorsConstant.V3500.LOGIN_PAGE_CLICK, properties)
    }

    /**
     * 注册
     */
    private fun registerClick(nextType: Int) {
        mPresenter.nextType = nextType
        ActivityManagerUtil.getInstance().finishActivity(SignUpActivity::class.java)
        openActivity(SignUpActivity::class.java, getTypeBundle())
    }

    override fun goBind(bundle: Bundle) {
        NavHostFragment.findNavController(this).navigate(R.id.actionLoginBind, bundle)
    }

    // 登录接口loginNew触发滑块验证
    override fun showCaptcha() {
        initCaptcha(1)
    }

    // 短信验证码接口getTelSms触发滑块验证
    override fun showVeriCaptcha(userTel: String, mobile: String, countryCode: String, code: String, pwd: String) {
        initCaptcha(2, mobile, countryCode, code, pwd, userTel = userTel)
    }

    /**
     * 三方登录触发网易易盾
     */
    override fun showThirdLoginCaptcha() {
        initCaptcha(3)
    }

    /**
     * 邮箱滑块验证
     */
    override fun showVerifyEmail(email: String, pwd: String, bizType: String) {
        initCaptcha(4, email, pwd, bizType = bizType)
    }

    /**
     * 跳转邮箱OTP验证页面
     */
    override fun goEmailOTPVerify(email: String, pwd: String, txId: String, type: String) {
        if (type == "10") { // 切换设备触发的邮箱OTP
            val signUpRequestBean = SignUpRequestBean()
            signUpRequestBean.email = email
            signUpRequestBean.pwd = pwd
            signUpRequestBean.txId = txId
            signUpRequestBean.sendCodeType = SendCodeType.EMAIL
            signUpRequestBean.handleType = LoginHandleType.EMAIL
            if (mPresenter.handleType == 1) {
                signUpRequestBean.thirdHandleType = ThirdHandleType.TELEGRAM
            }
            signUpRequestBean.verifyEmailCodeType = VerifyEmailCodeType.LOGIN_CHANGE_DEVICE
            VerifyEmailCodeActivity.open(requireContext(), signUpRequestBean)
        } else {
            val bundle = Bundle()
            bundle.putString("email", email)
            bundle.putString("txId", txId)
            bundle.putString("smsSendType", VerificationActivity.TYPE_SEND_EMAIL)
            bundle.putString("pwd", pwd)
            bundle.putBoolean("firstEmailLogin", true) // crm注册来源用户，未绑定手机号
            bundle.putInt(Constants.HANDLE_TYPE, mPresenter.handleType)
            NavHostFragment.findNavController(this).navigate(R.id.action_loginFragment_to_forgetPwdSecondFragment, bundle)
        }
    }

    /**
     * telegram-获取botId成功
     */
    override fun telegramGetBotIdSuccess(bean: TelegramGetBotIdObjBean?) {
        val intent = TelegramHelper.getTelegramH5Intent(requireContext(), bean)
        telegramH5AuthLauncher.launch(intent)
    }

    override fun showDialogToChangePassword() {
        CenterActionDialog.Builder(requireActivity())
            .setContent(getString(R.string.the_system_has_for_we_password_immediately))
            .setSingleButton(true)
            .setSingleButtonText(getString(R.string.reset_password))
            .setOnSingleButtonListener {
                NavHostFragment
                    .findNavController(this)
                    .navigate(R.id.actionForgetFirstPwd, getTypeBundle())
            }.build().showDialog()
    }

    /**
     * 初始化易盾滑块验证
     */
    private fun initCaptcha(type: Int, account: String = "", countryCode: String = "", code: String = "", pwd: String = "", bizType: String? = null, userTel: String = "") {
        // 易盾
        val loginCaptchaListener = object : CaptchaListener {
            override fun onReady() {}
            override fun onValidate(result: String, validate: String, msg: String) {
                if (!TextUtils.isEmpty(validate)) {
                    if (type == 1) {
                        mPresenter.pwdLoginApi(userAccount, userPwd, validate)
                    } else if (type == 2) {
                        mPresenter.getBindingTelSMSApi(userTel, pwd, account, countryCode, code, "10", validate)
                    } else if (type == 3) {
                        mPresenter.thirdLoginTelegramApi(validate, "")
                    } else if (type == 4) {
                        mPresenter.emailSendEmailCodeApi(bizType.ifNull(), account, pwd)
                    }
                }
            }

            // 建议直接打印错误码，便于排查问题
            override fun onError(code: Int, msg: String) {
            }

            override fun onClose(closeType: Captcha.CloseType) {
                // TODO Felix 删了吧
                if (closeType == Captcha.CloseType.VERIFY_SUCCESS_CLOSE) {
                    Handler(Looper.getMainLooper()).post {
                        // 成功 + 关闭
                    }
                }
            }
        }
        captcha = CaptchaUtil.getCaptcha(requireContext(), loginCaptchaListener)
        captcha?.validate()
    }

    @Suppress("DEPRECATION")
    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (resultCode) {
            Constants.SELECT_AREA -> {
                val areaData = data?.extras?.get(Constants.SELECT_AREA_CODE) as SelectCountryNumberObjDetail
                mPresenter.areaCodeData = areaData
                mViewModel.setAreaCode(areaData.countryNum ?: "86")

                mPresenter.areaCodeData = areaData

                SpManager.putCountryNum(areaData.countryNum.ifNull())
                SpManager.putCountryName(areaData.countryName.ifNull())
                SpManager.putCountryCode(areaData.countryCode.ifNull())
            }
        }
    }

    private fun getTypeBundle(): Bundle {
        val bundle = Bundle()
        bundle.putBoolean("isShowEmail", mPresenter.nextType == 1)
        bundle.putInt(Constants.HANDLE_TYPE, mPresenter.handleType)
        bundle.putSerializable("areaCodeData", mPresenter.areaCodeData)
        bundle.putString("username", userAccount.trim())
        return bundle
    }

    /**
     * 三方登录成功后刷新布局
     */
    override fun thirdLoginSuccessRefreshView() {
        mBinding.mHeaderBar.setTitleText(getString(R.string.link_your_account))
        mViewModel.setHintStr("")
        mViewModel.setNextStr(getString(R.string.link_now))
        mViewModel.hideBottomView()
    }

    /**
     * telegram H5授权完成回调
     */
    private val telegramH5AuthLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            mPresenter.thirdLoginTelegramApi("", "")
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
//        mBinding.mViewPager2.adapter = null
    }

    companion object {

        @JvmStatic
        fun newInstance() = LoginPwdFragment()
    }
}