package cn.com.vau.page

import android.net.Uri
import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

/**
 * 输入描述
 * Created by THINKPAD on 2020/4/27.
 */
@Keep
@Parcelize
data class DepositBundleData(
    var mt4AccountId: String? = null,
    var currency: String? = null,
    var couponId: String? = null,
    var userCouponId: String? = null,
    var orderAmount: String? = null,
    var realAmount: String? = null,
    var url: String? = null,
    var depositRate: String? = null,
    var title: String? = null,
    var tradeType: Int? = null,
    var depositeFrom: String? = null,//页面来源  1入金页面  2来自银行卡管理页面
    var isDepositeMuchOf: Int? = null,//是否超限 1代表未超限额，0代表超限额或第一次信用卡支付
    var cardName: String? = null,
    var cardNumber: String? = null,
    var cardYear: String? = null,
    var cardMonth: String? = null,
    var cardCvv: String? = null,
    var bankName: String? = null,
    var payType: String? = null,
    var creditPath: Uri? = null,
    var canChangePayType: Boolean = true,
    var couponSource: String? = null,
) : Parcelable