package cn.com.vau.page.login.fragment

import android.content.Intent
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.*
import cn.com.vau.*
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmFragment
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.SelectCountryNumberObjDetail
import cn.com.vau.databinding.FragmentSignUpPwdBinding
import cn.com.vau.page.common.selectArea.SelectAreaCodeActivity
import cn.com.vau.page.html.HtmlActivity
import cn.com.vau.page.login.*
import cn.com.vau.page.login.activity.SignUpActivity
import cn.com.vau.page.login.viewmodel.fragment.SignUpPwdViewModel
import cn.com.vau.page.user.loginPwd.LoginPwdActivity
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.CenterActionWithIconDialog
import com.netease.nis.captcha.Captcha

/**
 * author：lvy
 * date：2025/03/10
 * desc：kyc注册第二步 -> 输入手机号/邮箱、密码
 */
class SignUpPwdFragment : BaseMvvmFragment<FragmentSignUpPwdBinding, SignUpPwdViewModel>() {

    private var pwdAllSelected = false // 检查密码是否符合规则

    private var mCaptcha: Captcha? = null

    override fun initParam(savedInstanceState: Bundle?) {
        arguments?.run {
            mViewModel.signUpRequestBean = getParcelable("signUpRequestBean")
            mViewModel.signUpRequestBean?.handleType = getParcelable<LoginHandleType>(Constants.HANDLE_TYPE)
        }
    }

    override fun initView() {
        // 显示不同布局
        if (mViewModel.signUpRequestBean?.handleType == LoginHandleType.EMAIL) {
            mBinding.mobileView.isVisible = false
            mBinding.emailView.isVisible = true
        } else {
            mBinding.mobileView.isVisible = true
            mBinding.emailView.isVisible = false
        }
        mBinding.pwdView.setHintText("${getString(R.string.password)} ${getString(R.string._8_16_characters)}")
        // 邀请信息title
        mBinding.tvReferralTitle.text = buildString {
            append(getString(R.string.referral_if_any))
            append(" (")
            append(getString(R.string.optional))
            append(")")
        }
        // 是否有邀请码
        val shareCode = SpManager.getInviteCodeRegister("")
        if (shareCode.isNotBlank()) {
            mBinding.ivReferral.rotation = 180f
            mBinding.referralView.isVisible = true
            mBinding.referralView.setInputText(shareCode)
        }
        // 检查是否存在参数
        val cid = SpManager.getCid("")
        val cxd = SpManager.getCxd("")
        val raf = SpManager.getRaf("")
        val agentAccountNum = SpManager.getAgentAccountNum("")
        if (cid.isNotEmpty() || cxd.isNotEmpty() || raf.isNotEmpty() || agentAccountNum.isNotEmpty()) {
            mBinding.tvReferralTitle.isVisible = false
            mBinding.ivReferral.isVisible = false
            mBinding.referralView.isVisible = false
        }

        // 密码检测提示
        mBinding.layoutPasswordCheck.tvPasswordMatch.isGone = true
        mBinding.layoutPasswordCheck.tvPasswordSpecial.text = buildString {
            append(getString(R.string.at_least_1_following_characters))
            append("!@#$%^&*.()")
        }
        // 隐私政策文字及跳转
        val urlTextColor = AttrResourceUtil.getColor(requireContext(), R.attr.color_c1e1e1e_cebffffff)
        mBinding.tvAgreementTip.text = getString(R.string.by_click_sign_up_you_privacy_policy, getString(R.string.privacy_policy))
        mBinding.tvAgreementTip.set(getString(R.string.privacy_policy), urlTextColor) {
            openActivity(HtmlActivity::class.java, Bundle().apply {
                putInt("tradeType", 9)
                putString("title", getString(R.string.privacy_policy))
            })
        }
        // 按钮下方链接文案
        showLinkTittleInfo()
    }

    override fun initData() {
        // 初始化国家区号
        mViewModel.initAreaCode()
    }

    override fun createObserver() {
        // 国家区号设置成功
        mViewModel.areaCodeLiveData.observe(this) {
            mBinding.mobileView.setAreaCodeText(it)
        }
        // 手机号已存在
        mViewModel.phoneExistLiveData.observe(this) {
            showPhoneExistDialog()
        }
        // 触发网易易盾验证
        mViewModel.showCaptchaLiveData.observe(this) {
            showCaptcha()
        }
        // 注册成功
        mViewModel.signUpSuccessLiveData.observe(this) {
            openActivity(MainActivity::class.java)
            ActivityManagerUtil.getInstance().finishOtherActivities(MainActivity::class.java)
        }
    }

    override fun initListener() {
        // 选择国家区号
        mBinding.mobileView.areaCodeClickListener {
            val bundle = Bundle()
            bundle.putString("selectAreaCode", mViewModel.signUpRequestBean?.countryNum.ifNull(Constants.defaultCountryNum))
            selectAreaCodeLauncher.launch(Intent().apply {
                setClass(requireContext(), SelectAreaCodeActivity::class.java)
                putExtras(bundle)
            })
        }
        // 手机号输入框
        mBinding.mobileView.afterTextChangedListener {
            checkNextBtn()
        }
        // 邮箱输入框
        mBinding.emailView.afterTextChangedListener {
            checkNextBtn()
        }
        // 密码输入框
        mBinding.pwdView.afterTextChangedListener {
            checkPassword()
        }
        // 密码输入框的焦点
        mBinding.pwdView.onFocusChangeListener {
            mBinding.layoutPasswordCheck.root.isVisible = it
            if (it) {
                mBinding.tvPwdErrTips.isVisible = false
            } else {
                // 密码输入框失去焦点时要检查密码是否合规
                if (mBinding.pwdView.getInputText().isNotBlank()) {
                    mBinding.tvPwdErrTips.isVisible = !pwdAllSelected
                }
            }
        }
        // 邀请人
        mBinding.tvReferralTitle.setOnClickListener {
            if (mBinding.referralView.isVisible) {
                mBinding.ivReferral.rotation = 0f
                mBinding.referralView.isVisible = false
            } else {
                mBinding.ivReferral.rotation = 180f
                mBinding.referralView.isVisible = true
            }
        }
        mBinding.ivReferral.setOnClickListener {
            mBinding.tvReferralTitle.performClick()
        }
        // 隐私政策
        mBinding.cbAgreement.setOnCheckedChangeListener { _, isChecked ->
            checkNextBtn()
        }
        // 下一步
        mBinding.tvNext.clickNoRepeat {
            clickNext()
            mViewModel.sensorsTrackClick()
        }
    }

    /**
     * 检查密码是否符合规则
     */
    private fun checkPassword() {
        val pwdStr = mBinding.pwdView.getInputText()
        mBinding.layoutPasswordCheck.tvPasswordLength.isSelected = pwdStr.length in 8..16
        mBinding.layoutPasswordCheck.tvPasswordContent.isSelected = RegexUtil.isContainsLetter(pwdStr)
        mBinding.layoutPasswordCheck.tvPasswordNumber.isSelected = RegexUtil.isContainsNumber(pwdStr)
        mBinding.layoutPasswordCheck.tvPasswordSpecial.isSelected = RegexUtil.isContainsSpecial(pwdStr)
        // 对下方的按钮的判断进行赋值
        pwdAllSelected = mBinding.layoutPasswordCheck.tvPasswordLength.isSelected &&
                mBinding.layoutPasswordCheck.tvPasswordContent.isSelected &&
                mBinding.layoutPasswordCheck.tvPasswordNumber.isSelected &&
                mBinding.layoutPasswordCheck.tvPasswordSpecial.isSelected
        checkNextBtn()
    }

    /**
     * 检测按钮是否可点击
     */
    private fun checkNextBtn() {
        // 账号是否符合规则
        val accountNotBlank = if (mViewModel.signUpRequestBean?.handleType == LoginHandleType.PHONE) {
            mBinding.mobileView.getInputText().isNotBlank()
        } else {
            mBinding.emailView.getInputText().isNotBlank()
        }
        // 是否同意了隐私政策
        val cbAgreement = mBinding.cbAgreement.isChecked
        // 按钮是否可点击
        mBinding.tvNext.isEnabled = accountNotBlank && pwdAllSelected && cbAgreement
    }

    /**
     * 下一步
     */
    private fun clickNext() {
        // 赋值给 mViewModel.signUpRequestBean 方便操作
        mViewModel.signUpRequestBean?.referral = mBinding.referralView.getInputText() // 邀请信息
        if (mViewModel.signUpRequestBean?.handleType == LoginHandleType.PHONE) {
            mViewModel.signUpRequestBean?.mobile = mBinding.mobileView.getInputText() // 手机号
        } else {
            mViewModel.signUpRequestBean?.email = mBinding.emailView.getInputText() // 邮箱
        }
        mViewModel.signUpRequestBean?.pwd = mBinding.pwdView.getInputText() // 密码
        // 注册操作
        mViewModel.appUserRegisterApi()
    }

    /**
     * 触发网易易盾验证
     */
    private fun showCaptcha() {
        mCaptcha = CaptchaUtil.getCaptcha(requireContext()) {
            mViewModel.appUserRegisterApi(it)
        }
        mCaptcha?.validate()
    }

    /**
     * 按钮下方链接文案
     */
    private fun showLinkTittleInfo() {
        val urlColor = ContextCompat.getColor(requireContext(), R.color.ce35728)
        if (mViewModel.signUpRequestBean?.thirdHandleType == ThirdHandleType.TELEGRAM) {
            mBinding.tvLogin.text = getString(R.string.already_have_an_account_link_your_account)
            mBinding.tvLogin.set(getString(R.string.link_your_account), urlColor, false) {
                // 跳转到三方绑定，目前先复用登录页面处理
                jumpLoginPage(1)
            }
        } else {
            mBinding.tvLogin.text = getString(R.string.already_have_an_account_login)
            mBinding.tvLogin.set(getString(R.string.log_in), urlColor, false) {
                jumpLoginPage(0)
            }
        }
    }

    private fun jumpLoginPage(handleType: Int) {
        openActivity(LoginPwdActivity::class.java, bundleOf().apply {
            putInt(Constants.HANDLE_TYPE, handleType)
        })
        requireActivity().finish()
        ActivityManagerUtil.getInstance().finishActivity(SignUpActivity::class.java)
    }

    /**
     * 手机号注册时发现该手机号已绑定了邮箱，引导用户去邮箱登录
     */
    private fun showPhoneExistDialog() {
        CenterActionWithIconDialog.Builder(requireActivity())
            .setTitle(getString(R.string.existing_account_found))
            .setContent(getString(R.string.this_phone_number_please_to_continue))
            .setSingleButton(true)
            .setSingleButtonText(getString(R.string.log_in_with_email))
            .setDismissOnBackPressed(false)
            .setOnSingleButtonListener {
                // TODO: lvyang 重构登录页面时所有传99的地方都要修改（需要切换到登录页面并切换到邮箱tab）
                val bundle = Bundle()
                bundle.putInt(Constants.HANDLE_TYPE, 99)
//                bundle.putString(Constants.DATA_MSG, msg)
                openActivity(LoginPwdActivity::class.java, bundle)
                ActivityManagerUtil.getInstance().finishActivity(SignUpActivity::class.java)
            }
            .setLinkText(getString(R.string.use_a_different_phone_number))
            .setLinkListener {
                mBinding.mobileView.clearText()
            }
            .build().showDialog()
    }

    private val selectAreaCodeLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        if (it.resultCode == Constants.SELECT_AREA) {
            val areaData = it?.data?.getParcelableExtra<SelectCountryNumberObjDetail>(Constants.SELECT_AREA_CODE)
            areaData?.let {
                mViewModel.setAreaCodeData(it)
            }
        }
    }

    companion object {
        fun newInstance(signUpRequestBean: SignUpRequestBean?, handleType: LoginHandleType?) = SignUpPwdFragment().apply {
            arguments = bundleOf().apply {
                putParcelable("signUpRequestBean", signUpRequestBean)
                putParcelable(Constants.HANDLE_TYPE, handleType)
            }
        }
    }
}