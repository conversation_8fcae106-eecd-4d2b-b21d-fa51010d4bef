package cn.com.vau.page.user.openAccoGuide.lv3

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import cn.com.vau.common.base.fragment.BaseFragment
import cn.com.vau.common.view.VerifyCompHandler
import cn.com.vau.data.account.AccoSelectItem
import cn.com.vau.databinding.FragmentOpenLv3PoaInfoBinding
import cn.com.vau.page.user.openAccoGuide.OpenAccoGuideBaseActivity
import cn.com.vau.page.user.openAccoGuide.lv3.vm.OpenLv3ViewModel
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import org.json.JSONObject

class OpenLv3POAInfoFragment : BaseFragment() {

    private val binding: FragmentOpenLv3PoaInfoBinding by lazy { FragmentOpenLv3PoaInfoBinding.inflate(layoutInflater) }
    private var verifyHandler = VerifyCompHandler()
    private val viewModel: OpenLv3ViewModel by lazy { ViewModelProvider(requireActivity())[OpenLv3ViewModel::class.java] }

    private var poaTypeList = mutableListOf<AccoSelectItem>()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        return binding.root
    }

    override fun initParam() {
        super.initParam()
        LogEventUtil.setLogEvent(
            BuryPointConstant.V334.REGISTER_LIVE_PAGE_VIEW, hashMapOf(
                "Page_name" to "${(activity as? OpenAccoGuideBaseActivity<*>)?.buryPointMsg}-Lvl3-1"
            )
        )
    }

    override fun initView() {
        super.initView()
        verifyHandler.add(
            binding.viewEtNationality,
            binding.viewEtCountryResidence,
            binding.viewEtAddress,
            binding.viewEtCityResidence,
            binding.viewDdPoaType
        ).to(
            binding.tvNext
        ).submit {
            viewModel.inputAddress = binding.viewEtAddress.text()
            viewModel.inputCityResidence = binding.viewEtCityResidence.text()
            activity?.let {
                (it as OpenAccoGuideLv3Activity).gotoNext()
            }

            // 神策自定义埋点(v3500)，点击事件
            sensorsTrackClick()
        }
    }

    override fun initData() {
        super.initData()
        viewModel.showLiveDataLoading()
        viewModel.getProcess()
    }

    override fun initListener() {
        super.initListener()
        viewModel.accountSelectLiveData.observe(this) {
            if ("V00000" == it.resultCode) {
                val list = it.data?.obj?.accountPoaTypeList
                poaTypeList.clear()
                poaTypeList.addAll(list ?: arrayListOf())
                val dataList = mutableListOf<String>()
                dataList.addAll((list ?: arrayListOf()).map { data -> data.displayName.ifNull() })
                binding.viewDdPoaType.setData(dataList).onSelected { position ->
                    val selectData = poaTypeList.elementAtOrNull(position)
                    viewModel.selectedPOAType = selectData
                    viewModel.poaTypeLiveData.value = selectData?.id.ifNull(-1)
                }
                val poaType = viewModel.openData?.poaType.ifNull(-1)
                if (poaType != -1 && poaTypeList.isNotEmpty()) {
                    viewModel.poaTypeLiveData.value = poaType
                    val accountSelectBean = poaTypeList.find { item -> item.id == poaType }
                    viewModel.selectedPOAType = accountSelectBean
                    binding.viewDdPoaType.setText(accountSelectBean?.displayName.ifNull())
                }
            } else {
                ToastUtil.showToast(it.msgInfo)
            }
        }
        viewModel.getProcessLiveData.observe(this) {
            viewModel.hideLiveDataLoading()
            if ("V00000" == it.resultCode) {
                val data = it.data?.obj
                data?.let { bean ->
                    viewModel.openData = bean
                    viewModel.getAccountSelect()

                    binding.viewEtAddress.setText(bean.address.ifNull())
                    binding.viewEtCityResidence.setText(bean.suburb.ifNull())
                    binding.viewEtNationality.setText(bean.nationalityName.ifNull())
                    binding.viewEtCountryResidence.setText(bean.countryName.ifNull())
                }
            } else {
                ToastUtil.showToast(it.msgInfo)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // 神策自定义埋点(v3500)，页面浏览事件
        sensorsTrackPageView()
    }

    /**
     * 神策自定义埋点(v3500)
     * 开户及验证页面浏览 -> 开户验证页面加载完成时触发
     */
    private fun sensorsTrackPageView() {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.IDENTITY_LEVEL, "Lv3.POA Authentication") // 验证阶段
        SensorsDataUtil.track(SensorsConstant.V3500.OPEN_IDENTITY_PAGE_VIEW, properties)
    }

    /**
     * 神策自定义埋点(v3500)
     * 开户及验证页面点击 -> 开户验证页面按钮点击成功时触发
     */
    private fun sensorsTrackClick() {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.IDENTITY_LEVEL, "Lv3.POA Authentication") // 验证阶段
        properties.put(SensorsConstant.Key.IDENTITY_STEP, "Lv3-POA Information") // 验证步骤
        properties.put(SensorsConstant.Key.BUTTON_NAME, "Lv3-Next") // 按钮名称
        SensorsDataUtil.track(SensorsConstant.V3500.OPEN_IDENTITY_PAGE_CLICK, properties)
    }

    companion object {
        fun newInstance() = OpenLv3POAInfoFragment()
    }

}