package cn.com.vau.page.user.login.presenter

import android.os.Bundle
import android.text.TextUtils
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.*
import cn.com.vau.common.event.TokenErrorData
import cn.com.vau.common.greendao.dbUtils.*
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.*
import cn.com.vau.data.account.*
import cn.com.vau.page.WithdrawalBundleBean
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.profile.activity.twoFactorAuth.activity.*
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.FirebaseManager
import io.reactivex.disposables.Disposable
import org.greenrobot.eventbus.EventBus

class VerificationPresenter : VerificationContract.Presenter() {

    var isFristCount = true

    private val sendCodeUtil by lazy { SendCodeUtil() }

    override fun initSendCodeUtil(listener: SendCodeUtil.SendCodeListener) {
        sendCodeUtil.initData(60, listener)
    }

    override fun startSendCodeUtil() {
        if (sendCodeUtil.isAlive() != false) {
            sendCodeUtil.start()
            mRxManager.add(sendCodeUtil.disposable)
        }
    }

    override fun getBindingTelSMSApi(
        userTel: String,
        userPassword: String,
        phoneCountryCode: String,
        code: String,
        type: String,
        validateCode: String?,
        smsSendType: String
    ) {
        val params = HashMap<String, Any>()
        if (!validateCode.isNullOrEmpty()) {
            params["recaptcha"] = validateCode
            params["smsCodeId"] = SpManager.getSmsCodeId("")
        }
        params["userTel"] = userTel
        params["type"] = type //类型

        params["phoneCountryCode"] = phoneCountryCode
        params["smsSendType"] = smsSendType
        params["userPassword"] = userPassword
        params["code"] = code
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(params.json, AESUtil.PWD_AES_KEY)
        mModel?.getBindingTelSMSApi(paramMap, object : BaseObserver<ForgetPwdVerificationCodeBean>() {
            override fun onHandleSubscribe(d: Disposable) {
                mRxManager.add(d)
            }

            override fun onNext(baseBean: ForgetPwdVerificationCodeBean) {
                SpManager.putSmsCodeId("")
                if ("V00000" == baseBean.resultCode) {
                    ToastUtil.showToast(baseBean.msgInfo)
                    startSendCodeUtil()
                } else {
                    if ("V10060" == baseBean.resultCode) { //易盾 需要滑动窗口
                        if (baseBean.data != null && baseBean.data?.obj != null) {
                            SpManager.putSmsCodeId(baseBean.data?.obj?.smsCodeId)
                        }
                        mView?.showCaptcha(0)
                        return
                    }
                    ToastUtil.showToast(baseBean.msgInfo)
                }
            }
        })
    }

    fun thirdpartyLoginApi(veriCode: String?, recaptcha: String?) {

        mView?.showNetDialog()
        val map = hashMapOf<String, Any?>()
        map["type"] = "10"
        // 第三方账号(facebook的邮箱)
        map["validateCode"] = veriCode.ifNull()
        map["recaptcha"] = recaptcha.ifNull()
        SpManager.getTelegramH5Data()?.let { bean ->
            map["thirdpartyType"] = "4"
            map["telegramId"] = bean.id.ifNull()
            map["first_name"] = bean.first_name.ifNull()
            map["last_name"] = bean.last_name.ifNull()
            map["auth_date"] = bean.auth_date.ifNull()
            map["hash"] = bean.hash.ifNull()
            map["username"] = bean.username.ifNull()
            map["photo_url"] = bean.photo_url.ifNull()
        }
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().thirdpartyLoginApi(paramMap),
            object : BaseObserver<LoginBean>() {
                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onNext(loginBean: LoginBean?) {
                    mView?.hideNetDialog()
                    val loginResultCode = loginBean?.resultCode

                    val bundle = Bundle()

//                    if (loginResultCode == "V50000") {
//                        val status = loginBean.data?.obj?.status
//                        bundle.putInt(Constants.HANDLE_TYPE, 1)
//                        // 失败时返回,0:接口错误, 4:绑定手机, 5:账号绑定, 6:注册
//                        when (status) {
//                            "4" -> openActivity(BindEmailActivity::class.java, bundle)
//                            "5" -> openActivity(LoginPwdActivity::class.java, bundle)
//                            "6" -> openActivity(RegisterFirstActivity::class.java, bundle)
//                            else -> ToastUtils.showToast(loginBean.msgInfo)
//                        }
//                        return
//                    }

                    // V10016:IB V10017:普通用户
                    if (loginResultCode == "V10016" || loginResultCode == "V10017") {
                        saveUserData(loginBean)
                        // Fcm上报推送设备号
                        val localFcmToken = SpManager.getTokenFcm()
                        FirebaseManager.bindFcmToken(localFcmToken)

                        val isBind2FA = loginBean.data?.obj?.twoFactorUser == true
                        val userid = loginBean.data?.obj?.userId.ifNull()
                        val isBinded = SpManager.getUser2faBindEd(userid, false)
                        if (isBind2FA || isBinded) {
                            // 走原逻辑
                            bundle.putInt(Constants.IS_FROM, 1)
                            openActivity(AccountManagerActivity::class.java, bundle)
                            EventBus.getDefault().post(NoticeConstants.REFRESH_PERSONAL_INFO_DATA)
                        } else {
                            // 未绑定 走绑定流程
                            TFABindActivity.open(context, TFAVerifyActivity.FROM_LOGIN)
                        }

                        mView?.finish()
                    }
//                    else if (loginResultCode == "V10035") {
//                        val mobile = loginBean.data?.obj?.userTel.ifNull()         //xujin说一定会有，因为没有会先让他补全
//                        val countryCode = loginBean.data?.obj?.countryCode.ifNull()
//                        val code = loginBean.data?.obj?.code.ifNull()
//                        getBindingTelSMS(thirdpartyId, thirdpartyAccount, mobile, countryCode, code, "10", "")
//                    }
                    else if (loginResultCode == "V10060") {
                        mView?.showCaptcha(1)
                    } else {
                        ToastUtil.showToast(loginBean?.msgInfo)
                    }
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    mView?.hideNetDialog()
                }
            })
    }

    fun pwdLoginApi(
        handleType: Int,
        nextType: Int,
        veriCode: String?,
        countryCode: String?,
        code: String?,
        mobile: String?,
        pwd: String?,
        recaptcha: String?,
    ) {
        mView?.showNetDialog()

        val map = hashMapOf<String, Any?>()
        map["count"] = mobile ?: ""
        map["userPassword"] = pwd ?: ""
        map["recaptcha"] = recaptcha ?: ""
        map["type"] = "10"
        map["validateCode"] = veriCode.ifNull()
        if (nextType == 0) {
            map["countryCode"] = countryCode.ifNull()
            map["code"] = code.ifNull()
        }
        if (handleType == 1) {
            if (nextType == 0) {
                map["userTel"] = mobile ?: ""
            } else {
                map["userEmail"] = mobile ?: ""
            }

            SpManager.getTelegramH5Data()?.let { bean ->
                map["thirdpartyType"] = "4"
                map["telegramId"] = bean.id.ifNull()
                map["first_name"] = bean.first_name.ifNull()
                map["last_name"] = bean.last_name.ifNull()
                map["auth_date"] = bean.auth_date.ifNull()
                map["hash"] = bean.hash.ifNull()
                map["username"] = bean.username.ifNull()
                map["photo_url"] = bean.photo_url.ifNull()
            }
            val paramMap = hashMapOf<String, Any?>()
            paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
            mModel?.bindUserApi(paramMap, object : BaseObserver<LoginBean>() {
                override fun onNext(data: LoginBean) {
                    mView?.hideNetDialog()
                    dealLoginData(handleType, nextType, data, mobile ?: "", pwd ?: "")
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    mView?.hideNetDialog()
                }
            })
        } else {
            // loginNew
            val paramMap = hashMapOf<String, Any?>()
            paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
            mModel?.pwdLoginApi(paramMap, object : BaseObserver<LoginBean>() {
                override fun onNext(data: LoginBean) {
                    mView?.hideNetDialog()
                    dealLoginData(handleType, nextType, data, mobile ?: "", pwd ?: "")
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    mView?.hideNetDialog()
                }
            })
        }
    }

    override fun updateTelApi(userToken: String?, phone: String?, password: String?, validateCode: String?, code: String?, countryCode: String?) {
        mView?.showNetDialog()
        val params = HashMap<String, Any>()
        params["userToken"] = userToken ?: ""
        params["phone"] = phone ?: ""
        params["password"] = password ?: "" //登录密码
        params["validateCode"] = validateCode ?: "" //验证码
        params["code"] = code ?: ""
        params["countryCode"] = countryCode ?: ""
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(params.json, AESUtil.PWD_AES_KEY)
        mModel?.updateTelApi(paramMap, object : BaseObserver<ChangeUserInfoSuccessBean>() {
            override fun onHandleSubscribe(d: Disposable) {
                mRxManager.add(d)
            }

            override fun onNext(baseBean: ChangeUserInfoSuccessBean) {
                mView?.hideNetDialog()
                // 更改手机号流程变更：(后台会变更该账号的Token)
                // 由原来的接口成功关闭页面、等待通知退出登录、或请求其他接口被动触发V50002 退出登录，这样会造成后续其他接口带空token请求，在退出登录的登录页连续吐司Parmeter Error
                // 现改为接口成功时连带返回退出弹窗的文案，主动执行退出登录(或ReStartApp 或清栈回首页)，这样做避免了触发其他页面onResume接口请求时带空token吐司的问题，同时也简化了流程
                if ("********" == baseBean.resultCode) {
                    ToastUtil.showToast(baseBean.msgInfo)
                    mView?.refreshTel(baseBean.data)
                } else {
                    ToastUtil.showToast(baseBean.msgInfo)
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    fun dealLoginData(handleType: Int, nextType: Int, loginBean: LoginBean, userTel: String, userPassword: String) {
        if (loginBean.data?.obj?.crmUserId.isNullOrBlank().not()) {
            SpManager.putCrmUserId(loginBean.data?.obj?.crmUserId.ifNull())
        }
        val bundle = Bundle()
        when (loginBean.resultCode) {
            /*
            16,17 账户列表
            05 绑定页面
             */
            "V10017", "V10016" -> { // 用户登陆成功
                SpManager.putSuperviseNum(loginBean.data?.obj?.regulator.ifNull("0"))
                saveUserData(handleType, nextType, loginBean, userPassword)
                // Fcm上报推送设备号
                val localFcmToken = SpManager.getTokenFcm()
                FirebaseManager.bindFcmToken(localFcmToken)

                val isBind2FA = loginBean.data?.obj?.twoFactorUser == true
                val userid = loginBean.data?.obj?.userId.ifNull()
                val isBinded = SpManager.getUser2faBindEd(userid, false)
                if (isBind2FA || isBinded) {
                    // 走原逻辑
                    bundle.putInt(Constants.IS_FROM, 1)
                    openActivity(AccountManagerActivity::class.java, bundle)
                } else {
                    // 未绑定 走绑定流程
                    TFABindActivity.open(context, TFAVerifyActivity.FROM_LOGIN)
                }

                mView?.finish()
            }
//            "V10005" -> {
//                MMKVUtil.getInstance()
//                    .saveString(StorageConstants.SUPERVISE_NUM, loginBean.data?.obj?.regulator ?: "0")
//                bundle.putString(Constants.USER_EMAIL, userTel)
//                bundle.putString(Constants.USER_PWD, userPassword)
//                bundle.putInt(Constants.HANDLE_TYPE, handleType)
//                mView?.goBind(bundle)
//            }
            "V10060" -> {
                mView?.showCaptcha(1)
                return
            }

            else -> {
                ToastUtil.showToast(loginBean.msgInfo)
            }
        }
    }

    private fun saveUserData(loginBean: LoginBean) {
        val userTel = loginBean.data?.obj?.userTel
        val areaCode = loginBean.data?.obj?.code
        val countryCode = loginBean.data?.obj?.countryCode
        UserDataUtil.setUserTel(userTel)
        UserDataUtil.setCountryCode(countryCode)
        UserDataUtil.setAreaCode(areaCode)
        UserDataUtil.setUserId(loginBean.data?.obj?.userId)
        UserDataUtil.setUserType(if (loginBean.resultCode == "V10017") 1 else 0)
        UserDataUtil.setLoginToken(loginBean.data?.obj?.token)
        UserDataUtil.setXToken(loginBean.data?.obj?.xtoken)
        val fastCloseState = loginBean.data?.obj?.fastCloseState
        UserDataUtil.setFastCloseState(if (TextUtils.isEmpty(fastCloseState)) "2" else fastCloseState)
        val fastCloseCopyOrder = loginBean.data?.obj?.fastCloseCopyOrder // 快速停止跟单
        UserDataUtil.setFastStopCopyState(if (TextUtils.isEmpty(fastCloseCopyOrder)) "2" else fastCloseCopyOrder)
        val orderConfirmation = loginBean.data?.obj?.orderConfirmation
        UserDataUtil.setOrderConfirmState(if (TextUtils.isEmpty(orderConfirmation)) "2" else orderConfirmation)
        UserDataUtil.setEmail(loginBean.data?.obj?.email)
        UserDataUtil.setUserNickName(loginBean.data?.obj?.userNick)
        UserDataUtil.setUserPic(loginBean.data?.obj?.pic)

        SpManager.putUserTel(userTel.ifNull())
        SpManager.putCountryCode(countryCode.ifNull())
        SpManager.putCountryNum(areaCode.ifNull())
//        SPUtil.saveData(context, StoreConstants.COUNTRY_NAME, "")

        //绑定神策业务ID，场景4、场景2
        val userBean = loginBean.data?.obj
        SensorsDataUtil.bindBusinessIdForLogin(
            userBean?.userTel, userBean?.email, userBean?.emailEventID, userBean?.crmUserId
        )

        // 登录firebase
        FirebaseManager.userLogin()
    }

    private fun saveUserData(handleType: Int, nextType: Int, loginBean: LoginBean, userPassword: String) {
        val userTel = loginBean.data?.obj?.userTel
        val areaCode = loginBean.data?.obj?.code
        val countryCode = loginBean.data?.obj?.countryCode
        val email = loginBean.data?.obj?.email
        UserDataUtil.setUserTel(userTel)
        UserDataUtil.setCountryCode(countryCode)
        UserDataUtil.setAreaCode(areaCode)
        UserDataUtil.setUserId(loginBean.data?.obj?.userId)
        UserDataUtil.setUserType(if (loginBean.resultCode == "V10017") 1 else 0)
        UserDataUtil.setLoginToken(loginBean.data?.obj?.token)
        UserDataUtil.setXToken(loginBean.data?.obj?.xtoken)
        val fastCloseState = loginBean.data?.obj?.fastCloseState
        UserDataUtil.setFastCloseState(if (TextUtils.isEmpty(fastCloseState)) "2" else fastCloseState)
        val fastCloseCopyOrder = loginBean.data?.obj?.fastCloseCopyOrder // 快速停止跟单
        UserDataUtil.setFastStopCopyState(if (TextUtils.isEmpty(fastCloseCopyOrder)) "2" else fastCloseCopyOrder)
        val orderConfirmation = loginBean.data?.obj?.orderConfirmation
        UserDataUtil.setOrderConfirmState(if (TextUtils.isEmpty(orderConfirmation)) "2" else orderConfirmation)
        UserDataUtil.setEmail(email)
        UserDataUtil.setUserNickName(loginBean.data?.obj?.userNick)
        UserDataUtil.setUserPic(loginBean.data?.obj?.pic)
        UserDataUtil.setUserPassword(userPassword)

        LogEventUtil.mFirebaseAnalytics.setUserId(areaCode + userTel)

        // 手机号
        if (nextType == 0) {
            val userPhoneHistory = UserPhoneHistory()
            userPhoneHistory.phoneNumber = userTel
            DbManager.getInstance().saveUserPhoneHistory(userPhoneHistory)
            SpManager.putUserTel(userTel.ifNull())
            SpManager.putCountryCode(countryCode.ifNull()) // 国家code
            SpManager.putCountryNum(areaCode.ifNull())     // 区号
//            SPUtil.saveData(context, StoreConstants.COUNTRY_NAME, areaCodeData.countryName ?: "")
        } else {
            // 邮箱
            val userEmailHistory = UserEmailHistory()
            userEmailHistory.email = email
            DbManager.getInstance().saveUserEmailHistory(userEmailHistory)
        }
        if (handleType == 1) EventBus.getDefault().post(NoticeConstants.REFRESH_PERSONAL_INFO_DATA)

        //绑定神策业务ID，场景4、场景2
        val userBean = loginBean.data?.obj
        SensorsDataUtil.bindBusinessIdForLogin(
            userBean?.userTel, userBean?.email, userBean?.emailEventID, userBean?.crmUserId
        )

        // 登录firebase
        FirebaseManager.userLogin()
    }

    override fun goEditPwdApi(pwd: String?, pwdAgain: String?, verificationCode: String?) {

        mView?.showNetDialog()

        val map = HashMap<String, Any>()
        map["userTel"] = UserDataUtil.userTel()
        map["randStr"] = verificationCode.ifNull()
        map["userNewPassword"] = pwd ?: ""
        map["userPasswordConfirm"] = pwdAgain ?: ""
        map["phoneCountryCode"] = UserDataUtil.countryCode()
        map["isChangePass"] = true
        map["code"] = UserDataUtil.areaCode()
        // 修改密码
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        mModel?.goEditPwdApi(paramMap, object : BaseObserver<ChangeUserInfoSuccessBean>() {
            override fun onNext(data: ChangeUserInfoSuccessBean) {
                mView?.hideNetDialog()
                if (data.resultCode == "V00000") {
                    EventBus.getDefault().post(
                        DataEvent(NoticeConstants.WS.LOGIN_ERROR_OF_TOKEN, TokenErrorData(data.data?.type.ifNull("4"), data.data?.msg.ifNull()))
                    )
                } else {
                    ToastUtil.showToast(data.msgInfo)
                }
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    override fun getVerificationCodeApi(telNum: String, userPassword: String, validateCode: String?, type: Int, smsSendType: String) {

        mView?.showNetDialog()
        val map = hashMapOf<String, Any>()
        if (validateCode?.isNotEmpty() == true) {
            map["recaptcha"] = validateCode
            map["smsCodeId"] = SpManager.getSmsCodeId("")
        }
        map["count"] = UserDataUtil.userTel()
        map["countryCode"] = UserDataUtil.countryCode()
        map["smsSendType"] = smsSendType
        map["userPassword"] = userPassword
        map["code"] = UserDataUtil.areaCode()
        map["isChangePass"] = true
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        mModel?.getVerificationCodeApi(paramMap, object : BaseObserver<ForgetPwdVerificationCodeBean>() {
            override fun onNext(data: ForgetPwdVerificationCodeBean) {
                mView?.hideNetDialog()
                SpManager.putSmsCodeId("")
                if (data.resultCode == "V00000") {
                    mView?.sendCodeSuccess()
                    startSendCodeUtil()
                }
                if (data.resultCode == "V10060") {//易盾 需要滑动窗口
                    mView?.showCaptcha(type)
                    SpManager.putSmsCodeId(data.data?.obj?.smsCodeId ?: "")
                    return
                }
                ToastUtil.showToast(data.msgInfo)
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    override fun checkVerificationCodeApi(
        pwd: String?,
        pwdAgain: String?,
        verificationCode: String?,
        phoneNo: String?,
        areaCode: String?
    ) {
        if (TextUtils.isEmpty(verificationCode) || verificationCode?.length != 6) {
            ToastUtil.showToast(context.getString(R.string.please_enter_the_code))
            return
        }
        mView?.showNetDialog()
        val paramMap = HashMap<String, Any>()
        val code = areaCode?.filter { it.isDigit() }
        paramMap["code"] = code ?: ""
        paramMap["phoneNum"] = phoneNo ?: ""
        paramMap["validateCode"] = verificationCode
        paramMap["isChangePass"] = true
        mModel?.checkVerificationCodeApi(paramMap, object : BaseObserver<BaseBean>() {
            override fun onNext(data: BaseBean) {
                mView?.hideNetDialog()
                if (data.resultCode == "V00000") {
                    goEditPwdApi(pwd, pwdAgain, verificationCode)
                } else {
                    ToastUtil.showToast(data.msgInfo)
                }
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    override fun insertFundPWDApi(userToken: String?, optType: String?, phone: String?, validateCode: String?, fundPwd: String?, confirmPwd: String?, code: String?) {
        mView?.showNetDialog()
        val params = HashMap<String, Any>()
        params["userToken"] = userToken ?: ""
        params["optType"] = optType ?: ""//操作类型
        params["phone"] = phone ?: ""//手机号
        params["validateCode"] = validateCode ?: ""//验证码
        params["fundPwd"] = fundPwd ?: ""//新密码
        params["confirmPwd"] = confirmPwd ?: ""
        params["code"] = code ?: ""//手机区号
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(params.json, AESUtil.PWD_AES_KEY)
        mModel?.insertFundPWDApi(paramMap, object : BaseObserver<BaseBean>() {
            override fun onHandleSubscribe(d: Disposable) {
                mRxManager.add(d)
            }

            override fun onNext(baseBean: BaseBean) {
                mView?.hideNetDialog()
                if ("V00000" == baseBean.resultCode) mView?.hideNetDialog()
                if ("********" == baseBean.resultCode) {
                    ToastUtil.showToast(baseBean.msgInfo)
                    mView?.refreshFundPWD(0)
                } else {
                    ToastUtil.showToast(baseBean.msgInfo)
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    override fun forgotFundPWDApi(userToken: String?, phone: String?, validateCode: String?, fundPwd: String?, confirmPwd: String?, code: String?) {
        mView?.showNetDialog()
        val params = HashMap<String, Any>()
        params["userToken"] = userToken ?: ""
        params["phone"] = phone ?: ""//手机号
        params["validateCode"] = validateCode ?: ""//验证码
        params["fundPwd"] = fundPwd ?: ""//新密码
        params["confirmPwd"] = confirmPwd ?: ""//二次新密码
        params["code"] = code ?: ""//手机区号
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(params.json, AESUtil.PWD_AES_KEY)
        mModel?.forgotFundPWDApi(paramMap, object : BaseObserver<BaseBean>() {
            override fun onHandleSubscribe(d: Disposable) {
                mRxManager.add(d)
            }

            override fun onNext(baseBean: BaseBean) {
                mView?.hideNetDialog()
                if ("********" == baseBean.resultCode) {
                    ToastUtil.showToast(baseBean.msgInfo)
                    mView?.refreshFundPWD(1)
                } else {
                    ToastUtil.showToast(baseBean.msgInfo)
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    override fun withdrawal(
        userToken: String?,
        accountId: String?,
        currency: String?,
        data: WithdrawalBundleBean,
        fundSafePwd: String?,
        state: Int
    ) {
        val params = HashMap<String, Any>()
        params["userToken"] = userToken ?: ""
        params["accountId"] = accountId ?: "" //mt4
        params["currency"] = currency ?: "" //出金账户币种
        params["agreeed"] = data.isAgreeed ?: ""//是否阅读同意声明
        params["amount"] = data.amountX1 ?: "" //出金金额
        params["importantNotes"] = data.importantNotesX1 ?: ""//出金备注
        params["fundSafePwd"] = fundSafePwd ?: "" //资金安全密码
        if (state == 1) {
            params["withdrawMethod"] = "1" //出金方式，
            params["cardHolder"] = data.nameOnCardX1 ?: ""//持卡人姓名，
            params["cardPreFourNum"] = data.first4DigitsX1 ?: "" //信用卡前4位
            params["cardAfterThreeNum"] = data.last3DigitsX1 ?: "" //信用卡后3位
            params["cardExpiry"] = data.cardExpiryX1 ?: ""//信用卡过期时间
        } else if (state == 2) {
            params["withdrawMethod"] = "2"
            params["region"] = "Australia"
            params["cardHolder"] = data.nameOnCardX1 ?: ""
            params["bankName"] = data.australianBankNameX2 ?: ""//银行名称
            params["bsb"] = data.getbSBX2() ?: ""//不知道啥意思
            params["bankBeneficiaryName"] = data.bankBeneficiaryNameX2 ?: "" //持卡人姓名&&cardHolder一样
            params["swift"] = data.swiftX2 ?: ""
            params["bankCardNo"] = data.bankAccountNumberX2 ?: ""
        } else if (state == 3) {
            params["withdrawMethod"] = "2"
            params["region"] = "International"
            params["cardHolder"] = data.nameOnCardX1 ?: ""
            params["bankName"] = data.australianBankNameX2 ?: ""
            params["bankAdress"] = data.bankAddressX3 ?: ""
            params["bankBeneficiaryName"] = data.bankBeneficiaryNameX2 ?: ""
            params["bankCardNo"] = data.bankAccountNumberX2 ?: ""
            params["cardHolderAdress"] = data.accountHolderAddressX3 ?: ""
            params["swift"] = data.swiftX2 ?: ""
            params["sortCode"] = data.getaBASortCodeX3() ?: ""
        } else if (state == 5) {
            params["withdrawMethod"] = "31"
            params["skillEmail"] = data.skrillNetellerEmailX5 ?: ""
        } else if (state == 7) {
            params["withdrawMethod"] = "32"
            params["skillEmail"] = data.skrillNetellerEmailX5 ?: ""
        } else if (state == 6) {
            val tempBankBranchRegion = data.bankBranchRegionX6?.split(" ".toRegex())?.dropLastWhile { it.isEmpty() }?.toTypedArray()
            params["withdrawMethod"] = "4"
            params["cardHolder"] = data.nameOnCardX1 ?: ""
            params["bankName"] = data.australianBankNameX2 ?: ""
            params["bankCardNo"] = data.bankAccountNumberX2 ?: ""
            params["bankBranchProvince"] = tempBankBranchRegion?.getOrNull(0) ?: ""
            params["bankBranchCity"] = tempBankBranchRegion?.getOrNull(1) ?: ""
            params["bankBranchName"] = data.bankBranchX4 ?: ""
            params["bankBeneficiaryName"] = data.bankBeneficiaryNameX2 ?: ""
        }
        mModel?.withdrawal(params, object : BaseObserver<DataObjStringBean>() {
            override fun onHandleSubscribe(d: Disposable) {
                mRxManager.add(d)
            }

            override fun onNext(withdrawalBean: DataObjStringBean) {
                mView?.hideNetDialog()
                if (withdrawalBean.resultCode == "********") {
//                    ToastUtils.showToast(withdrawalBean.getMsgInfo());
                    mView?.refreshWithdrawal(withdrawalBean.data?.obj)
                } else {
                    ToastUtil.showToast(withdrawalBean.msgInfo)
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }
}