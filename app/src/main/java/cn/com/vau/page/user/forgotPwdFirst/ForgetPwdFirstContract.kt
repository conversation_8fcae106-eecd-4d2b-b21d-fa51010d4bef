package cn.com.vau.page.user.forgotPwdFirst

import cn.com.vau.common.base.mvp.*
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.BaseBean
import cn.com.vau.data.account.ForgetPwdVerificationCodeBean
import cn.com.vau.data.account.SelectCountryNumberObjDetail
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
interface ForgetPwdFirstContract {

    interface Model : BaseModel {
        fun getVerificationCodeApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<ForgetPwdVerificationCodeBean>): Disposable
    }

    interface View : BaseView {
        fun goForgetSecond()
        fun showTel()
        fun showCaptcha()
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun getVerificationCodeApi(mobile: String?, validateCode: String)
        abstract fun setSelectAreaData(areaCodeData: SelectCountryNumberObjDetail)
        abstract fun initCode()
    }

}
