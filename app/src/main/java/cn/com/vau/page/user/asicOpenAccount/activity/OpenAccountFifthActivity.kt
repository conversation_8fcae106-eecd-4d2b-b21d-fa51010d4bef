package cn.com.vau.page.user.asicOpenAccount.activity

import android.os.Bundle
import android.view.View
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.*
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.databinding.ActivityOpenAcountFifthWhiteBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.html.HtmlActivity
import cn.com.vau.page.user.asicOpenAccount.viewmodel.AsicOpenViewModel
import cn.com.vau.page.user.openAccountFifth.OpenFifthIdentifyActivity
import cn.com.vau.util.tracking.*
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.json.JSONObject

/**
 * Created by Haipeng.
 */
class OpenAccountFifthActivity : BaseMvvmActivity<ActivityOpenAcountFifthWhiteBinding, AsicOpenViewModel>() {

    private val skipType by lazy { intent?.extras?.getInt("skipType") ?: 1 }
    private val fromType by lazy { intent?.extras?.getInt("fromType") ?: 0 }

    override fun initView() {
        if (fromType == 1) {
            mBinding.keepOutView.visibility = View.GONE
        } else {
            mViewModel.getStep5Data()
        }
        // 神策自定义埋点(v3500)，页面浏览事件
        sensorsTrack()
    }

    override fun createObserver() {
        super.createObserver()
        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED).collectLatest {
                if (it is DataEvent) {
                    when (it.tag) {
                        Constants.EVENT_TAG_ASIC_FIFTH_ERROR -> {
                            mBinding.keepOutView.visibility = View.GONE
                        }

                        Constants.EVENT_TAG_ASIC_FIFTH_TO_H5 -> {
                            openActivity(HtmlActivity::class.java, Bundle().apply {
                                putString("url", HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/noTitle/greenId?token=${UserDataUtil.loginToken()}")
                                putInt("tradeType", 3)
                            })
                            finish()
                        }
                    }
                }
            }
        }
    }

    override fun initListener() {
        super.initListener()
        mBinding.tvBankStatement.setOnClickListener(this)
        mBinding.tvUtilityBills.setOnClickListener(this)
        mBinding.tvLetterIssued.setOnClickListener(this)
        mBinding.mHeaderBar.setEndIconClickListener {
            finish()
        }
        mBinding.mHeaderBar.setEndIcon1ClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {
            R.id.tvBankStatement -> { // 护照
                val bundle = Bundle()
                bundle.putInt("skipType", skipType)
                bundle.putInt("pageType", 1)
                openActivity(OpenFifthIdentifyActivity::class.java, bundle)
            }

            R.id.tvUtilityBills -> { // 驾照
                val bundle = Bundle()
                bundle.putInt("skipType", skipType)
                bundle.putInt("pageType", 2)
                openActivity(OpenFifthIdentifyActivity::class.java, bundle)
            }

            R.id.tvLetterIssued -> { // 政府签发的带照片身份证
                val bundle = Bundle()
                bundle.putInt("skipType", skipType)
                bundle.putInt("pageType", 3)
                openActivity(OpenFifthIdentifyActivity::class.java, bundle)
            }
        }
    }

    /**
     * 神策自定义埋点(v3500)
     * 开户及验证页面浏览 -> 开户验证页面加载完成时触发
     */
    private fun sensorsTrack() {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.IDENTITY_LEVEL, "ASIC") // 验证阶段
        SensorsDataUtil.track(SensorsConstant.V3500.OPEN_IDENTITY_PAGE_VIEW, properties)
    }
}
