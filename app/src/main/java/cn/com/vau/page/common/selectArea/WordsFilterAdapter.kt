package cn.com.vau.page.common.selectArea

import android.annotation.SuppressLint
import android.content.Context
import android.view.*
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.data.account.SelectCountryNumberObjDetail
import cn.com.vau.util.AttrResourceUtil

class WordsFilterAdapter(
    var mContext: Context,
    var list: ArrayList<SelectCountryNumberObjDetail>?, val selectedCountryNumber: String,
    var listener: onItemClickListener?
) : RecyclerView.Adapter<WordsFilterAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder = ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_select_country_number_words, parent, false))

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val bean = list?.elementAtOrNull(position)
        holder.tvCountryName.text = "${bean?.countryName ?: ""}(${bean?.countryCode ?: ""})"
        holder.tvCountryNumber.text = "+${bean?.countryNum ?: ""}"
        holder.gapline.visibility = if (position == (list?.size ?: 0) - 1) View.GONE else View.VISIBLE
        holder.itemView.setOnClickListener {
            listener?.onItemSelect(position)
        }
        if (selectedCountryNumber == bean?.countryNum) {
            holder.tvCountryName?.setBackgroundColor(AttrResourceUtil.getColor(mContext, R.attr.color_c0a1e1e1e_c0affffff))
        } else {
            holder.tvCountryName?.setBackgroundColor(0)
        }
    }

    fun refreshData(list: ArrayList<SelectCountryNumberObjDetail>) {
        this.list = list
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int {
        return list?.size ?: 0
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val tvCountryName = view.findViewById<TextView>(R.id.tvCountryName)
        val tvCountryNumber = view.findViewById<TextView>(R.id.tvCountryNumber)
        val gapline = view.findViewById<View>(R.id.gapline)
    }

    interface onItemClickListener {
        fun onItemSelect(position: Int)
    }

}