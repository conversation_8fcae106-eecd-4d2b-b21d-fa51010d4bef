package cn.com.vau.page.common.selectArea

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.account.SelectCountryNumberBean
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
class SelectAreaCodeModel : SelectAreaCodeContract.Model {

    override fun getAreaCodeApi(baseObserver: BaseObserver<SelectCountryNumberBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().selectCountryNumberClassifyScreeningApi(), baseObserver)
        return baseObserver.disposable
    }

}
