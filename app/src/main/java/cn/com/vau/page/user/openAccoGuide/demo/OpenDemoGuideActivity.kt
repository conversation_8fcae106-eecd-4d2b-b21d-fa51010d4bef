package cn.com.vau.page.user.openAccoGuide.demo

import android.annotation.SuppressLint
import android.content.*
import android.os.Bundle
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.*
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.view.*
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.popup.*
import cn.com.vau.common.view.popup.adapter.AccountTypeTipAdapter
import cn.com.vau.common.view.popup.bean.AccountTypeTipBean
import cn.com.vau.databinding.ActivityOpenDemoGuideBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.user.openAccoGuide.demo.vm.OpenDemoViewModel
import cn.com.vau.page.user.openAccoGuide.lv1.adapter.*
import cn.com.vau.util.dp2px
import cn.com.vau.util.widget.dialog.base.BottomListDialog
import com.chad.library.adapter.base.BaseQuickAdapter
import java.util.Locale

class OpenDemoGuideActivity : BaseMvvmActivity<ActivityOpenDemoGuideBinding, OpenDemoViewModel>() {

    private val platAdapter by lazy { OpenAccoPlatAdapter() }
    private val typeAdapter by lazy { OpenAccoTypeAdapter() }
    private val currencyAdapter by lazy { OpenAccoCurrencyAdapter() }

    // 选择mt4平台时需要弹框提示用户
    private val selectMt4ConfirmDialog by lazy {
        BottomSelectMt4ConfirmDialog.Builder(this).build()
    }

    // 选择交易平台的提示弹框适配器
    private val platTypeTipAdapter: AccountTypeTipAdapter by lazy {
        AccountTypeTipAdapter().apply {
            val metaList = arrayListOf<AccountTypeTipBean>().apply {
                add(
                    AccountTypeTipBean(
                        title = "PLATFORM 5th",
                        tip1 = getString(R.string.comprehensive_features_ideal_for_experienced_traders),
                        tip2 = getString(R.string.covers_a_wider_etfs_and_more),
                        tip3 = getString(R.string.flexible_trading_options_indicators_and_timeframes),
                        labelBgRes = R.drawable.shape_ce35728_r100,
                        labelStr = getString(R.string.recommended)
                    )
                )
                add(
                    AccountTypeTipBean(
                        title = "VTS",
                        tip1 = getString(R.string.without_third_is_more_secure),
                        tip2 = getString(R.string.autonomous_control_more_stable_system),
                        labelBgRes = R.drawable.shape_c034854_r100,
                        labelStr = getString(R.string.self_developed)
                    )
                )
                add(
                    AccountTypeTipBean(
                        title = "PLATFORM 4th",
                        tip1 = getString(R.string.fewer_built_in_features),
                        tip2 = getString(R.string.limited_product_diversity),
                    )
                )
            }
            setList(metaList)
        }
    }

    override fun initParam(savedInstanceState: Bundle?) {
        mViewModel.isReset = intent.extras?.getInt("isReset") ?: 0
    }

    override fun initView() {
        mBinding.rcyAccoPlat.layoutManager = WrapContentLinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        mBinding.rcyAccoPlat.adapter = platAdapter
        mBinding.rcyAccoPlat.addItemDecoration(DividerItemDecoration(dividerSize = 12.dp2px(), orientation = RecyclerView.HORIZONTAL))

        mBinding.rcyAccoType.adapter = typeAdapter
        mBinding.rcyAccoType.addItemDecoration(SpanItemDecoration(12f.dp2px()))

        mBinding.rcyAccoCurrency.adapter = currencyAdapter
        mBinding.rcyAccoCurrency.addItemDecoration(SpanItemDecoration(12f.dp2px()))
        val typeGridLayout = mBinding.rcyAccoType.layoutManager as GridLayoutManager
        typeGridLayout.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                return if (mViewModel.typeDataList.elementAtOrNull(position)?.accountTypeNum != 99) 1 else 2
            }
        }
        requestPlatTypeCurrency()

        if (SpManager.getSuperviseNum() == "1") { // asic显示底部文案
            mBinding.tvAsicPrompt.isVisible = true
        }
    }

    override fun createObserver() {
        // 交易平台
        mViewModel.platTypeCurrencyLiveData.observe(this) {
            mViewModel.platDataList.clear()
            mViewModel.platDataList.addAll(it?.obj ?: arrayListOf())

            if (mViewModel.platDataList.isNotEmpty()) {
                mViewModel.getAccountTypeTitleApi()
            }
            platAdapter.selectIndex = 0

            mViewModel.tempData = mViewModel.openAccountData.value
            mViewModel.tempData?.platform = mViewModel.platDataList.elementAtOrNull(0)?.platFormName
            mViewModel.openAccountData.value = mViewModel.tempData
            typeAdapter.selectIndex = 0
            //                mViewModel.selectedType = typeDataList.elementAtOrNull(0)
            currencyAdapter.selectIndex = 0
            //                mViewModel.selectedCurrency = currencyDataList.elementAtOrNull(0)
            platAdapter.setList(mViewModel.platDataList)
            initAdapterData()
        }
        // 平台文案
        mViewModel.platTitleLiveData.observe(this) {
            //赋值说明文案
            mViewModel.platDataList.forEach { bean ->
                bean.platTypeTitle = it
            }
            notifyAdapter(platAdapter)
        }
        // 底部按钮状态
        mViewModel.openAccountData.observe(this) { data ->
            if (data?.accountType != null && !data.platform.isNullOrBlank() && !data.currency.isNullOrBlank()) {
                // 点击下一步  新增/重置 vts demo
                mBinding.tvNext.setOnClickListener { _ ->
                    mViewModel.accountDemoEditAccountApi()
                }
                mBinding.tvNext.background = ContextCompat.getDrawable(this, R.drawable.draw_shape_c1e1e1e_cebffffff_r100)
            } else {
                mBinding.tvNext.setOnClickListener(null)
                mBinding.tvNext.background = ContextCompat.getDrawable(this, R.drawable.draw_shape_c1f1e1e1e_c1fffffff_r100)
            }
        }
        // 新增/重置成功
        mViewModel.resetSuccessLiveData.observe(this) {
            setResult(RESULT_OK)
            finish()
        }
    }

    override fun initListener() {
        super.initListener()
        mBinding.mHeaderBar.setEndIconClickListener {
            finish()
        }.setEndIcon1ClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
        platAdapter.setOnItemClickListener { _, _, position ->
            if (platAdapter.selectIndex != position) {
                changePlatformRefreshData(position)

                // 选择mt4平台时需要弹框提示用户
                val platFormName = mViewModel.platDataList.elementAtOrNull(position)?.platFormName
                if ("mt4".equals(platFormName, true)) {
                    showSelectMt4ConfirmPopup()
                }
            }
        }
        typeAdapter.setOnItemClickListener { _, _, position ->
            if (typeAdapter.selectIndex != position) {
                typeAdapter.selectIndex = position
                mViewModel.tempData = mViewModel.openAccountData.value
                mViewModel.tempData?.accountType = mViewModel.typeDataList.elementAtOrNull(position)?.accountTypeNum
                mViewModel.openAccountData.value = mViewModel.tempData
                currencyAdapter.selectIndex = 0
                //                mViewModel.selectedCurrency = currencyDataList.elementAtOrNull(0)
                initAdapterData(false)
                notifyAdapter(platAdapter, typeAdapter)
            }
        }
        currencyAdapter.setOnItemClickListener { _, _, position ->
            if (currencyAdapter.selectIndex != position) {
                currencyAdapter.selectIndex = position
                mViewModel.tempData = mViewModel.openAccountData.value
                mViewModel.tempData?.currency = mViewModel.currencyDataList.elementAtOrNull(position)?.currencyName
                mViewModel.openAccountData.value = mViewModel.tempData
                notifyAdapter(currencyAdapter)
            }
        }
        mBinding.tvAccoPlatTip.setOnClickListener {
            if (platTypeTipAdapter.data.isNotEmpty()) {
                BottomListDialog.Builder(this)
                    .setTitle(getString(R.string.platform))
                    .setAdapter(platTypeTipAdapter)
                    .build()
                    .showDialog()
            }
        }
        mBinding.tvAccoType.setOnClickListener {
            BottomListDialog.Builder(this)
                .setTitle(getString(R.string.glossary))
                .setAdapter(getAccountTypeTipAdapter())
                .build()
                .showDialog()
        }
    }

    private fun getAccountTypeTipAdapter(): AccountTypeTipAdapter {
        val plat = mViewModel.platDataList.getOrNull(platAdapter.selectIndex)
        val supervise = SpManager.getSuperviseNum("")
        val isMt4 = "mt4" == plat?.platFormName?.lowercase(Locale.getDefault())
        return AccountTypeTipAdapter().apply {
            val dataList = mutableListOf(
                AccountTypeTipBean(
                    title = getString(R.string.standard_stp_account),
                    tip1 = getString(R.string.spreads_from),
                    tip2 = getString(R.string.trade_with_commissions),
                    tip3 = getString(R.string.up_to_leverage),
                ),
                AccountTypeTipBean(
                    title = getString(R.string.raw_ecn_account),
                    tip1 = getString(R.string.spreads_from2),
                    tip2 = if ("13" == supervise) {     // FCA
                        getString(R.string.trade_with_x_commissions_side, "1")
                    } else {                            // VFSC || VFSC2
                        if (isMt4) {
                            getString(R.string.trade_with_x_per_opening_trade, "6")
                        } else {
                            // Mt5 (默认跟单也是Mt5)
                            getString(R.string.trade_with_x_commissions_side, "3")
                        }
                    },
                    tip3 = getString(R.string.up_to_leverage),
                ),
                AccountTypeTipBean(
                    title = getString(R.string.swap_free_account),
                    tip1 = getString(R.string.islamic_stp_islamic_available),
                    tip2 = getString(R.string.up_to_leverage),
                )
            )
            setNewInstance(dataList)
        }
    }

    private fun requestPlatTypeCurrency() {
        mViewModel.getPlatFormAccountTypeCurrencyApi()
    }

    private fun initAdapterData(typeInit: Boolean = true, currencyInit: Boolean = true) {
        if (typeInit) {
            mViewModel.typeDataList.clear()
            mViewModel.typeDataList.addAll(
                mViewModel.platDataList.elementAtOrNull(platAdapter.selectIndex)?.listPlatFormAccountType
                    ?: ArrayList()
            )
            typeAdapter.setList(mViewModel.typeDataList)
            mViewModel.tempData = mViewModel.openAccountData.value
            mViewModel.tempData?.accountType = mViewModel.typeDataList.elementAtOrNull(typeAdapter.selectIndex)?.accountTypeNum
            mViewModel.openAccountData.value = mViewModel.tempData
        }
        if (currencyInit) {
            mViewModel.currencyDataList.clear()
            mViewModel.currencyDataList.addAll(
                mViewModel.typeDataList.elementAtOrNull(typeAdapter.selectIndex)?.listCurrency
                    ?: ArrayList()
            )
            currencyAdapter.setList(mViewModel.currencyDataList)
            mViewModel.tempData = mViewModel.openAccountData.value
            mViewModel.tempData?.currency = mViewModel.currencyDataList.elementAtOrNull(currencyAdapter.selectIndex)?.currencyName
            mViewModel.openAccountData.value = mViewModel.tempData
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun notifyAdapter(vararg adapters: BaseQuickAdapter<*, *>) {
        for (adapter in adapters) {
            adapter.notifyDataSetChanged()
        }
    }

    /**
     * 选择mt4平台时需要弹框提示用户
     */
    private fun showSelectMt4ConfirmPopup() {
        selectMt4ConfirmDialog.setTitle(getString(R.string.are_you_sure_select_platform_4th))
        // 切换mt5
        selectMt4ConfirmDialog.switchMt5Listener {
            var selectIndex = 0
            for ((index, bean) in mViewModel.platDataList.withIndex()) {
                if ("mt5".equals(bean.platFormName, true)) {
                    selectIndex = index // 找到第一个mt5账号
                    break
                }
            }
            changePlatformRefreshData(selectIndex)
            selectMt4ConfirmDialog.dismissDialog()
        }
        // 继续mt4
        selectMt4ConfirmDialog.continueMt4Listener {
            selectMt4ConfirmDialog.dismissDialog()
        }
        selectMt4ConfirmDialog.showDialog()
    }

    /**
     * 切换平台后刷新数据
     */
    private fun changePlatformRefreshData(position: Int) {
        platAdapter.selectIndex = position
        mViewModel.tempData = mViewModel.openAccountData.value
        val platFormName = mViewModel.platDataList.elementAtOrNull(position)?.platFormName
        mViewModel.tempData?.platform = platFormName
        mViewModel.openAccountData.value = mViewModel.tempData
        typeAdapter.selectIndex = 0
        //                viewModel.selectedType = typeDataList.elementAtOrNull(0)
        currencyAdapter.selectIndex = 0
        //                viewModel.selectedCurrency = currencyDataList.elementAtOrNull(0)
        initAdapterData()
        notifyAdapter(platAdapter, typeAdapter, currencyAdapter)
    }

    companion object {
        /**
         * @param isReset    0=新增；1=重置
         * @param accountCd  重置时需要更新缓存里的 accountCd
         */
        fun createIntent(context: Context, isReset: Int, accountCd: String? = null) =
            Intent(context, OpenDemoGuideActivity::class.java).apply {
                putExtra("isReset", isReset)
                putExtra("accountCd", accountCd)
            }
    }
}