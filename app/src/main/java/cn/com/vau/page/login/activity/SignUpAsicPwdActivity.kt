package cn.com.vau.page.login.activity

import android.os.Bundle
import android.view.MotionEvent
import androidx.core.os.bundleOf
import androidx.core.view.isGone
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.utils.VAUStartUtil
import cn.com.vau.databinding.ActivitySignUpAsicPwdBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.login.viewmodel.activity.SignUpAsicPwdViewModel
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.page.user.loginPwd.LoginPwdActivity
import cn.com.vau.util.*
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.widget.dialog.CenterActionDialog

/**
 * author：lvy
 * date：2025/03/12
 * desc：ASIC：注册第四步 -> 输入姓、名、邮箱、密码
 *
 * 流程：
 * 输入姓、名、邮箱、密码，邮箱输入框失去焦点时检查邮箱是否存在，存在则跳转到绑定邮箱页面
 * 调用注册接口，三方注册需要加三方信息参数，注册接口请求成功后保存信息并调用获取交易token接口 tradeAccountLogin
 * 获取交易token接口成功后跳转页面，失败则情况用户缓存
 */
class SignUpAsicPwdActivity : BaseMvvmActivity<ActivitySignUpAsicPwdBinding, SignUpAsicPwdViewModel>() {

    override fun initParam(savedInstanceState: Bundle?) {
        mViewModel.signUpRequestBean = intent.getParcelableExtra("signUpRequestBean")
    }

    override fun initView() {
        mBinding.firstNameView.setHintText("${getString(R.string.first_name)}*")
        mBinding.lastNameView.setHintText("${getString(R.string.last_name)}*")
        mBinding.emailView.setHintText("${getString(R.string.email)}*")
        mBinding.pwdView.setHintText("${getString(R.string.password)}* ${getString(R.string._8_16_characters)}")

        mBinding.layoutPasswordCheck.tvPasswordMatch.isGone = true
        mBinding.layoutPasswordCheck.tvPasswordSpecial.text = buildString {
            append(getString(R.string.at_least_1_following_characters))
            append("!@#$%^&*.()")
        }
    }

    override fun createObserver() {
        // 邮箱已存在，跳转到绑定邮箱页面
        mViewModel.emailIsExistLiveData.observe(this) {
            showBindDialog(it)
        }
        // 注册成功
        mViewModel.signUpSuccessLiveData.observe(this) {
            signUpSuccess()
        }
    }

    override fun initListener() {
        mBinding.mHeaderBar.setEndIconClickListener {
            LogEventUtil.setLogEvent("cs_button", Bundle().apply { putString("process_name", "Demo_SignUp") })
            openActivity(HelpCenterActivity::class.java)
        }
        // 名
        mBinding.firstNameView.afterTextChangedListener {
            checkNextBtn()
        }
        // 姓
        mBinding.lastNameView.afterTextChangedListener {
            checkNextBtn()
        }
        // 邮箱
        mBinding.emailView.afterTextChangedListener {
            checkNextBtn()
        }
        mBinding.emailView.onFocusChangeListener { hasFocus ->
            val email = mBinding.emailView.getInputText()
            if (!hasFocus && RegexUtil.isEmail(email)) {
                mViewModel.isExistEmailApi(email)
            }
        }
        // 密码
        mBinding.pwdView.afterTextChangedListener {
            checkNextBtn()
        }
        // 下一步
        mBinding.tvNext.clickNoRepeat {
            mViewModel.signUpRequestBean?.pwd = mBinding.pwdView.getInputText()
            mViewModel.registerAccountApi(
                mBinding.firstNameView.getInputText(), mBinding.lastNameView.getInputText(),
                mBinding.emailView.getInputText()
            )
        }
    }

    /**
     * 检测按钮是否可点击
     */
    private fun checkNextBtn() {
        val password = mBinding.pwdView.getInputText()
        mBinding.layoutPasswordCheck.tvPasswordLength.isSelected = password.length in 8..16
        mBinding.layoutPasswordCheck.tvPasswordContent.isSelected = RegexUtil.isContainsLetter(password)
        mBinding.layoutPasswordCheck.tvPasswordNumber.isSelected = RegexUtil.isContainsNumber(password)
        mBinding.layoutPasswordCheck.tvPasswordSpecial.isSelected = RegexUtil.isContainsSpecial(password)
        // 对下方的按钮的判断进行赋值
        val isNext = mBinding.layoutPasswordCheck.tvPasswordLength.isSelected &&
                mBinding.layoutPasswordCheck.tvPasswordContent.isSelected &&
                mBinding.layoutPasswordCheck.tvPasswordNumber.isSelected &&
                mBinding.layoutPasswordCheck.tvPasswordSpecial.isSelected
        val isNextFirst = mBinding.firstNameView.getInputText().isNotBlank()
        val isNextLast = mBinding.lastNameView.getInputText().isNotBlank()
        val isNextEmail = mBinding.emailView.getInputText().isNotBlank()
        mBinding.tvNext.isEnabled = isNextFirst && isNextLast && isNext && isNextEmail
    }

    /**
     * 注册成功
     */
    private fun signUpSuccess() {
        VAUStartUtil.dispatchOpenAccount(this)
        finish()
        // 关闭来源页面
        ActivityManagerUtil.getInstance().finishActivity(LoginActivity::class.java)
        ActivityManagerUtil.getInstance().finishActivity(LoginPwdActivity::class.java)
        ActivityManagerUtil.getInstance().finishActivity(SignUpActivity::class.java)
        ActivityManagerUtil.getInstance().finishActivity(SignUpAsicActivity::class.java)
        ActivityManagerUtil.getInstance().finishActivity(InputCodeActivity::class.java)
        ActivityManagerUtil.getInstance().finishActivity(VerifyEmailCodeActivity::class.java)
        ActivityManagerUtil.getInstance().finishActivity(VerifySmsCodeActivity::class.java)
    }

    /**
     * 邮箱已存在，跳转到绑定邮箱页面
     */
    private fun showBindDialog(email: String?) {
        mBinding.emailView.setInputText(null)
        CenterActionDialog.Builder(this)
            .setContent(getString(R.string.this_email_has_would_account))
            .setStartText(getString(R.string.cancel))
            .setEndText(getString(R.string.link))
            .setOnStartListener {
                mBinding.emailView.setInputText("")
            }.setOnEndListener {
                openActivity(BindEmailActivity::class.java, bundleOf().apply {
                    putParcelable("signUpRequestBean", mViewModel.signUpRequestBean)
                    putString("email", email)
                })
            }.build().showDialog()
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        KeyboardUtil.hideSoftKeyboard(this, currentFocus?.rootView, event, R.id.bgView)
        return super.dispatchTouchEvent(event)
    }
}