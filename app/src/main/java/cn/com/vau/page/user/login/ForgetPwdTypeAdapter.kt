package cn.com.vau.page.user.login

import android.view.View
import android.widget.TextView
import androidx.annotation.Keep
import cn.com.vau.R
import cn.com.vau.common.view.ClearAndHideEditText
import cn.com.vau.util.AttrResourceUtil
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class ForgetPwdTypeAdapter : BaseQuickAdapter<LoginType, BaseViewHolder>(
    R.layout.item_forget_pwd_phone_with_email,
    mutableListOf(LoginType(LoginType.TYPE_PHONE), LoginType(LoginType.TYPE_EMAIL))
) {

    var mobile: String? = ""
    var areaCode: String? = "+61"
    var email: String? = ""

    private var areaCodeClick: (() -> Unit)? = null
    private var smsClick: ((String?) -> Unit)? = null
    private var whatsAppClick: ((String?) -> Unit)? = null
    private var emailClick: ((String?) -> Unit)? = null

    private val color_c731e1e1e_c61ffffff by lazy {
        AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff)
    }

    private val color_cffffff_c1e1e1e by lazy {
        AttrResourceUtil.getColor(context, R.attr.color_cebffffff_c1e1e1e)
    }

    override fun convert(holder: BaseViewHolder, item: LoginType) {
        val tvAreaCode = holder.getView<TextView>(R.id.tvAreaCode)
        val sendEms = holder.getView<TextView>(R.id.tvSendEms)
        val sendWhatsApp = holder.getView<View>(R.id.viewWhatsApp)
        val etMobile = holder.getView<ClearAndHideEditText>(R.id.etMobile)
        val etEmail = holder.getView<ClearAndHideEditText>(R.id.etEmail)
        val bgView = holder.getView<View>(R.id.bgView)
        tvAreaCode.setOnClickListener {
            areaCodeClick?.invoke()
        }
        etMobile.doAfterTextChanged {
            mobile = it.toString()
            initNextView(holder, item)
        }
        etEmail.doAfterTextChanged {
            email = it.toString()
            initNextView(holder, item)
        }

        sendEms.setOnClickListener {
            when (item.type) {
                LoginType.TYPE_PHONE -> {
                    smsClick?.invoke(etMobile.getText().trim())
                }

                LoginType.TYPE_EMAIL -> {
                    emailClick?.invoke(etEmail.getText().trim())
                }
            }
        }

        etMobile.onFocusChange = {
            bgView.isSelected = it
        }

        sendWhatsApp.setOnClickListener {
            whatsAppClick?.invoke(etMobile.getText().trim())
        }
        when (item.type) {
            LoginType.TYPE_PHONE -> {
                holder.setVisible(R.id.groupPhone, true)
                    .setVisible(R.id.etEmail, false)
                    .setGone(R.id.tvEmailPrompt, true)
                    .setText(R.id.tvSendEms, R.string.send_otp_via_sms)

                if (!mobile.isNullOrBlank()) {
                    etMobile.setText(mobile)
                }
                tvAreaCode.text = areaCode ?: "+61"
            }

            LoginType.TYPE_EMAIL -> {
                holder.setVisible(R.id.groupPhone, false)
                    .setVisible(R.id.etEmail, true)
                    .setGone(R.id.tvEmailPrompt, false)
                    .setGone(R.id.groupWhatsApp, true)
                    .setText(R.id.tvSendEms, R.string.send)
                if (!email.isNullOrEmpty()){
                    etEmail.setText(email)
                }
            }
        }
    }

    private fun initNextView(holder: BaseViewHolder, item: LoginType) {
        val sendEms = holder.getView<TextView>(R.id.tvSendEms)
        val sendWhatsApp = holder.getView<View>(R.id.viewWhatsApp)
        val etMobile = holder.getView<ClearAndHideEditText>(R.id.etMobile)
        val etEmail = holder.getView<ClearAndHideEditText>(R.id.etEmail)
        when (item.type) {
            LoginType.TYPE_PHONE -> {
                sendEms.setBackgroundResource(
                    if (etMobile.length() > 0)
                        R.drawable.draw_shape_c1e1e1e_cebffffff_r100
                    else
                        R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100
                )
                sendEms.setTextColor(if (etMobile.length() > 0) color_cffffff_c1e1e1e else color_c731e1e1e_c61ffffff)
                sendWhatsApp.setBackgroundResource(
                    if (etMobile.length() > 0)
                        R.drawable.shape_cbf25d366_r100
                    else
                        R.drawable.shape_c3325d366_r100
                )

            }

            LoginType.TYPE_EMAIL -> {
                sendEms.setBackgroundResource(
                    if (etEmail.length() > 0)
                        R.drawable.draw_shape_c1e1e1e_cebffffff_r100
                    else
                        R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100
                )
                sendEms.setTextColor(if (etEmail.length() > 0) color_cffffff_c1e1e1e else color_c731e1e1e_c61ffffff)
            }
        }
    }


    fun smsClick(smsClick: (String?) -> Unit) {
        this.smsClick = smsClick
    }

    fun whatsAppClick(whatsAppClick: (String?) -> Unit) {
        this.whatsAppClick = whatsAppClick
    }

    fun emailClick(emailClick: (String?) -> Unit) {
        this.emailClick = emailClick
    }

    fun setAdapterPhone(str:String){
        this.mobile = str
        notifyItemChanged(0)
    }
    fun setAdapterEmail(str:String){
        this.email = str
        notifyItemChanged(1)
    }
    fun areaCodeClick(areaCodeClick: () -> Unit) {
        this.areaCodeClick = areaCodeClick
    }

    fun setAreaCodeStr(areaCode: String?) {
        this.areaCode = areaCode
        notifyItemChanged(0)
    }
}
@Keep
data class LoginType(val type: Int) {
    companion object {
        const val TYPE_PHONE = 0
        const val TYPE_EMAIL = 1
    }
}