package cn.com.vau.page.common.selectResidence.activity;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.view.ViewStub;
import android.widget.AbsListView;
import android.widget.EditText;
import android.widget.ExpandableListView;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.OrientationHelper;
import androidx.recyclerview.widget.RecyclerView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import cn.com.vau.R;
import cn.com.vau.common.base.DataEvent;
import cn.com.vau.common.base.activity.BaseFrameActivity;
import cn.com.vau.common.view.WrapContentLinearLayoutManager;
import cn.com.vau.common.view.system.MyRecyclerView;
import cn.com.vau.data.account.ResidenceObj;
import cn.com.vau.data.account.ResidenceObjList;
import cn.com.vau.databinding.VsLayoutNoDataBinding;
import cn.com.vau.page.ResidenceEvent;
import cn.com.vau.page.common.selectResidence.adapter.BigLetterAdapter;
import cn.com.vau.page.common.selectResidence.adapter.ResidenceAdapter;
import cn.com.vau.page.common.selectResidence.adapter.SelectRegionAdapter;
import cn.com.vau.page.customerservice.HelpCenterActivity;
import cn.com.vau.util.widget.HeaderBar;

/**
 * 选择地区
 * Created by zhy on 2018/12/20.
 * 三级联动入口
 */
public class SelectResidenceActivity extends BaseFrameActivity<SelectResidencePresenter, SelectResidenceModel>
        implements SelectResidenceContract.View {

    private HeaderBar mHeaderBar;
    private EditText etSearch;
    private AppCompatImageView ivClear;
    private ExpandableListView elvResidenceList;
    private RecyclerView rcyBigLetter;

    private List<ResidenceObj> mList = new ArrayList<>();
    private ResidenceAdapter residenceAdapter;//内容数据adapter
    private BigLetterAdapter bigLetterAdapter;//大写字母adapter

    private String tempCountryEn = "";//国家英文
    private String tempCountryId = "";//国家Id

    private boolean filterClick = false;

    private ConstraintLayout ctlSearch;
    private MyRecyclerView searchRecyclerView;
    private ViewStub viewStub;
    private List<ResidenceObjList> searchList = new ArrayList<>();
    private SelectRegionAdapter residenceResultAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_select_residence);
    }

    @Override
    public void initParam() {
        super.initParam();
        EventBus.getDefault().register(this);
        if(getIntent().hasExtra("regulator"))
            mPresenter.regulator = getIntent().getExtras().getString("regulator", "");
        if(getIntent().hasExtra("isSelectNation"))
            mPresenter.isSelectNation = getIntent().getExtras().getBoolean("isSelectNation", false);
    }

    @SuppressLint ({"WrongConstant", "SetTextI18n"})
    @Override
    public void initView() {
        super.initView();
        mHeaderBar = findViewById(R.id.mHeaderBar);
        etSearch = findViewById(R.id.etSearch);
        ivClear = findViewById(R.id.ivClear);
        elvResidenceList = findViewById(R.id.elvResidenceList);
        rcyBigLetter = findViewById(R.id.rcyBigLetter);

        ctlSearch = findViewById(R.id.ctlSearch);
        searchRecyclerView = findViewById(R.id.searchRecyclerView);
        viewStub = findViewById(R.id.mVsNoData);
        viewStub.setOnInflateListener((stub, inflated) -> {
            VsLayoutNoDataBinding vs = VsLayoutNoDataBinding.bind(inflated);
            vs.mNoDataView.setHintMessage(getString(R.string.not_found_desc1) + "\n" + getString(R.string.not_found_desc2));
        });

        etSearch.setHint(getString(R.string.search_for_country) + "/" + getString(R.string.region));

        WrapContentLinearLayoutManager linearLayoutManager = new WrapContentLinearLayoutManager(context);
        linearLayoutManager.setOrientation(OrientationHelper.VERTICAL);
        searchRecyclerView.setLayoutManager(linearLayoutManager);
        residenceResultAdapter = new SelectRegionAdapter(context, searchList, 0);
        searchRecyclerView.setAdapter(residenceResultAdapter);
        searchRecyclerView.setEmptyView(viewStub);

        residenceResultAdapter.setOnItemClickListener((view, position) -> {
            tempCountryEn = searchList.get(position).countryNameEn;
            tempCountryId = String.valueOf(searchList.get(position).id);
            if(mPresenter.isSelectNation) {
                EventBus.getDefault().post(
                        new DataEvent(
                                "",
                                new ResidenceEvent(
                                        tempCountryId, tempCountryEn, "", "", "", ""
                                )
                        )
                );
                finish();
                return;
            }
            //查询省份，有数据跳转，没数据不跳
            mPresenter.queryProvince(searchList.get(position).countryNameEn, "", 0);
            //清空
            etSearch.setText("");
            ctlSearch.setVisibility(View.GONE);
        });

    }

    @SuppressLint ("SetTextI18n")
    @Override
    public void initData() {
        super.initData();
        if(getIntent().hasExtra("title_place_of_birth"))
            mHeaderBar.setTitleText(getString(R.string.place_of_birth));
        else
            mHeaderBar.setTitleText(getString(R.string.select_country) + "/" + getString(R.string.region));

        if(getIntent().hasExtra("interface_new")) {
            mPresenter.queryPlaceOfBirthResidence("", 0);
        } else { // 居住地
            mPresenter.queryResidence("", 0);
        }

    }

    @Override
    public void initListener() {
        super.initListener();
        mHeaderBar.setStartBackIconClickListener(() -> {
            finish();
            return null;
        });
        mHeaderBar.setEndIconClickListener(() -> {
            openActivity(HelpCenterActivity.class);
            return null;
        });

        ivClear.setOnClickListener(v -> etSearch.setText(""));
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if(s.length() == 0) {
                    ctlSearch.setVisibility(View.GONE);
                    ivClear.setVisibility(View.GONE);
                } else {
                    ctlSearch.setVisibility(View.VISIBLE);
                    ivClear.setVisibility(View.VISIBLE);
                    if(getIntent().hasExtra("interface_new")) {
                        mPresenter.queryPlaceOfBirthResidence(etSearch.getText().toString().trim(), 1);
                    } else {
                        mPresenter.queryResidence(etSearch.getText().toString().trim(), 1);
                    }
                }
            }
        });
    }

    @SuppressLint ("WrongConstant")
    @Override
    public void refreshResidence(List<ResidenceObj> list) {
        mList.clear();
        mList.addAll(list);
        //内容数据
        residenceAdapter = new ResidenceAdapter(this, mList, 0);
        residenceAdapter.setOnNationSelectedListener(new ResidenceAdapter.OnNationSelectedListener() {
            @Override
            public void onSelected(int groupPosition, int childPosition) {
                tempCountryEn = mList.get(groupPosition).list.get(childPosition).countryNameEn;
                tempCountryId = String.valueOf(mList.get(groupPosition).list.get(childPosition).id);
                if(mPresenter.isSelectNation) {
                    EventBus.getDefault().post(
                            new DataEvent(
                                    "",
                                    new ResidenceEvent(
                                            tempCountryId,
                                            tempCountryEn,
                                            "",
                                            "",
                                            "",
                                            ""
                                    )
                            )
                    );
                    finish();
                    return;
                }
                //查询省份，有数据跳转，没数据不跳
                mPresenter.queryProvince(mList.get(groupPosition).list.get(childPosition).countryNameEn, "", 0);
            }
        });
        elvResidenceList.setAdapter(residenceAdapter);
        for (int i = 0; i < mList.size(); i++) {
            elvResidenceList.expandGroup(i);
        }
        elvResidenceList.setOnGroupClickListener(new ExpandableListView.OnGroupClickListener() {
            @Override
            public boolean onGroupClick(ExpandableListView parent, View v, int groupPosition, long id) {
                return true;
            }
        });

        //大写字母
        WrapContentLinearLayoutManager linearLayoutManager = new WrapContentLinearLayoutManager(this);
        linearLayoutManager.setOrientation(OrientationHelper.VERTICAL);
        rcyBigLetter.setLayoutManager(linearLayoutManager);
        bigLetterAdapter = new BigLetterAdapter(this, mList);
        rcyBigLetter.setAdapter(bigLetterAdapter);
        rcyBigLetter.setItemAnimator(null);
        bigLetterAdapter.setOnItemClickListener(new BigLetterAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(View view, int position) {
                elvResidenceList.setSelectionFromTop(elvResidenceList.getFlatListPosition(ExpandableListView.getPackedPositionForGroup(position)), 0);
                String letterName = list.get(position).lettername;
                bigLetterAdapter.showSelectedName(letterName);
                filterClick = true;
            }
        });

        elvResidenceList.setOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {

            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                long packedPosition = elvResidenceList.getExpandableListPosition(firstVisibleItem);
                int positionType = ExpandableListView.getPackedPositionType(packedPosition);
                if(positionType != ExpandableListView.PACKED_POSITION_TYPE_NULL) {
                    int groupPosition = ExpandableListView.getPackedPositionGroup(packedPosition);
                    if(filterClick) {
                        filterClick = false;
                    } else {   //透過點擊選擇快捷字母  事後滑動到該字母頁面後就不必再重設定點擊的快捷字母
                        String letterName = list.get(groupPosition).lettername;
                        bigLetterAdapter.showSelectedName(letterName);
                    }
                }
            }
        });
    }

    @Override
    public void refreshProvince(List<ResidenceObj> list) {
        if(mPresenter.isSelectNation) {
            EventBus.getDefault().post(
                    new DataEvent(
                            "",
                            new ResidenceEvent(
                                    tempCountryId,
                                    tempCountryEn,
                                    "",
                                    "",
                                    "",
                                    ""
                            )
                    )
            );
            finish();
        } else if(list.size() != 0) {

            if(list.size() == 1) {
                if(list.get(0).list.size() == 1 && list.get(0).list.get(0).provinceNameEn.equals(tempCountryEn)) {
                    ResidenceObjList provinceDataBean = list.get(0).list.get(0);
                    if(tempCountryEn.equals(provinceDataBean.provinceNameEn)) {
                        Bundle bundle1 = new Bundle();
                        bundle1.putString("countryId", tempCountryId);
                        bundle1.putString("countryEn", tempCountryEn);
                        bundle1.putString("provinceCode", provinceDataBean.provinceCode);
                        bundle1.putString("provinceEn", provinceDataBean.provinceNameEn);
                        openActivity(SelectCityActivity.class, bundle1);
                        return;
                    }
                }
            }

            Bundle bundle = new Bundle();
            bundle.putString("countryId", tempCountryId);
            bundle.putString("countryEn", tempCountryEn);
            openActivity(SelectProvinceActivity.class, bundle);
        } else {
            EventBus.getDefault().post(
                    new DataEvent(
                            "",
                            new ResidenceEvent(
                                    tempCountryId,
                                    tempCountryEn,
                                    "", "", "", ""
                            )
                    )
            );
            finish();
        }
    }

    @Override
    public void refreshCity(List<ResidenceObj> list) {

    }

    @SuppressLint ("NotifyDataSetChanged")
    @Override
    public void resultResidence(List<ResidenceObj> list) {
        boolean flag = list.size() > 0;
        if(flag) {
            if(list.get(0).list.size() != 0) { //有数据
                //遍历数据
                final List<ResidenceObjList> objList = new ArrayList<>();//总数据
                for (int i = 0; i < list.size(); i++) {
                    for (int j = i; j < list.get(i).list.size(); j++) {
                        objList.add(list.get(i).list.get(j));
                    }
                }
                searchList.clear();
                searchList.addAll(objList);
                residenceResultAdapter.notifyDataSetChanged();
            } else {//无数据
                searchList.clear();
                residenceResultAdapter.notifyDataSetChanged();
            }
        } else {//无数据
            searchList.clear();
            residenceResultAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void resultProvince(List<ResidenceObj> list) {

    }

    @Override
    public void resultCity(List<ResidenceObj> list) {

    }

    @Subscribe (threadMode = ThreadMode.MAIN)
    public void onDataEvent(DataEvent event) {
        if(event.getData() instanceof ResidenceEvent) {
            finish();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

}
