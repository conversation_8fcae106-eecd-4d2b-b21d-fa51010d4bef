package cn.com.vau.page.user.question

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseFrameActivity
import cn.com.vau.common.constants.Constants
import cn.com.vau.data.account.AuditQuestionData
import cn.com.vau.databinding.ActivityAsicQuestionnaireBinding
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.page.user.question.adapter.AsicQuestionnaireRecyclerAdapter
import cn.com.vau.util.widget.dialog.CenterActionDialog

/**
 * ASIC调查问卷
 * asic_questionnaire
 */
class AsicQuestionnaireActivity : BaseFrameActivity<AsicQuestionnairePresenter, AsicQuestionnaireModel>(),
    AsicQuestionnaireContract.View {

    private var mAdapter: AsicQuestionnaireRecyclerAdapter? = null
    private val mBinding by lazy { ActivityAsicQuestionnaireBinding.inflate(layoutInflater) }
    private var from: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
    }

    override fun initParam() {
        super.initParam()
        from = intent?.extras?.getString("from", "") ?: ""
    }
    
    override fun initView() {
        super.initView()
        
        mBinding.mHeaderBar.setStartBackIconClickListener {
            if (from == "235") {
                openActivity(AccountManagerActivity::class.java)
            }
            finish()
        }
        
        mAdapter = AsicQuestionnaireRecyclerAdapter(this, mPresenter.dataList)
        mBinding.mRecyclerView.adapter = mAdapter

        mBinding.tvNext.setOnClickListener(this)
        mPresenter.getQustionList()

        // 在Activity中处理返回手势
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (from == "235") {
                    openActivity(AccountManagerActivity::class.java)
                }
                finish()
            }
        })
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.tvNext -> mPresenter.submitAnswer(mAdapter?.selectMap)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun updateView() {
        mAdapter?.notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onSubmitResult(result: AuditQuestionData) {
        when (result.resultCode) {
            "V00000" -> {
                setResult(Constants.SUBMIT_SUCCESS, intent.putExtra("submitResult", true))
                finish()
            }

            "V10033" -> {
                showFailedDialog()
            }

            "V10034" -> {
                showFailedDialog2(result.data.obj?.incorrectAnswerPrompt, result.data.obj?.jumpLink)
            }

            else -> {
                mAdapter?.selectMap?.clear()
                mAdapter?.notifyDataSetChanged()
            }
        }

    }

    private fun showFailedDialog() {//第一次问卷失败
        CenterActionDialog.Builder(this)
            .setTitle(context.getString(R.string.you_have_not_passed_the_questionnaire))
            .setContent(context.getString(R.string.failed_asic_questionnaire))
            .setStartText(context.getString(R.string.failed_asic_questionnaire_back))
            .setEndText(context.getString(R.string.failed_asic_questionnaire_try_again))
            .setOnStartListener {
                hideNetDialog()
                finish()
            }
            .setOnEndListener {
                mAdapter?.selectMap?.clear()
                mAdapter?.notifyDataSetChanged()
                mBinding.mRecyclerView.scrollToPosition(0)
                mPresenter.getQustionList()
            }
            .build().showDialog()
    }

    override fun showFailedDialog2(incorrectAnswerPrompt: String?, jumpLink: String?) {//第二次问卷失败
        val dialog = CenterActionDialog.Builder(this)
            .setTitle(getString(R.string.you_have_not_passed_the_questionnaire))
            .setSingleButton(true)
            .setSingleButtonText(getString(R.string.confirm))
            .setOnSingleButtonListener {
                jump()
            }
            .build()
        val contentTextView = dialog.getContentViewBinding().tvDetail
        val wholeText = incorrectAnswerPrompt ?: ""
        val spannableString = SpannableString(wholeText)
        val span = jumpLink ?: ""
        val index = wholeText.indexOf(span)
        if (index != -1 && (index + span.length) <= wholeText.length) {
            spannableString.setSpan(object : ClickableSpan() {
                override fun onClick(widget: View) {
                    jump()
                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.isUnderlineText = true
                    ds.color = ContextCompat.getColor(this@AsicQuestionnaireActivity, R.color.ce35728)
                }
            }, index, (index + span.length), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            contentTextView.movementMethod = LinkMovementMethod.getInstance()
            contentTextView.highlightColor = ContextCompat.getColor(context, R.color.transparent)
        }
        (dialog as CenterActionDialog).setContent(spannableString)
        dialog.showDialog()
    }

    private fun jump() {
        val intent = Intent(this, AccountManagerActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
        }
        startActivity(intent)
        finish()
    }

    override fun onBackPressed() {
        super.onBackPressed()
        if (from == "235") {
            openActivity(AccountManagerActivity::class.java)
        }
        finish()
    }

}
