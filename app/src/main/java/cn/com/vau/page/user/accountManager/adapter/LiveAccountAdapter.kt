package cn.com.vau.page.user.accountManager.adapter

import android.view.View
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.AccountTradeBean
import cn.com.vau.util.*
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class LiveAccountAdapter : BaseMultiItemQuickAdapter<AccountTradeBean, BaseViewHolder>() {

    private val selectedAccountCd by lazy { UserDataUtil.accountCd() }

    init {
        addItemType(AccountTradeBean.TYPE_NORMAL, R.layout.item_manage_account_live_normal)
        addItemType(AccountTradeBean.TYPE_APPLYING, R.layout.item_manage_account_live_applying)
        addItemType(AccountTradeBean.TYPE_VIRTUAL, R.layout.item_manage_account_live_virtual_mt5)

        addChildClickViewIds(
            R.id.tvSetupNow,
            R.id.llExt, R.id.linearLayout, R.id.ivClose, R.id.ivSave, R.id.tvLiveUpgrade
        )
    }

    override fun convert(holder: BaseViewHolder, item: AccountTradeBean) {

        when (holder.itemViewType) {
            // 处理 审核中 类型的布局
            AccountTradeBean.TYPE_APPLYING -> {}
            // 处理 虚拟账号 类型的布局
            AccountTradeBean.TYPE_VIRTUAL -> {
                holder.setBackgroundResource(
                    R.id.clAccountCard,
                    if (selectedAccountCd == item.acountCd)
                        R.drawable.draw_shape_stroke_c1e1e1e_cebffffff_r10
                    else
                        R.drawable.draw_shape_stroke_c331e1e1e_c33ffffff_r10
                )
            }
            // 处理 正常/归档 账户类型的布局
            AccountTradeBean.TYPE_NORMAL -> {
                holder.setBackgroundResource(R.id.tvAccountType, R.drawable.shape_c00c79c_r100)
                holder.setText(R.id.tvAccountType, context.getString(R.string.live))
                holder.setGone(R.id.llEditNick, true)
                holder.setVisible(R.id.linearLayout, true)
                holder.setText(R.id.tvAccountName, if (item.nickName.isNullOrEmpty()) context.getString(R.string.set_account_name) else item.nickName)
                holder.setText(R.id.tvReadOnly, "(${context.getString(R.string.read_only)})")
                holder.setGone(R.id.tvReadOnly, true != item.detailData?.readyOnlyAccount)
                holder.setText(R.id.tvAccountNo, item.acountCd)
                holder.setText(
                    R.id.tvAccountAmount, when (item.equitySuccess) {
                        "1" -> {
                            item.detailData?.equity?.numCurrencyFormat(item.detailData?.currencyType.ifNull())
                        }

                        "2" -> {
                            "-"
                        }

                        else -> {
                            "..."
                        }
                    }
                )
                holder.setText(
                    R.id.tvAccountAmountUnit, when (item.equitySuccess) {
                        "1" -> {
                            item.detailData?.currencyType.ifNull()
                        }

                        else -> {
                            ""
                        }
                    }
                )
                // Live账户不涉及Reset
                holder.setGone(R.id.tvReset, true)
                holder.setGone(R.id.tvDemoReset, true)

                holder.setBackgroundResource(
                    R.id.clAccountCard,
                    if (selectedAccountCd == item.acountCd)
                        R.drawable.draw_shape_stroke_c1e1e1e_cebffffff_r10
                    else
                        R.drawable.draw_shape_stroke_c331e1e1e_c33ffffff_r10
                )

                when (item.secondSuccess) {
                    "1" -> { // 成功
                        val detailData = item.detailData

                        // Win Rate
                        holder.setText(R.id.tvWinRateNum, "${detailData?.profitRate?.numFormat(2)}%")
                        // Free Margin
                        holder.setText(R.id.tvFreeMarginNum, "${(detailData?.guarantee ?: "").numCurrencyFormat(detailData?.currencyType ?: "")} ${detailData?.currencyType}".arabicReverseTextByFlag(" "))
                        // Profit
                        holder.setText(R.id.tvProfitNum, "${(detailData?.profit ?: "").numCurrencyFormat(detailData?.currencyType ?: "")} ${detailData?.currencyType}".arabicReverseTextByFlag(" "))
                        // Margin Level
                        val guaranteeAmount = detailData?.guarantee.toDoubleCatching()
                        val equityAmount = detailData?.equity.toDoubleCatching()
                        holder.setText(
                            R.id.tvMarginLevelNum,
                            if (guaranteeAmount == equityAmount) "---"  // 可用保证金 == 净值，说明已用保证金是0 没有持仓
                            else "${detailData?.payRate?.numFormat(2)}%"
                        )
                        // Last Login
                        val lastLoginDate = if (detailData?.lastLoginDate.isNullOrBlank()) "" else detailData?.lastLoginDate
                        //                    holder.setGone(R.id.tvLoginTime, TextUtils.isEmpty(lastLoginDate))
                        holder.setText(R.id.tvLoginTime, context.getString(R.string.last_log_in_x, lastLoginDate))
                        // Type
                        holder.setText(R.id.tvTypeNum, detailData?.accountTypeName ?: "-")
                        // Leverage
                        holder.setText(R.id.tvLeverageNum, "${detailData?.leverage}:1") //杠杆

                        //是否显示ECN升级按钮
                        if (detailData?.ecnUpgrade == true) {
                            holder.setGone(R.id.tvLiveUpgrade, false)
                            val supervise = SpManager.getSuperviseNum("")
                            when (supervise) {
                                "1" -> { //ASIC
                                    holder.setText(
                                        R.id.tvLiveUpgrade,
                                        context.getString(R.string.upgrade_to_x, "Raw Premium")
                                    )
                                }

                                "14" -> { //VFSC2
                                    holder.setText(
                                        R.id.tvLiveUpgrade,
                                        context.getString(R.string.upgrade_to_x, "Pro ECN")
                                    )
                                }

                                else -> {
                                    holder.setGone(R.id.tvLiveUpgrade, true)
                                }
                            }
                        } else {
                            holder.setGone(R.id.tvLiveUpgrade, true)
                        }
                    }

                    "2" -> { // 失败
                        holder.setText(R.id.tvWinRateNum, "-")
                        holder.setText(R.id.tvFreeMarginNum, "-")
                        holder.setText(R.id.tvProfitNum, "-")
                        holder.setText(R.id.tvMarginLevelNum, "-")
                        holder.setText(R.id.tvTypeNum, "-")
                        holder.setText(R.id.tvLeverageNum, "-")
                        holder.setText(R.id.tvLoginTime, context.getString(R.string.last_log_in_x, ""))
                        //是否显示ECN升级按钮
                        holder.setGone(R.id.tvLiveUpgrade, true)
                    }

                    else -> { // 未加载
                        holder.setText(R.id.tvWinRateNum, "...")
                        holder.setText(R.id.tvFreeMarginNum, "...")
                        holder.setText(R.id.tvProfitNum, "...")
                        holder.setText(R.id.tvMarginLevelNum, "...")
                        holder.setText(R.id.tvTypeNum, "...")
                        holder.setText(R.id.tvLeverageNum, "...")
                        holder.setText(R.id.tvLoginTime, context.getString(R.string.last_log_in_x, ""))
                        //是否显示ECN升级按钮
                        holder.setGone(R.id.tvLiveUpgrade, true)
                    }
                }

                // 归档状态
                if (item.isArchive == true) {
                    val expiredColorRes = R.attr.color_c731e1e1e_c61ffffff
                    holder.setBackgroundResource(
                        R.id.clAccountCard,
                        R.drawable.draw_shape_stroke_c331e1e1e_c33ffffff_r10
                    )
                    holder.setTextColor(R.id.tvAccountName, AttrResourceUtil.getColor(context, expiredColorRes))
                    holder.setTextColor(R.id.tvAccountNo, AttrResourceUtil.getColor(context, expiredColorRes))
                    holder.setTextColor(R.id.tvAccountAmount, AttrResourceUtil.getColor(context, expiredColorRes))
                    holder.setTextColor(R.id.tvAccountAmountUnit, AttrResourceUtil.getColor(context, expiredColorRes))
                    holder.setTextColor(R.id.tvWinRate, AttrResourceUtil.getColor(context, expiredColorRes))
                    holder.setTextColor(R.id.tvWinRateNum, AttrResourceUtil.getColor(context, expiredColorRes))
                    holder.setTextColor(R.id.tvFreeMargin, AttrResourceUtil.getColor(context, expiredColorRes))
                    holder.setTextColor(R.id.tvFreeMarginNum, AttrResourceUtil.getColor(context, expiredColorRes))
                    holder.setTextColor(R.id.tvMarginLevel, AttrResourceUtil.getColor(context, expiredColorRes))
                    holder.setTextColor(R.id.tvMarginLevelNum, AttrResourceUtil.getColor(context, expiredColorRes))
                    holder.setTextColor(R.id.tvProfit, AttrResourceUtil.getColor(context, expiredColorRes))
                    holder.setTextColor(R.id.tvProfitNum, AttrResourceUtil.getColor(context, expiredColorRes))
                    holder.setTextColor(R.id.tvLoginTime, AttrResourceUtil.getColor(context, expiredColorRes))
                    holder.setTextColor(R.id.tvType, AttrResourceUtil.getColor(context, expiredColorRes))
                    holder.setTextColor(R.id.tvTypeNum, AttrResourceUtil.getColor(context, expiredColorRes))
                    holder.setTextColor(R.id.tvLeverage, AttrResourceUtil.getColor(context, expiredColorRes))
                    holder.setTextColor(R.id.tvLeverageNum, AttrResourceUtil.getColor(context, expiredColorRes))

                    holder.setText(R.id.tvAccountType, context.resources.getString(R.string.archived))
                    holder.setBackgroundResource(R.id.tvAccountType, R.drawable.draw_shape_c731e1e1e_c61ffffff_r100)
                    holder.setImageResource(R.id.ivArrow, R.drawable.draw_bitmap2_arrow_end10x10_c731e1e1e_c61ffffff)
                }

                val clAccountInfo = holder.getView<ConstraintLayout>(R.id.clAccountInfo)
                val ivExtent = holder.getView<ImageView>(R.id.ivExtent)
                if (item.showAccountInfo) {
                    if (clAccountInfo.visibility == View.GONE) {
                        clAccountInfo.visibility = View.VISIBLE
                        holder.setImageResource(
                            R.id.ivExtent,
                            if (item.isArchive == true)
                                R.drawable.draw_bitmap2_arrow_top10x10_c731e1e1e_c61ffffff
                            else
                                R.drawable.draw_bitmap2_arrow_top10x10_c1e1e1e_cebffffff
                        )
                    }
                } else {
                    if (clAccountInfo.visibility == View.VISIBLE) {
                        clAccountInfo.visibility = View.GONE
                        holder.setImageResource(
                            R.id.ivExtent,
                            if (item.isArchive == true)
                                R.drawable.draw_bitmap2_arrow_bottom10x10_c731e1e1e_c61ffffff
                            else
                                R.drawable.draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff
                        )
                    }
                }
            }
        }
    }

}