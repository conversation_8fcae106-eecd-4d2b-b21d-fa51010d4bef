package cn.com.vau.page.user.login

import android.content.Context
import android.content.Intent
import androidx.core.os.bundleOf
import cn.com.vau.data.profile.TelegramGetBotIdObjBean
import cn.com.vau.page.html.HtmlActivity

/**
 * author：lvy
 * date：2024/12/25
 * desc：
 */
object TelegramHelper {

    /**
     * 获取调转到 telegram H5页面的 intent
     */
    fun getTelegramH5Intent(context: Context, bean: TelegramGetBotIdObjBean?): Intent {
//        val htmlUrl = "${HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix}/telegramLogin" +
//                "?telegram_login=${bean?.userName.ifNull()}" +
//                "&bot_id=${bean?.botId.ifNull()}"
        val intent = Intent(context, HtmlActivity::class.java)
        intent.putExtras(bundleOf().apply {
            putInt("tradeType", 3)
            putString("title", "")
            putString("url", bean?.url)
        })
        return intent
    }
}