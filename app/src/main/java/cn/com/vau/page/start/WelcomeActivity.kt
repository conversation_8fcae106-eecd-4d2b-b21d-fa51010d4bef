package cn.com.vau.page.start

import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.Window
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import cn.com.vau.MainActivity
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.databinding.ActivityWelcomeBinding
import cn.com.vau.util.BarUtil
import cn.com.vau.util.PermissionUtil

/**
 * 引导页
 * Created by Sunshine on 2023/3/29
 */
class WelcomeActivity : BaseMvvmBindingActivity<ActivityWelcomeBinding>() {

    private val list by lazy {
        mutableListOf(
            WelcomeBean(
                "Trade faster", "Seamless execution &\n" +
                        "stable performance"
            ),
            WelcomeBean(
                "Trade better", "Lower trading costs &\n" +
                        "peace of mind"
            ),
            WelcomeBean(
                "Trade smarter", "Leading global multi-asset broker\n" +
                        "with 12+ years’ experience"
            ),
        )
    }

    override fun initTheme() {
        super.initTheme()
        //沉浸式状态栏
        val window: Window = window
        BarUtil.transparentStatusBar(window)
        BarUtil.transparentNavigationBar(window)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            PermissionUtil.checkPermissionWithCallback(this, Constants.PERMISSION_NOTIFICATIONS)
        }
    }

    override fun initView() {
        mBinding.mViewPager.adapter = ViewPager2Adapter(list)
        mBinding.mViewPager.offscreenPageLimit = 2
        mBinding.mViewPager.registerOnPageChangeCallback(object : OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                // 当前页面被选中时的回调
                if (position == list.size - 1) {
                    mBinding.goHome.visibility = View.VISIBLE
                }
            }

            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                // 页面滑动时的回调
            }

            override fun onPageScrollStateChanged(state: Int) {
            }
        })
    }

    override fun initListener() {
        super.initListener()
        mBinding.tvSkip.setOnClickListener {
            goHome()
        }
        mBinding.goHome.setOnClickListener {
            goHome()
        }
    }

    private fun goHome() {
        openActivity(MainActivity::class.java)
        SpManager.putAppStartFirst(false)
        finish()
    }
}