package cn.com.vau.page.user.openAccoGuide.lv2

import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import cn.com.vau.R
import cn.com.vau.page.user.openAccoGuide.OpenAccoGuideBaseActivity
import cn.com.vau.page.user.openAccoGuide.lv2.vm.OpenLv2ViewModel

class OpenAccoGuideLv2Activity : OpenAccoGuideBaseActivity<OpenLv2ViewModel>() {

    override fun initViewModels(): OpenLv2ViewModel =
        ViewModelProvider(this)[OpenLv2ViewModel::class.java]

    override fun setTitle(): String = getString(R.string.lv2_id_authentication)

    override fun setTitleTip(): String = getString(R.string.open_account_annotation_title)

    override fun getTabIconSelector(): Array<Array<Int>> = arrayOf(
        arrayOf(
            R.attr.imgOpenStepPersonal,
            R.attr.imgOpenStepUnSelectedPersonal
        ),
        arrayOf(
            R.attr.imgOpenStepID,
            R.attr.imgOpenStepUnSelectedID
        )
    )

    override fun getTabText(): Array<String> = arrayOf(
        getString(R.string.id_information),
        getString(R.string.id_photo)
    )

    override fun getFragmentList(): Array<Fragment> = arrayOf(
        OpenLv2IDInfoFragment.newInstance(),
        OpenLv2UploadFragment.newInstance()
    )

    override fun initParam() {
        super.initParam()
        mViewModel.fromWallet = intent.extras?.getBoolean("fromWallet", false) == true
    }

    override fun scrollBack() {
        super.scrollBack()
        if (page == 0) {
            mViewModel.clearUploadData()
        }
    }

    fun gotoNext() {
        tabSelected(1)
        if (page == 1) {
            mViewModel.initUploadData()
        }
    }
}