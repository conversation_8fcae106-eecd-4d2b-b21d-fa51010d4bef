#Vau_App_Android
# 代码规范
文档链接：
https://pj4w2l1pwuq.sg.larksuite.com/docx/CpfRdEis7oRfkIxX0hrlVkXGgz8
上述文档中的已确定规范和本README.md文档中的规则均为必须遵守的规则。不允许擅自更改，如有不同的意见或者建议需要先提出大家均认可后即可修改。

# 开发需知
需求文档为产品经理输出，如发现不合理 或 巨复杂入不敷出的点，主动反馈提出
完成需求文档开发内容，为每个开发基本工作要求，对自己所负责 模块/页面 应高标准要求
对比竞品，站在用户视角体验 vantage/竞品 ，发现有利于用户体验及时修改 或 同步产品经理
每个开发都需为整个项目负责，应站在整个项目的角度 思考逻辑 考虑问题

# Layout相关：
1 Activity and Fragment 主布局背景色统一使用： android:background="?attr/**mainLayoutBg**"
2 Layout 卡片化，所有卡片统一使用： android:background="@drawable/**draw_main_card**"

# 图片资源相关：【郑佼/崔飞】
从UI切图一律取3x切图，放入到drawable-xxhdpi目录
展示位较大或存储较大的图片需进行webp转换，尽量不用svg格式，不方便预览 ，其他用png格式

# drawable bitmap 命名相关【郑佼/新欢】
1 内部配置双主题 draw_bitmap 开头,无双主题配置 bitmap 开头
2 内部 src 图片资源命名统一 icon_source 开头

# 接口相关
1 命名规则：直接使用 url 变异为驼峰 (特殊符号后首字母大写)，去除特殊符号，所有方法加注释
     注：后端同事绞尽脑汁想出的名字，Android直接拿来主义就好，把时间精力放在 代码逻辑/性能优化。
2 入参直接使用接口规定名称，不允许私自修改
3 接口反参解析 Data 类必须继承 BaseBean()

# 页面 view 相关
1 所有View统一使用驼峰命名规则
2 页面只有单ViewGroup，使用mViewGroup (RecyclerView/ScrollView/ViewPager 等) 
  例：页面只有一个RecyclerView,直接使用 mRecyclerView,mAdapter
3 View 点击事件,赋值 统一顺序：按照布局从左到右,从上到下的
4 底部单按钮 ID 统一使用 tvNext / Activity & Fragment 父布局 ID 统一使用 main ( 351 UI改版结束统一更新 ) 

# shape 命名相关
1 内部配置双主题 draw 开头,无双主题配置 shape 开头
2 shape 后 _ 根据内部配置, 只有 solid 和 radius 为默认配置，默认配置时 solid 无需命名，所有 radius 以 r(角度) 结尾命名
3 除 solid 单独使用外，其他情况所有配置属性均需命名显示

# string 多语言相关 【吕洋】
1 新增多语言全部添加到 strings.xml 默认文件分隔线以下，分隔线以上文案禁止随意修改 key 和 value，需要修改时通知 owner，owner 同步到多语言文档
2 命名规则：①首句开头三个单词 + 标点符号后的第一个单词 + 末尾两个单词，使用`_`连接，命名不用太长10个单词内保证不重复即可
  ② 如果包含需要动态填充的内容，全部使用`%s`禁止使用其他替换符，在命名相应位置添加`_x`，owner 放完翻译后，owner 会对每种语言都对照一下看有没有问题，有多个%s要用`%1$s`、`%2$s`等
  ③ 如果内容中有替换符`%s`并且其他内容也含有百分号，其他内容的百分号后面需要再添加一个`%`如`20%%`
3 开发人员需要进行多语言翻译之前，对照多语言文档看是否缺翻译并与iOS沟通是否可复用别的 key，缺少的自己追产品要，文档都全了后通知 owner 进行国际化
4 开发人员做完东西后使用AS自带的 Layout Validation 工具多预览，看有没有布局覆盖、RTL布局适配问题。owner 多语言国际化后，会通知到对应开发，需要开发再核对一下自己页面布局，在多语言下是否有覆盖跑偏等情况
5 自己需求用不到的多语言加 <!-- todo 可删除 --> 注释，方便 owner 后期整理删除
6 全局禁用 String.format 拼接字符串，避免性能问题，需要拼接字符串时使用其他方案代替
7 开发禁止随意跟随 PM/UI 乱适配，文案显示不全找对应PM修改多语言或修改设计图
多语言文档：https://www.kdocs.cn/l/cpj7s7SA4llw

# 注意性能
1 ViewGroup 复杂度排行⬇️（从低至高 ），禁止使用 RelativeLayout
    FrameLayout -> LinearLayout（不涉及权重）-> ConstraintLayout -> RecyclerView
2 减少布局层级（布局层级会指数级增加页面的复杂度,尤其是根布局或者接近根布局）
3 ViewGroup / View 已知宽高值，尽量直接赋固定值，减少计算
4 **频繁刷新**的 RecyclerView 相关 【包含主页面频繁刷新的 layout】
    a: RecyclerView【Color】 在onBindViewHolder中使用需提取出来，禁止每次都通过xml来渲染
    b: 注意性能1 适用 RecyclerViewAdapter
    c: adapter 内禁止二次计算,所有数据外部计算，adapter内只做展示
    d: RecyclerView 尽量设置固定高度
5 **TextView 字体大小统一使用 dp**
    设置字体大小使用 sp 本身没有问题，使用 dp 本意是 去除基类不跟随系统字体变化代码（此处有性能消耗）
6 禁止在onDraw，onMeasure，onLayout中创建临时对象，应使用全局对象。
7 耗时操作，可加缓存，以减少计算的次数。可采用Lru算法。
8 固定集合且基本数据类型，List改成数组，以减少包装类转换的耗时。减少内存占用。
9 **全局替换LinearLayoutManager为WrapContentLinearLayoutManager**，避免异常crash

# 适配阿拉伯语样式
1 使用约束布局需要用 start end 代替 left right
2 如果TextView的宽度是 match_parent 或者 0dp撑满的 情况，如果文字不是居中的则需要设置 android:gravity 以及 android:textAlignment 来设置文字的重心（适配三星手机）
3 如果有TextView展示的文案有 “负数” 或者 货币类型等一些特殊的情况，需要设置 android:textDirection="ltr" 强制文案从左到右. 文案的反转关系 开发通过调用 arabicReverseTextByFlag() 处理
相关文档 ： https://pj4w2l1pwuq.sg.larksuite.com/docx/XTLSdzsLzoz9NMxJMkLl9kYQgaf

# 埋点相关
1 需要修改逻辑的埋点，简单修改可以接受，复杂修改或可能影响性能的计算等直接拒绝 PM。
2 自定义属性的 key，先找之前是否已经有，如果有意思一样但 key 不一样的通知 PM 修改成一样的。
3 自定义属性的 value，最好从现有存在的值取，如接口有返回、常量值等。
4 无特殊说明，神策埋点的布尔值，写成`1`和`0`，分别代表 true 和 false。
5 自定义属性如`identity_`开头属于神策内置字段需要修改为其他，事件名称如`TP/SLPopup_Confirm`包含特殊字符`/`需要修改为下划线`TP_SLPopup_Confirm`，否则无法正常上报。
6 埋点属于额外业务模块，在不影响埋点逻辑的前提下建议添加到方法最下方，不要写到方法开头。
7 埋点代码尽量放到业务层，如 Presenter、viewModel 层，尽量不要写到 UI 层。

# 功能 owner 榜

    SplashActivity / 引导页面 【*】

    交易初始化接口 【崔飞/*】
    WebSocket 【王剑/新欢】
    交易公共数据 【新欢】

    Main 首页 【王剑/金玉/周蕾】
        产品行情/产品自选
        账户消息/公告/其他消息/客服相关
        首页其他
      【周蕾】
         产品搜索
    Main 订单 
        【崔飞】
        下单挂单/改单/互抵平仓/部分平仓/批量平仓
        交易设置/杠杠修改
        跟单自主交易 -- 持仓列表/挂单列表
        多品牌交易  -- 持仓列表/挂单列表   
        【金玉】
        跟随策略列表/历史跟随策略列表/分润比例/待审核策略列表/已拒绝策略列表
        策略订单详情页
        价格提醒   
        【周蕾】
        跟单自主交易 -- 历史订单
        多品牌交易  -- 历史订单
        设置止盈止损/反向开仓
        交易权限校验
    Main 发现/活动【金玉】
        策略社区/策略搜索
        学院/24*7
        财经日历/财经日历筛选/财经日历详情
        直播列表/直播间
        分析师观点/Newsletter
        Fx Tv 列表/Fx Tv播放页
    Main Profile 【金玉】
    Main 底部Tab 【新欢/文超】

    登录注册【崔飞/吕洋】
        登录引导页/手机号登录/邮箱登录/passkey登录
        注册/设置密码
        忘记密码/重置密码
        绑定手机号/绑定邮箱
        验证OTP
        
    开户/账户列表【王剑】
        ASIC监管开户（老开户）
        非ASIC监管开户（新开户）
        开同名账户

    跟单策略 【吕洋】
        信号源中心/创建策略/编辑策略
        信号源详情/策略详情
        跟随策略/策略追加减少资金/跟随策略设置
        策略收藏列表/策略收藏者列表

    K线页面 【崔飞/王剑/宇鹤】
    【崔飞】K线图表

    选商品(新搜索) 【周蕾】
    个人信息【金玉】
    认证中心【金玉】
    安全认证（账户与安全）【吕洋】
        通行密钥/双重身份验证(2FA)/安全锁/更新手机号码/修改登录密码/设置资金密码/设备历史记录
    IB佣金【*】

    InApp 【金玉】
    Dialog / PopupWindow 【文超】https://pj4w2l1pwuq.sg.larksuite.com/docx/JnRRdMjtuo7SsUxtiqRlm1udgYe
    
    本地数据存储 MMKV/SP/GreenDao & Data【王剑/崔飞】
    MVVM 架构 【吕洋/王剑】
    Performance 页面功能拆分框架【周蕾】
    扩展函数【*】
    Strings 国际化 (包含阿拉伯语适配)【吕洋/崔飞】
    页面间跳转参数格式 【*】
    埋点【吕洋】

    HeaderBar 【新欢/郑佼】
    TabLayout 控件 + ViewPager2 + ViewPager【吕洋】
    缺省布局 NoDataView/NoDataScrollView 【新欢/郑佼】

    Icons组件库 main 【新欢】
    Icons组件库 可点击 【崔飞】
    Icons组件库 不可点击【崔飞】

